package com.jiuzhekan.cbkj.beans.business.chargeitem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TChargeItem implements Serializable{

    @ApiModelProperty(value = "")
    private String chargeItemId;

    @ApiModelProperty(value = "收费项目代码")
    private String chargeItemCode;

    @ApiModelProperty(value = "收费项目名称")
    private String chargeItemName;

    @ApiModelProperty(value = "收费项目类型0 中心药房 1 HIS 2、通用")
    private String chargeItemType;

    @ApiModelProperty(value = "收费项目描述")
    private String chargeItemDesc;

    @ApiModelProperty(value = "收费项目拼音")
    private String chargeItemPy;

    @ApiModelProperty(value = "收费项目五笔")
    private String chargeItemWb;

    @ApiModelProperty(value = "0正常1删除")
    private Integer status;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    private String createUserId;

    @ApiModelProperty(value = "")
    private Date createTime;


}
