//package com.jiuzhekan.cbkj.beans.business.sysCode.VO;
//
//import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCode;
//import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCodeItem;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.List;
//
//@Data
//@ApiModel(value = "公用代码大类下拉框类")
//public class TSysCodeSelVO implements Serializable {
//    @ApiModelProperty(value = "代码大类ID")
//    private Short codeId;
//    @ApiModelProperty(value = "代码大类名称")
//    private String codeName;
//    @ApiModelProperty(value = "代码大类对应的代码明细")
//    List<TSysCodeItemSelVO> itemList;
//}