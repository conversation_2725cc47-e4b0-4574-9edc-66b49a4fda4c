package com.jiuzhekan.cbkj.beans.pharmacy;

import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel
public class TDisplayProduction implements Serializable {

    @ApiModelProperty(value = "主键")
    private String setId;

    @ApiModelProperty(value = "机构药房配置ID")
    private String displayId;

    @ApiModelProperty(value = "门诊，住院(1门诊，2住院)")
    private String outpatientOrHospitalization;

    @ApiModelProperty(value = "制膏配置是否开启(0是，1否)")
    private String showProduction;

    @ApiModelProperty(value = "默认制膏")
    private String isProduction;

    @ApiModelProperty(value = "医保患者是否显示膏方(0是，1否)")
    private String isShowProduction;

    @ApiModelProperty(value = "是否显示膏方类型(0是，1否)")
    private String isShowProductionType;


    @ApiModelProperty(value = "膏方控制开方贴数")
    private Integer productionNum;

    @ApiModelProperty(value = "通用制膏费用配置（0默认，1配置）")
    private String usuallyProductionSet;

    @ApiModelProperty(value = "配方制膏费用配置（0默认，1配置）")
    private String formulaProductionSet;

    @ApiModelProperty(value = "状态",hidden = true)
    private String status;

    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createDate;

    @ApiModelProperty(value = "创建人ID",hidden = true)
    private String createUser;

    @ApiModelProperty(value = "默认制膏费用-默认")
    List<TDisplayMoneySetting> showDefaultList;

    @ApiModelProperty(value = "通用制膏费用配置-配置-字典获取")
    List<TDisplayMoneySetting> showDicList;


    @ApiModelProperty(value = "默认配方制膏费用-默认")
    List<TDisplayMoneySetting> formulaDefaultList;

    @ApiModelProperty(value = "配方制膏费用配置-配置-字典获取")
    List<TDisplayMoneySetting> formulaDicList;

    /**
     *
     * @param displayId 机构药房配置ID
     * @param outpatientOrHospitalization 门诊，住院(1门诊，2住院)
     * @param showProduction 制膏配置是否开启(0是，1否)
     * @param isShowProduction 医保患者是否显示膏方(0是，1否)
     * @param isShowProductionType 是否显示膏方类型(0是，1否)
     * @param usuallyProductionSet 通用制膏费用配置（0默认，1配置）
     * @param formulaProductionSet 配方制膏费用配置（0默认，1配置）
     */
    public TDisplayProduction(String displayId, String outpatientOrHospitalization,
                              String showProduction, String isShowProduction,String isProduction, String isShowProductionType,
                              String usuallyProductionSet, String formulaProductionSet) {
        this.displayId = displayId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
        this.showProduction = showProduction;
        this.isProduction = isProduction;
        this.isShowProduction = isShowProduction;
        this.isShowProductionType = isShowProductionType;
        this.usuallyProductionSet = usuallyProductionSet;
        this.formulaProductionSet = formulaProductionSet;

    }

    /**
     *
     * @param displayId 机构药房配置ID
     * @param outpatientOrHospitalization 门诊，住院(1门诊，2住院)
     */
    public TDisplayProduction(String displayId, String outpatientOrHospitalization) {
        this.displayId = displayId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
    }
    public void initDefaultValue() {
        this.status = Constant.BASIC_STRING_ZERO;
        this.createDate = new Date();
        this.setCreateUser(AdminUtils.getCurrentHr().getUserId());
    }
}
