package com.jiuzhekan.cbkj.beans.business.setting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel(value = "附件类" , description = "附件类")
public class TBusinessAnnex implements Serializable {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "第三方ID")
    private String annexForeignId;

    @ApiModelProperty(value = "附件名称")
    private String annexName;

    @ApiModelProperty(value = "附件原名称")
    private String annexOriginalName;

    @ApiModelProperty(value = "附件后缀名")
    private String annexSuffixName;

    @ApiModelProperty(value = "附件大小")
    private String annexSize;

    @ApiModelProperty(value = "附件路径")
    private String annexPath;

    @ApiModelProperty(value = "附件所属业务类型：1我的建议,2操作手册")
    private Byte annexType;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private Byte isDel;

    /**
     * @Description :
     * <AUTHOR> xhq
     * @updateTime : 2020/3/3 11:30
     */
    @ApiModelProperty(value = "标题")
    private String manualTitle;
}