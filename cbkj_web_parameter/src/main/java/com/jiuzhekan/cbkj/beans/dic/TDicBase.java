package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDicBase implements Serializable {

    @ApiModelProperty(value = "id")
    private String dicId;

    @ApiModelProperty(value = "字典代码")
    private String dicCode;

    @ApiModelProperty(value = "字典名称")
    private String dicName;

    @ApiModelProperty(value = "父ID（第一级为0）")
    private String parentId;

    @ApiModelProperty(value = "父级名称")
    private String parentName;

    @ApiModelProperty(value = "字典描述")
    private String dicDesc;


    @ApiModelProperty(value = "是否默认(1是0否)")
    private String isDefault;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构ID")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

/*    @ApiModelProperty(value = "药房ID")
    private String phaId;*/

    @ApiModelProperty(value = "机构药房ID")
    private String displayId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "标准编码")
    private String stanCode;

    @ApiModelProperty(value = "标准名称")
    private String stanName;

    @ApiModelProperty(value = "是否映射")
    private String mapping;

    @ApiModelProperty(value = "映射ID")
    private String mapId;

    @ApiModelProperty(value = "搜索关键词")
    private String keyWord;

    @ApiModelProperty(value = "机构药房显示名称")
    private String displayName;

    @ApiModelProperty(value = "医共体名称")
    private String appName;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "业务明细区分 1:系统，2:HIS,3:药房")
    private String businessType;

    @ApiModelProperty(value = "系统是否配置")
    private String sysChecked;

    @ApiModelProperty(value = "his是否配置")
    private String hisChecked;

    @ApiModelProperty(value = "药房是否配置")
    private String displayChecked;
    private String otherJson;

    @ApiModelProperty(value = "费用是否配置")
    private Integer dosageCostStatus;
    @ApiModelProperty(value = "制剂开关是否配置")
    private Integer dosageDescribeStatus;
}
