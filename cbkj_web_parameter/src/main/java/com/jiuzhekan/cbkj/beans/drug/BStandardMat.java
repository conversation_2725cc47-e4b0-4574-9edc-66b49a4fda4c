package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel
@NoArgsConstructor
public class BStandardMat implements Serializable{

    @ApiModelProperty(value = "id")
    private String sId;

    @ApiModelProperty(value = "标准代码")
    private String sMatCode;

    @ApiModelProperty(value = "标准名称")
    private String sMatName;

    @ApiModelProperty(value = "药品类型(1、饮片  2、颗粒  3膏方 4 配方 5制剂 6中成药)")
    private String sMatClass;

    @ApiModelProperty(value = "1浙江标准,2全国标准")
    private String sMatType;

    private String keyWord;

    public String getsId() {
        return sId;
    }

    public void setsId(String sId) {
        this.sId = sId;
    }

    public String getsMatCode() {
        return sMatCode;
    }

    public void setsMatCode(String sMatCode) {
        this.sMatCode = sMatCode;
    }

    public String getsMatName() {
        return sMatName;
    }

    public void setsMatName(String sMatName) {
        this.sMatName = sMatName;
    }

    public String getsMatClass() {
        return sMatClass;
    }

    public void setsMatClass(String sMatClass) {
        this.sMatClass = sMatClass;
    }

    public String getsMatType() {
        return sMatType;
    }

    public void setsMatType(String sMatType) {
        this.sMatType = sMatType;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }
}
