package com.jiuzhekan.cbkj.beans.sysBeans;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import springfox.documentation.annotations.ApiIgnore;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 角色
 */
@Data
@NoArgsConstructor
@ApiModel
public class AdminRule implements Serializable {

    private static final long serialVersionUID = -6335654944450560361L;
    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "角色英文名")
    private String roleName;

    @ApiModelProperty(value = "角色中文名")
    private String rnameZh;

    @ApiModelProperty(value = "备注说明")
    private String roleDesc;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    private String createUser;

    private String objId;//扩展第三方主键【】

    @ApiModelProperty(value = "首页地址")
    private String indexUrl;

    @ApiModelProperty(value = "搜索条件")
    private String keyWord;
}