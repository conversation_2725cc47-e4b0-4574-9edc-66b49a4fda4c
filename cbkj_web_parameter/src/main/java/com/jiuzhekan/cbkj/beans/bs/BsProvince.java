package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("省")
public class BsProvince implements Serializable {

    private Integer provinceId;

    @ApiModelProperty("省代码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("简称")
    private String provinceShortName;

    private String provinceLng;

    private String provinceLat;

    private Integer sort;

//    private Date gmtCreate; CREATE_DATE
    private Date createDate;

//    private Date gmtModified; MODIFY_DATE
    private Date modifyDate;

//    private String memo; DESC
    private String desc;

//    private Integer dataState; IS_ENABLE
    private Integer status;


}
