package com.jiuzhekan.cbkj.beans.business.displaydosage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayDosageDescribe implements Serializable{

    @ApiModelProperty(value = "")
    private String dosageDescribeId;

    @ApiModelProperty(value = "")
    private String dicId;

    @ApiModelProperty(value = "3.系统明细2.HIS明细1.药房明细")
    private Integer dicType;

    @ApiModelProperty(value = "每日剂数（0显示1隐藏）")
    private Integer dailyDosageShow;

    @ApiModelProperty(value = "每日剂数（0必填1不必填）")
    private Integer dailyDosageRequired;

    @ApiModelProperty(value = "每日制剂次数（0显示1隐藏）")
    private Integer dailyPreparationsShow;

    @ApiModelProperty(value = "每日制剂次数（0必填1不必填）")
    private Integer dailyPreparationsRequired;

    @ApiModelProperty(value = "每日加工量-0显示1隐藏")
    private Integer dailyProcessingShow;

    @ApiModelProperty(value = "每日加工量-0必填1不必填")
    private Integer dailyProcessingRequired;

    @ApiModelProperty(value = "每次用量-0显示1隐藏")
    private Integer eachTimeShow;

    @ApiModelProperty(value = "每次用量-0必填1不必填")
    private Integer eachTimeRequired;


}
