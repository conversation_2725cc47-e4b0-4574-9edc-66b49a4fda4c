package com.jiuzhekan.cbkj.beans.business.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysBanner implements Serializable{

    @ApiModelProperty(value = "")
    private String bannerId;

    @ApiModelProperty(value = "")
    private String bannerName;

    @ApiModelProperty(value = "")
    private String bannerUrl;

    @ApiModelProperty(value = "")
    private String status;


}
