package com.jiuzhekan.cbkj.beans.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayCurrency implements Serializable{

    @ApiModelProperty(value = "")
    private String displayCurrencyId;
    private Integer manySpeSwitch;

    @ApiModelProperty(value = "机构药房ID")
    private String displayId;

    @ApiModelProperty(value = "是否预扣计算 0否1是")
    private String withholdSwitch;

    @ApiModelProperty(value = "处方保存接口校验库存开关 1是0否")
    private String preStockSwitch;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "内服服药时间开关 0关 1开，默认开")
    private Integer oralMedicationTimeSwitch;

    @ApiModelProperty(value = "是否加急 0否 1是")
    private Integer urgentSign;

    public TDisplayCurrency(String displayCurrencyId, String displayId, String withholdSwitch,
                            String preStockSwitch, Date createDate, String createUser,Integer manySpeSwitch,Integer oralMedicationTimeSwitch,Integer urgentSign) {
        this.displayCurrencyId = displayCurrencyId;
        this.displayId = displayId;
        this.withholdSwitch = withholdSwitch;
        this.preStockSwitch = preStockSwitch;
        this.createDate = createDate;
        this.createUser = createUser;
        this.manySpeSwitch = manySpeSwitch;
        this.oralMedicationTimeSwitch = oralMedicationTimeSwitch;
        this.urgentSign = urgentSign;
    }
}