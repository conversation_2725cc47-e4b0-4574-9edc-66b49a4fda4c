package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysAdminPractice implements Serializable{

    @ApiModelProperty(value = "执业ID")
    private Integer practiceId;

    @ApiModelProperty(value = "用户ID")
    private String adminId;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "科室ID")
    private String depId;

    @ApiModelProperty(value = "科室名称")
    private String depName;

    @ApiModelProperty(value = "工号")
    private String employeeId;

    @ApiModelProperty(value = "第三方医生ID")
    private String originDoctorId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "修改人")
    private String updateUser;


}
