package com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@NoArgsConstructor
@ApiModel
@Data
public class TDisplayDecoctionCurrencyVo {

    @ApiModelProperty(value = "住院信息")
    List<String> displayIds;

    @ApiModelProperty(value = "门诊信息")
    private TDisplayDecoction outpatient;

    @ApiModelProperty(value = "住院信息")
    private TDisplayDecoction hospitalization;
}
