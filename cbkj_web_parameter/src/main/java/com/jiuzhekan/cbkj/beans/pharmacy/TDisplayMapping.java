package com.jiuzhekan.cbkj.beans.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayMapping implements Serializable{

    @ApiModelProperty(value = "药房配置ID")
    private String displayId;

    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "HIS科室ID")
    private String deptId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;


}
