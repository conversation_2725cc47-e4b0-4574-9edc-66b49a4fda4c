package com.jiuzhekan.cbkj.beans.drug;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel
public class VCenterYpmlmxVo {
    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品名称id")
    private String matId;

    @ApiModelProperty(value = "价格id")
    private String matPriceId;

    @ApiModelProperty(value = "日最大剂量（内服）")
    private Double dailyMaxDoseIn;

    @ApiModelProperty(value = "日最大剂量（外用）")
    private Double dailyMaxDoseExt;

    @ApiModelProperty(value = "单独使用时不予支付(医保字段)")
    private String notPayAlone;

    @ApiModelProperty(value = "不纳入基金支付范围")
    private String notPayInFund;

    @ApiModelProperty(value = "毒性超剂量双签倍数")
    private String toxicityOverdoseMultiple;
    @ApiModelProperty(value = "仅限外用(0关1开)")
    private String externalUseOnly;

    @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

    @ApiModelProperty(value = "最大剂量")
    private Double mindose;


    /************************* v1.2.5 新增***********************/
    @ApiModelProperty(value = "毒性")
    private String toxicity;

    @ApiModelProperty(value = "孕妇慎禁忌")
    private String motherTaboos;

    @ApiModelProperty(value = "仅限内服(0关1开)")
    private String externalUseOrally;
    @ApiModelProperty(value = "仅限丸散(0关1开)")
    private String externalMarusan;
    @ApiModelProperty(value = "仅限丸散关键字")
    private String externalMarusanName;

    @ApiModelProperty(value = "药品特殊用法")
    private String  specialUsages;

    @ApiModelProperty(value = "特殊用法限制")
    private String  usagesAstrict;
    private String  sdkTagsCodes;

}
