package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2024/11/7 14:11
 */
@Data
public class TAppMaterial {
    @ApiModelProperty(value = "知识库药品ID")
    private String kMatId;

    @ApiModelProperty(value = "知识库药品名称")
    private String kMatName;

    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品价格id")
    private String matPriceId;

    @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

    @ApiModelProperty(value = "最大剂量")
    private Double mindose;

    @ApiModelProperty(value = "毒性")
    private String toxicity;

    @ApiModelProperty(value = "孕妇慎禁忌")
    private String motherTaboos;


    @ApiModelProperty(value = "药品特殊用法")
    private String  specialUsages;
}
