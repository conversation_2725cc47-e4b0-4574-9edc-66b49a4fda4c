package com.jiuzhekan.cbkj.beans.business.chargeitem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/5 14:22
 * @Version 1.0
 */
@ApiModel
@Data
public class GetChargeItemList {
    @ApiModelProperty(value = "收费项目id")
    private String chargeItemId;
    @ApiModelProperty(value = "收费项目详情编码或者名称")
    private String itemDetailCodeOrName;
    @ApiModelProperty(value = "项目分类")
    private String itemDetailType;
    @ApiModelProperty(value = "执行科室")
    private List<String> itemDetailExecuteDepts;
    @ApiModelProperty(value = "收费项目详情状态0启用1停用")
    private Integer itemDetailStatus;
    private Integer page = 1;
    private Integer limit = 10;

}
