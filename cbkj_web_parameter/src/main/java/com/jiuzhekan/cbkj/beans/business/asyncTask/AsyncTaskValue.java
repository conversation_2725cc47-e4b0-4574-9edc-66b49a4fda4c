package com.jiuzhekan.cbkj.beans.business.asyncTask;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class AsyncTaskValue implements Serializable{

    @ApiModelProperty(value = "任务主表id")
    private Integer asyncId;

    @ApiModelProperty(value = "药品目录")
    private String drugId;

    @ApiModelProperty(value = "医共体id")
    private String appId;

    @ApiModelProperty(value = "机构代码")
    private String insCode;

    @ApiModelProperty(value = "机构id")
    private String insId;

    @ApiModelProperty(value = "任务分类：1.药品目录2.药品库存3.医生4.科室")
    private String asyncType;


}
