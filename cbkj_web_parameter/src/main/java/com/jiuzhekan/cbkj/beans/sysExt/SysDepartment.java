package com.jiuzhekan.cbkj.beans.sysExt;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("科室")
public class SysDepartment implements Serializable {

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty("科室名称")
    private String deptName;

    @ApiModelProperty("医联体ID")
    private String appId;

//    @ApiModelProperty("上级医疗机构代码")
//    private String insPcode;

    @ApiModelProperty(value = "医疗机构代码 和 appId唯一确定机构"+
            "大科室有可能是公共的，所以这里非必填，小科室一定对应到医疗机构")
    private String insCode;

    private String appName;

    private String insName;

    @ApiModelProperty("第三方科室ID")
    private String depOriginId;

    @ApiModelProperty("上级科室ID")
    private String depParentId;

    @ApiModelProperty("上级科室")
    private String depParentName;

//    @ApiModelProperty("末级标志(0否 1是)")
//    private Short isLast;

    @ApiModelProperty(hidden = true)

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String createUser;

    @ApiModelProperty(hidden = true)
    private String createUserName;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @ApiModelProperty(hidden = true)
    private String updateUser;

    @ApiModelProperty(hidden = true)
    private String updateUserName;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "拼音码")
    private String deptPinyin;

    @ApiModelProperty(value = "五笔码")
    private String deptWubi;


    @ApiModelProperty(value = "类别：1 科室、2 病区 默认1")
    private String deptType;

    @ApiModelProperty(value = "sort")
    private Integer sort;

    private String insParentCode;

    @ApiModelProperty(value = "科室地点")
    private String deptAddress;

    @ApiModelProperty(value = "发热门诊标志：0发热1正常")
    private Integer frmzbz;


}