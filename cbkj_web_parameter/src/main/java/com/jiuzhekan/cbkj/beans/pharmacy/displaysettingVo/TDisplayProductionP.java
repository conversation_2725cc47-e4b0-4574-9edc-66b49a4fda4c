package com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 **/
@NoArgsConstructor
@ApiModel
@Data
public class TDisplayProductionP {

    @ApiModelProperty(value = "门诊信息")
    private TDisplayProduction outpatient;

    @ApiModelProperty(value = "住院信息")
    private TDisplayProduction hospitalization;

    @ApiModelProperty(value = "disPlayId多个用逗号拼接")
    private String pharmacyDisplayList;
}
