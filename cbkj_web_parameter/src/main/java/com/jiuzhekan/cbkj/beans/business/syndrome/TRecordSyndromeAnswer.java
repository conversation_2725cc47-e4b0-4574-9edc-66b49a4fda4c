package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class TRecordSyndromeAnswer implements Serializable{

    @ApiModelProperty(value = "答案记录ID")
    private String rsAnswerId;

    @ApiModelProperty(value = "问题记录ID")
    private String rsAskId;

    @ApiModelProperty(value = "答案ID")
    private String answerId;

    @ApiModelProperty(value = "答案")
    private String answerName;

    @ApiModelProperty(value = "序号")
    private String answerSeqn;

    @ApiModelProperty(value = "症状ID")
    private String itemId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "所属病历内容类型")
    private String tempType;


}
