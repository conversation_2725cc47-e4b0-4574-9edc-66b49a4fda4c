package com.jiuzhekan.cbkj.beans.sysParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@ApiModel
public class DiagnosisParamNewVO implements Serializable{

    @ApiModelProperty(value = "参数ID")
    private String parId;

    @ApiModelProperty(value = "参数代码")
    private String parCode;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码（000000代表该医联体中所有的医疗机构）")
    private String insCode;

    @ApiModelProperty(value = "科室id")
    private String deptId;

    @ApiModelProperty(value = "参数类型：1 判断：是|否  开通|不开通  2 单选（多选一）：铺开  21不铺开3 多选（多选多）：铺开4 排序（多文本）：可拖拽排序5 文本输入：6 时间段")
    private String paramType;

    @ApiModelProperty(value = "参数父节点菜单id，若是页面分类，则菜单为不显示配置")
    private String menuId;

    @ApiModelProperty(value = "用户选择的值")
    private String parValues;

    private Map<String,Object> data;
}

