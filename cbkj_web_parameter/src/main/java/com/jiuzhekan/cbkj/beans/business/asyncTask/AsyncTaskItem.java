package com.jiuzhekan.cbkj.beans.business.asyncTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel
public class AsyncTaskItem implements Serializable{

    @ApiModelProperty(value = "ID")
    private Integer taskItemId;

    @ApiModelProperty(value = "任务主表id")
    private Integer asyncId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @ApiModelProperty(value = "同步开始时间")
    private Date asyncStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @ApiModelProperty(value = "同步结束时间")
    private Date asyncEndDate;

    @ApiModelProperty(value = "同步耗时时长 单位毫秒")
    private Integer asyncTime;

    @ApiModelProperty(value = "同步完成，成功**条，失败**条")
    private String asyncResult;

    @ApiModelProperty(value = "调用his开始："+
                                "调用his结束："+
                                "耗时："+
                                "调用入库开始："+
                                "调用入库结束："+
                                "耗时："+
                                "共同步**条，成功**条，失败**条")
    private String asyncResultDetail;

    @ApiModelProperty(value = "任务分类：1.药品目录2.药品库存3.医生4.科室")
    private String asyncType;

    @ApiModelProperty(value = "任务类型：1.定时执行  2 一次任务")
    private String asyncExecType;

    @ApiModelProperty(value = "任务期间-开始时间")
    private String asyncStartTime;

    @ApiModelProperty(value = "任务期间-截止时间")
    private String asyncEndTime;

    @ApiModelProperty(value = "任务执行间隔时间值。")
    private String asyncBetweenTime;

    @ApiModelProperty(value = "任务执行间隔时间单位  m/h")
    private String asyncBetweenUnit;

//    @ApiModelProperty(value = "药品目录id，医疗机构id，若为多个，以逗号间隔")
//    private String asyncValue;

    @ApiModelProperty(value = "对应的是哪个物理his，及相应的接口信息，任务以物理his进行任务记录，药品目录有对应关系，医疗机构没有对应关系，以实际配置为准")
    private String hisId;

    @ApiModelProperty(value = "状态0.成功1失败2同步中")
    private String status;

    @ApiModelProperty(value = "任务详情（只在分页查询返回）")
    private String taskDetail;

//    @ApiModelProperty(value = "接口类型:1视图、2http、3webService、")
//    private String requestType;

    @ApiModelProperty(value = "任务名称")
    private String asyncName;

    @ApiModelProperty(value = "第三方名称")
    private String hisName;


}
