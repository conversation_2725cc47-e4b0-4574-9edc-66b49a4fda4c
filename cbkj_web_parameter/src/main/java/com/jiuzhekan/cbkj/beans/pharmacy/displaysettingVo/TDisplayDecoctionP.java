package com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@ApiModel
@Data
public class TDisplayDecoctionP {
    @ApiModelProperty(value = "门诊信息")
    private TDisplayDecoction outpatient;

    @ApiModelProperty(value = "住院信息")
    private TDisplayDecoction hospitalization;

    @ApiModelProperty(value = "disPlayId多个用逗号拼接")
    private String pharmacyDisplayList;
}
