package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.common.utils.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应实体
 */
@Data
public class ResEntity implements Serializable {

    private static final long serialVersionUID = -1439573222637546375L;
    private boolean status = true;
    private int code = 0;
    private String message;
    private Object data;

    public ResEntity() {

    }

    public ResEntity(boolean status, Object data) {
        this.status = status;
        this.data = data;
    }


    public ResEntity(boolean status, String message, Object data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public ResEntity(boolean status, int code, String message, Object data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static ResEntity entity(boolean status, String message, Object data) {
        return new ResEntity(status, message, data);
    }

    public static ResEntity success(Object data) {
        return new ResEntity(true, Constant.SUCCESS_DX, data);
    }

    public static ResEntity error(String message) {
        return new ResEntity(false, message, null);
    }

    public static ResEntity error(int code, String message) {
        return new ResEntity(false, code, message, null);
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}

