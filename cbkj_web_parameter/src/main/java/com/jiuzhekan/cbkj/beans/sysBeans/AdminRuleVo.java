package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel
public class AdminRuleVo {

    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "角色英文名")
    private String roleName;

    @ApiModelProperty(value = "角色中文名")
    private String rnameZh;

    @ApiModelProperty(value = "备注说明")
    private String roleDesc;
}
