package com.jiuzhekan.cbkj.beans.sysapp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("医联体|医共体表")
public class SysApp implements Serializable {

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医联体名称")
    private String appName;

    @ApiModelProperty("医联体密码")
    private String appPwd;

    @ApiModelProperty("医联体描述")
    private String appDesc;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String createUser;

    @ApiModelProperty(hidden = true)
    private String createUserName;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @ApiModelProperty(hidden = true)
    private String updateUser;

    @ApiModelProperty(hidden = true)
    private String updateUserName;

    @ApiModelProperty(hidden = true)
    private Integer sort;

    @ApiModelProperty(hidden = true,value = "是否有效（0删除1有效 2禁用）默认1")
    private String status;

    @ApiModelProperty(hidden = true,value = "所属平台",required = true)
    private String platformCode;

    private List<SysInstitution> insList;

    private List<Map<String, Object>> appInsList;
}