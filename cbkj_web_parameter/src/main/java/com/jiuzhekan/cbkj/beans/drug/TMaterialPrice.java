package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TMaterialPrice implements Serializable{

    @ApiModelProperty(value = "价格id")
    private String matPriceId;

    @ApiModelProperty(value = "药品目录Id")
    private String drugId;

    @ApiModelProperty(value = "药品产地id")
    private String matOriginId;

    @ApiModelProperty(value = "规格id")
    private String matSpeId;

    @ApiModelProperty(value = "药品id")
    private String matId;

    @ApiModelProperty(value = "")
    private Double retailPrice;

    @ApiModelProperty(value = "大规格进价")
    private Double purchasePrice;

    @ApiModelProperty(value = "小规格进价")
    private Double smallPurchasePrice;

    @ApiModelProperty(value = "小规格零售价")
    private Double smallRetailPrice;

    @ApiModelProperty(value = "加价率")
    private Double markupRate;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "是否赠品")
    private String isGive;

    @ApiModelProperty(value = "")
    private Short priceSeqn;

    @ApiModelProperty(value = "产地名称")
    private String matOriginName;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "")
    private Date disableDate;

    @ApiModelProperty(value = "")
    private String disableUser;

    @ApiModelProperty(value = "")
    private String disableUsername;

    @ApiModelProperty(value = "")
    private String isDisable;

    @ApiModelProperty(value = "是否有库存 （0没有  1有）")
    private String isStock;

    @ApiModelProperty(value = "日最大剂量（内服）")
    private Double dailyMaxDoseIn;

    @ApiModelProperty(value = "日最大剂量（外用）")
    private Double dailyMaxDoseExt;

    @ApiModelProperty(value = "医保日最大开药量（制剂）")
    private String dailyMaxNumPrep;

    @ApiModelProperty(value = "单独使用时不予支付")
    private String notPayAlone;

    @ApiModelProperty(value = "不纳入基金支付范围")
    private String notPayInFund;

    @ApiModelProperty(value = "频次ID")
    private String frequencyId;

    @ApiModelProperty(value = "频次")
    private String frequency;

    @ApiModelProperty(value = "频次系数(次数/天)")
    private Double frequencyRate;

    @ApiModelProperty(value = "毒性超剂量双签倍数")
    private String toxicityOverdoseMultiple;
    @ApiModelProperty(value = "不纳入基金支付范围")
    private String externalUseOnly;

    @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

    @ApiModelProperty(value = "最大剂量")
    private Double mindose;



    @ApiModelProperty(value = "毒性")
    private String toxicity;

    @ApiModelProperty(value = "孕妇慎禁忌")
    private String motherTaboos;

    @ApiModelProperty(value = "仅限内服(0关1开)")
    private String externalUseOrally;
    @ApiModelProperty(value = "仅限丸散(0关1开)")
    private String externalMarusan;
    @ApiModelProperty(value = "限制用法关键字")
    private String externalMarusanName;


    @ApiModelProperty(value = "药品特殊用法")
    private String  specialUsages;

    @ApiModelProperty(value = "特殊用法限制(0关1开)")
    private String  usagesAstrict;

    private String  sdkTagsCodes;

}
