package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;


@Data
@NoArgsConstructor
@ApiModel
public class TStandardMaterialMappingVo {

    @ApiModelProperty(value = "id")
    private String sId;

    @ApiModelProperty(value = "标准代码")
    private String sMatCode;

    @ApiModelProperty(value = "标准名称")
    private String sMatName;


    @ApiModelProperty(value = "1浙江标准,2全国标准")
    private String sMatType;

    @ApiModelProperty(value = "本地药品")
    private List<TMaterialMapping> tMaterials;
}
