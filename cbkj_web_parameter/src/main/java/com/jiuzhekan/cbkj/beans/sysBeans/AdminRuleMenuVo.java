package com.jiuzhekan.cbkj.beans.sysBeans;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class AdminRuleMenuVo implements Serializable {
    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "平台代码")
    private String modualCode;

    @ApiModelProperty(value = "平台名称")
    private String modualName;

    @ApiModelProperty(value = "菜单数组")
    List<String> menuId;

/*    private List<MenuVo> menu;*/
}
