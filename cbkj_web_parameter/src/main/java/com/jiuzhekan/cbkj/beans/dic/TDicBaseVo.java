package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TDicBaseVo implements Serializable {

    @ApiModelProperty(value = "映射状态 0:未映射，1:已映射")
    private String mapping;

    @ApiModelProperty(value = "字典Id")
    private String dicId;

    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构code")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "字典代码")
    private String dicCode;

    @ApiModelProperty(value = "字典名称")
    private String dicName;

    @ApiModelProperty(value = "搜索关键词")
    private String keyWord;
}
