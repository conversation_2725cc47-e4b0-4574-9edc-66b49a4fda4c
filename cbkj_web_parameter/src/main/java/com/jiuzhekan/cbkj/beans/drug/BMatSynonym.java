package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class BMatSynonym implements Serializable{

    @ApiModelProperty(value = "")
    private String matSynId;

    @ApiModelProperty(value = "")
    private String kMatId;

    @ApiModelProperty(value = "")
    private String kMatName;

    @ApiModelProperty(value = "知识库药品别名")
    private String kMatSynName;

    @ApiModelProperty(value = "")
    private String kMatSynPy;

    @ApiModelProperty(value = "")
    private String kMatSynWb;

    @ApiModelProperty(value = "")
    private String kMatSynOrigin;

    @ApiModelProperty(value = "0为标准名称，1为别名")
    private String kMatType;

    @ApiModelProperty(value = "")
    private Integer seqn;


}
