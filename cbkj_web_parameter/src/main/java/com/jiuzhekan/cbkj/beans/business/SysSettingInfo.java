package com.jiuzhekan.cbkj.beans.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysSettingInfo implements Serializable{

    @ApiModelProperty(value = "主键id（前端更新需要传这个）")
    private Integer setId;



    @ApiModelProperty(value = "配置系统名称")
    private String platformSysName;
    @ApiModelProperty(value = "辅诊系统名称")
    private String preSysName;

    @ApiModelProperty(value = "base64 图片")
    private String sysLogo;

    @ApiModelProperty(value = "")
    private Date insertTime;

    @ApiModelProperty(value = "")
    private String insertUserId;

    @ApiModelProperty(value = "")
    private String insertUserName;


}
