package com.jiuzhekan.cbkj.beans.pharmacy;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class PharmacyConfig {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "中药类型（1散装饮片 2散装颗粒 3膏方 4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药）")
    private String matType;
    private Integer sort;

    @ApiModelProperty(value = "应用范围科室集合")
    private List<TDisplayMapping> deptLists;
}
