package com.jiuzhekan.cbkj.beans.business.asyncTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel
public class AsyncTaskItemQuery implements Serializable {

    @ApiModelProperty(value = "同步时间查询-起始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "同步时间查询-结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "-1全部 状态0.成功 1失败 2同步中")
    private String status;

    @ApiModelProperty(value = "药品目录Id")
    private String drugId;

    @ApiModelProperty(value = "任务名称")
    private String asyncName;

    @ApiModelProperty(value = "任务详情")
    private String taskDetail;

    @ApiModelProperty(value = "任务分类：1.药品目录2.药品库存3.医生4.科室")
    private String asyncType;


}
