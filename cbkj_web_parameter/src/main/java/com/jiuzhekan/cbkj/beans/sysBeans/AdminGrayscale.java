package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@ApiModel
@Data
@NoArgsConstructor
public class AdminGrayscale {

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "是否试用(1:试用,0:不试用)")
    private Integer grayscaleStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    public AdminGrayscale(String userId, Integer grayscaleStatus, Date createDate, String createUser, String createUserName) {
        this.userId = userId;
        this.grayscaleStatus = grayscaleStatus;
        this.createDate = createDate;
        this.createUser = createUser;
        this.createUserName = createUserName;
    }
}
