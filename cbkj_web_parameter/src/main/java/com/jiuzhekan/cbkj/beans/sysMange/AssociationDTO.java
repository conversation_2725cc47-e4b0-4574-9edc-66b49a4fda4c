package com.jiuzhekan.cbkj.beans.sysMange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel
public class AssociationDTO {
    @ApiModelProperty(value = "APPID,必传",required = true)
    private  String appId;

    @ApiModelProperty(value = "医联体名称必填",required = true)
    private  String appName;

    @ApiModelProperty(value = "描述",required = true)
    private  String appDesc;

    @ApiModelProperty(value = "排序",required = true)
    private  Integer sort;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1",required = true)
    private  String status;

}
