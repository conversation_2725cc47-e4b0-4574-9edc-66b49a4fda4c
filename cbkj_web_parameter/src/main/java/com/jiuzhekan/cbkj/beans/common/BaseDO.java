package com.jiuzhekan.cbkj.beans.common;

import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/8/22 16:03
 * @Version 1.0
 */
@Data
public class BaseDO {

    @ApiModelProperty(value="是否删除,0:未删除，1:删除（自动填充）")
    private String isDel;

    @ApiModelProperty(value="创建时间（自动填充）")
    private Date createDate;

    @ApiModelProperty(value="创建人（自动填充）")
    private String createUser;

    @ApiModelProperty(value="创建人姓名（自动填充）")
    private String createUsername;

    @ApiModelProperty(value="更新时间（自动填充）")
    private Date updateDate;

    @ApiModelProperty(value="修改人（自动填充）")
    private String updateUser;

    @ApiModelProperty(value="修改人姓名（自动填充）")
    private String updateUsername;

    @ApiModelProperty(value="删除时间（自动填充）")
    private Date delDate;

    @ApiModelProperty(value="删除人（自动填充）")
    private String delUser;

    @ApiModelProperty(value="删除人姓名（自动填充）")
    private String delUsername;

    /***
     * 填充操作信息
     * @param type
     */
    public void fullOptData(String type){
        // 置空系统参数
        this.setCreateDate(null);
        this.setCreateUser(null);
        this.setCreateUsername(null);
        this.setUpdateDate(null);
        this.setUpdateUser(null);
        this.setUpdateUsername(null);
        this.setDelDate(null);
        this.setDelUser(null);
        this.setDelUsername(null);

        if (Constant.DB_SAVE.equals(type)){
            this.setIsDel(null);
            this.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            this.setCreateUsername(AdminUtils.getCurrentHr().getUsername());
            this.setCreateDate(new Date());
            this.setUpdateDate(new Date());
        }else if (Constant.DB_UPDATE.equals(type)){
            this.setIsDel(null);
            this.setUpdateUser(AdminUtils.getCurrentHr().getUserId());
            this.setUpdateUsername(AdminUtils.getCurrentHr().getUsername());
            this.setUpdateDate(new Date());
        }else if (Constant.DB_DEL.equals(type)){
            this.setDelUser(AdminUtils.getCurrentHr().getUserId());
            this.setDelUsername(AdminUtils.getCurrentHr().getUsername());
            this.setDelDate(new Date());
        }
    }
}
