package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysLogInterfaceError implements Serializable{

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String doctorId;

    @ApiModelProperty(value = "")
    private String doctorName;

    @ApiModelProperty(value = "")
    private String patientId;

    @ApiModelProperty(value = "")
    private String patientName;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private String interfaceName;

    @ApiModelProperty(value = "")
    private String interfaceDesc;

    @ApiModelProperty(value = "")
    private String interfaceToken;

    @ApiModelProperty(value = "")
    private String interfaceParams;

    @ApiModelProperty(value = "")
    private String errorStack;
    @ApiModelProperty(value = "")
    private String beginTime;
    @ApiModelProperty(value = "")
    private String endTime;

}
