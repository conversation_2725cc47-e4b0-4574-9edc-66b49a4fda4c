package com.jiuzhekan.cbkj.beans.sysBeans;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.controller.sysService.vo.SysAdminPracticeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 管理员
 *
 * <AUTHOR>
 */
@ApiModel
@Data
@NoArgsConstructor
public class AdminInfoVO implements Serializable {

    @ApiModelProperty(value = "繼續教育圖片")
    private String continueEduImg;

    @ApiModelProperty(value = "績效考核图片")
    private String performanceAppraisalImg;

    @ApiModelProperty(value = "考核评价图片")
    private String assessmentEvaluationImg;


    private String userId;

    @ApiModelProperty(value = "用户登录名字")
    private String userName;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用 3待审核）默认0")
    private String status;


    @ApiModelProperty(value = "中医资质（1有0无）")
    private String isQualifier;

    @ApiModelProperty(value = "1拼音 2五笔")
    private String isPyWb;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;
    //    @ApiModelProperty(value = "修改时间")
//    private Date updateDate;
    @ApiModelProperty(value = "APPID")
    private String appId;
    @ApiModelProperty(value = "所属医疗机构代码，若为0，则对应医共体医生，不为0，则为医疗机构医生")
    private String insCode;
    private String insName;

    @ApiModelProperty(value = "科室id")
    private String deptId;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "HIS科室ID")
    private String deptIdHis;

    @ApiModelProperty(value = "HIS科室名称")
    private String deptNameHis;

    @ApiModelProperty(value = "最后登录IP")
    private String lastIp;
    @ApiModelProperty(value = "中医资质图片路径(多张图片用英文逗号分隔)")
    private String qualifierPicPath;

    @ApiModelProperty(value = "协定方分享范围0 私有 1 本科室 2 本医疗机构")
    private String personalShare;

    @ApiModelProperty(value = "病历模板共享权限(0私有 1本科室 2本医疗机构 3医共体 多个英文逗号拼接)")
    private String templateShare;

    @ApiModelProperty(value = "默认处方权限(1内服中药方 2外用中药方 4适宜技术方 多个英文逗号拼接)")
    private String defaultPre;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "手机号")
    private String phone;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "姓名")
    private String nameZh;

    @ApiModelProperty(value = "头像")
    private String userHeand;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "身份证号")
    private String certificate;

    /**
     * 入参格式化
     * 出参格式化
     */
    @ApiModelProperty(value = "有效时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireDate;

    @ApiModelProperty(value = "创建人")
    private String createUsername;

    private List<String> picList;

    //    @ApiModelProperty(hidden = true)
    private List<AdminRule> roles;
    @JsonIgnore
    @ApiModelProperty(value = "查询使用字段：角色")
    private String roleName;
    @ApiModelProperty(value = "11.执业医师12.执业助理医师13.见习医师21.注册护士22.助产士31.西药师（士）32.中药师（士）41.检验技师（士）42.影像技师（士）69.其他卫生技术人员70.其他技术人员80.管理人员90.工勤及技能人员")
    private String professional;

    @ApiModelProperty(value = "更新人")
    private String updateUser;
    private String updateUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(value = "用户机构信息")
    private SysAdminPracticeVo sysDoctorMultipointVo;
    @ApiModelProperty(value = "职称证书，多图用,分隔")
    private String professionalPicPath;

    @ApiModelProperty(value = "工号")
    private String employeeId;

    private String sort;

    private String rnamess;
    private String doctorMultipoint;
    @ApiModelProperty(value = "医生签名图片路径")
    private String doctorSignImgPath;

    @ApiModelProperty(value = "用户扩展信息")
    private SysAdminInfoex sysAdminInfoex;

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp == null ? null : lastIp.trim();
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public void setName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    public void setRoles(List<AdminRule> roles) {
        this.roles = roles;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


}