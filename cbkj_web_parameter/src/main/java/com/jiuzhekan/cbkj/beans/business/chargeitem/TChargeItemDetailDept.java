package com.jiuzhekan.cbkj.beans.business.chargeitem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TChargeItemDetailDept implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private String chargeItemDetailId;

    @ApiModelProperty(value = "科室编码")
    private String deptId;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "医工体id")
    private String appId;

    @ApiModelProperty(value = "医工体名称")
    private String appName;

    @ApiModelProperty(value = "机构id")
    private String insName;

    @ApiModelProperty(value = "机构编码")
    private String insCode;


}
