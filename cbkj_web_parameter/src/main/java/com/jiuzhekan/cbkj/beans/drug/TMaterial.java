package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TMaterial implements Serializable{

    @ApiModelProperty(value = "药品名称id")
    private String matId;

    @ApiModelProperty(value = "药品目录id")
    private String drugId;

    @ApiModelProperty(value = "药品名称")
    private String matName;

    @ApiModelProperty(value = "药品类型(1、饮片 2、颗粒3膏方4 配方 5制剂 6中成药)")
    private String matClass;

    @ApiModelProperty(value = "标准编码")
    private String matStandard;

    @ApiModelProperty(value = "医保编码")
    private String matMedicalCode;

    @ApiModelProperty(value = "毒理分类（1、正常  2、有毒   3、微毒）")
    private String toxinType;

    @ApiModelProperty(value = "价值分类（1、普价   2、便宜   3、昂贵）")
    private String worthType;

    @ApiModelProperty(value = "是否医保")
    private String isMedical;

    @ApiModelProperty(value = "拼音码")
    private String matPinyin;

    @ApiModelProperty(value = "五笔码")
    private String matWubi;

    @ApiModelProperty(value = "是否能被修改 (0 没有  1有)有的话，不允许修改 -1 his 只查  0 没有库存  1有库存")
    private String isStock;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private String createUserName;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "创建人")
    private String updateUserName;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "作废标志（0、未作废      1、已作废）")
    private Date delDate;

    @ApiModelProperty(value = "作废人")
    private String delUser;

    @ApiModelProperty(value = "作废人姓名")
    private String delUsername;

    @ApiModelProperty(value = "是否作废")
    private String isDel;

    @ApiModelProperty(value = "不适应症")
    private String medicontrolText;

    @ApiModelProperty(value = "药品用法")
    private String matUsage;

    @ApiModelProperty(value = "用法用量")
    private String usageDesc;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "用法编码")
    private String matUsageCode;

    @ApiModelProperty(value = "功能主治")
    private String effect;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;


}
