package com.jiuzhekan.cbkj.beans.common;

import lombok.Data;

import java.io.Serializable;

/**
 * Created By xhq on  2020/1/2 11:20
 * 主要是用来接收CommonUtil的方法的返回参数
 */
@Data
public class CommonPatientVO  implements Serializable {
    private String docId;      // 登录人id
    private String docNameZh;    // 登录人中文名
    private String name;       // 请求参数 ,判断是中英文 五笔
    private String py;
    private String wb;
}