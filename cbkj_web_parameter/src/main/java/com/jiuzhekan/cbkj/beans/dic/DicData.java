package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DicData
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
@NoArgsConstructor
@ApiModel
public class DicData implements Serializable {

    @ApiModelProperty(value = "字典代码")
    private String dicCode;

    @ApiModelProperty(value = "字典名称")
    private String dicName;

    @ApiModelProperty(value = "父ID（第一级为0）")
    private String parentId;

    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构ID")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "药房ID")
    private String displayId;

    @ApiModelProperty(value = "代码来源  0自己的  1 his 2 药房 空所有，可多传0,1 ")
    private String fromCode;

    @ApiModelProperty(value = "标准类型，不传，不映射")
    private String stanType;

    @ApiModelProperty(value = "标准字典ID")
    private String stanId;

    @ApiModelProperty(value = "标准字典代码")
    private String stanCode;

    @ApiModelProperty(value = "标准字典名称")
    private String stanName;
}
