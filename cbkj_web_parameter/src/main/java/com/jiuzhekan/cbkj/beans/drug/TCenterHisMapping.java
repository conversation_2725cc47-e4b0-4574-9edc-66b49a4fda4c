package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TCenterHisMapping implements Serializable{

    @ApiModelProperty(value = "映射ID")
    private String id;

    @ApiModelProperty(value = "药房药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品代码（唯一识别一个药品）药品价格id")
    private String matPriceId;

    @ApiModelProperty(value = "HIS药品目录ID")
    private String drugIdHis;

    @ApiModelProperty(value = "HIS药品价格id（唯一识别一个药品）")
    private String matPriceIdHis;

    @ApiModelProperty(value = "映射类型(1自动 2手工)")
    private Byte type;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;


}
