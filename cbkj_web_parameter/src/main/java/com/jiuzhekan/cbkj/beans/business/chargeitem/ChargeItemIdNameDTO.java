package com.jiuzhekan.cbkj.beans.business.chargeitem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 收费项目ID和名称DTO
 * @Date 2025/8/5 14:30
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@ApiModel(value = "收费项目ID和名称DTO")
public class ChargeItemIdNameDTO implements Serializable {

    @ApiModelProperty(value = "收费项目ID")
    private String chargeItemId;

    @ApiModelProperty(value = "收费项目名称")
    private String chargeItemName;

    public ChargeItemIdNameDTO(String chargeItemId, String chargeItemName) {
        this.chargeItemId = chargeItemId;
        this.chargeItemName = chargeItemName;
    }
}
