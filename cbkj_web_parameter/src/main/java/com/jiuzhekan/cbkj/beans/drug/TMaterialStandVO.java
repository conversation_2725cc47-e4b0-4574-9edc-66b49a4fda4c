package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@ApiModel
public class TMaterialStandVO implements Serializable {
    @ApiModelProperty(value = "1.已映射2.未映射")
    private String isMapping;
    @ApiModelProperty(value = "药品名或者是药品标准编码")
    private String keyWord;
    @ApiModelProperty(value = "药剂类型（1散装饮片 2散装颗粒 3膏方 4小包装饮片 5小包装颗粒 6配方 7制剂8 中成药）")
//  @ApiModelProperty(value = "药剂类型（1饮片 2颗粒 3膏方  4配方 5制剂 6中成药）")
    private String matType;

    @ApiModelProperty(value = "药品名称id")
    private String matId;

    @ApiModelProperty(value = "药品目录id")
    private String drugId;

    @ApiModelProperty(value = "药品本地名称")
    private String matName;

    @ApiModelProperty(value = "药品本地类型(1、饮片 2、颗粒3膏方4 配方 5制剂 6中成药)")
    //@ApiModelProperty(value = "药品类型(1散装饮片2散装颗粒3膏方4小包装饮片5小包装颗粒6配方7制剂8中成药)")
    private String matClass;

    @ApiModelProperty(value = "药品本地编码")
    private String matStandard;

    @ApiModelProperty(value = "标准编码表ID")
    private String sId;

    @ApiModelProperty(value = "标准编码表-标准代码（编码）")
    private String sMatCode;

    @ApiModelProperty(value = "标准编码表-标准名称")
    private String sMatName;

    @ApiModelProperty(value = "便准编码表-1浙江标准,2全国标准")
    private String sMatType;

    @ApiModelProperty(value = "映射Id")
    private String mapId;
    @ApiModelProperty(value = "药品目录类型（0 中心药房 1 HIS 2、通用）")
    private String drugType;

    public String getIsMapping() {
        return isMapping;
    }

    public void setIsMapping(String isMapping) {
        this.isMapping = isMapping;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getMatType() {
        return matType;
    }

    public void setMatType(String matType) {
        this.matType = matType;
    }

    public String getMatId() {
        return matId;
    }

    public void setMatId(String matId) {
        this.matId = matId;
    }

    public String getDrugId() {
        return drugId;
    }

    public void setDrugId(String drugId) {
        this.drugId = drugId;
    }

    public String getMatName() {
        return matName;
    }

    public void setMatName(String matName) {
        this.matName = matName;
    }

    public String getMatClass() {
        return matClass;
    }

    public void setMatClass(String matClass) {
        this.matClass = matClass;
    }

    public String getMatStandard() {
        return matStandard;
    }

    public void setMatStandard(String matStandard) {
        this.matStandard = matStandard;
    }

    public String getsId() {
        return sId;
    }

    public void setsId(String sId) {
        this.sId = sId;
    }

    public String getsMatCode() {
        return sMatCode;
    }

    public void setsMatCode(String sMatCode) {
        this.sMatCode = sMatCode;
    }

    public String getsMatName() {
        return sMatName;
    }

    public void setsMatName(String sMatName) {
        this.sMatName = sMatName;
    }

    public String getsMatType() {
        return sMatType;
    }

    public void setsMatType(String sMatType) {
        this.sMatType = sMatType;
    }

    public String getMapId() {
        return mapId;
    }

    public void setMapId(String mapId) {
        this.mapId = mapId;
    }

    public String getDrugType() {
        return drugType;
    }

    public void setDrugType(String drugType) {
        this.drugType = drugType;
    }
}
