package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class TRecordSyndromeAsk implements Serializable{

    @ApiModelProperty(value = "问题记录ID")
    private String rsAskId;

    @ApiModelProperty(value = "分组记录ID")
    private String rsGroupId;

    @ApiModelProperty(value = "问题ID")
    private String askId;

    @ApiModelProperty(value = "问题")
    private String askName;

    @ApiModelProperty(value = "序号")
    private String askSeqn;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "答案列表")
    private List<TRecordSyndromeAnswer> answerList;

}
