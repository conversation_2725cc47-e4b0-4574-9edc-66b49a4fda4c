package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@ApiModel
public class TMaterialKnowVO implements Serializable{
    @ApiModelProperty(value = "1.已映射2.未映射")
    private String isMapping;
    @ApiModelProperty(value = "药品名或者是药品标准编码")
    private String keyWord;
    @ApiModelProperty(value = "药房类型1、药房 2、药库 3、 饮片厂 4、代煎室")
    private String matType;

    @ApiModelProperty(value = "药品名称id")
    private String matId;
    @ApiModelProperty(value = "药品价格id")
    private String matPriceId;

    @ApiModelProperty(value = "药品目录id")
    private String drugId;

    @ApiModelProperty(value = "药品名称")
    private String matName;

    @ApiModelProperty(value = "药品类型(1、饮片 2、颗粒3膏方4 配方 5制剂 6中成药)")
    private String matClass;

    @ApiModelProperty(value = "标准编码")
    private String matStandard;

    @ApiModelProperty(value = "知识库中药信息表ID")
    private String kMatId;
    @ApiModelProperty(value = "知识库中药信息表-名称")
    private String kMatName;


    @ApiModelProperty(value = "映射Id")
    private String mapId;

    public String getIsMapping() {
        return isMapping;
    }

    public void setIsMapping(String isMapping) {
        this.isMapping = isMapping;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getMatType() {
        return matType;
    }

    public void setMatType(String matType) {
        this.matType = matType;
    }

    public String getMatId() {
        return matId;
    }

    public void setMatId(String matId) {
        this.matId = matId;
    }

    public String getMatPriceId() {
        return matPriceId;
    }

    public void setMatPriceId(String matPriceId) {
        this.matPriceId = matPriceId;
    }

    public String getDrugId() {
        return drugId;
    }

    public void setDrugId(String drugId) {
        this.drugId = drugId;
    }

    public String getMatName() {
        return matName;
    }

    public void setMatName(String matName) {
        this.matName = matName;
    }

    public String getMatClass() {
        return matClass;
    }

    public void setMatClass(String matClass) {
        this.matClass = matClass;
    }

    public String getMatStandard() {
        return matStandard;
    }

    public void setMatStandard(String matStandard) {
        this.matStandard = matStandard;
    }

    public String getkMatId() {
        return kMatId;
    }

    public void setkMatId(String kMatId) {
        this.kMatId = kMatId;
    }

    public String getkMatName() {
        return kMatName;
    }

    public void setkMatName(String kMatName) {
        this.kMatName = kMatName;
    }

    public String getMapId() {
        return mapId;
    }

    public void setMapId(String mapId) {
        this.mapId = mapId;
    }
}
