package com.jiuzhekan.cbkj.beans.drug;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@ApiModel
@Data
public class TDrugList implements Serializable{

    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品目录名称")
    private String drugName;

    @ApiModelProperty(value = "药品目录描述")
    private String drugDesc;

    @ApiModelProperty(value = "药品目录类型（0 中心药房  1 HIS 2  通用）")
    private String drugType;

    @ApiModelProperty(value = "版本号")
    private String drugVersion;

    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date drugSyntime;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "同步来源 cbkj_drug：聪宝共享药房 his_view：HIS提供视图 his_http：HIS提供http接口")
    @Deprecated
    private String syncSource;

    @ApiModelProperty(value = "药品代码")
    private String drugCode;

    @ApiModelProperty(value = "来源HIS")
    private String hisId;

    @ApiModelProperty(value = "接口类型")
    private String hisUrlType;
}
