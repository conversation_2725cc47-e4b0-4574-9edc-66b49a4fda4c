package com.jiuzhekan.cbkj.beans.sysMange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class InstitutionVo {
    @ApiModelProperty(value = "医共体名称")
    private String appId;

    @ApiModelProperty(value = "医共体名称")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "医疗机构地址")
    private String insAddress;

    @ApiModelProperty(value = "上级医疗机构")
    private String insParentName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "医共体描述")
    private String appDesc;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "更新时间")
    private Date updatetime;

    @ApiModelProperty(value = "更新人名称")
    private String  updateUserName;
}
