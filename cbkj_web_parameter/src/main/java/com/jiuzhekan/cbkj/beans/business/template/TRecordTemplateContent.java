package com.jiuzhekan.cbkj.beans.business.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel
public class TRecordTemplateContent implements Serializable{

    @ApiModelProperty(value = "中医电子病历模版明细内容ID")
    private String contentId;

    @ApiModelProperty(value = "中医电子病历模版明细ID")
    private String detailId;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "序号")
    private Byte contentNum;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private String createUser;

    @ApiModelProperty(value = "")
    private String createUsername;

    @ApiModelProperty(value = "")
    private Date updateDate;

    @ApiModelProperty(value = "")
    private String updateUser;

    @ApiModelProperty(value = "")
    private String updateUsername;

    @ApiModelProperty(value = "")
    private Date delDate;

    @ApiModelProperty(value = "")
    private String delUser;

    @ApiModelProperty(value = "")
    private String delUsername;

    @ApiModelProperty(value = "")
    private String isDel;


}
