package com.jiuzhekan.cbkj.beans.business.sysParam;

import com.jiuzhekan.cbkj.beans.business.TSysParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel
@NoArgsConstructor
public class AppInsDeptVal implements Serializable {

    @ApiModelProperty("医共体ID")
    private String appId;

    @ApiModelProperty("医疗机构ID")
    private String insCode;

    @ApiModelProperty("科室ID")
    private String deptId;


    public AppInsDeptVal(String appId, String insCode, String deptId) {
        this.appId = appId;
        this.insCode = insCode;
        this.deptId = deptId;
    }

    public static AppInsDeptVal from(TSysParam p) {
        return new AppInsDeptVal(p.getAppId(), p.getInsCode(), p.getDeptId());
    }
}