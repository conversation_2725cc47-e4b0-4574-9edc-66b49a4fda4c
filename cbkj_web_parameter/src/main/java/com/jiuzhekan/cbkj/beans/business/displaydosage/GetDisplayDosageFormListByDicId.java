package com.jiuzhekan.cbkj.beans.business.displaydosage;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/6 11:35
 * @Version 1.0
 */
@Data
@ApiModel
public class GetDisplayDosageFormListByDicId {

    @ApiModelProperty(value = "字典id")
    private String dicId;

    @ApiModelProperty(value = "3.系统明细2.HIS明细1.药房明细")
    private Integer dicType;
}
