package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel
public class TMaterialHISVO implements Serializable{
    @ApiModelProperty(value = "1.已映射2.未映射")
    private String isMapping;
    @ApiModelProperty(value = "药品名或者是药品标准编码")
    private String keyWord;

    @ApiModelProperty(value = "药房药品名称id")
    private String matId;

    @ApiModelProperty(value = "药房药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药房药品名称")
    private String matName;

    @ApiModelProperty(value = "药房药品价格id")
    private String matPriceId;

    @ApiModelProperty(value = "HIS药品目录ID")
    private String drugIdHis;
    @ApiModelProperty(value = "HIS药品价格id")
    private String matPriceIdHis;
    @ApiModelProperty(value = "HIS药品名称")
    private String matNameHis;

    public String getIsMapping() {
        return isMapping;
    }

    public void setIsMapping(String isMapping) {
        this.isMapping = isMapping;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getMatId() {
        return matId;
    }

    public void setMatId(String matId) {
        this.matId = matId;
    }

    public String getDrugId() {
        return drugId;
    }

    public void setDrugId(String drugId) {
        this.drugId = drugId;
    }

    public String getMatName() {
        return matName;
    }

    public void setMatName(String matName) {
        this.matName = matName;
    }

    public String getMatPriceId() {
        return matPriceId;
    }

    public void setMatPriceId(String matPriceId) {
        this.matPriceId = matPriceId;
    }

    public String getDrugIdHis() {
        return drugIdHis;
    }

    public void setDrugIdHis(String drugIdHis) {
        this.drugIdHis = drugIdHis;
    }

    public String getMatPriceIdHis() {
        return matPriceIdHis;
    }

    public void setMatPriceIdHis(String matPriceIdHis) {
        this.matPriceIdHis = matPriceIdHis;
    }

    public String getMatNameHis() {
        return matNameHis;
    }

    public void setMatNameHis(String matNameHis) {
        this.matNameHis = matNameHis;
    }
}
