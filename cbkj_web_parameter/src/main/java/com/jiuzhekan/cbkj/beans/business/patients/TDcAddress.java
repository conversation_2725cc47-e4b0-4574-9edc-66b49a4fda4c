package com.jiuzhekan.cbkj.beans.business.patients;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TDcAddress implements Serializable {

    private String dcAddressId;

    private String patientId;

    @ApiModelProperty(value = "收货人", required = true)
    private String dcName;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "收货人手机号", required = true)
    private String dcMobile;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "收货人详细地址", required = true)
    private String dcAddress;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "省", required = true)
    private String dcCounty;

    @ApiModelProperty(value = "省代码", required = true)
    private String dcCountyCode;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "市", required = true)
    private String dcTown;

    @ApiModelProperty(value = "市代码", required = true)
    private String dcTownCode;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "区", required = true)
    private String dcVillage;

    @ApiModelProperty(value = "区代码", required = true)
    private String dcVillageCode;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "街道", required = true)
    private String dcStreet;

    @ApiModelProperty(value = "街道代码", required = true)
    private String dcStreetCode;

    private Date createDate;

    private String createUser;

    private String createUsername;

    private Date updateDate;

    private String updateUser;

    private String updateUsername;

    private Date delDate;

    private String delUser;

    private String delUsername;

    private String isDel;


    public TDcAddress() {

    }

    public TDcAddress(String dcId, TPatients pat, AdminInfo admin) {
        this.dcAddressId = dcId;
        this.patientId = pat.getPatientId();
        this.dcName = pat.getPatientName();
        this.dcMobile = pat.getPatientMobile();
        this.dcAddress = pat.getPatientAddress();
        this.dcCounty = pat.getPatientCounty();
        this.dcTown = pat.getPatientTown();
        this.dcStreet = pat.getPatientStreet();
        this.dcVillage = pat.getPatientVillage();
        this.dcCountyCode = pat.getPatientCountyCode();
        this.dcTownCode = pat.getPatientTownCode();
        this.dcStreetCode = pat.getPatientStreetCode();
        this.dcVillageCode = pat.getPatientVillageCode();
        this.createDate = new Date();
        this.createUser = admin.getUserId();
        this.createUsername = admin.getNameZh();
        this.isDel = Constant.BASIC_DEL_NO;
    }

    public TDcAddress(String dcAddress, String dcStreet, String dcStreetCode) {
        this.dcAddress = dcAddress;
        this.dcStreet = dcStreet;
        this.dcStreetCode = dcStreetCode;
    }
}