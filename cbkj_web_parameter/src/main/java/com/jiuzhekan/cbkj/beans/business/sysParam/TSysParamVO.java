package com.jiuzhekan.cbkj.beans.business.sysParam;

import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@ApiModel
public class TSysParamVO implements Serializable {

    private String parId;

    @ApiModelProperty("参数代码")
    private String parCode;

    @ApiModelProperty("参数名称")
    private String parName;

    @ApiModelProperty("系统参数值")
    private String parValue;

    @ApiModelProperty("参数描述")
    private String parDes;

    @ApiModelProperty("参数分类")
    private String parClassify;

    @ApiModelProperty("参数编号")
    private String parNumber;

    private List<TSysParamVal> paramVal;

    private TSysParamVO(TSysParam p) {
        this.parId = p.getParId();
        this.parCode = p.getParCode();
        this.parName = p.getParName();
        this.parDes = p.getParDes();
        this.parNumber = p.getParNumber();
        this.parClassify = p.getParClassify();
    }


    public static TSysParamVO from(List<TSysParam> list) {
        if (list != null && !list.isEmpty()) {
            TSysParam p = list.get(0);
            TSysParamVO po = new TSysParamVO(p);

            List<TSysParamVal> valList = new ArrayList<>();
            list.stream().filter(pa -> {
                if (Constant.BASIC_DEPT_ID.equals(pa.getDeptId()) && Constant.BASIC_INS_CODE.equals(pa.getInsCode()) && Constant.BASIC_APP_ID.equals(pa.getAppId())) {
                    po.setParValue(pa.getParValues());
                    return false;
                }
                return true;
            }).collect(Collectors.groupingBy(pa -> {
                if (Constant.BASIC_DEPT_ID.equals(pa.getDeptId())) {
                    if (Constant.BASIC_INS_CODE.equals(pa.getInsCode())) {
                        if (Constant.BASIC_APP_ID.equals(pa.getAppId())) {
                            return 0 + pa.getParValues();
                        }
                        return 1 + pa.getParValues();
                    }
                    return 2 + pa.getParValues();
                }
                return 3 + pa.getParValues();
            }, LinkedHashMap::new, Collectors.toList())).forEach((k, v) -> valList.add(new TSysParamVal(k.substring(1), Integer.parseInt(k.substring(0, 1)), v)));

            po.setParamVal(valList);
            return po;
        }
        return null;
    }

    public List<TSysParam> trans() {

        List<TSysParam> list = new ArrayList<>();
        TSysParam param;

        param = new TSysParam(IDUtil.getID(), this.parCode, this.parName, this.parDes, this.getParValue(),this.parClassify,this.getParNumber());
        param.setAppId(Constant.BASIC_APP_ID);
        param.setInsCode(Constant.BASIC_INS_CODE);
        param.setDeptId(Constant.BASIC_DEPT_ID);
        param.setSeqn(-1);
        list.add(param);

        for (int i = 0; i < this.paramVal.size(); i++) {
            TSysParamVal val = this.paramVal.get(i);

            switch (val.getType()) {
                case 0:
                    param = new TSysParam(IDUtil.getID(), this.parCode, this.parName, this.parDes, val.getParValues(),this.parClassify,this.getParNumber());
                    param.setAppId(Constant.BASIC_APP_ID);
                    param.setInsCode(Constant.BASIC_INS_CODE);
                    param.setDeptId(Constant.BASIC_DEPT_ID);
                    param.setSeqn(i);
                    list.add(param);
                    break;
                case 1:
                    for (AppInsDeptVal app : val.getValList()) {
                        param = new TSysParam(IDUtil.getID(), this.parCode, this.parName, this.parDes, val.getParValues(),this.parClassify,this.getParNumber());
                        param.setAppId(app.getAppId());
                        param.setInsCode(Constant.BASIC_INS_CODE);
                        param.setDeptId(Constant.BASIC_DEPT_ID);
                        param.setSeqn(i);
                        list.add(param);
                    }
                    break;
                case 2:
                    for (AppInsDeptVal ins : val.getValList()) {
                        param = new TSysParam(IDUtil.getID(), this.parCode, this.parName, this.parDes, val.getParValues(),this.parClassify,this.getParNumber());
                        param.setAppId(ins.getAppId());
                        param.setInsCode(ins.getInsCode());
                        param.setDeptId(Constant.BASIC_DEPT_ID);
                        param.setSeqn(i);
                        list.add(param);
                    }
                    break;
                case 3:
                    for (AppInsDeptVal dept : val.getValList()) {
                        param = new TSysParam(IDUtil.getID(), this.parCode, this.parName, this.parDes, val.getParValues(),this.parClassify,this.getParNumber());
                        param.setAppId(dept.getAppId());
                        param.setInsCode(dept.getInsCode());
                        param.setDeptId(dept.getDeptId());
                        param.setSeqn(i);
                        list.add(param);
                    }
                    break;
                default:
                    break;

            }
        }

        return list;
    }

}