package com.jiuzhekan.cbkj.beans.sysExt;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysWebserviceBasic implements Serializable{

    private static final long serialVersionUID = -1405053489393022101L;

    public String id;//主键

    public String name;//接口名称

    public String wsdlUrl;//wsdl路径

    public String sContentType;

    public String sAccept;

    public String sMethod;//方法

    public String defaultParams;//默认参数（用于测试）

    public String sResultBegin;//结果开始标志

    public String sResultEnd;//结果结束标志

    public String sTargetNamespace;//命名空间

    public String objId;//对象主键

    public String beizhu;//备注

    public String isAuth;//是否需要认证 1需要认证 0不需要认证

    public String authFormat;//认证格式

    public Date createDate;

    public Date updateDate;

    public String createId;

    public String updateId;

    public String createName;

    public String updateName;

    public String params;//传递参数

}