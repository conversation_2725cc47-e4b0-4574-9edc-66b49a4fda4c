package com.jiuzhekan.cbkj.beans.business.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TUSerAnalysusResultItem
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/4/28 14:10
 */
@Data
@ApiModel(value = "体质辨识问题勾选记录", description = "体质辨识问题勾选记录")
public class TUSerAnalysusResultItem implements Serializable {

    @ApiModelProperty(value = "主键")
    private String analyItemId;
    @ApiModelProperty(value = "辨识ID")
    private String analyId;
    @ApiModelProperty(value = "问题ID")
    private String itemId;
    @ApiModelProperty(value = "得分")
    private Integer score;
    @ApiModelProperty(value = "顺序")
    private Integer seqn;
}
