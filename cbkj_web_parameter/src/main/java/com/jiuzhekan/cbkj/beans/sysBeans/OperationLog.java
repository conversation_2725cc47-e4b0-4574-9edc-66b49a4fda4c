package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
public class OperationLog implements Serializable {

    private static final long serialVersionUID = 7413430229048767124L;
    public String id;


    public String operateId;

    public String operateName;
    /**
     * 操作内容 如：删除了 ***   新增了***
     */
    public String content;



    public String ip;

    public Date operateDate;

    /**
     * 说明
     */
    public String desc;
    public String methodName;

    public String methodParams;

    @ApiModelProperty(value = "")
    private String beginTime;
    @ApiModelProperty(value = "")
    private String endTime;

}