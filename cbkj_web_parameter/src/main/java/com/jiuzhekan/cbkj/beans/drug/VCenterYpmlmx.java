package com.jiuzhekan.cbkj.beans.drug;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class VCenterYpmlmx implements Serializable{

    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品代码")
    private String drugCode;

    @ApiModelProperty(value = "价格id")
    private String matPriceId;

    @ApiModelProperty(value = "药品名称")
    private String matName;

    @ApiModelProperty(value = "规格id")
    private String matSpeId;

    @ApiModelProperty(value = "药品规格")
    private String matSpe;

    @ApiModelProperty(value = "产地id")
    private String matOriginId;

    @ApiModelProperty(value = "产地名称")
    private String matOriginName;

    @ApiModelProperty(value = "零售价")
    private Double retailPrice;

    @ApiModelProperty(value = "大规格进价")
    private Double purchasePrice;

    @ApiModelProperty(value = "小规格进价")
    private Double smallPurchasePrice;

    @ApiModelProperty(value = "小规格零售价")
    private Double smallRetailPrice;

    @ApiModelProperty(value = "")
    private Double matDose;

    @ApiModelProperty(value = "剂量单位")
    private String matDoseUnit;

    @ApiModelProperty(value = "包装量")
    private Double matPackMount;

    @ApiModelProperty(value = "包装单位")
    private String matPackUnit;

    @ApiModelProperty(value = "中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药）")
    private String matType;

    @ApiModelProperty(value = "一次剂量")
    private String matOnceDose;

    @ApiModelProperty(value = "一次剂量单位")
    private String matOnceDoseUnit;

    @ApiModelProperty(value = "药品类型(1、饮片  2、颗粒  3膏方 4 配方 5制剂 6中成药)")
    private String matClass;

    @ApiModelProperty(value = "颗粒转成饮片的转换系数")
    private Integer conversionFactor;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "拼音码")
    private String py;

    @ApiModelProperty(value = "五笔码")
    private String wb;

    @ApiModelProperty(value = "是否医保")
    private String isMedical;

    @ApiModelProperty(value = "是否作废")
    private String isDel;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @ApiModelProperty(value = "日最大剂量（内服）")
    private String dailyMaxDoseIn;

    @ApiModelProperty(value = "日最大剂量（外用）")
    private String dailyMaxDoseExt;

    @ApiModelProperty(value = "医保日最大开药量（制剂）")
    private String dailyMaxNumPrep;

    @ApiModelProperty(value = "药品用法")
    private String matUsage;

    @ApiModelProperty(value = "单独使用时不予支付")
    private String notPayAlone;

    @ApiModelProperty(value = "不纳入基金支付范围")
    private String notPayInFund;

    @ApiModelProperty(value = "频次ID")
    private String frequencyId;

    @ApiModelProperty(value = "频次")
    private String frequency;

    @ApiModelProperty(value = "频次系数(次数/天)")
    private Double frequencyRate;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "用法用量")
    private String usageDesc;

    @ApiModelProperty(value = "功能主治")
    private String effect;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "药品名称id")
    private String matId;

    @ApiModelProperty(value = "不适应症")
    private String medicontrolText;

    @ApiModelProperty(value = "药品代码/药品名称(查询使用)")
    private String keyWord;

    @ApiModelProperty(value = "毒性超剂量双签倍数")
    private String toxicityOverdoseMultiple;
    @ApiModelProperty(value = "仅限外用(0关1开)")
    private String externalUseOnly;
    @ApiModelProperty(value = "最大剂量")
    private Double maxdose;

    @ApiModelProperty(value = "最大剂量")
    private Double mindose;

    @ApiModelProperty(value = "毒性")
    private String toxicity;

    @ApiModelProperty(value = "孕妇慎禁忌")
    private String motherTaboos;

    @ApiModelProperty(value = "仅限内服(0关1开)")
    private String externalUseOrally;
    @ApiModelProperty(value = "仅限丸散(0关1开)")
    private String externalMarusan;
    @ApiModelProperty(value = "仅限丸散关键字")
    private String externalMarusanName;

    @ApiModelProperty(value = "药品特殊用法")
    private String  specialUsages;
    @ApiModelProperty(value = "药品特殊用法")
    private String  dicName;

    @ApiModelProperty(value = "特殊用法限制")
    private String  usagesAstrict;

    @ApiModelProperty(value = "药品安全限制配置标签字典代码值,分隔")
    private String  sdkTagsCodes;
    @ApiModelProperty(hidden = true)
    private String[]  sdkTagsCodesArray;



}
