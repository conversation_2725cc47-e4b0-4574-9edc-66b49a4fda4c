package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel("区")
public class BsArea implements Serializable{

    private Integer areaId;

    @ApiModelProperty("市代码")
    private String cityCode;

    @ApiModelProperty("区代码")
    private String areaCode;

    @ApiModelProperty("区名称")
    private String areaName;

    @ApiModelProperty("简称")
    private String areaShortName;

    private String areaLng;

    private String areaLat;

    private Integer sort;

    private Date createDate;

    private Date modifyDate;

    private String desc;

    private Integer status;


}
