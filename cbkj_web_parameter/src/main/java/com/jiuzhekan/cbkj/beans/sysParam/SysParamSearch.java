package com.jiuzhekan.cbkj.beans.sysParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date
 **/
@Data
@ApiModel
public class SysParamSearch {

    @ApiModelProperty(value = "搜索关键字")
    private String keyWord;

    @ApiModelProperty(value = "APPID，默认值传000000",required = true)
    private String appId;


    @ApiModelProperty(value = "医疗机构代码（000000代表该医联体中所有的医疗机构）",required = true)
    private String insCode;


    @ApiModelProperty(value = "科室id，默认值传000000",required = true)
    private String deptId;

    @ApiModelProperty(value = "用户ID")
    private String userId;
}
