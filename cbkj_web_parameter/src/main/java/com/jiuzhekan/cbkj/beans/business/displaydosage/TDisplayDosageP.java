package com.jiuzhekan.cbkj.beans.business.displaydosage;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayExpress;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/6 14:41
 * @Version 1.0
 */
@NoArgsConstructor
@ApiModel
@Data
public class TDisplayDosageP {

    @ApiModelProperty(value = "门诊信息")
    private TDisplayDosageForm outpatient;

    @ApiModelProperty(value = "住院信息")
    private TDisplayDosageForm hospitalization;

    @ApiModelProperty(value = "disPlayId多个用逗号拼接")
    private String pharmacyDisplayList;
}
