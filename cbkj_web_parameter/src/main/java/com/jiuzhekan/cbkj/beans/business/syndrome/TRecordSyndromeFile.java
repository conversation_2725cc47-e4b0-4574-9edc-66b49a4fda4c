package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "圣美孚体质检查附件信息")
public class TRecordSyndromeFile implements Serializable {

    @ApiModelProperty("检查ID")
    private Integer checkId;

    @ApiModelProperty("挂号ID，流水号")
    private String registerId;

    @ApiModelProperty("检查时间")
    private Date checkTime;

    @ApiModelProperty("文件类型（1舌相(分割)，2脉象，3报告，4舌相(压缩)，0面相(基础)")
    private String fileType;

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("圣美孚ID")
    private String smfId;

    @ApiModelProperty("添加时间")
    private Date createDate;

    @ApiModelProperty("添加人")
    private String createUser;

    @ApiModelProperty("更新时间")
    private Date updateDate;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("删除时间")
    private Date deleteDate;

    @ApiModelProperty("删除人")
    private String deleteUser;

    @ApiModelProperty("是否删除（0、否 1、是）")
    private String isDel;
}
