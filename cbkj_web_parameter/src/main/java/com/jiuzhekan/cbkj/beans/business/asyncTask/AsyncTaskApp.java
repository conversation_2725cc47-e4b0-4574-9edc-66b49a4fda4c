package com.jiuzhekan.cbkj.beans.business.asyncTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class AsyncTaskApp implements Serializable{

    @ApiModelProperty(value = "医共体id")
    private String appId;

    @ApiModelProperty(value = "医疗机构code")
    private String insCode;



}
