package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class Logentity implements Serializable {

    private static final long serialVersionUID = 8409589211995859308L;
    public String lid;

    public String url;

    public String httpMethod;

    public String ip;

    public String className;

    public String methodName;

    public String execuType;

    public String status;

    public String descr;

    public Date createDate;

    public String createId;

    public String errmsg;
    
    private String createName;

    public Integer isOk;//是否已解决 1 未解决 2 已解决

    @ApiModelProperty(value = "")
    private String beginTime;
    @ApiModelProperty(value = "")
    private String endTime;


}