package com.jiuzhekan.cbkj.beans.sysParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TSysParamInitDesc implements Serializable{

    @ApiModelProperty(value = "参数ID")
    private String paramId;

    @ApiModelProperty(value = "参数初始选项code")
    private String paramInitCode;

    @ApiModelProperty(value = "参数初始选项对应的名称")
    private String paramInitName;

    @ApiModelProperty(value = "选项图示")
    private String optionDiagram;

    @ApiModelProperty(value = "选项排序")
    private int sort;


    private String paramInitCodes;


}
