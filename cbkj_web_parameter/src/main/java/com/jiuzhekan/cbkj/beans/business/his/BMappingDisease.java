package com.jiuzhekan.cbkj.beans.business.his;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 疾病映射
 *
 * <AUTHOR>
 * @date 2020/11/21
 */
@Data
@NoArgsConstructor
@ApiModel
public class BMappingDisease implements Serializable {

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "疾病ID-HIS")
    private String disIdHis;

    @ApiModelProperty(value = "疾病ID-SYS")
    private String disIdSys;

    @ApiModelProperty(value = "疾病编码-HIS")
    private String disCodeHis;

    @ApiModelProperty(value = "疾病编码-SYS")
    private String disCodeSys;

    @ApiModelProperty(value = "疾病名称-HIS")
    private String disNameHis;

    @ApiModelProperty(value = "疾病名称-SYS")
    private String disNameSys;


}
