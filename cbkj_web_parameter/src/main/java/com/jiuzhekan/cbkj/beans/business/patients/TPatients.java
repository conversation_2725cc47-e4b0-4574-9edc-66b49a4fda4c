package com.jiuzhekan.cbkj.beans.business.patients;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel("患者信息表")
@Data
public class TPatients implements Serializable {
    @ApiModelProperty(value = "患者ID")
    private String patientId;
    @ApiModelProperty(value = "APPID")
    private String appId;
    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;
    @ApiModelProperty(value = "姓名")
    private String patientName;
    @ApiModelProperty(value = "性别")
    private String patientGender;
    @ApiModelProperty(value = "出生日期")
    private Date patientBirthday;
    @ApiModelProperty(value = "出生日期字符串")
    private String patientBirthdayStr;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "身份证")
    private String patientCertificate;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "手机号")
    private String patientMobile;

    @ApiModelProperty(value = "姓名拼音")
    private String patientPy;
    @ApiModelProperty(value = "姓名五笔")
    private String patientWb;
    @ApiModelProperty(value = "就诊卡号")
    private String medicalCardNo;
    @ApiModelProperty(value = "第三方患者ID")
    private String originPatientId;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "省")
    private String patientCounty;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "市")
    private String patientTown;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "区")
    private String patientVillage;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "街道")
    private String patientStreet;

    @TianAnEncryptField
    @TianAnDecryptField
    @ApiModelProperty(value = "地址")
    private String patientAddress;

    @ApiModelProperty(value = "默认收货地址ID")
    private String dcAddressId;

    private Date createDate;
    private String createUser;
    private String createUsername;
    private Date updateDate;
    private String updateUser;
    private String updateUsername;

    private String delDate;
    private String delUser;
    private String delUsername;

    @ApiModelProperty(value = "是否删除（0否 1是）")
    private String isDel;

    private String age;
    private String sdateStr;

    private List<TDcAddress> addressList;
    @ApiModelProperty(hidden = true)
    private String patientVisitRange;

    @ApiModelProperty(value = "省代码")
    private String patientCountyCode;
    @ApiModelProperty(value = "市代码")
    private String patientTownCode;
    @ApiModelProperty(value = "区代码")
    private String patientVillageCode;
    @ApiModelProperty(value = "街道代码")
    private String patientStreetCode;

}