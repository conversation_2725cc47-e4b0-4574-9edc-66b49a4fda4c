package com.jiuzhekan.cbkj.beans.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayVo {
    @ApiModelProperty(value = "药房id")
    private String phaId;

    @ApiModelProperty(value = "药房名称")
    private String phaName;

    @ApiModelProperty(value = "药房所属医共体")
    private String appId;

    @ApiModelProperty(value = "药房所属医疗机构")
    private String insCode;

    @ApiModelProperty(value = "药房所属科室")
    private String deptId;

    @ApiModelProperty(value = "应用配置")
    private List<PharmacyConfig> pharmacyConfigList;
}
