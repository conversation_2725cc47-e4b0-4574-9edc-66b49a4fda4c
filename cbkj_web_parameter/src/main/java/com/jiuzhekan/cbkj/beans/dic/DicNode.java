package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * DicData
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
@NoArgsConstructor
@ApiModel
public class DicNode implements Serializable {

    @ApiModelProperty(value = "字典id")
    private String dicId;

    @ApiModelProperty(value = "字典代码")
    private String dicCode;

    @ApiModelProperty(value = "字典名称")
    private String dicName;

    @ApiModelProperty(value = "字典描述")
    private String dicDesc;

    @ApiModelProperty(value = "是否默认（1是0否）")
    private String isDefault;

    @ApiModelProperty(value = "其他属性(json格式)")
    private String otherJson;

    @ApiModelProperty(value = "父ID（第一级为0）")
    private String parentId;

    @ApiModelProperty(value = "序号")
    private String sort;

    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构ID")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "药房ID")
    private String displayId;

//    @ApiModelProperty(value = "上级字典")
//    private DicNode parent;

    @ApiModelProperty(value = "下级字典列表")
    private List<DicNode> children;


    @ApiModelProperty(value = "标准类型，不传，不映射")
    private String stanType;

    @ApiModelProperty(value = "标准字典ID")
    private String stanId;

    @ApiModelProperty(value = "标准字典代码")
    private String stanCode;

    @ApiModelProperty(value = "标准字典名称")
    private String stanName;


    public DicNode(String stanType) {
        this.stanType = stanType;
    }


    public DicNode(DicData data) {
        this.dicCode = data.getDicCode();
        this.dicName = data.getDicName();
        this.parentId = data.getParentId();
        this.appId = data.getAppId();
        this.insCode = data.getInsCode();
        this.deptId = data.getDeptId();
        this.displayId = data.getDisplayId();
        this.stanType = data.getStanType();
    }
}
