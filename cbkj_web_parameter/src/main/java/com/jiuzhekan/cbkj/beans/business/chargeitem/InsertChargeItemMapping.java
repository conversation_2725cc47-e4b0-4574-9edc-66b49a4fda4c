package com.jiuzhekan.cbkj.beans.business.chargeitem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/5 17:18
 * @Version 1.0
 */
@Data
@ApiModel
public class InsertChargeItemMapping {
    @ApiModelProperty(value = "收费项目id")
    private String chargeItemId;
    @ApiModelProperty(value = "收费项目机构应用权限配置列表:只存选中的，如果是机构中所有科室都选中的那就传机构就行不要传科室，如果机构下的个别科室没选中那就传所有选中的科室。参考tPharmacy/savePharmacyConfig接口入参")
    List<TChargeItemMapping> chargeItemMappingList;
}
