package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStandardMaterialMapping implements Serializable{

    @ApiModelProperty(value = "映射ID")
    private String id;

    @ApiModelProperty(value = "标准编码ID")
    private String sId;

    @ApiModelProperty(value = "标准编码药品名称")
    private String sMatName;

    @ApiModelProperty(value = "标准编码药品代码")
    private String sMatCode;

    @ApiModelProperty(value = "1浙江标准,2全国标准")
    private String sMatType;

    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品id")
    private String matId;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人")
    private String createUserName;


}
