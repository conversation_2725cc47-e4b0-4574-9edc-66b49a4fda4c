package com.jiuzhekan.cbkj.beans.sysExt;

import com.jiuzhekan.cbkj.beans.common.BaseDO;
import com.jiuzhekan.cbkj.beans.common.Create;
import com.jiuzhekan.cbkj.beans.common.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 产品矩阵
 * <AUTHOR>
 * @Date 2025/8/22 15:02
 * @Version 1.0
 */
@ApiModel("产品矩阵")
@Data
public class SysProductMatrix extends BaseDO {

    @ApiModelProperty(value="产品矩阵ID")
    private String productMatrixId;

    @ApiModelProperty(value="系统分类")
    private String category;

    @ApiModelProperty(value="系统分类排序序号（源于系统字段顺序")
    private Integer categoryOrderNum;

    @ApiModelProperty(value="排序号")
    private Integer orderNum;

    @ApiModelProperty(value="状态,0:未启用，1:启用")
    private String status;

    @ApiModelProperty(value="系统名称")
    private String name;

    @ApiModelProperty(value="系统类型")
    private String type;

    @ApiModelProperty(value="系统标签")
    private String tag;

    @ApiModelProperty(value="访问链接")
    private String link;

    @ApiModelProperty(value="视频地址")
    private String videoUrl;

    @ApiModelProperty(value="logo地址")
    private String logoUrl;

    @ApiModelProperty(value="是否被选中")
    private boolean checked;

    @ApiModelProperty(value="子级列表")
    private List<SysProductMatrix> childList;

}
