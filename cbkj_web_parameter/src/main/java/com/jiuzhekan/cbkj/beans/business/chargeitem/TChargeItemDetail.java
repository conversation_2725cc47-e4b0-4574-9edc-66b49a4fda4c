package com.jiuzhekan.cbkj.beans.business.chargeitem;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TChargeItemDetail implements Serializable{

    @ApiModelProperty(value = "")
    private String chargeItemDetailId;

    @ApiModelProperty(value = "收费编码")
    private String itemDetailCode;

    @ApiModelProperty(value = "项目名称")
    private String itemDetailName;

    @ApiModelProperty(value = "")
    private String chargeItemId;

    @ApiModelProperty(value = "0启用1停用")
    private Integer itemDetailStatus;

    @ApiModelProperty(value = "单价")
    private BigDecimal itemDetailSinglePrice;

    @ApiModelProperty(value = "单位")
    private String itemDetailSingleUnit;

    @ApiModelProperty(value = "拼音")
    private String itemDetailPy;

    @ApiModelProperty(value = "五笔")
    private String itemDetailWb;

    @ApiModelProperty(value = "穴位必填 0必填1非必填")
    private Integer itemDetailAcupointMust;

    @ApiModelProperty(value = "项目分类名称-字典表取")
    private String itemDetailTypeName;

    @ApiModelProperty(value = "项目分类代码-字典表取")
    private String itemDetailType;

    @ApiModelProperty(value = "项目绑定代码-字典表取")
    private String itemDetailBinding;

    @ApiModelProperty(value = "项目绑定名称-字典表取")
    private String itemDetailBindingName;

    @ApiModelProperty(value = "前置审核1关闭0开启")
    private Integer itemDetailPreCheck;

    @ApiModelProperty(value = "执行科室-启用执行：1关闭 0执行")
    private Integer itemDetailExecuteStatus;

    @ApiModelProperty(value = "项目内涵")
    private String itemDetailInnerText;

    @ApiModelProperty(value = "备注")
    private String itemDetailNotes;

    @ApiModelProperty(value = "")
    private String createUserId;

    @ApiModelProperty(value = "")
    private String createUserName;


    @ApiModelProperty(value = "")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "")
    private String updateUserId;

    @ApiModelProperty(value = "")
    private String updateUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date updateTime;

    @ApiModelProperty(value = "项目医保编码")
    private String medicalInsuranceCode;

    @ApiModelProperty(value = "项目医保国家（区域）编码")
    private String medicalInsuranceCountryRegionCode;

    @ApiModelProperty(value = "关联的科室列表")
    private List<TChargeItemDetailDept> itemDetailDepts;

    private String deptNames;


}
