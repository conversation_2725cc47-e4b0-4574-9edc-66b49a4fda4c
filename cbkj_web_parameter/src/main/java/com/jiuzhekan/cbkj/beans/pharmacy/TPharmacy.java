package com.jiuzhekan.cbkj.beans.pharmacy;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TPharmacy implements Serializable{

    @ApiModelProperty(value = "药房id")
    private String phaId;

    @ApiModelProperty(value = "药房名称")
    private String phaName;

    @ApiModelProperty(value = "药房类型(1、药房   2、药库    3、 饮片厂   4、代煎室)")
    private String phaType;

    @ApiModelProperty(value = "上级药房id")
    private String phaPid;

    @ApiModelProperty(value = "科室id")
    private String deptId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "禁用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date disableDate;

    @ApiModelProperty(value = "禁用人")
    private String disableUser;

    @ApiModelProperty(value = "禁用人姓名")
    private String disableUsername;

    @ApiModelProperty(value = "物理地点")
    private String phaAddress;

    @ApiModelProperty(value = "药品目录id")
    private String drugId;

    @ApiModelProperty(value = "药品目录名称")
    private String drugName;

    @ApiModelProperty(value = "医共体id")
    private String appId;

    @ApiModelProperty(value = "医疗机构id")
    private String insCode;

    @ApiModelProperty(value = "药房代码")
    private String pharmacyCode;

    @ApiModelProperty(value = "药房序号(开方系统使用)")
    private Integer sortNum;

    @ApiModelProperty(value = "药房级别")
    private String phaCategory;

    @ApiModelProperty(value = "地址编号")
    private String phaAddressNumber;

    @ApiModelProperty(value = "能否修改药房ID")
    private Integer isAlter;

    private List<TDisplay> tDisplayList;


}
