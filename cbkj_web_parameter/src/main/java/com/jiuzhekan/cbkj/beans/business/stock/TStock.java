package com.jiuzhekan.cbkj.beans.business.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStock implements Serializable{

    @ApiModelProperty(value = "库存id")
    private String stoId;

    @ApiModelProperty(value = "药房id")
    private String phaId;

    @ApiModelProperty(value = "药品id")
    private String matId;

    @ApiModelProperty(value = "药品名称")
    private String matName;
    @ApiModelProperty(value = "药品代码/名称：查询使用字段")
    private String matNameOrCode;

    @ApiModelProperty(value = "剂型")
    private String matType;

    @ApiModelProperty(value = "药品规格id")
    private String matSpeId;

    @ApiModelProperty(value = "药品规格")
    private String matSpe;

    @ApiModelProperty(value = "药品价格id")
    private String matPriceId;

    @ApiModelProperty(value = "库存数量")
    private Double stockNum;

    @ApiModelProperty(value = "库存上限")
    private Double stockUpper;

    @ApiModelProperty(value = "库存下限")
    private Double stockLower;

    @ApiModelProperty(value = "摆放位置")
    private String placement;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "摆放位置修改时间")
    private Date placementUpdateDate;

    @ApiModelProperty(value = "摆放位置修改人")
    private String placementUpdateUsername;

    @ApiModelProperty(value = "库存预警修改时间")
    private Date warningUpdateDate;

    @ApiModelProperty(value = "库存预警修改人")
    private String warningUpdateUsername;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;


}
