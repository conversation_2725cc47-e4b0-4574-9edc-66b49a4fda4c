package com.jiuzhekan.cbkj.beans.sysMange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class AssociationVo {
    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医共体名称")
    private String appName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "医共体描述")
    private String appDesc;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(hidden = true)
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String createUser;

    @ApiModelProperty(hidden = true)
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    @ApiModelProperty(hidden = true)
    private String updateUser;

    @ApiModelProperty(value = "更新人名称")
    private String  updateUserName;
}
