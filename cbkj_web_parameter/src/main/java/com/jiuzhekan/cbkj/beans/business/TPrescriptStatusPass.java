package com.jiuzhekan.cbkj.beans.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TPrescriptStatusPass implements Serializable{

    @ApiModelProperty(value = "补传主键")
    private Long passId;

    @ApiModelProperty(value = "处方id")
    private String preId;

    @ApiModelProperty(value = "处方号码")
    private String preNo;

    @ApiModelProperty(value = "处方状态")
    private String preStatus;

    @ApiModelProperty(value = "补传路径")
    private String passUrl;

    @ApiModelProperty(value = "参数")
    private String passParams;

    @ApiModelProperty(value = "get post")
    private String passMethod;

    @ApiModelProperty(value = "")
    private String passMessage;

    @ApiModelProperty(value = "1 药房  2 开方")
    private Integer serverType;

    @ApiModelProperty(value = "补传次数")
    private Integer passNum;

    @ApiModelProperty(value = "新增时间")
    private Date createDate;

    @ApiModelProperty(value = "新增人")
    private String createUser;

    @ApiModelProperty(value = "删除人")
    private String delUser;

    @ApiModelProperty(value = "删除类型 1、定时任务删除    2、人工删除")
    private String delType;

    @ApiModelProperty(value = "删除时间")
    private Date delDate;

    @ApiModelProperty(value = "0 正常 1 删除")
    private String status;


}
