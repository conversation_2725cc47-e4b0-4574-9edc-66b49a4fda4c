package com.jiuzhekan.cbkj.beans.business.sysCode;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
public class TSysHisCodeitemMapping implements Serializable{

    private String mapId;

    private String itemId;

    private String appId;

    private String insCode;

    private String itemHisId;

    private Date createDate;

    private String createUser;

    private String createUsername;

    private Date updateDate;

    private String updateUser;

    private String updateUsername;

    private Date delDate;

    private String delUser;

    private String delUsername;

    private String isDel;

}