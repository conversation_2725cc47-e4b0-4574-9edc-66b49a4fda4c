package com.jiuzhekan.cbkj.beans.business.syndrome;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class TRecordSyndromeGroup implements Serializable{

    @ApiModelProperty(value = "分组记录ID")
    private String rsGroupId;

    @ApiModelProperty(value = "挂号ID")
    private String registerId;

    @ApiModelProperty(value = "医生ID")
    private String doctorId;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "分组ID")
    private String groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "疾病ID")
    private String disId;

    @ApiModelProperty(value = "疾病名称")
    private String disName;

    @ApiModelProperty(value = "分类ID")
    private String classId;

    @ApiModelProperty(value = "分类名称")
    private String className;

    @ApiModelProperty(value = "是否保存开方")
    private String hasSave;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "问题列表")
    private List<TRecordSyndromeAsk> askList;

}
