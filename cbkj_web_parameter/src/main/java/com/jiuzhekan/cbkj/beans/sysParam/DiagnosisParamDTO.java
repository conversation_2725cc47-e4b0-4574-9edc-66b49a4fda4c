package com.jiuzhekan.cbkj.beans.sysParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class DiagnosisParamDTO implements Serializable{



    @ApiModelProperty(value = "APPID，默认值传000000",required = true)
    private String appId;


    @ApiModelProperty(value = "医疗机构代码（000000代表该医联体中所有的医疗机构）",required = true)
    private String insCode;



    @ApiModelProperty(value = "科室id，默认值传000000",required = true)
    private String deptId;



    @ApiModelProperty(value = "参数父节点菜单id，若是页面分类，则菜单为不显示配置",required = true)
    private String menuId;

    @ApiModelProperty(value = "",required = false,hidden = true)
    private String menuIds;
}
