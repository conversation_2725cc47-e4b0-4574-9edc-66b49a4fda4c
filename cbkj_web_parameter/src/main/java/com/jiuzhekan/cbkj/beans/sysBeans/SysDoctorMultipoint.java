package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel
public class SysDoctorMultipoint implements Serializable{

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "登录名")
    private String userName;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用 3待审核）默认0")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "住址")
    private String address;
//
    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "所属医共体id")
    private String appId;
    private String appName;

    @ApiModelProperty(value = "所属医疗机构代码")
    private String insCode;
    private String insName;

    @ApiModelProperty(value = "所在科室id")
    private String deptId;

    @ApiModelProperty(value = "所在科室名称")
    private String deptName;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有中医资质（1有 0无）")
    private Integer isQualifier;

    @ApiModelProperty(value = "HIS科室ID")
    private String deptIdHis;

    @ApiModelProperty(value = "HIS科室名称")
    private String deptNameHis;

    @ApiModelProperty(value = "工号")
    private String employeeId;

    @ApiModelProperty(value = "中医资质图片路径(多张图片用英文逗号分隔)")
    private String qualifierPicPath;

    @ApiModelProperty(value = "协定方分享范围0 私有  1 本科室  2 本医疗机构")
    private String personalShare;


}
