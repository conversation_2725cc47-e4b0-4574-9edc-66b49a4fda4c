package com.jiuzhekan.cbkj.beans.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplay implements Serializable{

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "医共体id")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码 和 appId唯一确定机构")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药）")
    private String matType;

    @ApiModelProperty(value = "药房ID(一种剂型只能对应一个药房)")
    private String phaId;

    @ApiModelProperty(value = "药房名称")
    private String phaName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否为默认项(暂时没用，通过参数配置顺序默认第一个)")
    private String isDefault;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用） 默认0")
    private String status;

    @ApiModelProperty(value = "药房类型")
    private String phaType;

    //系统字典配置用这个字段
    @ApiModelProperty(value = "是否配置")
    private String displayChecked;
}
