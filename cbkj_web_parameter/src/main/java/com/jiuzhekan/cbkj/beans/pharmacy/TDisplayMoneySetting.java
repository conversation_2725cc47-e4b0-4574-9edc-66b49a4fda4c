package com.jiuzhekan.cbkj.beans.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayMoneySetting implements Serializable {

    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "配置ID")
    private String setId;

    @ApiModelProperty(value = "门诊住院（1门诊，2住院）",required = true)
    private String outpatientOrHospitalization;

    @ApiModelProperty(value = "代煎，制膏，配送（1代煎，2制膏，3配送）",required = true)
    private String decoctionOrProductionExpress;

    @ApiModelProperty(value = "通用，配方（1通用，2配方）",required = true)
    private String currencyOrFormula;

    @ApiModelProperty(value = "字典ID",required = true)
    private String dicId;

    @ApiModelProperty(value = "字典代码")
    private String dicCode;

    @ApiModelProperty(value = "煎药方式",required = true)
    private String dicName;

    @ApiModelProperty(value = "HIS收费代码")
    private String chargeCode;

    @ApiModelProperty(value = "收费项目名称")
    private String chargeName;


    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createDate;

    @ApiModelProperty(value = "创建人ID",hidden = true)
    private String createUser;

    @ApiModelProperty(value = "状态",hidden = true)
    private String status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "收费项目配置是否显示",required = true)
    private String isShowSetting;

    /**
     * @param setId                        配置ID
     * @param outpatientOrHospitalization  门诊住院（1门诊，2住院）
     * @param decoctionOrProductionExpress 代煎，制膏，配送（1代煎，2制膏，3配送）
     * @param currencyOrFormula 通用，配方（1通用，2配方）
     * @param dicCode                      字典代码
     * @param dicName                      煎药方式
     * @param isShowSetting                收费项目配置是否显示
     */
    public TDisplayMoneySetting(String setId, String outpatientOrHospitalization,
                                String decoctionOrProductionExpress, String currencyOrFormula,
                                String dicCode,
                                String dicName, String isShowSetting) {
        this.setId = setId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
        this.decoctionOrProductionExpress = decoctionOrProductionExpress;
//        this.dicId = "000000";
        this.dicCode = dicCode;
        this.dicName = dicName;
        this.isShowSetting = isShowSetting;
        this.currencyOrFormula = currencyOrFormula;

    }
}
