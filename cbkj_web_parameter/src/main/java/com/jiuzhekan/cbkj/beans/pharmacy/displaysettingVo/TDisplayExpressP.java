package com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayExpress;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 **/
@NoArgsConstructor
@ApiModel
@Data
public class TDisplayExpressP {

    @ApiModelProperty(value = "门诊信息")
    private TDisplayExpress outpatient;

    @ApiModelProperty(value = "住院信息")
    private TDisplayExpress hospitalization;

    @ApiModelProperty(value = "disPlayId多个用逗号拼接")
    private String pharmacyDisplayList;
}
