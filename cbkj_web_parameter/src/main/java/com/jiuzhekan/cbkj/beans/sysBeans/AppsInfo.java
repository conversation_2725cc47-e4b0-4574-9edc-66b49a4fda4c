package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import lombok.Data;

@Data
public class AppsInfo {
    private String updateDate;
    private String insCode;
    @TianAnDecryptField
    private String nameZh;
    private String sex;
    private String updateUserName;
    private String deptId;
    private String updateUser;
    private String createUserName;
    private String userName;
    private String doctorMultipoint;
    private String userId;
    private String password;
    @TianAnDecryptField
    private String phone;
    private String appid;
    private String rnamess;
    private String createUser;
    private String status;
    private String insNames;

    private String createDate;
    private String address;
}
