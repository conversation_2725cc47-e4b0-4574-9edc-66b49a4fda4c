package com.jiuzhekan.cbkj.beans.sysBeans;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单
 */
@Data
public class Menu implements Serializable {
    private static final long serialVersionUID = -6861501038368008229L;
    private int mid;
    private String title;
    private String url;
    private String href;
    private String icon;
    private int parent_mid;

    private String menuType;
    private String btnClass;
    private String btnType;
    private String btnWeight;

    private List<Menu> children;

}