package com.jiuzhekan.cbkj.beans.business.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
@ApiModel
public class TRecordTemplateMapping implements Serializable{

    @ApiModelProperty(value = "映射ID")
    private String mapId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "个人中医电子病历模版ID")
    private String mytemplId;

    @ApiModelProperty(value = "原中医电子病历模版ID")
    private String templId;

    @ApiModelProperty(value = "默认模版(0否 1是)")
    private String isDefault;

    @ApiModelProperty(value = "序号")
    private Byte templNum;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private String createUser;

    @ApiModelProperty(value = "")
    private String createUsername;

    @ApiModelProperty(value = "")
    private Date updateDate;

    @ApiModelProperty(value = "")
    private String updateUser;

    @ApiModelProperty(value = "")
    private String updateUsername;

    @ApiModelProperty(value = "")
    private Date delDate;

    @ApiModelProperty(value = "")
    private String delUser;

    @ApiModelProperty(value = "")
    private String delUsername;

    @ApiModelProperty(value = "")
    private String isDel;


}
