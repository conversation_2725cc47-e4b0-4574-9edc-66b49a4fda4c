package com.jiuzhekan.cbkj.beans.business.interfaceHis;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TInterfaceHis implements Serializable{

    @ApiModelProperty(value = "id")
    private String hisId;

    @ApiModelProperty(value = "his名称")
    private String hisName;

    @ApiModelProperty(value = "his接口地址")
    private String hisUrl;

    @ApiModelProperty(value = "1 MYSQL 2 ORACLE 3 SqlServer 4 http 5 webservice")
    private String hisUrlType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "create_user_name")
    private String createUserName;

    @ApiModelProperty(value = "是否删除（0、有效 1、删除）默认0")
    private String status;

    @ApiModelProperty(value = "是否通用(1:通用,0:不通用)")
    private String isGeneral;

    @ApiModelProperty(value = "备注")
    private String interfaceDesc;

    @ApiModelProperty(value = "请求方式：GET、POST")
    private String requestMethodType;

    @ApiModelProperty(value = "接口内容：1.药品目录2.药品库存3.人员字典4.科室字典")
    private String dicCode;

    @ApiModelProperty(value = "业务名称")
    private String dicName;

    @ApiModelProperty(value = "数据库驱动")
    private String jdbcDriver;


    @ApiModelProperty(value = "数据库连接IP")
    private String jdbcIp;

    @ApiModelProperty(value = "数据库端口")
    private String jdbcPort;

    @ApiModelProperty(value = "数据库名")
    private String jdbcDatabaseName;

    @ApiModelProperty(value = "数据库用户名")
    private String jdbcUserName;

    @ApiModelProperty(value = "数据库密码")
    private String jdbcPassWord;

    @ApiModelProperty(value = "数据库表名/视图名")
    private String jdbcTable;

    @ApiModelProperty(value = "数据库查询条件")
    private String jdbcSqlwhere;


    @ApiModelProperty(value = "HTTP请求固定参数（JSON）")
    private String requestParams;

    @ApiModelProperty(value = "webService入参（"+">"+"<"+"需转换成"+"&gt;"+"&lt;")
    private String webserviceParams;

    @ApiModelProperty(value = "webService命名空间")
    private String webserviceNamespace;

    @ApiModelProperty(value = "webService方法名")
    private String webserviceMethod;

    @ApiModelProperty(value = "webService出参的节点")
    private String webserviceResultnode;

    @ApiModelProperty(value = "webService参数类型")
    private String webserviceContenttype;



}
