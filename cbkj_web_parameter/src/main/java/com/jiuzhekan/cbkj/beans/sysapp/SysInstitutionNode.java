package com.jiuzhekan.cbkj.beans.sysapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@ApiModel("医疗机构节点")
public class SysInstitutionNode implements Serializable {

    @ApiModelProperty("节点ID")
    private String id;

    @ApiModelProperty("节点名称")
    private String name;

    @ApiModelProperty("医共体ID")
    private String appId;

    public SysInstitutionNode(String id,String name,String appId){
        this.id = id;
        this.name = name;
        this.appId = appId;
    }

    public SysInstitutionNode(String id,String name,String aid,List<SysInstitution> list){
        this(id, name, aid);
        this.sublist = list.stream().map(m->SysInstitutionNode.fromSysInstitution(m,aid)).collect(Collectors.toList());
    }

    private List<SysInstitutionNode> sublist;

    private static SysInstitutionNode fromSysInstitution(SysInstitution sysInstitution,String aid){
        SysInstitutionNode node = new SysInstitutionNode(sysInstitution.getInsCode(),sysInstitution.getInsName(), aid);
        List<SysInstitution> insList = sysInstitution.getInsList();
        if (insList!=null && insList.size()>0){
            node.setSublist(sysInstitution.getInsList().stream().map(m->SysInstitutionNode.fromSysInstitution(m,aid)).collect(Collectors.toList()));
        }
        return node;
    }

}