package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@ApiModel
public class TDicMappingVo {
    @ApiModelProperty(value = "映射ID")
    private String id;

    @ApiModelProperty(value = "标准字典ID")
    private String stanId;

    @ApiModelProperty(value = "标准类型（0国标1浙标）")
    private String stanType;

    @ApiModelProperty(value = "本地字典集合")
    private List<TDicBase> tDicBases;
}
