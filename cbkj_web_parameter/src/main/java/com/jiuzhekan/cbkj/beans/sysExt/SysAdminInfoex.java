package com.jiuzhekan.cbkj.beans.sysExt;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysAdminInfoex implements Serializable{

//    @ApiModelProperty(value = "用户扩展表ID")
//    private String adminInfoexId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "扩展文字: 权限(私有 科室 医疗机构 医共体)")
    private String userExText;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "输入码(1拼音 2五笔)")
    private String inputCode;

    @ApiModelProperty(value = "介绍")
    private String introduce;

    @ApiModelProperty(value = "中医资质 （1有0无）")
    private String isQualifier;

    @ApiModelProperty(value = "协定方分享权限(0.个人1.科室2全院) 多个用逗号分隔")
    private String prescriptionShare;

    @ApiModelProperty(value = "病历模板分享权限(0私有 1科室 2医疗机构 3医共体) 多个用逗号分隔")
    private String descriptionShare;

    @ApiModelProperty(value = "默认处方类型(1内服 2外用 4适宜技术 5中药制剂) 多个用逗号分隔")
    private String defaultPrescriptionShare;

    @ApiModelProperty(value = "用户习惯内服（1:表示一行一列,2:表示一行2列）")
    private String userHabitOral;

    @ApiModelProperty(value = "用户习惯外用（1:表示一行一列,2:表示一行2列）")
    private String userHabitOutward;

    @ApiModelProperty(value = "用户习惯适宜技术（1:表示一行一列,2:表示一行2列）")
    private String userHabitAppropriate;

    @ApiModelProperty(value = "一个月内不提醒剂量不能整除(1四舍五入2自己填)")
    private String oneMonthNoDivide;

    @ApiModelProperty(value = "特殊药品的使用权限(1有 0无)超安全用药权限")
    private String specialDrugsUsePermission;


}
