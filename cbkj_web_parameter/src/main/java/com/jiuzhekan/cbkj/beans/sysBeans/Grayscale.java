package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptField;
import lombok.Data;

import java.util.Date;

@Data
public class Grayscale {
    private Date createDate;
    private String createUserName;
    private String doctorMultipoint;
    private String grayscaleStatus;
    @TianAnDecryptField
    private String nameZh;
    private String rnamess;
    private String userId;
    private String userName;
}
