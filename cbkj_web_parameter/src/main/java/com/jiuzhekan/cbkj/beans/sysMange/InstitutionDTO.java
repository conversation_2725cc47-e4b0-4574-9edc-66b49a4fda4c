package com.jiuzhekan.cbkj.beans.sysMange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class InstitutionDTO {
    @ApiModelProperty(value = "医共体Id")
    private String appId;

    @ApiModelProperty(value = "医共体名称")
    private String appName;

    @ApiModelProperty(value = "医疗机构Id")
    private String insId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构名称")
    private String insName;

    @ApiModelProperty(value = "拼音码")
    private String insPinyin;

    @ApiModelProperty(value = "五笔码")
    private String insWubi;

    @ApiModelProperty("医疗机构类别（1医院 2乡镇卫生院 3卫生站）")
    private String insCategory;

    @ApiModelProperty(value = "上级医疗机构")
    private String insParentCode;

    @ApiModelProperty(value = "省代码 必填，便于后续统计")
    private String provinceCode;

    @ApiModelProperty(value = "省名称")
    private String provinceName;

    @ApiModelProperty(value = "城市代码 必填，便于后续统计")
    private String cityCode;

    @ApiModelProperty(value = "城名称")
    private String cityName;

    @ApiModelProperty(value = "区代码 必填，便于后续统计")
    private String areaCode;

    @ApiModelProperty(value = "区名称")
    private String areaName;

    @ApiModelProperty(value = "街代码")
    private String streetCode;

    @ApiModelProperty(value = "街名称")
    private String streetName;

    @ApiModelProperty(value = "医疗机构详细地址")
    private String insAddress;

    @ApiModelProperty(value = "排序")
    private Integer sort;


    @ApiModelProperty(value = "医疗机构简称")
    private String insShorterName;

    @ApiModelProperty(value = "是否开启上传(0开启，1禁用)")
    private String upload;

    @ApiModelProperty(value = "院区名称")
    private String yardName;

    @ApiModelProperty(value = "院区代码")
    private String yardCode;

    @ApiModelProperty(value = "医疗机构代码(处方上传)")
    private String prescriptionInstitutionCode;

    @ApiModelProperty(value = "医疗机构名称(处方上传)")
    private String prescriptionInstitutionName;

    @ApiModelProperty(value = "社会统一信用代码")
    private String socialCode;

    @ApiModelProperty(value = "上传KEY")
    private String commitKey;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "等级")
    private String insLevel;

}
