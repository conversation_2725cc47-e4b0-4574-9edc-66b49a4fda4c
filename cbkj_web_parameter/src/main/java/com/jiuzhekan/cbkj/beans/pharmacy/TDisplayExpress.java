package com.jiuzhekan.cbkj.beans.pharmacy;

import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayExpress implements Serializable{

    @ApiModelProperty(value = "主键")
    private String setId;

    @ApiModelProperty(value = "机构药房配置ID")
    private String displayId;

    @ApiModelProperty(value = "门诊，住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty(value = "配送是否开启（0是，1否）")
    private String showExpress;

    @ApiModelProperty(value = "是否默认配送（0是，1否）")
    private String isExpress;

    @ApiModelProperty(value = "是否启用配送地址（0是，1否）")
    private String isUseExpress;

    @ApiModelProperty(value = "0 开启定点配送 1 不启动定点配送")
    private String dcTypeSubclass;

    @ApiModelProperty(value = "配送费用配置（0默认，1配置）")
    private String expressSet;

    @ApiModelProperty(value = "状态",hidden = true)
    private String status;

    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createDate;

    @ApiModelProperty(value = "创建人ID",hidden = true)
    private String createUser;

    @ApiModelProperty(value = "通用配送费用配置-通用")
    List<TDisplayMoneySetting> showDefaultList;






    /**
     *
     * @param displayId 机构药房配置ID
     * @param outpatientOrHospitalization 门诊，住院(1门诊，2住院)
     * @param showExpress 配送是否开启（0是，1否）
     * @param isExpress 是否默认配送（0是，1否）
     * @param isUseExpress 是否启用配送地址（0是，1否）
     * @param expressSet 通用制膏费用配置（0默认，1配置）
     */
    public TDisplayExpress(String displayId, String outpatientOrHospitalization,
                           String showExpress,String isExpress,String isUseExpress,String expressSet
                              ) {
        this.displayId = displayId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
        this.showExpress = showExpress;
        this.isExpress = isExpress;
        this.isUseExpress = isUseExpress;
        this.expressSet = expressSet;
        this.dcTypeSubclass = Constant.BASIC_STRING_ONE;

    }

    /**
     *
     * @param displayId 机构药房配置ID
     * @param outpatientOrHospitalization 门诊，住院(1门诊，2住院)
     */
    public TDisplayExpress(String displayId, String outpatientOrHospitalization) {
        this.displayId = displayId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
    }
    public void initDefaultValue() {
        this.status = Constant.BASIC_STRING_ZERO;
        this.createDate = new Date();
        this.setCreateUser(AdminUtils.getCurrentHr().getUserId());
    }


}
