package com.jiuzhekan.cbkj.beans.business.asyncTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class AsyncTask implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "同步名称")
    private String asyncName;

    @ApiModelProperty(value = "任务分类：1.药品目录2.药品库存3.医生 4.科室5.状态同步6HIS临床路径.7批次库存8费用同步")
    private String asyncType;

    @ApiModelProperty(value = "任务类型：1.定时执行  2 一次任务  3 全部")
    private String asyncExecType;

    @ApiModelProperty(value = "任务期间-开始时间")
    private String asyncStartTime;

    @ApiModelProperty(value = "任务期间-截止时间")
    private String asyncEndTime;

    @ApiModelProperty(value = "任务执行间隔时间值。")
    private String asyncBetweenTime;

    @ApiModelProperty(value = "任务执行间隔时间单位  m/h。")
    private String asyncBetweenUnit;

    @ApiModelProperty(value = "序号")
    private String sort;

    @ApiModelProperty(value = "任务状态：0开启1执行中2停止3.删除4.完成")
    private String status;

    @ApiModelProperty(hidden = true)
    private String status2;

    @ApiModelProperty(value = "任务创建人id")
    private String createUser;

    @ApiModelProperty(value = "任务创建人名字")
    private String createUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "任务创建时间")
    private Date createDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "任务修改时间")
    private Date updateDate;

//    @ApiModelProperty(value = "数据库保存：药品目录id。医共体、医疗机构：若为多个，医共体和机构用,分隔。下一个用;分隔如：100006-1,100006A03;100006-1,100006A05;100006-1,100006A01")
//    private String asyncValue;

    @ApiModelProperty(value = "对应的是哪个物理his",required = true)
    private String hisId;

    @ApiModelProperty(value = "第三方名称")
    private String hisName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最近执行完成时间")
    private Date asyncLastTime;

    @ApiModelProperty(value = "药品目录id 多个用,分隔")
    private String drugId;
    @ApiModelProperty(value = "选择的医共体和机构。（添加 3.医生4.科室 时 传这个参数）")
    private List<AsyncTaskApp> taskAppList;

    @ApiModelProperty(value = "任务详情（只在分页查询返回）")
    private String taskDetail;




}
