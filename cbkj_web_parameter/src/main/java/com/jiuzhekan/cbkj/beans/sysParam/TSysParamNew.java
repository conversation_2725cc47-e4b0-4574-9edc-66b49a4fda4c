package com.jiuzhekan.cbkj.beans.sysParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TSysParamNew implements Serializable {

    @ApiModelProperty(value = "参数ID")
    private String parId;

    @ApiModelProperty(value = "APPID")
    private String appId;

    private String appName;

    @ApiModelProperty(value = "医疗机构代码（000000代表该医联体中所有的医疗机构）")
    private String insCode;

    private String insName;

    @ApiModelProperty(value = "科室id")
    private String deptId;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "参数代码")
    private String parCode;

    @ApiModelProperty(value = "参数名称")
    private String parName;

    @ApiModelProperty(value = "参数值")
    private String parValues;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "参数类型：1 判断：是|否  开通|不开通  2 单选（多选一）：铺开  21不铺开3 多选（多选多）：铺开4 排序（多文本）：可拖拽排序5 文本输入：6 时间段")
    private String paramType;

    @ApiModelProperty(value = "0 通用  1 非通用")
    private String isGlobal;

    @ApiModelProperty(value = "参数初始选项代码，类型2 3 4 的需要设置")
    private String paramInitValue;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "参数父节点菜单id，若是页面分类，则菜单为不显示配置")
    private String menuId;
    /**
     * 不是该表字段（菜单类型：1:菜单 2：不显示的菜单 3：按钮 4 不显示的按钮）
     */
    private String menuType;
    /**
     * 不是该表字段
     */
    private String menuName;

    @ApiModelProperty(value = "参数说明 ，备注 项目名称等")
    private String paramDesc;

    @ApiModelProperty(value = "参数图示")
    private String parameterDiagram;

    @ApiModelProperty(value = "选项图示")
    private String optionDiagram;
    private String parNumber;

    private String parentMenuId;

    //该字段是前端需要的字段
    private String parentMenuName;
    //    是否配置过
    private Boolean checked;
}