package com.jiuzhekan.cbkj.beans.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayCurrencyList implements Serializable {

    @ApiModelProperty(value = "药剂类型ID集合")
    private String pharmacyDisplayList;

    @ApiModelProperty(value = "通用配置对象")
    private TDisplayCurrency tDisplayCurrency;
}
