package com.jiuzhekan.cbkj.beans.sysBeans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysModual implements Serializable{

    @ApiModelProperty(value = "用于标记菜单归属业务")
    private String modualCode;

    @ApiModelProperty(value = "业务模块名称")
    private String modualName;

    @ApiModelProperty(value = "是否有效（1删除 0有效 2禁用） 默认0")
    private Integer status;

    @ApiModelProperty(value = "备注说明")
    private String desc;

    @ApiModelProperty(value = "平台code，用于标记菜单和机构、用户的所属平台")
    private String platformCode;


}
