package com.jiuzhekan.cbkj.beans.business.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class TRecordTemplate implements Serializable{

    @ApiModelProperty(value = "中医电子病历模版ID")
    private String templId;

    @ApiModelProperty(value = "中医电子病历模版名称")
    private String templName;

    @ApiModelProperty(value = "APPID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "疾病ID")
    private String disId;

    @ApiModelProperty(value = "疾病名称")
    private String disName;

    @ApiModelProperty(value = "模版类型(1门诊 2住院)")
    private String templType;

    @ApiModelProperty(value = "模版分类（1系统模版 2用户模版）")
    private String templClassify;

    @ApiModelProperty(value = "初复诊标志（1初诊 2复诊）")
    private String isFirst;

    @ApiModelProperty(value = "是否默认（0否 1是）")
    private String isDefault;

    @ApiModelProperty(value = "是否启用（0否 1是）")
    private String isUsing;

    @ApiModelProperty(value = "共享标记（0私有 1本科室 2本医疗机构 3医联体）默认0")
    private String isShare;

    @ApiModelProperty(value = "能否修改")
    private boolean selfTemp;

    @ApiModelProperty(value = "序号")
    private Integer templNum;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "创建日期")
    private String createDateStr;

    @ApiModelProperty(value = "")
    private String createUser;

    @ApiModelProperty(value = "")
    private String createUsername;

    @ApiModelProperty(value = "")
    private Date updateDate;

    @ApiModelProperty(value = "")
    private String updateUser;

    @ApiModelProperty(value = "")
    private String updateUsername;

    @ApiModelProperty(value = "")
    private Date delDate;

    @ApiModelProperty(value = "")
    private String delUser;

    @ApiModelProperty(value = "")
    private String delUsername;

    @ApiModelProperty(value = "")
    private String isDel;


    /*************EXT*************/
    @ApiModelProperty(value = "中医电子病历模版明细列表")
    private List<TRecordTemplateDetail> detailList;

    @ApiModelProperty(value = "模板下拉框是否选中")
    private boolean check;
    @ApiModelProperty(value = "电子病历id")
    private String recId;

}
