package com.jiuzhekan.cbkj.beans.sysapp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel("医疗机构")
public class SysInstitution implements Serializable {

    @ApiModelProperty("医疗机构ID")
    private String insId;

    @ApiModelProperty("医疗机构代码")
    private String insCode;

    @ApiModelProperty("医疗机构名称")
    private String insName;

    @ApiModelProperty("医联体ID")
    private String appId;

    @ApiModelProperty("医联体名称")
    private String appName;

    @ApiModelProperty("所属平台（冗余）")
    private String platformCode;


    @ApiModelProperty("上级医疗机构代码")
    private String insParentCode;

    @ApiModelProperty("上级医疗机构名称")
    private String insPname;

    @ApiModelProperty("医疗机构类别（1医院 2乡镇卫生院 3卫生站）")
    private String insCategory;

    @ApiModelProperty("末级标志（1是 0否）")
    private Integer insIslast;

    @ApiModelProperty(value = "拼音码")
    private String insPinyin;

    @ApiModelProperty(value = "五笔码")
    private String insWubi;

    @ApiModelProperty(value = "省代码 必填，便于后续统计")
    private String provinceCode;

    @ApiModelProperty(value = "省名称")
    private String provinceName;

    @ApiModelProperty(value = "城市代码 必填，便于后续统计")
    private String cityCode;

    @ApiModelProperty(value = "城名称")
    private String cityName;

    @ApiModelProperty(value = "区代码 必填，便于后续统计")
    private String areaCode;

    @ApiModelProperty(value = "区名称")
    private String areaName;

    @ApiModelProperty(value = "街代码")
    private String streetCode;

    @ApiModelProperty(value = "街名称")
    private String streetName;

    @ApiModelProperty(value = "详细地址")
    private String insAddress;

    @ApiModelProperty(value = "是否大屏显示   0否，1是 默认1")
    private String insIsonline;


    @ApiModelProperty(value = "在线模式医疗机构选择排序")
    private Integer sort;

    @ApiModelProperty(value = "医疗机构简称")
    private String insShorterName;

    @ApiModelProperty(value = "是否开启上传(0开启，1禁用)")
    private String upload;

    @ApiModelProperty(value = "院区名称")
    private String yardName;

    @ApiModelProperty(value = "院区代码")
    private String yardCode;

    @ApiModelProperty(value = "医疗机构代码(处方上传)")
    private String prescriptionInstitutionCode;

    @ApiModelProperty(value = "医疗机构名称(处方上传)")
    private String prescriptionInstitutionName;

    @ApiModelProperty(value = "社会统一信用代码")
    private String socialCode;

    @ApiModelProperty(value = "上传KEY")
    private String commitKey;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(hidden = true)
    private String createUser;

    @ApiModelProperty(hidden = true)
    private String createUserName;

    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @ApiModelProperty(hidden = true)
    private String updateUser;

    @ApiModelProperty(hidden = true)
    private String updateUserName;


    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;


    @ApiModelProperty(value = "医疗机构等级，必填，便于后续统计" +
            "33 三级甲等 32 三级乙等 31 三级其他" +
            "23 二级甲等 22 二级乙等 21 二级其他" +
            "13 一级及其他")
    private String insLevel;


    /**
     * 下面的表中不存在，可能会用到。
     */
    private List<SysInstitution> insList;

    @ApiModelProperty("是否包含下级")
    private Boolean lower;

    private List<SysDepartment> deptList;

    public SysInstitution(String appId, String insCode) {
        this.appId = appId;
        this.insCode = insCode;
    }
}