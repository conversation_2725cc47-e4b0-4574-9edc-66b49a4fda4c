package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@ApiModel
public class TDicStandardVO {
    @ApiModelProperty(value = "标准字典代码")
    private String stanCode;

    @ApiModelProperty(value = "标准字典名称")
    private String stanName;

    @ApiModelProperty(value = "标准类型（0国标1浙标）")
    private String stanType;

    @ApiModelProperty(value = "是否有效（0除  1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "父ID（第一级为0）")
    private String parentId;
}
