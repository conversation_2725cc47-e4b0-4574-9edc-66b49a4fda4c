package com.jiuzhekan.cbkj.beans.business.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StockListDo {
    @ApiModelProperty(value = " 药房名称")
    private String phaName;
    @ApiModelProperty(value = "代码")
    private String matStandard;
    @ApiModelProperty(value = "药品名称")
    private String matName;
    @ApiModelProperty(value = "规格")
    private String matSpe;
    @ApiModelProperty(value = "产地名")
    private String matOriginName;
    @ApiModelProperty(value = " 剂型")

    private String matType;
    @ApiModelProperty(value = "零售价")
    private BigDecimal smallRetailPrice;

    @ApiModelProperty(value = "单位")
    private String smallSpeUnit;
    @ApiModelProperty(value = "库存")
    private BigDecimal stockNum;

    @ApiModelProperty(value = "预扣库存")
    private BigDecimal ykStockNum;
    @ApiModelProperty(value = "可用库存")
    private BigDecimal availableStock;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
}
