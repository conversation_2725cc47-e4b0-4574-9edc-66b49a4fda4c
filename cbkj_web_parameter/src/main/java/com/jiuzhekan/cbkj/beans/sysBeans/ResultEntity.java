package com.jiuzhekan.cbkj.beans.sysBeans;

import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 泛型响应实体
 * ResEntity不支持泛型，SWAGGER无法方便标注响应结构，故复制一个出来，其他字段名保持一致
 * @param <T> 响应数据类型
 */
@Data
@ApiModel("响应实体")
public class ResultEntity<T> implements Serializable {

    private static final long serialVersionUID = -143912222631236375L;
    
    @ApiModelProperty(value = "响应状态")
    private boolean status = true;
    
    @ApiModelProperty(value = "响应码")
    private int code = 0;
    
    @ApiModelProperty(value = "响应消息")
    private String message;
    
    @ApiModelProperty(value = "响应数据")
    private T data;

    public ResultEntity() {

    }

    public ResultEntity(boolean status, T data) {
        this.status = status;
        this.data = data;
    }

    public ResultEntity(boolean status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public ResultEntity(boolean status, int code, String message, T data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> ResultEntity<T> entity(boolean status, String message, T data) {
        return new ResultEntity<>(status, message, data);
    }

    public static <T> ResultEntity<T> success(T data) {
        return new ResultEntity<>(true, Constant.SUCCESS_DX, data);
    }

    public static <T> ResultEntity<T> error(String message) {
        return new ResultEntity<>(false, message, null);
    }

    public static <T> ResultEntity<T> error(int code, String message) {
        return new ResultEntity<>(false, code, message, null);
    }

    // 保持与ResEntity相同的getter和setter方法名
    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
