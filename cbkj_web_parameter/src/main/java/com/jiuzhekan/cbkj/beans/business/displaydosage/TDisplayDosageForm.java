package com.jiuzhekan.cbkj.beans.business.displaydosage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayDosageForm implements Serializable{

    @ApiModelProperty(value = "主键")
    private String setId;

    @ApiModelProperty(value = "机构药房配置ID")
    private String displayId;

    @ApiModelProperty(value = "门诊，住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty(value = "状态0开启1关闭")
    private Integer status;

    @ApiModelProperty(value = "0录入入口开启1录入入口关闭")
    private Integer expenseEntry;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "创建人ID")
    private String createUser;


}
