package com.jiuzhekan.cbkj.beans.business.chargeitem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/6 11:12
 * @Version 1.0
 */
@ApiModel
@Data
public class GetChargeItemDetailListByChargeItemId {
    @ApiModelProperty(value = "收费目录id")
    private String chargeItemId;
    @ApiModelProperty(value = "搜索关键字")
    private String searchKey;
}
