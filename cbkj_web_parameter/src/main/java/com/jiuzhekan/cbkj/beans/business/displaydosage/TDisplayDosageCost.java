package com.jiuzhekan.cbkj.beans.business.displaydosage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayDosageCost implements Serializable{

    @ApiModelProperty(value = "")
    private String dosageCostId;

    @ApiModelProperty(value = "3.系统明细2.HIS明细1.药房明细")
    private Integer dicType;

    @ApiModelProperty(value = "")
    private String dicId;

    @ApiModelProperty(value = "收费目录id")
    private String costCatalogueId;

    @ApiModelProperty(value = "收费项目id")
    private String costItemId;

    @ApiModelProperty(value = "计价方式（按顺序从1开始）：按剂收取：按照处方剂数，计算公式：数量=剂数；总价=剂数*单价；按斤收取：按照处方总重量收取，计算公式：数量=(处方总重量g/500)；总价=(处方总重量g/500)*单价；按药味数收取：按照药味数收取，计算公式：数量=药味数；总价=药味数*单价；按克收费：按照处方总重量收取，计算公式：数量=处方总重量；总价=(处方总重量g)*单价；按次收取：处方张数计价，计算公式:数量=1；总价=1*单价；")
    private Integer pricingMethod;

    @ApiModelProperty(value = "数量计算方式：1四舍五入计算方式 2向前取整计算方式 3向后取整计算方式")
    private Integer numComputerMethod;

    @ApiModelProperty(value = "医生可编辑0可以 1不可以")
    private Integer doctorEdit;

    @ApiModelProperty(value = "顺序")
    private Integer sort;


}
