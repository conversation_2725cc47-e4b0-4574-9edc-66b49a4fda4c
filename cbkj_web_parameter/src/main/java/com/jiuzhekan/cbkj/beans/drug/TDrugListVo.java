package com.jiuzhekan.cbkj.beans.drug;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel
@Data
public class TDrugListVo {

    @ApiModelProperty(value = "药品Id")
    private String drugId;

    @ApiModelProperty(value = "药品代码")
    private String drugCode;

    @ApiModelProperty(value = "药品目录名称")
    private String drugName;

    @ApiModelProperty(value = "药品目录描述")
    private String drugDesc;

    @ApiModelProperty(value = "药品目录类型（0 中心药房  1 HIS  2通用 ）")
    private String drugType;

    @ApiModelProperty(value = "来源HIS")
    private String hisId;

    @ApiModelProperty(value = "接口类型")
    private String hisUrlType;
}
