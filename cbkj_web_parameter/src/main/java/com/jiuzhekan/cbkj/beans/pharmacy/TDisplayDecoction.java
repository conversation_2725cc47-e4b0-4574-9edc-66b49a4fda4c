package com.jiuzhekan.cbkj.beans.pharmacy;

import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TDisplayDecoction implements Serializable {

    @ApiModelProperty(value = "主键")
    private String setId;

    @ApiModelProperty(value = "机构药房配置ID")
    private String displayId;

    @ApiModelProperty(value = "门诊,住院（1门诊，2住院）")
    private String outpatientOrHospitalization;

    @ApiModelProperty(value = "代煎配置是否开启（0是，1否）")
    private String showDecoction;

    @ApiModelProperty(value = "是否默认代煎（0是，1否）")
    private String isDecoction;

    @ApiModelProperty(value = "代煎是否禁用（0是，1否）")
    private String isDecoctionMust;

    @ApiModelProperty(value = "是否显示代煎贴数（0是，1否）")
    private String isDecoctionNum;

    @ApiModelProperty(value = "可代煎最低贴数(默认0)")
    private Integer leastDecoction;

    @ApiModelProperty(value = "通用代煎费用配置（0默认，1配置）")
    private String usuallyDecoctionSet;

    @ApiModelProperty(value = "配方代煎费用配置（0默认，1配置）")
    private String formulaDecoctionSet;

    @ApiModelProperty(value = "是否开启状态")
    private String status;

    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createDate;

    @ApiModelProperty(value = "创建人ID",hidden = true)
    private String createUser;

    @ApiModelProperty(value = "通用代煎默认配置集合")
    private List<TDisplayMoneySetting> showDefaultList;

    @ApiModelProperty(value = "通用代煎配置集合")
    private List<TDisplayMoneySetting> showDicList;

    @ApiModelProperty(value = "配方代煎默认集合")
    private List<TDisplayMoneySetting> formulaDefaultList;

    @ApiModelProperty(value = "配方代煎配置集合")
    private List<TDisplayMoneySetting> formulaDicList;

    /**
     *
     * @param displayId 机构药房ID
     * @param outpatientOrHospitalization 门诊,住院（1门诊，2住院）
     * @param showDecoction 代煎配置是否开启（0是，1否）
     * @param isDecoction 是否默认代煎（0是，1否）
     * @param isDecoctionMust 代煎是否禁用（0是，1否）
     * @param isDecoctionNum 是否显示代煎贴数（0是，1否）
     * @param leastDecoction 可代煎最低贴数(默认0)
     * @param usuallyDecoctionSet 通用代煎费用配置（0默认，1配置）
     * @param formulaDecoctionSet 配方代煎费用配置（0默认，1配置）
     */
    public TDisplayDecoction(String displayId, String outpatientOrHospitalization,
                             String showDecoction, String isDecoction, String isDecoctionMust,
                             String isDecoctionNum, Integer leastDecoction, String usuallyDecoctionSet, String formulaDecoctionSet) {
        this.displayId = displayId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
        this.showDecoction = showDecoction;
        this.isDecoction = isDecoction;
        this.isDecoctionMust = isDecoctionMust;
        this.isDecoctionNum = isDecoctionNum;
        this.leastDecoction = leastDecoction;
        this.usuallyDecoctionSet = usuallyDecoctionSet;
        this.formulaDecoctionSet = formulaDecoctionSet;
    }

    /**
     *
     * @param displayId 机构药房ID
     * @param outpatientOrHospitalization 门诊,住院（1门诊，2住院）
     */
    public TDisplayDecoction(String displayId, String outpatientOrHospitalization) {
        this.displayId = displayId;
        this.outpatientOrHospitalization = outpatientOrHospitalization;
    }

    public void initDefaultValue() {
        this.status = Constant.BASIC_STRING_ZERO;
        this.createDate = new Date();
        this.setCreateUser(AdminUtils.getCurrentHr().getUserId());
    }
}
