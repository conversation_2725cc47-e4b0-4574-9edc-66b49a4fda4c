//package com.jiuzhekan.cbkj.beans.business.sysCode.VO;
//
//import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCode;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.Date;
//import java.util.List;
//@ApiModel(value = "公用代码明细查询响应类")
//@Data
//public class TSysCodeItemRespVO implements Serializable{
//    @ApiModelProperty(value = "查询结果总条数")
//    private Integer count;
//    @ApiModelProperty(value = "查询结果集合")
//    private List<TSysCode> respList;
//}