package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel
@NoArgsConstructor
public class BMaterial implements Serializable {

    @ApiModelProperty(value = "id",notes = "id")
    private String kMatId;

    @ApiModelProperty(value = "知识库中药信息表-药名",notes = "知识库中药信息表-药名")
    private String kMatName;

    @ApiModelProperty(value = "如百味叶为 bwy")
    private String kMatPy;

    @ApiModelProperty(value = "")
    private String kMatWb;

    @ApiModelProperty(value = "药品用法")
    private String kMatYf;

    @ApiModelProperty(value = "知识库中药信息表-备注")
    private String remarks;

    private String keyWord;

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    @ApiModelProperty(value = "知识库中药信息表-别名")
    private List<BMatSynonym> kMatSynonyms = new ArrayList<>();


    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<BMatSynonym> getkMatSynonyms() {
        return kMatSynonyms;
    }

    public void setkMatSynonyms(List<BMatSynonym> kMatSynonyms) {
        this.kMatSynonyms = kMatSynonyms;
    }

    public String getkMatId() {
        return kMatId;
    }

    public void setkMatId(String kMatId) {
        this.kMatId = kMatId;
    }

    public String getkMatName() {
        return kMatName;
    }

    public void setkMatName(String kMatName) {
        this.kMatName = kMatName;
    }

    public String getkMatPy() {
        return kMatPy;
    }

    public void setkMatPy(String kMatPy) {
        this.kMatPy = kMatPy;
    }

    public String getkMatWb() {
        return kMatWb;
    }

    public void setkMatWb(String kMatWb) {
        this.kMatWb = kMatWb;
    }

    public String getkMatYf() {
        return kMatYf;
    }

    public void setkMatYf(String kMatYf) {
        this.kMatYf = kMatYf;
    }
}
