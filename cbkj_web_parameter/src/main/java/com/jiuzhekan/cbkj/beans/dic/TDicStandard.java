package com.jiuzhekan.cbkj.beans.dic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TDicStandard implements Serializable {

    @ApiModelProperty(value = "标准字典ID")
    private String stanId;

    @ApiModelProperty(value = "标准字典代码")
    private String stanCode;

    @ApiModelProperty(value = "标准字典名称")
    private String stanName;

    @ApiModelProperty(value = "父ID（第一级为0）")
    private String parentId;

    @ApiModelProperty(value = "标准字典描述")
    private String stanDesc;

    @ApiModelProperty(value = "标准类型（0国标1浙标）")
    private String stanType;

    @ApiModelProperty(value = "序号")
    private String sort;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "是否有效（0除  1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "本地字典ID")
    private String dicId;

    @ApiModelProperty(value = "国标映射搜索条件")
    private String keyWord;


}
