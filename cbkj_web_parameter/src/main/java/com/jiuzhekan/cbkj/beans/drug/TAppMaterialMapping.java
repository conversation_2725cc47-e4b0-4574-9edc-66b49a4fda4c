package com.jiuzhekan.cbkj.beans.drug;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TAppMaterialMapping implements Serializable{

    @ApiModelProperty(value = "映射ID")
    private String id;

    @ApiModelProperty(value = "知识库药品ID")
    private String kMatId;

    @ApiModelProperty(value = "知识库药品名称")
    private String kMatName;

    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品价格id")
    private String matPriceId;

    @ApiModelProperty(value = "映射时间")
    private Date createDate;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;


}
