package com.jiuzhekan.cbkj.beans.business.asyncTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class AsyncTaskIsC implements Serializable {
    private String appId;
    private String insCode;
    private String status;
    private String drugId;
    private String asyncType;
    private String asyncExecType;


}
