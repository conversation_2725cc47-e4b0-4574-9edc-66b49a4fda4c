//package com.jiuzhekan.cbkj.beans.business.sysCode.VO;
//
//import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCodeItem;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.List;
//
//@Data
//@ApiModel(value = "公用代码大类下拉框类")
//public class TSysCodeItemSelVO implements Serializable {
//    @ApiModelProperty(value = "大类ID")
//    private Short codeId;
//    @ApiModelProperty(value = "代码明细ID")
//    private String itemId;
//    @ApiModelProperty(value = "代码明细名称")
//    private String itemName;
//}