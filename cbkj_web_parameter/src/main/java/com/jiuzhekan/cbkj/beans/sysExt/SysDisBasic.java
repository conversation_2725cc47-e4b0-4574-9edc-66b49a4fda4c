package com.jiuzhekan.cbkj.beans.sysExt;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysDisBasic implements Serializable{

    private Integer id;

    private String lable;

    private String describe;

    private Integer type;

    private String value;

    private Integer sort;

    private String remarks;

    private String createId;

    private String createName;

    private Date createDate;

    private String updateId;

    private String updateName;

    private Date updateDate;

    private Integer isDel;

    private Date delDate;

    private String delId;


}