package com.jiuzhekan.cbkj.beans.business.sysCode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

@Data
public class THisCodeItem implements Serializable{

    @ApiModelProperty(value = "")
    private String itemId;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String itemHisId;

    @ApiModelProperty(value = "")
    private String itemHisName;

    @ApiModelProperty(value = "")
    private String codeId;

    @ApiModelProperty(value = "隐藏子项")
    private String hideProject;

    @ApiModelProperty(value = "")
    private String zifu1;

    @ApiModelProperty(value = "")
    private String zifu2;

    @ApiModelProperty(value = "")
    private String zifu3;

    @ApiModelProperty(value = "")
    private String zifu4;

    @ApiModelProperty(value = "")
    private String zifu5;

    @ApiModelProperty(value = "")
    private Date createDate;

    @ApiModelProperty(value = "")
    private String createUser;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    private Date updateDate;

    @ApiModelProperty(value = "")
    private String updateUser;

    @ApiModelProperty(value = "")
    private String updateUserName;

    @ApiModelProperty(value = "")
    private Date delDate;

    @ApiModelProperty(value = "")
    private String delUser;

    @ApiModelProperty(value = "")
    private String delUsername;

    @ApiModelProperty(value = "")
    private String isDel;

    /*EXT*/
    private String mapId;

    private String itemName;

    private String appName;

    private String insName;

    private String codeName;

    @ApiModelProperty(value = "系数（频次：次/天）")
    private Short rate;

}