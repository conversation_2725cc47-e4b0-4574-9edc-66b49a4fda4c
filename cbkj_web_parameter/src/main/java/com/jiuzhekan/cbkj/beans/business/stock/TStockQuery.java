package com.jiuzhekan.cbkj.beans.business.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TStockQuery implements Serializable{



    @ApiModelProperty(value = "药房id")
    private String phaId="";


    @ApiModelProperty(value = "药品代码/名称：查询使用字段")
    private String matNameOrCode="";

    @ApiModelProperty(value = "剂型")
    private String matType="";




}
