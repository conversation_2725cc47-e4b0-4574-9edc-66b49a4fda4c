package com.jiuzhekan.cbkj.beans.bs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("市")
public class BsCity implements Serializable {

    private Integer cityId;

    @ApiModelProperty("省代码")
    private String provinceCode;

    @ApiModelProperty("市代码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("简称")
    private String cityShortName;

    private String cityLng;

    private String cityLat;

    private Integer sort;

    private Date createDate;

    private Date modifiedDate;

    private String desc;

    private Integer status;


}
