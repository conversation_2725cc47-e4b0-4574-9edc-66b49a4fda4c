package com.jiuzhekan.cbkj.beans.drug;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel
public class VCenterYpmlmxDetails {
    @ApiModelProperty(value = "药品目录ID")
    private String drugId;

    @ApiModelProperty(value = "药品名称id")
    private String matId;

    @ApiModelProperty(value = "价格id")
    private String matPriceId;

    @ApiModelProperty(value = "药品代码")
    private String drugCode;

    @ApiModelProperty(value = "药品名称")
    private String matName;

    @ApiModelProperty(value = "药品规格")
    private String matSpe;

    @ApiModelProperty(value = "产地名称")
    private String matOriginName;

    @ApiModelProperty(value = "零售价")
    private Double retailPrice;

    @ApiModelProperty(value = "包装量")
    private Double matPackMount;

    @ApiModelProperty(value = "包装单位")
    private String matPackUnit;

    @ApiModelProperty(value = "中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药）")
    private String matType;

    @ApiModelProperty(value = "剂量")
    private Double matDose;

    @ApiModelProperty(value = "一次剂量")
    private String matOnceDose;

    @ApiModelProperty(value = "一次剂量单位")
    private String matOnceDoseUnit;

    @ApiModelProperty(value = "拼音码")
    private String py;

    @ApiModelProperty(value = "五笔码")
    private String wb;

    @ApiModelProperty(value = "日最大剂量（内服）")
    private String dailyMaxDoseIn;

    @ApiModelProperty(value = "日最大剂量（外用）")
    private String dailyMaxDoseExt;

    @ApiModelProperty(value = "单独使用时不予支付(医保字段)")
    private String notPayAlone;

    @ApiModelProperty(value = "不纳入基金支付范围")
    private String notPayInFund;


}
