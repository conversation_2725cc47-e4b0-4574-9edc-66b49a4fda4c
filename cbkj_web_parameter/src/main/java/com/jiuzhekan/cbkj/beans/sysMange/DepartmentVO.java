package com.jiuzhekan.cbkj.beans.sysMange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class DepartmentVO implements Serializable {
    @ApiModelProperty(value = "医共体ID")
    private String appId;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室ID用来保存，His ID")
    private String depOriginId;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "科室类型 1科室,2病区。默认为1")
    private String deptType;

    @ApiModelProperty(value = "是否有效（0删除 1有效 2禁用） 默认1")
    private String status;

    @ApiModelProperty(value = "科室位置")
    private String deptAddress;

}
