package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.drug.TDrugListMapper;
import com.jiuzhekan.cbkj.mapper.drug.TStandardMaterialMappingMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class TStandardMaterialMappingService {

    @Autowired
    private TStandardMaterialMappingMapper tStandardMaterialMappingMapper;

    @Autowired
    private TDrugListMapper tDrugListMapper;

    /**
     * 加载分页数据
     *
     * @param tMaterialStandVO 标准编码和药房药品映射关系表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TMaterialStandVO tMaterialStandVO, Page page) {
        if (StringUtils.isBlank(tMaterialStandVO.getIsMapping())){
            return ResEntity.entity(false, "参数不能为空哦(isMapping)", null);
        }
        if (StringUtils.isBlank(tMaterialStandVO.getsMatType())){
            return ResEntity.entity(false, "参数不能为空哦(sMatType)", null);
        }
        if ("null".equals(tMaterialStandVO.getIsMapping())){
            return ResEntity.entity(false, "参数不能为空哦(isMapping)", null);
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        if (!StringUtils.isBlank(tMaterialStandVO.getMatType())&&StringUtils.isNumeric(tMaterialStandVO.getMatType())){
            if (Integer.parseInt(tMaterialStandVO.getMatType())>=Integer.parseInt(Constant.BASIC_STRING_SIX)){
                tMaterialStandVO.setMatType( (Integer.parseInt(tMaterialStandVO.getMatType())-2)+"");
            }
        }
        List<TMaterialStandVO> list = tStandardMaterialMappingMapper.getPageListByObj(tMaterialStandVO);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 标准编码和药房药品映射关系表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStandardMaterialMapping tStandardMaterialMapping = tStandardMaterialMappingMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStandardMaterialMapping);
    }


    /**
     * 插入新数据
     *
     * @param tStandardMaterialMappingVo 标准编码和药房药品映射关系表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStandardMaterialMappingVo tStandardMaterialMappingVo){
        if (StringUtils.isBlank(tStandardMaterialMappingVo.getSMatType())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(tStandardMaterialMappingVo.getSMatName())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(tStandardMaterialMappingVo.getSMatCode())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(tStandardMaterialMappingVo.getSId())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }

        List<TMaterialMapping> tMaterials = tStandardMaterialMappingVo.getTMaterials();
        ArrayList<TStandardMaterialMapping> list = new ArrayList<>();
        for (TMaterialMapping tMaterial : tMaterials) {
            if (StringUtils.isBlank(tMaterial.getDrugId())){
                return ResEntity.entity(false, "参数错误(缺少参数)！", null);
            }
            if (StringUtils.isBlank(tMaterial.getMatId())){
                return ResEntity.entity(false, "参数错误(缺少参数)！", null);
            }
            TMaterialStandVO tMaterialStandVO = new TMaterialStandVO();
            tMaterialStandVO.setsId(tStandardMaterialMappingVo.getSId());
//            tMaterialStandVO.setSMatCode(tStandardMaterialMappingVo.getSMatCode());
//            tMaterialStandVO.setDrugId(tMaterial.getDrugId());
            tMaterialStandVO.setMatId(tMaterial.getMatId());
            tMaterialStandVO.setsMatType(tStandardMaterialMappingVo.getSMatType());
            tMaterialStandVO.setDrugId(tMaterial.getDrugId());
            long countByObj = tStandardMaterialMappingMapper.getCountByObj(tMaterialStandVO);
            if (countByObj > 0) {
                continue;
            }
            TStandardMaterialMapping tStandardMaterialMapping = new TStandardMaterialMapping();
            tStandardMaterialMapping.setSId(tStandardMaterialMappingVo.getSId());
            tStandardMaterialMapping.setSMatName(tStandardMaterialMappingVo.getSMatName());
            tStandardMaterialMapping.setSMatCode(tStandardMaterialMappingVo.getSMatCode());
            tStandardMaterialMapping.setSMatType(tStandardMaterialMappingVo.getSMatType());
            tStandardMaterialMapping.setDrugId(tMaterial.getDrugId());
            tStandardMaterialMapping.setMatId(tMaterial.getMatId());
            tStandardMaterialMapping.setId(IDUtil.getID());
            tStandardMaterialMapping.setCreateDate(new Date());
            tStandardMaterialMapping.setCreateUser(AdminUtils.getCurrentHr().getCreateUser());
            tStandardMaterialMapping.setCreateUserName(AdminUtils.getCurrentHr().getUpdateUserName());
            list.add(tStandardMaterialMapping);
        }
        if (list.size()>0){
            long rows = tStandardMaterialMappingMapper.insertList(list);
            return ResEntity.success(rows);
        }
        return ResEntity.success(0);
    }


    /**
     * 修改
     *
     * @param tStandardMaterialMapping 标准编码和药房药品映射关系表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStandardMaterialMapping tStandardMaterialMapping) {

        long rows = tStandardMaterialMappingMapper.updateByPrimaryKey(tStandardMaterialMapping);

        return ResEntity.success(tStandardMaterialMapping);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStandardMaterialMappingMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }
    @Transactional(rollbackFor = Exception.class)
    public ResEntity autoMapping(StandTMAutoMappingVO standTmAutoMappingVO){
        if (StringUtils.isBlank(standTmAutoMappingVO.getDrugId())) {
            return new ResEntity(false, "药品目录ID不能为空！", null);
        }
     /*   TDrugList objectById = tDrugListMapper.getObjectById(standTmAutoMappingVO.getDrugId());
        if (!Constant.BASIC_STRING_ZERO.equals(objectById.getDrugType())) {
            return new ResEntity(false, "请同步药房药品目录！", null);
        }*/
        standTmAutoMappingVO.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        standTmAutoMappingVO.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
        int mappingNumber = tStandardMaterialMappingMapper.insertautoMapping(standTmAutoMappingVO);
        long noMappingNumber = tStandardMaterialMappingMapper.insertautoMappingCount(standTmAutoMappingVO);
        HashMap<String, Object> map = new HashMap<>();
        map.put("mappingNumber",mappingNumber);
        map.put("noMappingNumber",noMappingNumber);
        return new ResEntity(true, "映射完成！！", map);
    }

}
