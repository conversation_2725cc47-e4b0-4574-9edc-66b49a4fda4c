package com.jiuzhekan.cbkj.service.parameter.strategy;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
  * 参数获取和保存策略接口
  * <AUTHOR>
  * @date 2022/4/26 16:22
 **/
public interface ParamStrategy {
    /**
      * 获取参数处理
      * <AUTHOR>
      * @date 2022/4/26 16:23
      * @return void
     **/
    String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray);

    /**
      * 保存参数处理
      * <AUTHOR>
      * @date 2022/4/26 16:23
      * @return void
     **/
    void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew);
}
