package com.jiuzhekan.cbkj.service.business.asyncTask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskValue;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskValueMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AsyncTaskValueService {

    @Autowired
    private AsyncTaskValueMapper asyncTaskValueMapper;

    /**
     * 加载分页数据
     *
     * @param asyncTaskValue 任务日志关联-药品目录-医院
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-20
     */
    public Object getPageDatas(AsyncTaskValue asyncTaskValue, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AsyncTaskValue> list = asyncTaskValueMapper.getPageListByObj(asyncTaskValue);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param asyncId 任务日志关联-药品目录-医院
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    public ResEntity findObj(String asyncId) {

        if (StringUtils.isBlank(asyncId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        AsyncTaskValue asyncTaskValue = asyncTaskValueMapper.getObjectById(asyncId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, asyncTaskValue);
    }


    /**
     * 插入新数据
     *
     * @param asyncTaskValue 任务日志关联-药品目录-医院
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(AsyncTaskValue asyncTaskValue){

        long rows = asyncTaskValueMapper.insert(asyncTaskValue);

        return ResEntity.success(asyncTaskValue);
    }


    /**
     * 修改
     *
     * @param asyncTaskValue 任务日志关联-药品目录-医院
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(AsyncTaskValue asyncTaskValue) {

        long rows = asyncTaskValueMapper.updateByPrimaryKey(asyncTaskValue);

        return ResEntity.success(asyncTaskValue);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = asyncTaskValueMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
