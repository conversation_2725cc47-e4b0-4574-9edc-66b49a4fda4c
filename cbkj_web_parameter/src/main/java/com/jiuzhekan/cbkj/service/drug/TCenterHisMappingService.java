package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.drug.TCenterHisMappingMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class TCenterHisMappingService {

    @Autowired
    private TCenterHisMappingMapper tCenterHisMappingMapper;

    /**
     * 加载分页数据
     *
     * @param tCenterHisMapping 药房的药品和HIS的药品映射表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TCenterHisMapping tCenterHisMapping, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TCenterHisMapping> list = tCenterHisMappingMapper.getPageListByObj(tCenterHisMapping);
        return Page.getLayUiTablePageData(list);
    }
    public Object getPageData2(TMaterialHISVO materialHISVO, Page page) {
        if (StringUtils.isBlank(materialHISVO.getIsMapping())){
            return ResEntity.entity(false, "参数不能为空哦(isMapping)", null);
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TMaterialHISVO> list = tCenterHisMappingMapper.getPageDatas2(materialHISVO);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 药房的药品和HIS的药品映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TCenterHisMapping tCenterHisMapping = tCenterHisMappingMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tCenterHisMapping);
    }


    /**
     * 插入新数据
     *
     * @param thisMaterialMappingVo 药房的药品和HIS的药品映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(THISMaterialMappingVo thisMaterialMappingVo){
        if (StringUtils.isBlank(thisMaterialMappingVo.getDrugIdHis())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(thisMaterialMappingVo.getMatPriceIdHis())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        ArrayList<TCenterHisMapping> list = new ArrayList<>();
        List<TMaterialHISMapping> tMaterials = thisMaterialMappingVo.getTMaterials();
        for (TMaterialHISMapping tMaterialHisMapping : tMaterials) {
            if (StringUtils.isBlank(tMaterialHisMapping.getDrugId())){
                return ResEntity.entity(false, "参数错误(缺少参数)！", null);
            }
            if (StringUtils.isBlank(tMaterialHisMapping.getMatPriceId())){
                return ResEntity.entity(false, "参数错误(缺少参数)！", null);
            }
            TMaterialHISVO tMaterialHisVO = new TMaterialHISVO();
            tMaterialHisVO.setMatPriceId(tMaterialHisMapping.getMatPriceId());
            tMaterialHisVO.setMatPriceIdHis(thisMaterialMappingVo.getMatPriceIdHis());
            long l = tCenterHisMappingMapper.selectCountData(tMaterialHisVO);
            if (l>0){
                continue;
            }
            TCenterHisMapping tCenterHisMapping = new TCenterHisMapping();
            tCenterHisMapping.setId(IDUtil.getID());
            tCenterHisMapping.setDrugId(tMaterialHisMapping.getDrugId());
            tCenterHisMapping.setMatPriceId(tMaterialHisMapping.getMatPriceId());
            tCenterHisMapping.setDrugIdHis(thisMaterialMappingVo.getDrugIdHis());
            tCenterHisMapping.setMatPriceIdHis(thisMaterialMappingVo.getMatPriceIdHis());
            tCenterHisMapping.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            tCenterHisMapping.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
            tCenterHisMapping.setType(Byte.valueOf(Constant.BASIC_STRING_TWO));
            tCenterHisMapping.setStatus(Constant.BASIC_STRING_ONE);
            list.add(tCenterHisMapping);
        }
        if (list.size()>0){
            int i = tCenterHisMappingMapper.insertList(list);
            return ResEntity.success(i);
        }
        return ResEntity.success(0);
    }


    /**
     * 修改
     *
     * @param tCenterHisMapping 药房的药品和HIS的药品映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TCenterHisMapping tCenterHisMapping) {

        long rows = tCenterHisMappingMapper.updateByPrimaryKey(tCenterHisMapping);

        return ResEntity.success(tCenterHisMapping);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tCenterHisMappingMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 自动映射
     * @param standTmAutoMappingVO
     * @return
     */
    public ResEntity autoMapping(StandTMAutoMappingVO standTmAutoMappingVO) {
        if (StringUtils.isBlank(standTmAutoMappingVO.getDrugId())) {
            return new ResEntity(false, "药品目录ID不能为空！", null);
        }
        standTmAutoMappingVO.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        standTmAutoMappingVO.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
        int i = tCenterHisMappingMapper.insertautoMapping(standTmAutoMappingVO);
        return new ResEntity(true, "映射完成！！", i);
    }

}
