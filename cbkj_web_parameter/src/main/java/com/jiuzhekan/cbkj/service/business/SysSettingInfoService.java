package com.jiuzhekan.cbkj.service.business;

import com.jiuzhekan.cbkj.beans.business.SysSettingInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.SysSettingInfoMapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.jiuzhekan.cbkj.common.utils.*;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class SysSettingInfoService {

    @Autowired
    private SysSettingInfoMapper sysSettingInfoMapper;

    /**
     * 加载分页数据
     *
     * @param sysSettingInfo 系统logo标题配置
     * @param page           分页
     * @return Object
     * <AUTHOR>
     * @date 2025-07-04
     */
    public Object getPageDatas(SysSettingInfo sysSettingInfo, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysSettingInfo> list = sysSettingInfoMapper.getPageListByObj(sysSettingInfo);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param setId 系统logo标题配置
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-07-04
     */
    public ResEntity findObj(String setId) {

        if (StringUtils.isBlank(setId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        SysSettingInfo sysSettingInfo = sysSettingInfoMapper.getObjectById(setId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysSettingInfo);
    }


    /**
     * 插入新数据
     *
     * @param sysSettingInfo 系统logo标题配置
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-07-04
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(SysSettingInfo sysSettingInfo) {


        long rows = sysSettingInfoMapper.insert(sysSettingInfo);

        return ResEntity.success(sysSettingInfo);
    }


    /**
     * 修改
     *
     * @param sysSettingInfo 系统logo标题配置
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-07-04
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(SysSettingInfo sysSettingInfo) {

        long rows = sysSettingInfoMapper.updateByPrimaryKey(sysSettingInfo);

        return ResEntity.success(sysSettingInfo);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-07-04
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = sysSettingInfoMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public Object getLogoTitleInfo() {
        SysSettingInfo objectById = sysSettingInfoMapper.getObjectById("1");
        return objectById;
    }

    public Object updateLogoTitleInfo(SysSettingInfo sysSettingInfo, MultipartFile logo) {
        //MultipartFile logo 是个图片文件，需要转成base64 存到数据库
        byte[] bytes = null; // 获取文件字节数组
        try {
            if (logo != null){
                bytes = logo.getBytes();
            }
        } catch (IOException e) {
            log.error("图片转base64失败:{}", e.getMessage());
        }
        String s = null;
        if (bytes != null){
            s = Base64.getEncoder().encodeToString(bytes);// 编码为Base64字符串
        }


        SysSettingInfo objectById = sysSettingInfoMapper.getObjectById("1");
        if (null != objectById) {
            objectById.setSysLogo(sysSettingInfo.getSysLogo());
            objectById.setPreSysName(sysSettingInfo.getPreSysName());
            objectById.setPlatformSysName(sysSettingInfo.getPlatformSysName());
            objectById.setSysLogo(s);
            sysSettingInfoMapper.updateByPrimaryKey(objectById);
            return objectById;
        } else {
            sysSettingInfo.setSetId(1);
            sysSettingInfo.setSysLogo(s);
            sysSettingInfoMapper.insert(sysSettingInfo);
            return sysSettingInfo;
        }
    }

    public Object getPlatFormLogoTitleInfo() {
        SysSettingInfo objectById = sysSettingInfoMapper.getObjectById("1");
        return objectById;
    }
}
