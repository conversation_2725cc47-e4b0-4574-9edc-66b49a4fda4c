package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @date 16:30 2022-04-26$
 **/
@Service("PRESCRIPTION_SEARCH_RECIPE_OPTIONS")
public class PrescriptionSearchRecipeOptionsStrategy implements ParamStrategy {
    private final TSysParamInitDescMapper tSysParamInitDescMapper;
    public PrescriptionSearchRecipeOptionsStrategy(TSysParamInitDescMapper tSysParamInitDescMapper){
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
    }
    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        pjsonObject.put("paramType", tSysParam1.getParamType());
        jsonObject.remove("initValue");
        jsonObject.remove("parValues");
        jsonObject.remove("paramType");
        jsonObject.remove("parName", "列表");
        jsonObject.remove("sort");
        String parValues2 = tSysParam1.getParValues();
        String replace2 = parValues2.replace(":T", "");
        String realValues = replace2.replace("|", ",");
        jsonObject.put("parValues", realValues);
        jsonObject.put("paramType", "4");

        JSONObject j = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("parId", tSysParam1.getParId() + "-2");
        String parValues = tSysParam1.getParValues();
        String replace = parValues.replace("|", ",");
        String[] split = replace.split(",");
        String s1 = "";
        for (String s : split) {
            if (s.contains(":T")) {
                s1 = s;
            }
        }
        String replace1 = s1.replace(":T", "");
        jsonObject1.put("parValues", replace1);
        jsonObject1.put("parameterDiagramURL", StringUtils.isBlank(tSysParam1.getParameterDiagram()) ? " " : tSysParam1.getParameterDiagram());
        jsonObject1.put("paramType", "21");

        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
        for (TSysParamInitDesc sysParamInitDesc : pageListByObj) {
            j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
            j.put("label", sysParamInitDesc.getParamInitName());
            j.put("value", sysParamInitDesc.getParamInitCode());
            jsonArray.add(j);
        }
        jsonObject1.put("paramInitDesc", jsonArray);
        itemArray.add(jsonObject1);
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew) {
        String value1 = saveParameter.getValue1();
        String value2 = saveParameter.getValue2();
        if (!StringUtils.isBlank(value1)) {
            String[] split = value1.split(",");
            for (int i = 0; i < split.length; i++) {
                if (!StringUtils.isBlank(value2) && value2.equals(split[i])) {
                    //默认选中
                    split[i] = split[i] + ":T" + "|";
                } else {
                    split[i] = split[i] + "|";
                }
            }
            StringBuilder stringBuilder = new StringBuilder("");
            for (String s : split) {
                stringBuilder.append(s);
            }
            String s = stringBuilder.toString();
            tSysParamVO.setParValues(s.substring(0, s.length() - 1));
        } else {
            tSysParamVO.setParValues("");
        }

    }
}
