package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service("PRESCRIPTION_INTERNAL_HIGHEST_MONEY")
public class PresriptionInternalHighestMoneyStrategy implements ParamStrategy {
    private final TSysParamInitDescMapper tSysParamInitDescMapper;
    @Autowired
    PresriptionInternalHighestMoneyStrategy(TSysParamInitDescMapper tSysParamInitDescMapper){
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
    }
    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        String[] split = tSysParam1.getParValues().split("\\|");
        if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode())) {
            if (split.length == 1){
                if (StringUtils.isBlank(split[0])){
                    split[0] = "0";
                }
                split = new String[]{split[0], "0"};
            }
        }
        jsonObject.remove("paramType");
        jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
        jsonObject.remove("parName");
        if("PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS".equals(tSysParam1.getParCode())){
            jsonObject.put("parName", "提示标题");
        }
        jsonObject.remove("parValues");
        jsonObject.put("parValues", split[0]);
        for (int i = 1; i < split.length; i++) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.putAll(jsonObject);
            jsonObject1.remove("paramType");
            jsonObject1.put("parValues", split[i]);
            jsonObject1.put("paramType", tSysParam1.getParamType().split(",")[i]);
            TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
            tSysParamInitDesc.setParamId(tSysParam1.getParId());
            List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
            JSONArray jsonArray = new JSONArray();
            JSONObject j = new JSONObject();
            j.put("label", pageListByObj.get(i).getParamInitName());
            j.put("value", pageListByObj.get(i).getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(i).getOptionDiagram()) ? " " : pageListByObj.get(i).getOptionDiagram());
            jsonArray.add(j);
            jsonObject1.put("parId", tSysParam1.getParId() + "-" + (i + 1));
            if("PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS".equals(tSysParam1.getParCode())){
                if(jsonObject1.get("parId").toString().contains("-2")){
                    jsonObject1.remove("parName");
                    jsonObject1.put("parName", "确认按钮文本");
                }
                if(jsonObject1.get("parId").toString().contains("-3")){
                    jsonObject1.remove("parName");
                    jsonObject1.put("parName", "剂量输入提示");
                }
            }

            jsonObject1.put("paramInitDesc", jsonArray);
            if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode()) && i == 1) {
                jsonObject1.put("parName", "膏方处方金额");
            }
            itemArray.add(jsonObject1);
        }
        if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode())) {
            jsonObject.remove("parName");
            jsonObject.put("parName", "通用处方金额");
        }
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew) {
        String value1 = saveParameter.getValue1();
        String value2 = saveParameter.getValue2();
        String value3 = saveParameter.getValue3();
        String value4 = saveParameter.getValue4();
        String value5 = saveParameter.getValue4();
        String value6 = saveParameter.getValue4();
        StringBuilder stringBuilder = new StringBuilder("");
        if (!StringUtils.isBlank(value1)) {
            stringBuilder.append(value1);
            if (!StringUtils.isBlank(value2)) {
                stringBuilder.append("|").append(value2);
                if (!StringUtils.isBlank(value3)) {
                    stringBuilder.append("|").append(value3);
                    if (!StringUtils.isBlank(value4)) {
                        stringBuilder.append("|").append(value4);
                        if (!StringUtils.isBlank(value5)) {
                            stringBuilder.append("|").append(value5);
                            if (!StringUtils.isBlank(value6)) {
                                stringBuilder.append("|").append(value6);
                            }
                        }
                    }
                }
            }
        }
        if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParamNew.getParCode())) {
            String substring = stringBuilder.substring(0, stringBuilder.length());
            String[] split = substring.split("\\|");
            if (split.length == 2 && "0".equals(split[0]) && "0".equals(split[1])){
                tSysParamVO.setParValues("0");
            }else {
                tSysParamVO.setParValues(substring);
            }
        }else {
            tSysParamVO.setParValues(stringBuilder.substring(0, stringBuilder.length()));
        }
    }
}
