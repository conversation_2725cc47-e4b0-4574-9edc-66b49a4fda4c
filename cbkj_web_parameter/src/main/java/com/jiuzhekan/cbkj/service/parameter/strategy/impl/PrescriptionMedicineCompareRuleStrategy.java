package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service("PRESCRIPTION_MEDICINE_COMPARE_RULE")
public class PrescriptionMedicineCompareRuleStrategy implements ParamStrategy {
    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        //处方同种草药判定规则
        //[0或1且][知识库ID][药房药品代码][HIS药品代码][上级药品代码][HIS规格ID]
        StringBuilder codes = new StringBuilder("");
        String parValues = tSysParam1.getParValues();
        for (int i = 1; i < parValues.length(); i++) {
            String substring = parValues.substring(i, i + 1);
            if ("1".equals(substring)) {
                codes.append(i).append(",");
            }
        }
        jsonObject.put("parValues", codes.toString().length() <= 1 ? codes.toString() : codes.toString().substring(0, codes.toString().length() - 1));
        jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject1 = new JSONObject();
        JSONObject j = new JSONObject();
        JSONObject j2 = new JSONObject();

        jsonObject1.putAll(jsonObject);
        j.put("label", "或");
        j.put("value", "0");
        j.put("optionDiagramURL", " ");
        j2.put("label", "且");
        j2.put("value", "1");
        j2.put("optionDiagramURL", " ");
        jsonArray.add(j);
        jsonArray.add(j2);
        jsonObject1.put("paramType", tSysParam1.getParamType().split(",")[1]);
        jsonObject1.put("paramInitDesc", jsonArray);
        jsonObject1.put("parId", tSysParam1.getParId() + "-2");
        jsonObject1.put("parName", "选项关系或与且");
        jsonObject1.put("parValues", parValues.substring(0, 1));
        itemArray.add(jsonObject1);
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew) {
        if (!StringUtils.isBlank(saveParameter.getValue1())) {
            String[] a = {"0", "0", "0", "0", "0", "0"};
            String[] split = saveParameter.getValue1().split(",");
            for (String value : split) {
                int s = Integer.parseInt(value);
                a[s] = "1";
            }
            a[0] = saveParameter.getValue2();
            tSysParamVO.setParValues(a[0] + a[1] + a[2] + a[3] + a[4] + a[5]);

        } else {
            tSysParamVO.setParValues("000000");
        }
    }
}
