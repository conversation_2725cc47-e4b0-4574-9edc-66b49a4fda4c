package com.jiuzhekan.cbkj.service.sysapp;

import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysMange.DepartmentVO;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysapp.SysAppMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class SysDepartmentService {

    @Autowired
    private SysDepartmentMapper sysDepartmentMapper;

    @Autowired
    private SysAppMapper sysAppMapper;

    @Autowired
    private SysInstitutionMapper sysInstitutionMapper;

    /**
     * 加载分页数据
     *
     * @param sysDepartment
     * @param page
     * @return
     */
    public Object getPageDatas(SysDepartment sysDepartment, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        String appId = sysDepartment.getAppId();
        if (StringUtils.isBlank(sysDepartment.getAppId())) {
            appId = AdminUtils.getCurrentAppId();
        }
        sysDepartment.setAppId(appId);
        if (Constant.BASIC_APP_ID.equals(appId)) {
            sysDepartment.setAppId("");
        }
        List<SysDepartment> list = sysDepartmentMapper.findPageListByObj(sysDepartment);

        return Page.getLayUiTablePageData(list);
    }

    /**
     * 插入新数据
     *
     * @param departmentVO
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "org::dept",allEntries = true)
    public ResEntity insert(DepartmentVO departmentVO) {
        SysDepartment sysDepartment = new SysDepartment();
        sysDepartment.setAppId(departmentVO.getAppId());
        sysDepartment.setInsCode(departmentVO.getInsCode());
        sysDepartment.setDepOriginId(departmentVO.getDepOriginId());
        sysDepartment.setDeptName(departmentVO.getDeptName());
        sysDepartment.setSort(departmentVO.getSort());
        sysDepartment.setStatus(departmentVO.getStatus());
        sysDepartment.setDeptType(departmentVO.getDeptType());
        sysDepartment.setDeptAddress(departmentVO.getDeptAddress());
        Integer count = sysDepartmentMapper.getDepCountByName(sysDepartment);
        if (count > 0) {
            return new ResEntity(false, "科室代码重复", null);
        }
        if (sysDepartment.getAppId() == null) {
            String appId = AdminUtils.getCurrentAppId();
            sysDepartment.setAppId(appId);
            if (Constant.BASIC_APP_ID.equals(appId)) {
                sysDepartment.setAppId("");
            }
        }
        sysDepartment.setDeptId(IDUtil.getID());
        sysDepartment.setCreateDate(new Date());
        sysDepartment.setCreateUser(AdminUtils.getCurrentHr().getUserId());
     /*   sysDepartment.setStatus(Constant.BASIC_DEL_NO);*/
        sysDepartment.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        if (StringUtils.isBlank(sysDepartment.getDepOriginId())) {
            sysDepartment.setDepOriginId(sysDepartment.getDeptId());
        }
        long rows = sysDepartmentMapper.insert(sysDepartment);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, sysDepartment);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param sysDepartment
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::dept","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity update(SysDepartment sysDepartment) {
        if (StringUtils.isBlank(sysDepartment.getDeptId())||
        StringUtils.isBlank(sysDepartment.getDeptName())||
        StringUtils.isBlank(sysDepartment.getAppId())){
            return ResEntity.entity(false,"参数为空，检测参数",null);
        }
        Integer count = sysDepartmentMapper.getDepCountByName(sysDepartment);
        if (count > 1) {
            return new ResEntity(false, "该科室名称已经存在！！", null);
        }
        sysDepartment.setUpdateDate(new Date());
        sysDepartment.setUpdateUser(AdminUtils.getCurrentHr().getUserId());
        sysDepartment.setUpdateUserName(AdminUtils.getCurrentHr().getNameZh());
        long rows = sysDepartmentMapper.updateByPrimaryKey(sysDepartment);

        SysDepartment objectById = sysDepartmentMapper.getObjectById2(sysDepartment.getDeptId());
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, objectById);
        }
        return new ResEntity(false, "修改失败，数据库异常", null);
    }



    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::dept","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity deleteLis(String ids) {
        SysDepartment sys = new SysDepartment();
        sys.setDepParentId(ids);
        Integer count = sysDepartmentMapper.getDepCountByParent(sys);
        if (count > 0) {
            return new ResEntity(false, "该科室下级还有科室，不能删除！！", null);
        }
        SysDepartment sysDepartment = new SysDepartment();
        sysDepartment.setDeptId(ids);
        long rows = sysDepartmentMapper.deleteByPrimaryKey(sysDepartment);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "删除失败，数据库异常", null);
    }

    /**
     * 根据appId和InsCode查询科室
     * @param sysDepartment
     * @return
     */
    public ResEntity queryByAppIdInsCode(SysDepartment sysDepartment){
        List<Map<String, Object>> maps = sysDepartmentMapper.queryByAppIdInsCode(sysDepartment);
        return ResEntity.entity(true, Constant.SUCCESS_DX, maps);
    }
    /**
     * 加载某条数据
     *
     * @param depId
     * @return
     */
    /*public ResEntity findObj(String depId) {

        if (StringUtils.isBlank(depId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        SysDepartment sysDepartment = sysDepartmentMapper.getObjectById(depId);
        if (sysDepartment.getIsLast() != null) {
            sysDepartment.setLast(sysDepartment.getIsLast().toString());
        }
        return new ResEntity(true, Constant.SUCCESS_DX, sysDepartment);
    }*/
}