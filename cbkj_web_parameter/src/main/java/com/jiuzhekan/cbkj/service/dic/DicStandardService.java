package com.jiuzhekan.cbkj.service.dic;

import com.jiuzhekan.cbkj.beans.dic.TDicStandard;
import com.jiuzhekan.cbkj.beans.dic.TDicStandardVO;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.dic.DicStandardMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
/**
 * <AUTHOR>
 */
public class DicStandardService {


    private DicStandardMapper dicStandardMapper;

    @Autowired
    DicStandardService(DicStandardMapper dicStandardMapper){
        this.dicStandardMapper=dicStandardMapper;
    }
    /**
     * 加载分页数据
     *
     * @param tDicStandard 标准字典表
     * @param page         分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TDicStandard tDicStandard, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDicStandard> list;

        if (StringUtils.isBlank(tDicStandard.getDicId()) && StringUtils.isNotBlank(tDicStandard.getStanId())) {
            //维护国标展示接口
            list = dicStandardMapper.getTDicStandard(tDicStandard);
        } else {
            list = dicStandardMapper.getPageListByObj(tDicStandard);
        }
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param stanId 标准字典表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String stanId) {

        if (StringUtils.isBlank(stanId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TDicStandard tDicStandard = dicStandardMapper.getObjectById(stanId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDicStandard);
    }


    /**
     * 插入新数据
     *
     * @param tDicStandardVO 标准字典表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDicStandardVO tDicStandardVO) {
        if(StringUtils.isBlank(tDicStandardVO.getStanCode())){
            return ResEntity.entity(false,"编码不能为空",null);
        }
        if(StringUtils.isBlank(tDicStandardVO.getStanName())){
            return ResEntity.entity(false,"名称不能为空",null);
        }
        TDicStandard tDicStandard = new TDicStandard();

        tDicStandard.setStanId(IDUtil.getID());
        tDicStandard.setStanCode(tDicStandardVO.getStanCode());
        tDicStandard.setStanName(tDicStandardVO.getStanName());
        tDicStandard.setStanType(tDicStandardVO.getStanType());
        tDicStandard.setStatus(tDicStandardVO.getStatus());
        tDicStandard.setParentId(tDicStandardVO.getParentId());
        tDicStandard.setCreateDate(new Date());
        tDicStandard.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        tDicStandard.setCreateUserName(AdminUtils.getCurrentHr().getCreateUserName());
        TDicStandard standard = dicStandardMapper.findOneTDicStandard(tDicStandardVO.getStanCode(),tDicStandardVO.getStanType());
        if(null!=standard){
         return  ResEntity.entity(false,"编码重复",null);
        }
        String maxSort = dicStandardMapper.getMaxSort(tDicStandard.getParentId());
        if(null==maxSort){
            maxSort="0";
        }
        Integer max = Integer.parseInt(maxSort);
        Integer sort=max+1;
        tDicStandard.setSort(sort.toString());
        long rows = dicStandardMapper.insert(tDicStandard);

        return ResEntity.success(tDicStandard);
    }


    /**
     * 修改
     *
     * @param tDicStandard 标准字典表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDicStandard tDicStandard) {
        if(StringUtils.isBlank(tDicStandard.getStanCode())){
            return ResEntity.entity(false,"编码不能为空",null);
        }
        if(StringUtils.isBlank(tDicStandard.getStanName())){
            return ResEntity.entity(false,"名称不能为空",null);
        }
        TDicStandard standard = new TDicStandard();
        standard.setStanId(tDicStandard.getStanId());
        standard.setStanCode(tDicStandard.getStanCode());
        standard.setStanName(tDicStandard.getStanName());
        standard.setParentId(tDicStandard.getParentId());
        standard.setStanType(tDicStandard.getStanType());
        standard.setStatus(tDicStandard.getStatus());
        long rows = dicStandardMapper.updateByPrimaryKey(tDicStandard);
        TDicStandard oneDicStandard = dicStandardMapper.findOneTDicStandard(tDicStandard.getStanCode(),tDicStandard.getStanType());
        return ResEntity.success(oneDicStandard);
    }

    /**
     * 删除
     *
     * @param id
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        TDicStandard tDicStandard = new TDicStandard();
        tDicStandard.setStanId(id);
        long rowsR = dicStandardMapper.deleteByPrimaryKey(tDicStandard);

        return ResEntity.success(rowsR);
    }

    public ResEntity getFname() {
        List<TDicStandard> fname = dicStandardMapper.getFname();
        return ResEntity.success(fname);
    }
    public ResEntity getFcode(String dicId) {
        if(StringUtils.isBlank(dicId)){
            return ResEntity.entity(false,"名称不能为空",null);
        }
        TDicStandard fCode = dicStandardMapper.getFCode(dicId);
        return ResEntity.success(fCode);
    }
}
