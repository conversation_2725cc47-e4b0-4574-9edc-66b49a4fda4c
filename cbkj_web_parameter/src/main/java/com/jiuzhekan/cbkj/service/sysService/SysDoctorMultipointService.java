package com.jiuzhekan.cbkj.service.sysService;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.controller.sysService.vo.SysAdminPracticeVo;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysDoctorMultipointMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SysDoctorMultipointService {

    @Autowired
    private SysDoctorMultipointMapper sysDoctorMultipointMapper;
    @Autowired
    private SysDepartmentMapper sysDepartmentMapper;
    /**
     * 加载分页数据
     *
     * @param sysDoctorMultipoint 医生多点执业表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-02-18
     */
    public Object getPageDatas(SysDoctorMultipoint sysDoctorMultipoint, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysDoctorMultipoint> list = sysDoctorMultipointMapper.getPageListByObj(sysDoctorMultipoint);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param userId 医生多点执业表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-18
     */
    public ResEntity findObj(String userId) {

        if (StringUtils.isBlank(userId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        SysDoctorMultipoint sysDoctorMultipoint = sysDoctorMultipointMapper.getObjectById(userId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysDoctorMultipoint);
    }


    /**
     * 查询用户执业机构
     *
     * @param adminId 用户ID
     * @return Object
     * <AUTHOR>
     * @date 2021-07-08
     */
    public SysAdminPracticeVo getMultiPoint(String adminId,String appId,String insCode) {

        List<SysDoctorMultipoint> list = getPracticeList(adminId, null, null);

        SysAdminPracticeVo vo = new SysAdminPracticeVo(adminId, list);
        vo.setTree(getDeptTree(appId,insCode));

        return vo;
    }

    /**
     * 获取科室树
     *
     * @return java.util.List
     * <AUTHOR>
     * @date 2021/7/9
     */
    public JSONArray getDeptTree(String appId,String insCode) {
//        String appId = AdminUtils.getCurrentAppId();
//        String insCode = AdminUtils.getCurrentInsCode();
        if (Constant.BASIC_APP_ID.equals(appId)) {
            appId = null;
        }
        if (Constant.BASIC_INS_CODE.equals(insCode)) {
            insCode = null;
        }
        List<SysApp> list = getDeptTree2(appId, insCode);

        return parseAppToJson(list);
    }
    /**
     * 获取用户职业机构
     * @param adminId
     * @param appId
     * @param insCode
     * @return
     */
    public List<SysDoctorMultipoint> getPracticeList(String adminId, String appId, String insCode) {
        SysDoctorMultipoint practice = new SysDoctorMultipoint();
        practice.setUserId(adminId);
        practice.setAppId(appId);
        practice.setInsCode(insCode);
        return sysDoctorMultipointMapper.getPracticeList(practice);
    }

    /**
     * 插入新数据
     *
     * @param sysDoctorMultipoint 医生多点执业表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-18
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(SysDoctorMultipoint sysDoctorMultipoint){

        sysDoctorMultipoint.setUserId(IDUtil.getID());
        long rows = sysDoctorMultipointMapper.insert(sysDoctorMultipoint);

        return ResEntity.success(sysDoctorMultipoint);
    }


    /**
     * 保存
     *
     * @param practiceVo 用户执业机构
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-07-08
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"client::user::userId","parameter::params::all::userId"},allEntries = true)
    public ResEntity save(SysAdminPracticeVo practiceVo) {

        if (StringUtils.isBlank(practiceVo.getUserId())) {
            ExceptionUtils.throwErrorCustomRuntimeException("参数错误：userId不能为空");
        }

        sysDoctorMultipointMapper.deleteByAdminId(practiceVo.getUserId());

        Date createDate = new Date();
        String createUserName = AdminUtils.getCurrentHr().getNameZh();
        String userId = AdminUtils.getCurrentHr().getUserId();

        for (SysDoctorMultipoint practice : practiceVo.getPracticeList()) {
            practice.setUserId(practiceVo.getUserId());
            practice.setCreateDate(createDate);
            practice.setCreateUser(userId);
            practice.setCreateUserName(createUserName);
//            AdminInfo adminInfo = new AdminInfo();
//            adminInfo.setUserId(practiceVo.getUserId());
//            adminInfo.setDeptId(practice.getDeptId());
            //维护协定方权限
            //tPersonalRuleAuthMapper.saveUserAuth(adminInfo);
        }

        sysDoctorMultipointMapper.insertList(practiceVo.getPracticeList());

        return ResEntity.entity(true, Constant.SUCCESS_DX, practiceVo);
    }
    /**
     *
     * @param appId
     * @param insCode
     * @param employeeId
     * @param userId 不传userId则只判断 工号在机构存不存在。通常用于新增用户。
     * @return 如果同一机构有重复就返回ture
     */
    public boolean checkUserEmployeeId(String appId,String insCode,String employeeId,String userId){
        SysDoctorMultipoint sysDoctorMultipoint = new SysDoctorMultipoint();
        sysDoctorMultipoint.setAppId(appId);
        sysDoctorMultipoint.setInsCode(insCode);
        sysDoctorMultipoint.setEmployeeId(employeeId);
        List<SysDoctorMultipoint> practiceList = sysDoctorMultipointMapper.checkUserEmployeeId(sysDoctorMultipoint);
        if (StringUtils.isBlank(userId) && practiceList.size()>0){
            return true;
        }
        if (!StringUtils.isBlank(userId)){
            for (SysDoctorMultipoint doctorMultipoint : practiceList) {
                if (!doctorMultipoint.getUserId().equals(userId)) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-18
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = sysDoctorMultipointMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 获取科室树
     *
     * @param appId   appId
     * @param insCode insCode
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysApp.SysApp>
     * <AUTHOR>
     * @date 2021/7/9
     */
    private List<SysApp> getDeptTree2(String appId, String insCode) {

        SysDepartment department = new SysDepartment();
        department.setAppId(appId);
        department.setInsCode(insCode);
        List<SysDepartment> deptList = sysDepartmentMapper.getPageListByObj2(department);

        //去重获取医疗机构
        LinkedHashMap<String, SysInstitution> insMap = new LinkedHashMap<>();

        for (SysDepartment sd : deptList) {
            SysInstitution ins = insMap.computeIfAbsent(sd.getInsCode(), k -> {
                SysInstitution i = new SysInstitution();
                i.setAppId(sd.getAppId());
                i.setAppName(sd.getAppName());
                i.setInsCode(sd.getInsCode());
                i.setInsParentCode(sd.getInsParentCode());
                i.setInsName(sd.getInsName());
                i.setInsList(new ArrayList<>());
                i.setDeptList(new ArrayList<>());
                return i;
            });
            ins.getDeptList().add(sd);
        }

        //组装医疗机构树
        List<SysInstitution> rootInsList = new ArrayList<>();

        insMap.forEach((insKey, ins) -> {
            SysInstitution parent = insMap.get(ins.getInsParentCode());
            if (parent != null) {
                parent.getInsList().add(ins);
            } else {
                rootInsList.add(ins);
            }
        });

        //去重获取医联体
        LinkedHashMap<String, SysApp> appMap = new LinkedHashMap<>();

        for (SysInstitution ins : rootInsList) {
            SysApp app = appMap.computeIfAbsent(ins.getAppId(), k -> {
                SysApp a = new SysApp();
                a.setAppId(ins.getAppId());
                a.setAppName(ins.getAppName());
                a.setInsList(new ArrayList<>());
                return a;
            });
            app.getInsList().add(ins);
        }

        List<SysApp> appList = new ArrayList<>();

        appMap.forEach((appKey, app) -> {
            if (!Constant.BASIC_APP_ID.equals(app.getAppId())) {
                appList.add(app);
            }
        });

        deptList.clear();
        deptList = null;
        return appList;
    }

    /**
     * 医联体转换JSON
     *
     * @param appList appList
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @date 2021/7/9
     */
    private JSONArray parseAppToJson(List<SysApp> appList) {
        JSONArray arr = new JSONArray();
        for (SysApp app : appList) {
            JSONObject node = newNode(app.getAppId(), app.getAppName(), "app", parseInsToJson(app.getInsList()));
            arr.add(node);
        }
        return arr;
    }
    private JSONObject newNode(String id, String name, String type, JSONArray children) {
        JSONObject node = new JSONObject();
        node.put("id", id);
        node.put("name", name);
        node.put("type", type);
        node.put("children", children);
        return node;
    }
    /**
     * 医疗机构转换JSON
     *
     * @param insList insList
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @date 2021/7/9
     */
    private JSONArray parseInsToJson(List<SysInstitution> insList) {
        JSONArray arr = new JSONArray();
        for (SysInstitution ins : insList) {
            JSONObject node = newNode(ins.getInsCode(), ins.getInsName(), "ins", parseInsToJson(ins.getInsList()));
            node.put("deptList", parseDeptToJson(ins.getDeptList()));
            arr.add(node);
        }
        return arr;
    }
    private JSONObject newNode(String id, String name, String type) {
        JSONObject node = new JSONObject();
        node.put("id", id);
        node.put("name", name);
        node.put("type", type);
        return node;
    }
    /**
     * 科室转换JSON
     *
     * @param deptList deptList
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @date 2021/7/9
     */
    private JSONArray parseDeptToJson(List<SysDepartment> deptList) {
        JSONArray arr = new JSONArray();
        for (SysDepartment dept : deptList) {
            //如果his科室id不为空，取his的科室id
            JSONObject node = newNode(StringUtils.isNotBlank(dept.getDepOriginId()) ? dept.getDepOriginId() : dept.getDeptId(), dept.getDeptName(), "dept");
            arr.add(node);
        }
        return arr;
    }
}
