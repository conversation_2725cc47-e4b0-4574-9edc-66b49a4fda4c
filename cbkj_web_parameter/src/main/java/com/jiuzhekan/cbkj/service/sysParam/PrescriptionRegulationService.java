package com.jiuzhekan.cbkj.service.sysParam;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulation;
import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulationItem;
import com.jiuzhekan.cbkj.mapper.sysParam.PrescriptionRegulationMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2024/9/11 10:19
 */
@Service
public class PrescriptionRegulationService {


    @Autowired
    private PrescriptionRegulationMapper prescriptionRegulationMapper;

    @Transactional(rollbackFor = Exception.class)
    public ResEntity prescriptionRegulationInsertOrUpdate(List<PrescriptionRegulation> prescriptionRegulation) {
        if (prescriptionRegulation.size() > 0) {
            List<String> strList = new ArrayList<>();
            List<PrescriptionRegulationItem> list = new ArrayList<>();
            for (PrescriptionRegulation pre : prescriptionRegulation) {
                List<PrescriptionRegulationItem> children = pre.getChildren();
                children.forEach(item -> {
                    item.setLevel(pre.getLevel());
                });
                List<PrescriptionRegulationItem> collect = children.stream().distinct().collect(Collectors.toList());
                if (collect.size() != children.size()) {
                    String s = "保存失败，级别" + pre.getLevel() + "存在条件重复，请检查配置！";
                    strList.add(s);
                } else {
                    list.addAll(children);
                }

            }
            if (strList.size() > 0) {
                return ResEntity.error(JSON.toJSONString(strList));
            }

            prescriptionRegulationMapper.prescriptionRegulationInsertOrUpdate(prescriptionRegulation);
            prescriptionRegulationMapper.prescriptionRegulationItemInsertOrUpdate(list);
            return ResEntity.success(prescriptionRegulation);
        }

        prescriptionRegulationMapper.prescriptionRegulationDel();
        return ResEntity.success(prescriptionRegulation);

    }


    public ResEntity getPrescriptionRegulation() {

        List<PrescriptionRegulation> prescriptionRegulation = prescriptionRegulationMapper.getPrescriptionRegulation();
        if (prescriptionRegulation.size() > 0) {
            prescriptionRegulation.forEach(pre->{
                pre.getChildren().forEach(item->{
                    item.setOptions(Arrays.asList(item.getOpt().split(",")));
                });
            });
            return ResEntity.success(prescriptionRegulation);
        }
        return ResEntity.success(new ArrayList<>());
    }

    public Object getCondition(String name) {
        List<Map<String, Object>> map = prescriptionRegulationMapper.getCondition(name);
        if (map.size() > 0) {
            return ResEntity.success(map);
        }
        return ResEntity.success(null);
    }

    public Object getEvent() {
        List<Map<String, Object>> map = prescriptionRegulationMapper.getEvent();
        if (map.size() > 0) {
            return ResEntity.success(map);
        }
        return ResEntity.success(null);
    }

    public static void main(String[] args) {
        String s="1,2";
        List<String> list = Arrays.asList(s.split(","));
        System.out.println(list);
    }
}
