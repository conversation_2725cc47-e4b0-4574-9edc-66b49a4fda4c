package com.jiuzhekan.cbkj.service.business.interfaceHis;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.HttpUtil;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.encry.RSAEncryption;
import com.jiuzhekan.cbkj.mapper.business.interfacehis.TInterfaceHisMapper;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLDecoder;
import java.sql.*;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class TInterfaceHisService {

    @Autowired
    private TInterfaceHisMapper tInterfaceHisMapper;


    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RSAEncryption rsaEncryption;

    @Value("${rsa.privateKey}")
    private String privateKey;

    /**
     * 加载分页数据
     *
     * @param tInterfaceHis 物理his表
     * @param page          分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-15
     */
    public Object getPageDatas(TInterfaceHis tInterfaceHis, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TInterfaceHis> list = tInterfaceHisMapper.getPageList(tInterfaceHis);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 任务界面用到的接口
     *
     * @param tInterfaceHis
     * @return
     */
    public Object getDatas(TInterfaceHis tInterfaceHis) {
        List<TInterfaceHis> list = tInterfaceHisMapper.getPageListByObj(tInterfaceHis);
        return ResEntity.entity(true, "", list);
    }

    /**
     * 加载某条数据
     *
     * @param hisId 物理his表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    public ResEntity findObj(String hisId) {

        if (StringUtils.isBlank(hisId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TInterfaceHis tInterfaceHis = tInterfaceHisMapper.getObjectById(hisId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tInterfaceHis);
    }


    /**
     * 插入新数据
     *
     * @param tInterfaceHis 物理his表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TInterfaceHis tInterfaceHis) {
        Boolean sqlResEntity = getaBoolean(tInterfaceHis);
        if (!sqlResEntity) {
            return ResEntity.error("缺失必要的连接信息");
        }
        if (Constant.HIS_URL_TYPE_SQL.contains(tInterfaceHis.getHisUrlType())) {
            if (StringUtils.isBlank(tInterfaceHis.getJdbcPassWord())) {
                return ResEntity.error("缺失必要的连接信息");
            }
        }
        tInterfaceHis.setHisId(String.valueOf(System.currentTimeMillis()));
        if (Constant.BASIC_STRING_ONE.equals(tInterfaceHis.getHisUrlType())) {
            tInterfaceHis.setJdbcDriver("com.mysql.jdbc.Driver");
        } else if (Constant.BASIC_STRING_TWO.equals(tInterfaceHis.getHisUrlType())) {
            tInterfaceHis.setJdbcDriver("oracle.jdbc.driver.OracleDriver");
        } else if (Constant.BASIC_STRING_THREE.equals(tInterfaceHis.getHisUrlType())) {
            tInterfaceHis.setJdbcDriver("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        }
        tInterfaceHis.setCreateDate(new Date());
        tInterfaceHis.setCreateUser(AdminUtils.getCurrentHr().getCreateUser());
        tInterfaceHis.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        long rows = tInterfaceHisMapper.insert(tInterfaceHis);

        return ResEntity.success(tInterfaceHis);
    }


    public void cacheAll() {
        List<TInterfaceHis> all = tInterfaceHisMapper.getAll();
        redisTemplate.opsForList().rightPush("connections", all);
    }

    public void clearAll() {
        redisTemplate.delete("connections");
    }

    /**
     * 修改
     *
     * @param tInterfaceHis 物理his表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TInterfaceHis tInterfaceHis) {
        Boolean sqlResEntity = getaBoolean(tInterfaceHis);
        if (!sqlResEntity) {
            return ResEntity.error("缺失必要的连接信息");
        }
        long rows = tInterfaceHisMapper.updateByPrimaryKey(tInterfaceHis);
        TInterfaceHis objectById = tInterfaceHisMapper.getObjectById(tInterfaceHis.getHisId());
        tInterfaceHis.setDicName(objectById.getDicName());
        tInterfaceHis.setStatus(objectById.getStatus());
//        clearAll();
//        cacheAll();
        return ResEntity.success(tInterfaceHis);
    }

    /**
     * 删除
     *
     * @param id ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        TInterfaceHis tInterfaceHis = new TInterfaceHis();
        tInterfaceHis.setHisId(id);
        long rowsR = tInterfaceHisMapper.deleteByPrimaryKey(tInterfaceHis);


        return ResEntity.success(rowsR);
    }


    public Object testConnect(TInterfaceHis tInterfaceHis) {
        Boolean sqlResEntity = getaBoolean(tInterfaceHis);
        if (!sqlResEntity) {
            return ResEntity.error("缺失必要的连接信息");
        }
        if (StringUtils.isBlank(tInterfaceHis.getJdbcPassWord()) && Constant.HIS_URL_TYPE_SQL.contains(tInterfaceHis.getHisUrlType())) {
            TInterfaceHis objectById = tInterfaceHisMapper.getObjectById(tInterfaceHis.getHisId());
            tInterfaceHis.setJdbcPassWord(objectById.getJdbcPassWord());
        }
        //测试连接数据库方法
        Boolean isSuccess = false;
        if (Constant.HIS_URL_TYPE_SQL.contains(tInterfaceHis.getHisUrlType())) {
            isSuccess = testSqlConnection(tInterfaceHis);
        } else if (Constant.HIS_URL_TYPE_HTTP.contains(tInterfaceHis.getHisUrlType())) {
            isSuccess = testHttpConnection(tInterfaceHis);
        }
        JSONObject jsonObject = new JSONObject();

        if (isSuccess) {
            jsonObject.put("status", isSuccess);
            jsonObject.put("message", "连接成功");
            return ResEntity.entity(true, "连接成功", jsonObject);
        } else {
            jsonObject.put("status", isSuccess);
            jsonObject.put("message", "连接失败");
            return ResEntity.entity(true, "", jsonObject);
        }
    }

    /**
     * 测试连接数据库方法
     *
     * @param tInterfaceHis
     * @return
     */
    private Boolean testSqlConnection(TInterfaceHis tInterfaceHis) {
        Boolean sqlResEntity = getSqlResEntity(tInterfaceHis);
        if (!sqlResEntity) {
            return false;
        }
        String url = null;
        String passWord = null;
        try {
            passWord = URLDecoder.decode(tInterfaceHis.getJdbcPassWord(), "UTF-8");
            passWord = rsaEncryption.dencryptByPrivateKey(passWord, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        tInterfaceHis.setJdbcPassWord(passWord);
        try {
            if (Constant.BASIC_STRING_ONE.equals(tInterfaceHis.getHisUrlType())) {
                Class.forName("com.mysql.jdbc.Driver");
                url = "jdbc:mysql://" + tInterfaceHis.getJdbcIp() + ":" + tInterfaceHis.getJdbcPort() + "/" + tInterfaceHis.getJdbcDatabaseName() + "?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai";
                log.info(url);
            } else if (Constant.BASIC_STRING_TWO.equals(tInterfaceHis.getHisUrlType())) {
                Class.forName("oracle.jdbc.OracleDriver");
                url = "jdbc:oracle:thin:@" + tInterfaceHis.getJdbcIp() + ":" + tInterfaceHis.getJdbcPort() + "/" + tInterfaceHis.getJdbcDatabaseName();
                log.info(url);
            } else if (Constant.BASIC_STRING_THREE.equals(tInterfaceHis.getHisUrlType())) {
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                url = "jdbc:sqlserver://" + tInterfaceHis.getJdbcIp() + ":" + tInterfaceHis.getJdbcPort() + ";" + "databaseName=" + tInterfaceHis.getJdbcDatabaseName();
                log.info(url);
            }
            Connection connection = DriverManager.getConnection(url, tInterfaceHis.getJdbcUserName(), tInterfaceHis.getJdbcPassWord());
            connection.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private Boolean testHttpConnection(TInterfaceHis tInterfaceHis) {
        String s = null;
        if (tInterfaceHis.getRequestMethodType().equals("GET")) {
            try {
                s = HttpUtil.sendGet(tInterfaceHis.getHisUrl(), tInterfaceHis.getRequestParams());
            } catch (Exception e) {
                return false;
            }
        } else if (tInterfaceHis.getRequestMethodType().equals("POST")) {
            try {
                s = HttpUtil.sendPost(tInterfaceHis.getHisUrl(), tInterfaceHis.getRequestParams());
            } catch (Exception e) {
                return false;
            }
        }
        if (StringUtils.isBlank(s) || s.equals("false")) {
            return false;
        }
        JSONObject json = JSONObject.fromObject(s);
        Boolean status = (Boolean) json.get("status");
        return status;
    }

    /**
     * 校验连接信息是否完善
     *
     * @param tInterfaceHis
     * @return
     */
    private Boolean getaBoolean(TInterfaceHis tInterfaceHis) {
        Boolean sqlResEntity = false;
        if (Constant.HIS_URL_TYPE_SQL.contains(tInterfaceHis.getHisUrlType())) {
            sqlResEntity = getSqlResEntity(tInterfaceHis);
        } else if (Constant.HIS_URL_TYPE_HTTP.contains(tInterfaceHis.getHisUrlType())) {
            sqlResEntity = getHttpResEntity(tInterfaceHis);
        } else if (Constant.HIS_URL_TYPE_WEB.contains(tInterfaceHis.getHisUrlType())) {
            sqlResEntity = getWebServiceResEntity(tInterfaceHis);
        }
        return sqlResEntity;
    }

    private Boolean getSqlResEntity(TInterfaceHis tInterfaceHis) {
        if (StringUtils.isBlank(tInterfaceHis.getJdbcIp())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getJdbcPort())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getJdbcDatabaseName())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getJdbcUserName())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getJdbcTable())) {
            return false;
        }
        return true;
    }

    private Boolean getHttpResEntity(TInterfaceHis tInterfaceHis) {
        if (StringUtils.isBlank(tInterfaceHis.getRequestMethodType())) {
            return false;
        }
        return true;
    }

    private Boolean getWebServiceResEntity(TInterfaceHis tInterfaceHis) {
        if (StringUtils.isBlank(tInterfaceHis.getWebserviceContenttype())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getWebserviceMethod())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getWebserviceNamespace())) {
            return false;
        }
        if (StringUtils.isBlank(tInterfaceHis.getWebserviceResultnode())) {
            return false;
        }
        return true;
    }
}
