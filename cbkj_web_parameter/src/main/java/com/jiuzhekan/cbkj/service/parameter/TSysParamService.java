//package com.jiuzhekan.cbkj.service.parameter;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.github.pagehelper.PageHelper;
//import com.jiuzhekan.cbkj.beans.business.TSysParam;
//import com.jiuzhekan.cbkj.beans.business.sysParam.TSysParamVO;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.utils.AdminUtils;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.Page;
//import com.jiuzhekan.cbkj.mapper.business.TSysParamMapper;
//import com.jiuzhekan.cbkj.service.sysService.RedisService;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cache.annotation.Cacheable;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Service
//public class TSysParamService {
//
//    @Autowired
//    private TSysParamMapper tSysParamMapper;
//    @Autowired
//    private RedisService redisService;
//
//
//
//    /**
//     * 根据当前登录人获取所有参数详情
//     *
//     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
//     * <AUTHOR>
//     * @date 2020/3/2
//     */
////    @Cacheable(value = "pre-parameter-params:allParams", key = "#userId + #appId + #insCode + #deptId ")
////    public ResEntity getAllParams(String appId, String insCode, String deptId, String userId,String defaultPre,
////                                  //新增以下用户信息传参
////                                  String gravidity,String gender,String age) {
////
////        //所有参数
////        List<TSysParam> list = tSysParamMapper.getAllParamCode();
////        Map<String, Object> result = new HashMap<>();
////
////        for (TSysParam param : list) {
////
////            TSysParam p = redisService.getSysParam(appId, insCode, deptId, param.getParCode());
////
////            //有效的处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
////            if (Constant.VALID_PRESCRIPTION_TYPE.equals(param.getParCode())) {
////
////                String[] preType = new String[]{"", "内服中药方", "外用中药方", "中成药", "适宜技术方", "中药制剂"};
////                //String defaultPre = AdminUtils.getCurrentHr().getDefaultPre();
////                JSONArray arr = new JSONArray();
////                JSONObject obj;
////
////                for (String value : p.getParValues().split(Constant.ENGLISH_COMMA)) {
////                    obj = new JSONObject();
////                    obj.put("name", preType[Integer.parseInt(value)]);
////                    obj.put("value", value);
////                    //是否默认勾选处方类型
////                    if (StringUtils.isNotBlank(p.getParValues())) {
////                        obj.put("selected", defaultPre.contains(value));
////                    }
////                    arr.add(obj);
////                }
////
////                result.put(param.getParCode(), arr);
////
////            } else {
////
////                result.put(param.getParCode(), p.getParValues());
////            }
////
////        }
////
////        //医生设置
////        //result.put("USER_HABITS", redisService.getAdminInfoEx(userId));
////
////        //当前医生今天的月均贴金额
////        //result.put("MY_MONTHLY_AVG_AMOUNT", tStatisticsService.getDoctorMonthlyAvgAmount());
////
////        //公用代码
////        result.put("COMMON_CODE", getAllCodeParams(appId, insCode,deptId,gravidity,gender,age));
////
////        return new ResEntity(true, Constant.SUCCESS_DX, result);
////    }
//
//    /**
//     * 获取公用代码
//     *
//     * @param appId   appId
//     * @param insCode insCode
//     * @return java.util.Map<java.lang.String, java.lang.Object>
//     * <AUTHOR>
//     * @date 2021/7/23
//     */
////    private Map<String, Object> getAllCodeParams(String appId, String insCode
////            ,String deptId,String gravidity,String gender,String age) {
////        Map<String, Object> result = new HashMap<>();
////        result.put("PRESCRIPTION_INTERNAL_USAGE", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_INTERNAL_DESCRIPTION, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_INTERNAL_RATE", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_INTERNAL_FREQUENCY, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_INTERNAL_TIME", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_INTERNAL_USETIME, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_INTERNAL_ML", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_INTERNAL_ML, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_EXTERNAL_ML", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_EXTERNAL_ML, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_EXTERNAL_MODE", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_EXTERNAL_TYPE, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_EXTERNAL_OBJECT", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_EXTERNAL_INSTRUMENT, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_EXTERNAL_RATE", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_EXTERNAL_FREQUENCY, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_ACUPOINT_MODE", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_ACU_TYPE, "-100",deptId,gravidity,gender,age));
////        result.put("PRESCRIPTION_ACUPOINT_OBJECT", redisService.getCodeByCode(appId, insCode, Constant.CODE_PRE_ACU_PROJECT, "-100",deptId,gravidity,gender,age));
////        result.put("CODE_PREPARATION_MAT_FREQUENCY", redisService.getCodeByCode(appId, insCode, Constant.CODE_PREPARATION_MAT_FREQUENCY, "-100",deptId,gravidity,gender,age));
////        //2021-10-09增加 膏方有无糖
////        result.put("PRESCRIPTION_INTERNAL_PRODUCTION_TYPE", redisService.getCodeByCode(appId, insCode, Constant.CODE_PREPARATION_INTERNAL_PRODUCTION_TYPE, "-100",deptId,gravidity,gender,age));
////        return result;
////    }
//
//    /**
//     * 根据 code 获取系统参数表
//     *
//     * @param code
//     * @return
//     */
//    public TSysParam getSysParam(String code,String appId,String insCode,String deptId) {
////        String appId = AdminUtils.getCurrentAppId();
////        String insCode = AdminUtils.getCurrentInsCode();
////        String deptId = AdminUtils.getCurrentDeptId();
//        TSysParam param = redisService.getSysParam(appId, insCode, deptId, code);
//        if (param == null) {
//            param = new TSysParam();
//            param.setParCode(code);
//        }
//        return param;
//    }
//
//    /**
//     * 加载分页数据
//     *
//     * @param tSysParam
//     * @param page
//     * @return
//     */
////    public Object getPageDatas(TSysParam tSysParam, Page page) {
////        String appId = AdminUtils.getCurrentAppId();
////        String insCode = AdminUtils.getCurrentInsCode();
////        String deptId = AdminUtils.getCurrentDeptId();
////
////        if (!Constant.BASIC_APP_ID.equals(appId)) {
////            tSysParam.setAppId(appId);
////        }
////        if (!Constant.BASIC_INS_CODE.equals(insCode)) {
////            tSysParam.setInsCode(insCode);
////        }
////        if (!Constant.BASIC_DEPT_ID.equals(deptId)) {
////            tSysParam.setDeptId(deptId);
////        }
////
////        PageHelper.startPage(page.getPage(), page.getLimit());
////        List<TSysParam> list = tSysParamMapper.getPageListByObj(tSysParam);
////        return Page.getLayUiTablePageData(list);
////    }
//
//    /**
//     * 加载某条数据
//     *
//     * @param tSysParam
//     * @return
//     */
//    public ResEntity findObj(TSysParam tSysParam) {
//
//        if (StringUtils.isBlank(tSysParam.getParCode())) {
//            return new ResEntity(false, "参数不能为空哦", null);
//        }
//        List<TSysParam> list = tSysParamMapper.getListByCode(tSysParam);
//        return new ResEntity(true, Constant.SUCCESS_DX, TSysParamVO.from(list));
//    }
//
//
//    /**
//     * 保存参数
//     *
//     * @param params params
//     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
//     * <AUTHOR>
//     * @date 2021/7/19
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity save(TSysParamVO params) {
//
//        if (StringUtils.isBlank(params.getParCode())) {
//            return new ResEntity(false, "参数代码不能为空！！", null);
//        }
//        if (StringUtils.isBlank(params.getParNumber())) {
//            return new ResEntity(false, "保存失败，参数编号不能为空！！", null);
//        }
//        if (StringUtils.isBlank(params.getParId()) && tSysParamMapper.getObjExists(params.getParCode()) > 0) {
//            return new ResEntity(false, "保存失败，参数代码重复！！", null);
//        }
//        if (StringUtils.isBlank(params.getParValue())) {
//            return new ResEntity(false, "保存失败，系统参数值不能为空！！", null);
//        }
//        //没有主键id的情况
//        if (StringUtils.isBlank(params.getParId())) {
//            TSysParam tSysParam = new TSysParam();
//            tSysParam.setParNumber(params.getParNumber());
//            if (tSysParamMapper.getParamNumberRepeat(tSysParam) > 0) {
//                return new ResEntity(false, "保存失败，参数编号重复！！", null);
//            }
//        } else {
//            //不为空,查详情编号是否一致
//            TSysParam objectById = tSysParamMapper.getObjectById(params.getParId());
//            if (objectById == null){return new ResEntity(false, "保存失败，parId不存在", null);}
//            if (!StringUtils.isBlank(objectById.getParNumber()) ){
//                //之前没数据
//                if (!params.getParNumber().equals(objectById.getParNumber())) {
//                    return new ResEntity(false, "保存失败，参数编号无法修改", null);
//                }
//            }else {
//                //之前的数据编号是空的，需要查询，当前修改的编号是否已经存在
//                TSysParam tSysParam = new TSysParam();
//                tSysParam.setParNumber(params.getParNumber());
//                if (tSysParamMapper.getParamNumberRepeat(tSysParam) > 0) {
//                    return new ResEntity(false, "保存失败，参数编号重复！！", null);
//                }
//            }
//        }
//
//        List<TSysParam> list = params.trans();
//
//        tSysParamMapper.deleteByCondition(new TSysParam(params.getParCode()));
//        tSysParamMapper.insertList(list);
//
//        redisService.clearRedisCache("pre-ai-params::" + params.getParCode(), null);
//        redisService.clearRedisCache("pre-ai-params:allParams", null);
//
//        return ResEntity.success(null);
//    }
//
//
//    /**
//     * 删除
//     *
//     * @param param
//     * @return
//     */
//
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity deleteByParCode(TSysParam param) {
//        if (StringUtils.isBlank(param.getParCode())) {
//            return new ResEntity(false, "参数错误(缺少参数)！", null);
//        }
//
//        long rows = tSysParamMapper.deleteByCondition(param);
//
//        redisService.clearRedisCache("pre-ai-params::" + param.getParCode(), null);
//        redisService.clearRedisCache("pre-ai-params:allParams", null);
//
//        if (rows > 0) {
//            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
//        }
//        return new ResEntity(false, "删除失败，数据库异常", null);
//    }
//
//
////    /**
////     * 是否开启医保支付条件限制
////     *
////     * @return boolean
////     * <AUTHOR>
////     * @date 2021/9/2
////     */
////    public boolean insuranceLimitObject() {
////
////        //医保支付条件限制 0）不限制（所有人不提醒） 1）限制门特病人和住院病人 2）限制医保病人 3）限制所有人
////        TSysParam param = getSysParam(Constant.INSURANCE_LIMIT_OBJECT);
////
////        //0）不限制（所有人不提醒）
////        if (Constant.BASIC_STRING_ZERO.equals(param.getParValues())) {
////            return false;
////        }
////        //1）限制门特病人和住院病人
////        else if (Constant.BASIC_STRING_ONE.equals(param.getParValues())) {
////            TRegister register = AdminUtils.getCurrentRegister();
////            if (register == null) {
////                return false;
////            }
////            //1）限制住院病人
////            if (register.getClinicTypeId() != null && register.getClinicTypeId() == 2) {
////                return true;
////            }
////            //1）限制门特病人
////            if (register.getHisRecord() != null && Constant.BASIC_STRING_ONE.equals(register.getHisRecord().getIsSpecialDis())) {
////                return true;
////            }
////            return false;
////        }
////        //2）限制医保病人
////        else if (Constant.BASIC_STRING_TWO.equals(param.getParValues())) {
////            TRegister register = AdminUtils.getCurrentRegister();
////            if (register != null && register.getHisRecord() != null && !Constant.BASIC_STRING_ONE.equals(register.getHisRecord().getIsOwnExp())) {
////                return true;
////            }
////            return false;
////        }
////        //3）限制所有人
////        else if (Constant.BASIC_STRING_THREE.equals(param.getParValues())) {
////            return true;
////        }
////        return false;
////    }
//
////    /**
////     * 超剂量提醒文字
////     *
////     * @return java.lang.String
////     * <AUTHOR>
////     * @date 2021/9/2
////     */
////    public String ultralimitTips() {
////
////        //超剂量提醒文字设置 (医保限制报销|强制保存|超医保规定用药量)
////        TSysParam param = getSysParam(Constant.PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS);
////        String[] tips = param.getParValues().split("\\|");
////        if (tips.length == 3) {
////            return tips[2];
////        }
////        return "超规定用药量";
////    }
//
//    public ResEntity getAllParamClassifyList() {
//        List<Map<String, Object>> allParamClassifyList = tSysParamMapper.getAllParamClassifyList();
//        return new ResEntity(true, Constant.SUCCESS_DX, allParamClassifyList);
//    }
//
//}