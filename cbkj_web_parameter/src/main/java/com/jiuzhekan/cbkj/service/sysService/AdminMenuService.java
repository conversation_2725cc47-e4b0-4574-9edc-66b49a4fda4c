package com.jiuzhekan.cbkj.service.sysService;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRule;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminMenuMapper;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class AdminMenuService {

    @Autowired
    private AdminMenuMapper adminMenuMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 获取某个用户所有菜单
     *
     * @param uid
     * @return
     */
    public Object getMenuByUID(String uid,String modualCode) {

        return redisService.getMenuByUID(uid,modualCode);
    }


    /**
     * 获取分页数据
     *
     * @param adminMenu
     * @param page
     * @return
     */
    public Object getPageDatas(AdminMenu adminMenu, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<Map<String, Object>> lis = adminMenuMapper.getPageDatas(adminMenu);
        Object result = Page.getLayUiTablePageData(lis);
        return result;
    }

    /**
     * 获取某个单条数据
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {
        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "参数错误（缺少参数）", null);
        }
        AdminMenu adminMenu = adminMenuMapper.selectByPrimaryKey(id);
        if (null != adminMenu) {
            return new ResEntity(true, Constant.SUCCESS_DX, adminMenu);
        }
        return new ResEntity(false, "数据不存在，检查参数", null);
    }

    /**
     * 插入
     *
     * @param adminMenu
     * @return
     */
    @CacheEvict(value = "pre-parameter-menu", allEntries = true)
    public ResEntity insert(AdminMenu adminMenu) {
        if (StringUtils.isEmpty(adminMenu.getMenuName())) {
            return new ResEntity(false, "名称不能为空", null);
        }
        adminMenu.setStatus(Constant.BASIC_STRING_ZERO);
        adminMenu.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        if (adminMenu.getParentMenuId() == null) {
            adminMenu.setParentMenuId("0");
        }
        long rows = adminMenuMapper.insert(adminMenu);
        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, adminMenu.getMenuId());
        }
        return new ResEntity(false, "新增失败，系统错误！", null);
    }

    /**
     * 修改
     *
     * @param adminMenu
     * @return
     */
    @CacheEvict(value = "pre-parameter-menu", allEntries = true)
    public ResEntity update(AdminMenu adminMenu) {


        if (null == adminMenu.getMenuId()) {
            return new ResEntity(false, "mid不能为空", null);
        }
        if (StringUtils.isEmpty(adminMenu.getMenuName())) {
            return new ResEntity(false, "名称不能为空", null);
        }
        if (null == adminMenu.getStatus()) {
            return new ResEntity(false, "状态不能为空", null);
        }
        long rows = adminMenuMapper.updateByPrimaryKey(adminMenu);
        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, adminMenu.getMenuId());
        }
        return new ResEntity(false, "服务异常，请稍后重试", null);
    }

    /**
     * 非级联删除
     *
     * @param adminMenu
     * @return
     */
    @CacheEvict(value = "pre-parameter-menu", allEntries = true)
    public ResEntity deleteLis(AdminMenu adminMenu) throws Exception {
        if (StringUtils.isBlank(adminMenu.getIds())) {
            return new ResEntity(false, "缺少参数", null);
        }
        adminMenuMapper.deleteRMbyMid(adminMenu.getIds().split(","));
        long rowsA = adminMenuMapper.deleteBylis(adminMenu.getIds().split(","));
        return new ResEntity(true, Constant.SUCCESS_DX, rowsA);
    }

    /**
     * 修改是否启用
     *
     * @param adminMenu
     * @return
     */
    @CacheEvict(value = "pre-parameter-menu", allEntries = true)
    public ResEntity updateEnableds(AdminMenu adminMenu) {
        if (null == adminMenu.getMenuId() || null == adminMenu.getStatus()) {
            return new ResEntity(false, "缺少参数,请稍后重试", null);
        }
        Map<String, Object> params = new HashMap<>(16);
        params.put("menu_id", adminMenu.getMenuId());
        params.put("status", adminMenu.getStatus());
        long rows = adminMenuMapper.updateEnabled(params);
        return new ResEntity(true, Constant.SUCCESS_DX, rows);
    }


    /**
     * 返回所有非按钮菜单
     *
     * @return
     */
    public Object getAllMenuListM() {
        List<AdminMenu> adminMenus = redisService.getAllEnableMenu();
        List<AdminMenu> listObj = new ArrayList<>();
        for (AdminMenu adminMenu : adminMenus) {
//            if("1".equals(adminMenu.getMenuType())){
            listObj.add(adminMenu);
//            }
        }
        return listObj;
    }

    /**
     * 获取当前用户访问路径的按钮
     *
     * @param path
     * @return
     */
    public List<JSONObject> getBtnMenuLisByPath(String path) {
        if (StringUtils.isBlank(path)) {
            return null;
        }

        if (path.startsWith("/")) {
            path = path.substring(1, path.length());
        }


//        Map<String,Object> params = new HashMap<>();
//        params.put("path",path);
//        params.put("adminId",adminId);

        List<AdminRule> rules = AdminUtils.getCurrentHr().getRoles();

        List<AdminMenu> allAdmins = redisService.getAllEnableMenu();

        Map<String, List<AdminMenu>> pathListMenu = new HashMap<>();

        Map<String, Object> ruleM = new HashMap<>();

        for (AdminRule rule : rules) {
            ruleM.put(rule.getRoleId(), rule);
        }

        List<AdminMenu> pathMenuList = new ArrayList<>();
        boolean bl;
        //获取该角色所有的按钮菜单
        for (AdminMenu adminMenu : allAdmins) {
            bl = false;
            if ("2".equals(adminMenu.getMenuType())) {
                for (AdminRule rule : adminMenu.getRules()) {
                    if (ruleM.containsKey(rule.getRoleId())) {
                        bl = true;
                        break;
                    }
                }
                if (bl) {
                    if (pathListMenu.containsKey(adminMenu.getParentMenuId())) {
                        pathListMenu.get(adminMenu.getParentMenuId()).add(adminMenu);
                    } else {
                        List<AdminMenu> childList = new ArrayList<>();
                        childList.add(adminMenu);
                        pathListMenu.put(adminMenu.getParentMenuId(), childList);
                    }
                }
            } else {
//                if (adminMenu.getPath().equals(path)) {
                if (adminMenu.getMenuPath().equals(path)) {
                    pathMenuList.add(adminMenu);
                }
            }
        }

        List<JSONObject> resultList = new ArrayList<JSONObject>();
        JSONObject one;

        if (pathMenuList.size() > 0) {
            Map<String, AdminMenu> roleMenuM = new HashMap<>();
            //获取按钮的父级菜单
            List<AdminMenu> roleMenus = new ArrayList<>();
            for (AdminMenu adminMenu : pathMenuList) {
                if (pathListMenu.containsKey(adminMenu.getMenuId())) {
                    roleMenus.addAll(pathListMenu.get(adminMenu.getMenuId()));
                }
            }

            for (AdminMenu adminMenu : roleMenus) {
                if (!roleMenuM.containsKey(adminMenu.getMenuId())) {
                   // adminMenu.setIconcls(adminMenu.getIconcls());

                    one = new JSONObject();
                    one.put("id", adminMenu.getMenuId());
                    one.put("name", adminMenu.getMenuName());
//                    one.put("regEx", adminMenu.getUrl());
                    one.put("path", adminMenu.getMenuPath());
                    one.put("btnType", adminMenu.getBtnType());
//                    one.put("iconCls", adminMenu.getIconcls());
                    resultList.add(one);
                } else {
                    roleMenuM.put(adminMenu.getMenuId(), adminMenu);
                }
            }
        }
        return resultList;
    }

    /**
     * 级联删除
     *
     * @param adminMenu
     * @return
     */
    @CacheEvict(value = "pre-parameter-menu", allEntries = true)
    public ResEntity deleteLisJL(AdminMenu adminMenu) {

        if (StringUtils.isBlank(adminMenu.getIds())) {
            return new ResEntity(false, "缺少参数", null);
        }
        List<AdminMenu> result = getJIMenu(adminMenu.getIds());
        if (result.size() > 0) {

            String[] arrids = new String[result.size()];
            List<String> lisIds = new ArrayList<>();
            for (AdminMenu adminMenu1 : result) {
                lisIds.add(String.valueOf(adminMenu1.getMenuId()));
            }
            adminMenuMapper.deleteRMbyMid(lisIds.toArray(arrids));
            adminMenuMapper.deleteBylis(lisIds.toArray(arrids));
        }
        return new ResEntity(true, Constant.SUCCESS_DX, null);
    }


    /**
     * 判断节点
     *
     * @param parentID
     * @param list
     * @return
     */
    public List<AdminMenu> getChilds(String parentID, List<AdminMenu> list) {

        List<AdminMenu> result = new ArrayList<>();
        for (AdminMenu adminMenu : list) {
            if (adminMenu.getParentMenuId().equals(parentID)) {
                result.add(adminMenu);
            }
        }
        return result;
    }

    /**
     * 得出一个集合中的级联情况
     *
     * @param parent_id
     * @param list
     * @param resultList
     */
    private void getChAdminList(String parent_id, List<AdminMenu> list, List<AdminMenu> resultList) {

        for (AdminMenu adminMenu : list) {
            if (adminMenu.getParentMenuId().equals(parent_id) && getChilds(parent_id, list).size() > 0) {
                getChAdminList(adminMenu.getMenuId(), list, resultList);
                resultList.add(adminMenu);
            }
        }
    }

    /**
     * 返回该菜单中的所有级联
     *
     * @param parentID
     * @return
     */
    public List<AdminMenu> getJIMenu(String parentID) {

        List<AdminMenu> allMenu = redisService.getAllEnableMenu();
        List<AdminMenu> result = new ArrayList<>();

        getChAdminList(parentID, allMenu, result);
        for (AdminMenu adminMenu : allMenu) {
            if (adminMenu.getMenuId().equals(parentID)) {
                adminMenu.setFirst(true);
                result.add(adminMenu);
            }
        }
        return result;
    }

    /**
     * 根据主键返回个list 菜单
     *
     * @param id
     * @return
     */
    public List<AdminMenu> getObjByID(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return adminMenuMapper.getMenuObjByMID(id);
    }

    /**
     * 返回角色的菜单
     *
     * @param id
     * @return
     */

    public List<AdminMenu> getListObjByRID(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return adminMenuMapper.getMenuObjByRID(id);
    }

    /**
     * @param parentID      父ID
     * @param adminMenuList 菜单集合
     * @return List<AdminMenu>
     * @throws
     * @title 递归遍历集合返回一颗List<AdminMenu>无限树
     * @description 返回一颗树
     * <AUTHOR>
     * @updateTime 2019/7/19 13:30
     */
    private List<AdminMenu> getAdminMenuTree(String parentID, List<AdminMenu> adminMenuList) {

        List<AdminMenu> resultList = new ArrayList<>();
        if (null != parentID && adminMenuList.size() > 0) {
            for (AdminMenu adminMenu : adminMenuList) {
                if (adminMenu.getParentMenuId().equals(parentID)) {
                    List<AdminMenu> childList = getAdminMenuTree(adminMenu.getMenuId(), adminMenuList);
                    adminMenu.setChildList(childList);
                    resultList.add(adminMenu);
                }
            }
        }
        return resultList;
    }


    /**
     * @param
     * @return
     * @throws
     * @title 获取所有菜单树
     * @description 获取所有菜单树
     * <AUTHOR>
     * @updateTime 2019/12/18 14:46
     */
    public ResEntity getAllMenuList() {

        List<AdminMenu> allMenu = adminMenuMapper.getAllMenuList();
        List<AdminMenu> menuList1 = new ArrayList<AdminMenu>();

        if (allMenu != null && !allMenu.isEmpty()) {
            //大菜单
            for (AdminMenu menu : allMenu) {
                if ("0".equals(menu.getParentMenuId())) {
                    menuList1.add(menu);
                }
            }
            //二级菜单
            if (!menuList1.isEmpty()) {
                for (AdminMenu menu1 : menuList1) {
                    List<AdminMenu> menuList2 = new ArrayList<AdminMenu>();
                    for (AdminMenu menu : allMenu) {
                        if (menu1.getMenuId().equals(menu.getParentMenuId())) {
                            menuList2.add(menu);
                        }
                    }
                    //按钮
                    if (!menuList2.isEmpty()) {
                        for (AdminMenu menu2 : menuList2) {
                            List<AdminMenu> menuList3 = new ArrayList<AdminMenu>();
                            for (AdminMenu menu : allMenu) {
                                if (menu2.getMenuId().equals(menu.getParentMenuId())) {
                                    menuList3.add(menu);
                                }
                            }
                            if (!menuList3.isEmpty()) {
                                menu2.setChildList(menuList3);
                            }
                        }
                        menu1.setChildList(menuList2);
                    }
                }
            }

        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, menuList1);
    }

    /**
     * @param
     * @return
     * @throws
     * @title 修改菜单排序
     * @description 修改菜单排序
     * <AUTHOR>
     * @updateTime 2019/12/18 17:57
     */
    @CacheEvict(value = "pre-parameter-menu", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateSortNumberByList(List<AdminMenu> menuList) {
        if (menuList != null && !menuList.isEmpty()) {
            for (AdminMenu menu : menuList) {
                if ( StringUtils.isBlank( menu.getMenuId() )  || StringUtils.isBlank( menu.getParentMenuId())) {
                    return new ResEntity(false, "参数异常，请检查后重试", null);
                }

            }
            int i = adminMenuMapper.updateSortNumberByList(menuList);
            if (i < 1) {
                return new ResEntity(false, "服务异常，请稍后重试", null);
            }
        }
        return new ResEntity(true, Constant.SUCCESS_DX, null);
    }
}