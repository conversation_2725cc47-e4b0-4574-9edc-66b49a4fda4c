package com.jiuzhekan.cbkj.service.sysapp;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitutionNode;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysMange.InstitutionDTO;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.bs.BsAreaMapper;
import com.jiuzhekan.cbkj.mapper.bs.BsCityMapper;
import com.jiuzhekan.cbkj.mapper.bs.BsProvinceMapper;
import com.jiuzhekan.cbkj.mapper.bs.BsStreetMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysAppMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.swing.text.TabableView;
import java.util.*;

@Service
public class SysInstitutionService {


    private SysInstitutionMapper sysInstitutionMapper;


    private BsProvinceMapper bsProvinceMapper;

    private BsAreaMapper bsAreaMapper;

    private BsCityMapper bsCityMapper;


    private BsStreetMapper bsStreetMapper;

    @Autowired
    SysInstitutionService(SysInstitutionMapper sysInstitutionMapper, BsProvinceMapper bsProvinceMapper
            , BsAreaMapper bsAreaMapper, BsCityMapper bsCityMapper, BsStreetMapper bsStreetMapper) {
        this.sysInstitutionMapper=sysInstitutionMapper;
        this.bsProvinceMapper=bsProvinceMapper;
        this.bsAreaMapper=bsAreaMapper;
        this.bsCityMapper=bsCityMapper;
        this.bsStreetMapper=bsStreetMapper;
    }

    /**
     * 加载分页数据
     *
     * @param institutionDTO
     * @param page
     * @return
     */
    public Object getPageDatas(InstitutionDTO institutionDTO, Page page) {
        SysInstitution sysInstitution = getSysInstitution(institutionDTO);
        PageHelper.startPage(page.getPage(), page.getLimit());

        String appId = AdminUtils.getCurrentAppId();
        if (StringUtils.isBlank(sysInstitution.getAppId()) && !Constant.BASIC_APP_ID.equals(appId)) {
            sysInstitution.setAppId(appId);
        }
        if (Constant.BASIC_APP_ID.equals(sysInstitution.getAppId())) {
            sysInstitution.setAppId(null);
        }
        if (Constant.BASIC_INS_CODE.equals(sysInstitution.getInsCode())) {
            sysInstitution.setInsCode(null);
        }
        List<SysInstitution> list = sysInstitutionMapper.getPageListByObj(sysInstitution);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 插入新数据
     *
     * @param institutionDTO
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::ins"},allEntries = true)
    public ResEntity insert(InstitutionDTO institutionDTO) {
        SysInstitution sysInstitution = getSysInstitution(institutionDTO);
        Integer count = sysInstitutionMapper.getInsCountByIns(sysInstitution);
        if (count > 0) {
            return new ResEntity(false, "该医疗机构代码已存在！！", null);
        }
        if (StringUtils.isBlank(sysInstitution.getInsCategory())) {
            return new ResEntity(false, "缺少医疗机构类别！！", null);
        }
/*        SysInstitution objectById = sysInstitutionMapper.getObjectById(institutionDTO.getInsId());
        if (null != objectById) {
            return ResEntity.entity(false, "医疗机构代码重复", false);
        }*/
        sysInstitution.setInsId(IDUtil.getID());
        sysInstitution.setCreateDate(new Date());
        sysInstitution.setInsAddress(institutionDTO.getInsAddress());
        sysInstitution.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        /*  sysInstitution.setCreateUserName(AdminUtils.getCurrentHr().getUsername());*/
        sysInstitution.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());

        sysInstitution.setInsParentCode(institutionDTO.getInsParentCode());
        sysInstitution.setStatus(institutionDTO.getStatus());
        sysInstitution.setSort(institutionDTO.getSort());
        sysInstitution.setInsIslast(1);


        sysInstitution.setProvinceName(bsProvinceMapper.getProvinceByCode(institutionDTO.getProvinceCode()).getProvinceName());

        sysInstitution.setCityName(bsCityMapper.getBsCityByCode(institutionDTO.getCityCode()).getCityName());

        sysInstitution.setAreaName(bsAreaMapper.getAreaByCode(institutionDTO.getAreaCode()).getAreaName());

        sysInstitution.setStreetName(bsStreetMapper.getBsStreetByCode(institutionDTO.getStreetCode()).getStreetName());

        //如果有上级机构的话,修改上级机构的末极标志为否
        if (StringUtils.isNotBlank(sysInstitution.getInsParentCode())) {
            sysInstitution.setInsParentCode(sysInstitution.getInsParentCode());
            sysInstitution.setInsIslast(0);
        }
        long rows = sysInstitutionMapper.insert(sysInstitution);
        if (rows <= 0) {
            return new ResEntity(false, "保存失败，数据库异常！！", null);
        }


        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitution);
    }


    /**
     * 修改
     *
     * @param institutionDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::ins","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity update(InstitutionDTO institutionDTO) {
        SysInstitution sysInstitution = getSysInstitution(institutionDTO);
        sysInstitution.setInsParentCode(institutionDTO.getInsParentCode());
        sysInstitution.setSort(institutionDTO.getSort());

        sysInstitution.setProvinceName(bsProvinceMapper.getProvinceByCode(institutionDTO.getProvinceCode()).getProvinceName());

        sysInstitution.setCityName(bsCityMapper.getBsCityByCode(institutionDTO.getCityCode()).getCityName());

        sysInstitution.setAreaName(bsAreaMapper.getAreaByCode(institutionDTO.getAreaCode()).getAreaName());

        sysInstitution.setStreetName(bsStreetMapper.getBsStreetByCode(institutionDTO.getStreetCode()).getStreetName());
        sysInstitution.setUpdateDate(new Date());
        sysInstitution.setUpdateUser(AdminUtils.getCurrentHr().getUserId());
        // sysInstitution.setUpdateUserName(AdminUtils.getCurrentHr().getUsername());
        sysInstitution.setUpdateUserName(AdminUtils.getCurrentHr().getNameZh());
        sysInstitution.setAppName(institutionDTO.getAppName());
        /*
        Integer count = sysInstitutionMapper.getInsCountByIns(sysInstitution);
        if (count > 0) {
            return new ResEntity(false, "该医疗机构代码已存在！！", null);
        }*/
        long rows = sysInstitutionMapper.updateByPrimaryKey(sysInstitution);
        SysInstitution objectById = sysInstitutionMapper.getObjectById2(sysInstitution.getInsId());
     /*
        SysApp sysApp = sysAppMapper.getObjectById(objectById.getAppId());
        if (sysApp == null) {
            return ResEntity.entity(false, "该医疗机构所属医共体不存在", null);
        }
        objectById.setAppName(sysApp.getAppName());*/
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, objectById);
        }
        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param insId
     * @return
     */
    public ResEntity findObj(String insId) {

        if (StringUtils.isBlank(insId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        SysInstitution sysInstitution = sysInstitutionMapper.getObjectById(insId);
        return new ResEntity(true, Constant.SUCCESS_DX, sysInstitution);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::app","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity deleteLis(String id) {
        SysInstitution sysInstitution = new SysInstitution();
        sysInstitution.setInsId(id);
        sysInstitution.setStatus(Constant.BASIC_DEL_NO);
        long rows = sysInstitutionMapper.deleteByPrimaryKey(sysInstitution);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "删除失败，数据库异常", null);
    }

    /**
     * 根据insId，appId，insCode 三个条件组合查询医疗机构
     *
     * @param sysInstitution
     * @return
     * <AUTHOR>
     */
    public SysInstitution getInsByIns(SysInstitution sysInstitution) {
        return sysInstitutionMapper.getInsByIns(sysInstitution);
    }

    public List<SysInstitutionNode> getSysInstitutionListTree(List<SysApp> apps) {
        List<SysInstitutionNode> nodes = new ArrayList<>();
        apps.forEach(a -> nodes.add(new SysInstitutionNode(a.getAppId(), a.getAppName(), a.getAppId(), getInsTree(a.getAppId()))));
        return nodes;
    }

    /**
     * 获取医疗机构列表
     *
     * @param sysInstitution
     * @return
     */
    public List<SysInstitution> getSysInstitutionList(SysInstitution sysInstitution) {
        List<SysInstitution> resultList = new ArrayList<>();
        String appId = AdminUtils.getCurrentAppId();
        if (StringUtils.isBlank(sysInstitution.getAppId()) && !Constant.BASIC_APP_ID.equals(appId)) {
            sysInstitution.setAppId(appId);
        }

        List<SysInstitution> allList = sysInstitutionMapper.getSysInstitutionByOryCateGory(sysInstitution);

        if (StringUtils.isBlank(sysInstitution.getInsId())) {
            resultList = allList;
        } else {
            List<SysInstitution> rootList = new ArrayList<>();

            Map<String, SysInstitution> map = new HashMap<>();

            for (SysInstitution institution : allList) {
                institution.setInsList(new ArrayList<>());
                map.put(institution.getInsCode(), institution);
            }
            String insId = sysInstitution.getInsId();
            for (SysInstitution institution : allList) {
                SysInstitution parent = map.get(institution.getInsParentCode());
                if (parent != null) {
                    if (insId.equals(institution.getInsId())) {
                        continue;
                    }
                    parent.getInsList().add(institution);
                } else {
                    rootList.add(institution);
                }
            }

            resultList = rootList;
            //iterTreeToList(rootList, resultList, sysInstitution.getInsId());
        }
        return resultList;
    }

    private void iterTreeToList(List<SysInstitution> list, List<SysInstitution> resultList, String insId) {
        for (SysInstitution sysInstitution : list) {
            if (!sysInstitution.getInsId().equals(insId)) {
                resultList.add(sysInstitution);
                iterTreeToList(sysInstitution.getInsList(), resultList, insId);
            }
        }
    }

    /**
     * 启用/禁用
     *
     * @param sysInstitution
     */
    public ResEntity changeUpdate(SysInstitution sysInstitution) {
        long rows = sysInstitutionMapper.updateByPrimaryKey(sysInstitution);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "启用/禁用失败，数据库异常", null);
    }

    /**
     * @param
     * @return
     * @throws
     * @title 医疗机构三级联动
     * @description 方法详细描述
     * <AUTHOR>
     * @updateTime 2020/1/13 10:08
     */
    public List<SysInstitution> getInsListByIns(SysInstitution sysInstitution) {
        return sysInstitutionMapper.getInsListByIns(sysInstitution);
    }

    /**
     * 获取下级机构(包含已删除和已禁用的)
     *
     * @param sysInstitution appId和insCode
     * @return
     * <AUTHOR>
     */
    public List<SysInstitution> getSubSysInstitutionList(SysInstitution sysInstitution) {
        List<SysInstitution> resultList = new ArrayList<>();
        List<SysInstitution> listAll = sysInstitutionMapper.getInsByAppId(sysInstitution);

        if (null == listAll || listAll.isEmpty()) {
            return resultList;
        } else {
            Map<String, List<SysInstitution>> map = new HashMap<>();//key:父code   value:子List
            for (SysInstitution s : listAll) {
                if (StringUtils.isNotBlank(s.getInsParentCode())) {
                    if (null != map.get(s.getInsParentCode())) {
                        map.get(s.getInsParentCode()).add(s);
                    } else {
                        List<SysInstitution> l = new ArrayList<>();
                        l.add(s);
                        map.put(s.getInsParentCode(), l);
                    }
                }
            }

            List<SysInstitution> myChild = map.get(sysInstitution.getInsCode());

            if (null != myChild) {
                for (int i = 0; i < myChild.size(); i++) {//先把子节点加进去
                    resultList.add(myChild.get(i));
                }
                iterTreeToList2(myChild, map, resultList);//循环孙节点

            }

            listAll.clear();
            listAll = null;
            map.clear();
            map = null;
        }
        return resultList;
    }

    public List<String> getSubInsByCode(String insPcode) {
        List<String> result = new ArrayList<>();
        getSubInsCode(insPcode, result);
        return result;
    }

    private void getSubInsCode(String insPcode, List<String> result) {
        List<String> l1 = sysInstitutionMapper.getSubIns(insPcode);
        if (l1 == null || l1.isEmpty()) {
            result.add(insPcode);
        } else {
            l1.forEach(ic -> getSubInsCode(ic, result));
        }
    }

    public static void iterTreeToList2(List<SysInstitution> childList, Map<String, List<SysInstitution>> map, List<SysInstitution> resultList) {
        for (SysInstitution sysInstitution : childList) {
            List<SysInstitution> list = map.get(sysInstitution.getInsCode());
            if (null != list && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    resultList.add(list.get(i));
                }
                iterTreeToList2(map.get(sysInstitution.getInsCode()), map, resultList);
            }
        }
    }

    /**
     * 根据appId查询该医联体的所有顶层机构
     *
     * @param sysInstitution
     * @return
     * <AUTHOR>
     */
    public List<SysInstitution> getRootInsByAppId(SysInstitution sysInstitution) {
        return sysInstitutionMapper.getRootInsByAppId(sysInstitution);
    }

    /**
     * 获取医疗机构树
     *
     * @return
     */
    public List<SysInstitution> getInsTree(String appId) {
        SysInstitution sysInstitution = new SysInstitution();
        if (StringUtils.isBlank(appId)) {
            appId = AdminUtils.getCurrentAppId();
        }
        if (Constant.BASIC_APP_ID.equals(appId)) {
            appId = null;
        }

        sysInstitution.setAppId(appId);
        List<SysInstitution> allList = sysInstitutionMapper.getSysInstitutionByOryCateGory(sysInstitution);

        List<SysInstitution> rootList = new ArrayList<>();

        Map<String, SysInstitution> map = new HashMap<>();

        for (SysInstitution institution : allList) {
            institution.setInsList(new ArrayList<>());
            map.put(institution.getInsCode(), institution);
        }

        for (SysInstitution institution : allList) {
            SysInstitution parent = map.get(institution.getInsParentCode());
            if (parent != null) {
                parent.getInsList().add(institution);
            } else {
                rootList.add(institution);
            }
        }
        allList.clear();
        allList = null;
        return rootList;
    }

    private SysInstitution getSysInstitution(InstitutionDTO institutionDTO) {
        SysInstitution sysInstitution = new SysInstitution();
        sysInstitution.setInsId(institutionDTO.getInsId());
        sysInstitution.setAppId(institutionDTO.getAppId());
        sysInstitution.setAppName(institutionDTO.getAppName());
        sysInstitution.setInsCode(institutionDTO.getInsCode());
        sysInstitution.setInsName(institutionDTO.getInsName());
        sysInstitution.setInsAddress(institutionDTO.getInsAddress());
        sysInstitution.setInsPinyin(institutionDTO.getInsPinyin());
        sysInstitution.setInsWubi(institutionDTO.getInsWubi());
        sysInstitution.setInsCategory(institutionDTO.getInsCategory());
        sysInstitution.setProvinceCode(institutionDTO.getProvinceCode());
        sysInstitution.setProvinceName(institutionDTO.getProvinceName());
        sysInstitution.setCityCode(institutionDTO.getCityCode());
        sysInstitution.setAreaCode(institutionDTO.getAreaCode());
        sysInstitution.setAreaName(institutionDTO.getAreaName());
        sysInstitution.setStreetCode(institutionDTO.getStreetCode());
        sysInstitution.setStreetName(institutionDTO.getStreetName());
        sysInstitution.setInsLevel(institutionDTO.getInsLevel());
        sysInstitution.setStatus(institutionDTO.getStatus());

        sysInstitution.setUpload(institutionDTO.getUpload());
        sysInstitution.setInsShorterName(institutionDTO.getInsShorterName());
        sysInstitution.setYardName(institutionDTO.getYardName());
        sysInstitution.setYardCode(institutionDTO.getYardCode());
        sysInstitution.setPrescriptionInstitutionCode(institutionDTO.getPrescriptionInstitutionCode());
        sysInstitution.setPrescriptionInstitutionName(institutionDTO.getPrescriptionInstitutionName());
        sysInstitution.setSocialCode(institutionDTO.getSocialCode());
        sysInstitution.setCommitKey(institutionDTO.getCommitKey());


        return sysInstitution;
    }

}