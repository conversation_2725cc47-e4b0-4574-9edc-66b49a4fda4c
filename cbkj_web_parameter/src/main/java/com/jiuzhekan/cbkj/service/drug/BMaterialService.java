package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.BMatSynonym;
import com.jiuzhekan.cbkj.beans.drug.BMaterial;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.StringJudges;
import com.jiuzhekan.cbkj.mapper.drug.BMatSynonymMapper;
import com.jiuzhekan.cbkj.mapper.drug.BMaterialMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class BMaterialService {

    @Autowired
    private BMaterialMapper bMaterialMapper;
    @Autowired
    private BMatSynonymMapper bMatSynonymMapper;

    /**
     * 加载分页数据
     *
     * @param bMaterial 知识库中药信息表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(BMaterial bMaterial, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<BMaterial> list = bMaterialMapper.getPageListByObj(bMaterial);
        for (BMaterial material : list) {
            BMatSynonym bMatSynonym = new BMatSynonym();
            bMatSynonym.setKMatId(material.getkMatId());
            bMatSynonym.setKMatType(Constant.BASIC_STRING_ONE);
            List<BMatSynonym> pageListByObj = bMatSynonymMapper.getPageListByObj(bMatSynonym);
            material.setkMatSynonyms(pageListByObj);
        }
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param kMatId 知识库中药信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String kMatId) {

        if (StringUtils.isBlank(kMatId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        BMaterial bMaterial = bMaterialMapper.getObjectById(kMatId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, bMaterial);
    }


    /**
     * 插入新数据
     *
     * @param bMaterial 知识库中药信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(BMaterial bMaterial){

        bMaterial.setkMatId(IDUtil.getID());
        long rows = bMaterialMapper.insert(bMaterial);

        return ResEntity.success(bMaterial);
    }


    /**
     * 修改
     *
     * @param bMaterial 知识库中药信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(BMaterial bMaterial) {

        long rows = bMaterialMapper.updateByPrimaryKey(bMaterial);

        return ResEntity.success(bMaterial);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = bMaterialMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
