package com.jiuzhekan.cbkj.service.business.stock;
import com.jiuzhekan.cbkj.beans.business.stock.TStockQuery;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import com.jiuzhekan.cbkj.beans.business.stock.StockListDo;
import com.jiuzhekan.cbkj.beans.business.stock.TStock;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.stock.TStockMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class TStockService {

    @Autowired
    private TStockMapper tStockMapper;

    /**
     * 加载分页数据
     *
     * @param tStock 库存表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-15
     */
    public Object getPageDatas(TStock tStock, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStock> list = tStockMapper.getPageListByObj(tStock);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 获取库存明细
     * @param tStock
     * @param page
     * @return
     */
    //@Cacheable(value = "pre-parameter-stock", keyGenerator = "cacheKeyGenerator")
    public List<StockListDo> getStockList(TStockQuery tStock, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        TStock tStock1 = new TStock();
        BeanUtils.copyProperties(tStock,tStock1);
        List<StockListDo> stockList = tStockMapper.getStockList(tStock1);
        for (StockListDo stockListDo : stockList) {
            BigDecimal stockNum = stockListDo.getStockNum();
            BigDecimal ykStockNum = stockListDo.getYkStockNum();
            if (null != stockNum && null != ykStockNum) {
                BigDecimal subtract = stockNum.subtract(ykStockNum);
                stockListDo.setAvailableStock(subtract);
            }else {
                if (null == stockNum){
                    stockNum = new BigDecimal("0.00");
                    stockListDo.setStockNum(stockNum);
                }
                stockListDo.setYkStockNum(new BigDecimal("0.00"));
                stockListDo.setAvailableStock(stockNum);
            }
        }
        return stockList;

    }

    /**
     * 加载某条数据
     *
     * @param stoId 库存表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    public ResEntity findObj(String stoId) {

        if (StringUtils.isBlank(stoId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStock tStock = tStockMapper.getObjectById(stoId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStock);
    }


    /**
     * 插入新数据
     *
     * @param tStock 库存表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStock tStock){

        tStock.setStoId(IDUtil.getID());
        long rows = tStockMapper.insert(tStock);

        return ResEntity.success(tStock);
    }


    /**
     * 修改
     *
     * @param tStock 库存表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStock tStock) {

        long rows = tStockMapper.updateByPrimaryKey(tStock);

        return ResEntity.success(tStock);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStockMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
