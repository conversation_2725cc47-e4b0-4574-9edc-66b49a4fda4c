package com.jiuzhekan.cbkj.service.sysapp;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.beans.sysMange.AssociationDTO;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysapp.SysAppMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminInfoMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class SysAppService {

    @Autowired
    private SysAppMapper sysAppMapper;

    @Autowired
    private SysInstitutionMapper sysInstitutionMapper;

    @Autowired
    private SysDepartmentMapper sysDepartmentMapper;

    @Autowired
    private AdminInfoMapper adminInfoMapper;
    /**
     * 加载分页数据
     * @param sysApp
     * @param page
     * @return
     */
    public Object getPageDatas(SysApp sysApp, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysApp> list = sysAppMapper.getPageAssocation(sysApp);

        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 新增
     * @param associationDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = {"org::app"},allEntries = true)
    public ResEntity insert(AssociationDTO associationDTO) {
        if (StringUtil.isEmpty(associationDTO.getAppName())) {
            return new ResEntity(false, "请输入医联体名称", null);
        }
        if (StringUtil.isEmpty(associationDTO.getAppId())) {
            return new ResEntity(false, "请输入医联体代码", null);
        }
        SysApp sysApp = new SysApp();
        sysApp.setAppName(associationDTO.getAppName());
        SysApp objectById = sysAppMapper.getObjectById(associationDTO.getAppId());

        if (null!=objectById){
            return  ResEntity.entity(false,"医疗机构ID重复",null);
        }
        sysApp.setAppId(associationDTO.getAppId());
        sysApp.setAppDesc(associationDTO.getAppDesc());
        sysApp.setCreateDate(new Date());
        sysApp.setCreateUser(AdminUtils.getCurrentHr().getCreateUser());
        sysApp.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
        sysApp.setStatus(associationDTO.getStatus());
        sysApp.setSort(associationDTO.getSort());
        sysApp.setPlatformCode("1");
        long rows = sysAppMapper.insert(sysApp);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, sysApp);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param associationDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::app","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity update(AssociationDTO associationDTO) {
        if (StringUtil.isEmpty(associationDTO.getAppName())) {
            return new ResEntity(false, "请输入医联体名称", null);
        }
        SysApp sysApp = new SysApp();
        sysApp.setAppName(associationDTO.getAppName());
        sysApp.setAppId(associationDTO.getAppId());
        sysApp.setAppDesc(associationDTO.getAppDesc());
        sysApp.setUpdateDate(new Date());
        sysApp.setUpdateUser(AdminUtils.getCurrentHr().getCreateUser());
        sysApp.setUpdateUserName(AdminUtils.getCurrentHr().getUsername());
        sysApp.setStatus(associationDTO.getStatus());
        sysApp.setSort(associationDTO.getSort());
        long rows = sysAppMapper.updateByPrimaryKey(sysApp);
        SysApp objectById = sysAppMapper.getObjectById(sysApp.getAppId());
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX,objectById);
        }
        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param appId
     * @return
     */
    public ResEntity findObj(String appId) {

        if (StringUtils.isBlank(appId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        SysApp sysApp = sysAppMapper.getObjectById(appId);
        return new ResEntity(true, Constant.SUCCESS_DX, sysApp);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::app","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity deleteLis(String ids) {
        SysApp sysApp = new SysApp();
        sysApp.setAppId(ids);
        sysApp.setStatus(Constant.BASIC_DEL_NO);
        List<SysInstitution> institution = sysInstitutionMapper.getInstitutionByAppId(ids);
        List<SysDepartment> departmentByAppId = sysDepartmentMapper.getDepartmentByAppId(ids);
        List<AdminInfo> adminInfoByAppId = adminInfoMapper.getAdminInfoByAppId(ids);
        //判断医疗机构下级信息是否删除完毕
        if (institution.size()!=0){
            return ResEntity.entity(false,"该医共体所属医疗机构未删除，无法删除该医共体",null);
        }
        if (departmentByAppId.size()!=0){
            return ResEntity.entity(false,"该医共体所属科室未删除，无法删除该医共体",null);
        }
        if (adminInfoByAppId.size()!=0){
            return ResEntity.entity(false,"该医共体所属人员信息，无法删除该医共体",null);
        }
        long rows = sysAppMapper.deleteByPrimaryKey(sysApp);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "删除失败，数据库异常", null);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity changeUpdate(SysApp sysApp) {
        long rows = sysAppMapper.updateByPrimaryKey(sysApp);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "启用/禁用失败，数据库异常", null);
    }


    /**
     * 下拉框加载医联体
     *
     * @return
     */
    public List<SysApp> getApplist() {
        SysApp sysApp = new SysApp();
        String appId = AdminUtils.getCurrentAppId();
        sysApp.setAppId(appId);
        if (Constant.BASIC_APP_ID.equals(appId)) {
            sysApp.setAppId("");
        }
        return sysAppMapper.getApplist(sysApp);
    }
}