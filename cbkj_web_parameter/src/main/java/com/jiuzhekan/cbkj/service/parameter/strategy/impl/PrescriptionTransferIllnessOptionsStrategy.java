package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.service.parameter.strategy.NormalParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service("PRESCRIPTION_TRANSFER_ILLNESS_OPTIONS")
public class PrescriptionTransferIllnessOptionsStrategy implements NormalParamStrategy {

    @Override
    public void getParam() {

    }

    @Override
    public void setParam(DiagnosisParamNewVO tSysParamVO,Object v) {
//c：转病证；0：不转 ；  [未缴费处方][就诊记录][历史处方][名医验案][专家经验共享]  -
        if (!StringUtils.isBlank(v.toString())) {
            String[] a = {"0", "0", "0", "0", "0"};
            String[] split = v.toString().split(",");
            for (String value : split) {
                int s = Integer.parseInt(value);
                a[s - 1] = "c";
            }
            tSysParamVO.setParValues(a[0] + a[1] + a[2] + a[3] + a[4]);
        } else {
            tSysParamVO.setParValues("00000");
        }
    }
}
