package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.common.TimeConverter;
import com.jiuzhekan.cbkj.service.parameter.strategy.NormalParamStrategy;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Service("SYS_CHECK_WORK_TIME")
public class CheckWorkTimeStrategy implements NormalParamStrategy {
    @Autowired
    CheckWorkTimeStrategy(TSysParamInitDescMapper tSysParamInitDescMapper){
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
    }


    @Override
    public void getParam() {

    }

    @Override
    public void setParam(DiagnosisParamNewVO tSysParamVO,Object v) {
        tSysParamVO.setParValues(TimeConverter.convertTime(tSysParamVO.getParValues()));
    }
}
