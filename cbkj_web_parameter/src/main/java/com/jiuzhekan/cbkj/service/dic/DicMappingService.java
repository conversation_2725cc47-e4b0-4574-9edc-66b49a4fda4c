package com.jiuzhekan.cbkj.service.dic;

import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.dic.TDicMapping;
import com.jiuzhekan.cbkj.beans.dic.TDicMappingVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.dic.DicMappingMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
/**
 * <AUTHOR>
 */
public class DicMappingService {


    private DicMappingMapper dicMappingMapper;

    @Autowired
    DicMappingService(DicMappingMapper dicMappingMapper) {
        this.dicMappingMapper = dicMappingMapper;
    }

    /**
     * 加载分页数据
     *
     * @param tDicMapping 字典映射表
     * @param page        分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TDicMapping tDicMapping, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDicMapping> list = dicMappingMapper.getPageListByObj(tDicMapping);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 字典映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TDicMapping tDicMapping = dicMappingMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDicMapping);
    }


    /**
     * 插入新数据
     *
     * @param tDicMappingVo 字典映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDicMappingVo tDicMappingVo) {

        List<TDicBase> tDicBases = tDicMappingVo.getTDicBases();
        for (TDicBase tDicBase : tDicBases) {

            TDicMapping tDicMapping = new TDicMapping();
            tDicMapping.setId(IDUtil.getID());
            tDicMapping.setDicId(tDicBase.getDicId());
            tDicMapping.setStanId(tDicMappingVo.getStanId());

            tDicMapping.setStanType(tDicMappingVo.getStanType());
            TDicMapping inMapping = dicMappingMapper.getInMapping(tDicMapping);
            if (null != inMapping) {
                return ResEntity.entity(false, "已映射", null);
            }
            dicMappingMapper.insert(tDicMapping);
        }
        return ResEntity.entity(true, "映射成功", null);
    }


    /**
     * 修改
     *
     * @param tDicMapping 字典映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDicMapping tDicMapping) {

        long rows = dicMappingMapper.updateByPrimaryKey(tDicMapping);

        return ResEntity.success(tDicMapping);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        String[] split = ids.split(",");
        try {
            for (String s : split) {
                dicMappingMapper.deletePrimaryKey(s);
            }
        } catch (Exception e) {
            return ResEntity.entity(false, "取消映射失败", null);
        }
        return ResEntity.success(true);
    }

}
