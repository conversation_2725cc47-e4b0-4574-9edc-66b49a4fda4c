//package com.jiuzhekan.cbkj.service.parameter.smartPrescrib;
//
//import cn.hutool.core.util.StrUtil;
//import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.beans.sysParam.*;
//import com.jiuzhekan.cbkj.common.utils.AdminUtils;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.IDUtil;
//import com.jiuzhekan.cbkj.mapper.sysMapper.AdminMenuMapper;
//import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
//import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamNewMapper;
//import net.sf.json.JSONArray;
//import net.sf.json.JSONObject;
//import org.apache.commons.lang.StringUtils;
//import org.apache.xerces.xs.StringList;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//
///**
// * 诊断
// *
// * <AUTHOR>
// */
//@Service
//public class ToolbarService {
//    private TSysParamNewMapper tSysParamNewMapper;
//    private TSysParamInitDescMapper tSysParamInitDescMapper;
//
//    private AdminMenuMapper adminMenuMapper;
//
//    @Autowired
//    public ToolbarService(TSysParamNewMapper tSysParamNewMapper,
//                          TSysParamInitDescMapper tSysParamInitDescMapper,
//                          AdminMenuMapper adminMenuMapper) {
//        this.tSysParamNewMapper = tSysParamNewMapper;
//        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
//        this.adminMenuMapper = adminMenuMapper;
//    }
//
//    /**
//     * 获取参数列表
//     *
//     * @param tSysParam
//     * @return
//     */
//    public ResEntity getParameterList(DiagnosisParamDTO tSysParam) {
//        DiagnosisParamDTO diagnosisParamDTO = new DiagnosisParamDTO();
//        diagnosisParamDTO.setMenuId(tSysParam.getMenuId());
//        if (StringUtils.isBlank(tSysParam.getAppId())) {
//            tSysParam.setAppId(Constant.BASIC_APP_ID);
//        }
//        if (StringUtils.isBlank(tSysParam.getInsCode())) {
//            tSysParam.setInsCode(Constant.BASIC_INS_CODE);
//        }
//        if (StringUtils.isBlank(tSysParam.getDeptId())) {
//            tSysParam.setDeptId(Constant.BASIC_DEPT_ID);
//        }
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("uid", AdminUtils.getCurrentHr().getUserId());
//        params.put("modualCode", Constant.BASIC_STRING_ONE);
//        params.put("parentMenuId", tSysParam.getMenuId());
//        params.put("menuTypes", "1,2");
//        //查看当前菜单id是否有子菜单，即第四级不显示菜单。
//        StringBuilder menuIds = new StringBuilder(tSysParam.getMenuId());
//        List<AdminMenu> menuByPid = adminMenuMapper.getMenuByPID(params);
//        if (menuByPid.size() > 0) {
//            menuIds = new StringBuilder();
//            for (AdminMenu adminMenu : menuByPid) {
//                menuIds.append(adminMenu.getMenuId()).append(",");
//            }
//            tSysParam.setMenuIds(menuIds.append(tSysParam.getMenuId()).toString());
//            tSysParam.setMenuId(null);
//            diagnosisParamDTO.setMenuIds(menuIds.append(tSysParam.getMenuId()).toString());
//            diagnosisParamDTO.setMenuId(null);
//        }
//        List<TSysParamNew> newT = new ArrayList<>();
//
//        List<TSysParamNew> obj = tSysParamNewMapper.getDiagnosisParameterList(tSysParam);
//
//        List<TSysParamNew> obj2 = tSysParamNewMapper.getDiagnosisParameterList(diagnosisParamDTO);
//        for (TSysParamNew tSysParamNew : obj2) {
//            String parCode = tSysParamNew.getParCode();
//            boolean pT = false;
//            for (TSysParamNew sysParamNew : obj) {
//                boolean equals = sysParamNew.getParCode().equals(parCode);
//                if (equals) {
//                    //存在了，不需要额外添加
//                    pT = true;
//                    break;
//                }
//            }
//            if (!pT) {
//                newT.add(tSysParamNew);
//            }
//        }
//
//        obj.addAll(newT);
//        JSONArray jsonArray = getJsonArray(obj);
//        return ResEntity.entity(true, Constant.SUCCESS_DX, jsonArray);
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity saveParameterList(DiagnosisParamNewVO diagnosisParamNewVO) {
//
//        Map<String, Object> paramValues = diagnosisParamNewVO.getData();
//        Set<String> paramIds = paramValues.keySet();
//
//        ArrayList<SaveParameter> saveParameters = new ArrayList<>();
//        for (String paramId : paramIds) {
//            if (!(paramId.contains("-1") || paramId.contains("-2"))) {
//                //单一选项处理
//                singleOptionProcessing(diagnosisParamNewVO, paramValues, paramId);
//            } else if (paramId.contains("-1") || paramId.contains("-2")) {
//
//                SaveParameter saveParameter = null;
//                String mainKey = paramId.substring(0, paramId.length() - 2);
//                for (SaveParameter saveParameter2 : saveParameters) {
//                    boolean equals = saveParameter2.getKey().equals(mainKey);
//                    if (equals) {
//                        saveParameter = saveParameter2;
//                        break;
//                    }
//                }
//                if (null == saveParameter) {
//                    saveParameter = new SaveParameter();
//                    saveParameters.add(saveParameter);
//                }
//                boolean a = paramId.endsWith("-1");
//                boolean b = paramId.endsWith("-2");
//                boolean c = paramId.endsWith("-3");
//                boolean d = paramId.endsWith("-4");
//
//                saveParameter.setKey(mainKey);
//                if (a) {
//                    saveParameter.setValue1((null == paramValues.get(paramId)) ? "" : paramValues.get(paramId).toString());
//                } else if (b) {
//                    saveParameter.setValue2((null == paramValues.get(paramId)) ? "" : paramValues.get(paramId).toString());
//                } else if (c) {
//                    saveParameter.setValue3((null == paramValues.get(paramId)) ? "" : paramValues.get(paramId).toString());
//                } else if (d) {
//                    saveParameter.setValue4((null == paramValues.get(paramId)) ? "" : paramValues.get(paramId).toString());
//                }
//            }
//        }
//        for (SaveParameter saveParameter : saveParameters) {
//            TSysParamNew tSysParamNew = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//            if (null == tSysParamNew) {
//                continue;
//            }
//            if ("INPATIENT_ADVICE_DISPLAY".equals(tSysParamNew.getParCode())) {
//                String value1 = saveParameter.getValue1();
//                String value2 = saveParameter.getValue2();
//                String parValues = tSysParamNew.getParValues();
//                JSONArray jsonArray = JSONArray.fromObject(parValues);
//
//                if (value1.length() > 2) {
//                    String[] split = value1.split(",");
//                    Object o = jsonArray.get(0);
//                    Object o1 = jsonArray.get(1);
//                    JSONObject jsonObject = JSONObject.fromObject(o);
//                    JSONObject jsonObject1 = JSONObject.fromObject(o1);
//                    jsonObject.put("value", split[0]);
//                    jsonObject1.put("value", split[1]);
//                    if (value2.equals("1")) {
//                        jsonObject.put("checked", true);
//                        jsonObject1.put("checked", false);
//                    } else {
//                        jsonObject.put("checked", false);
//                        jsonObject1.put("checked", true);
//                    }
//                    JSONArray keepNewValues = new JSONArray();
//                    keepNewValues.add(jsonObject);
//                    keepNewValues.add(jsonObject1);
//                    if (StringUtils.isBlank(diagnosisParamNewVO.getAppId()) && StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) && StringUtils.isBlank(diagnosisParamNewVO.getDeptId())) {
//                        //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                        //判断和原数据的医共体、机构、部门是否一致，不一致就新增
//                    } else if (tSysParamNew.getAppId().equals(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId()) &&
//                            tSysParamNew.getInsCode().equals(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode()) &&
//                            tSysParamNew.getDeptId().equals(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId())){
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    }
//                   /* if (isEmptyMedicalcommunity(diagnosisParamNewVO)) {
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    } */else {
//                        intNewp(diagnosisParamNewVO, saveParameter, tSysParamNew, jsonArray.toString());
//                    }
//                } else if (value1.equals("1") && value1.length() == 1) {
//                    Object o = jsonArray.get(0);
//                    Object o1 = jsonArray.get(1);
//                    JSONObject jsonObject = JSONObject.fromObject(o);
//                    JSONObject jsonObject1 = JSONObject.fromObject(o1);
//                    jsonObject.put("value", 1);
//                    jsonObject1.put("value", "");
//                    if (value2.equals("1")) {
//                        jsonObject.put("checked", true);
//                        jsonObject1.put("checked", false);
//                    } else {
//                        jsonObject.put("checked", false);
//                        jsonObject1.put("checked", true);
//                    }
//                    JSONArray keepNewValues = new JSONArray();
//                    keepNewValues.add(jsonObject);
//                    keepNewValues.add(jsonObject1);
//                    if (StringUtils.isBlank(diagnosisParamNewVO.getAppId()) && StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) && StringUtils.isBlank(diagnosisParamNewVO.getDeptId())) {
//                        //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                        //判断和原数据的医共体、机构、部门是否一致，不一致就新增
//                    } else if (tSysParamNew.getAppId().equals(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId()) &&
//                            tSysParamNew.getInsCode().equals(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode()) &&
//                            tSysParamNew.getDeptId().equals(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId())){
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    }
//                   /* if (isEmptyMedicalcommunity(diagnosisParamNewVO)) {
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    } */else {
//                        intNewp(diagnosisParamNewVO, saveParameter, tSysParamNew, jsonArray.toString());
//                    }
//                } else if (value1.equals("2") && value1.length() == 1) {
//                    Object o = jsonArray.get(0);
//                    Object o1 = jsonArray.get(1);
//                    JSONObject jsonObject = JSONObject.fromObject(o);
//                    JSONObject jsonObject1 = JSONObject.fromObject(o1);
//                    jsonObject.put("value", "");
//                    jsonObject1.put("value", "2");
//                    if (value2.equals("1")) {
//                        jsonObject.put("checked", true);
//                        jsonObject1.put("checked", false);
//                    } else {
//                        jsonObject.put("checked", false);
//                        jsonObject1.put("checked", true);
//                    }
//               /*     jsonArray.add(o);
//                    jsonArray.add(o1);*/
//                    JSONArray keepNewValues = new JSONArray();
//                    keepNewValues.add(jsonObject);
//                    keepNewValues.add(jsonObject1);
//                    if (StringUtils.isBlank(diagnosisParamNewVO.getAppId()) && StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) && StringUtils.isBlank(diagnosisParamNewVO.getDeptId())) {
//                        //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                        //判断和原数据的医共体、机构、部门是否一致，不一致就新增
//                    } else if (tSysParamNew.getAppId().equals(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId()) &&
//                            tSysParamNew.getInsCode().equals(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode()) &&
//                            tSysParamNew.getDeptId().equals(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId())){
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    }
//                   /* if (isEmptyMedicalcommunity(diagnosisParamNewVO)) {
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    }*/ else {
//                        intNewp(diagnosisParamNewVO, saveParameter, tSysParamNew, jsonArray.toString());
//                    }
//                } else if (value1.equals("") && value2.equals("")) {
//                    Object o = jsonArray.get(0);
//                    Object o1 = jsonArray.get(1);
//                    JSONObject jsonObject = JSONObject.fromObject(o);
//                    JSONObject jsonObject1 = JSONObject.fromObject(o1);
//                    jsonObject.put("value", "");
//                    jsonObject1.put("value", "");
//                    jsonObject.put("checked", false);
//                    jsonObject1.put("checked", false);
//                    JSONArray keepNewValues = new JSONArray();
//                    keepNewValues.add(jsonObject);
//                    keepNewValues.add(jsonObject1);
//                    if (StringUtils.isBlank(diagnosisParamNewVO.getAppId()) && StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) && StringUtils.isBlank(diagnosisParamNewVO.getDeptId())) {
//                        //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                        //判断和原数据的医共体、机构、部门是否一致，不一致就新增
//                    } else if (tSysParamNew.getAppId().equals(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId()) &&
//                            tSysParamNew.getInsCode().equals(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode()) &&
//                            tSysParamNew.getDeptId().equals(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId())){
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    }
//                   /* if (isEmptyMedicalcommunity(diagnosisParamNewVO)) {
//                        TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                        tSysParamNewValues.setParValues(keepNewValues.toString());
//                        tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    }*/ else {
//                        intNewp(diagnosisParamNewVO, saveParameter, tSysParamNew, jsonArray.toString());
//                    }
//                }
//            }
//            if ("PRESCRIPTION_SEARCH_RECIPE_OPTIONS".equals(tSysParamNew.getParCode())) {
//                String value1 = saveParameter.getValue1();
//                String value2 = saveParameter.getValue2();
//                String detalValues = value1.replace(",", "|");
//                String realValues = detalValues.replace(value2, value2 + ":" + "T");
//                if (StringUtils.isBlank(diagnosisParamNewVO.getAppId()) && StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) && StringUtils.isBlank(diagnosisParamNewVO.getDeptId())) {
//                    //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//                    TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                    tSysParamNewValues.setParValues(realValues.toString());
//                    tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                    //判断和原数据的医共体、机构、部门是否一致，不一致就新增
//                } else if (tSysParamNew.getAppId().equals(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId()) &&
//                        tSysParamNew.getInsCode().equals(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode()) &&
//                        tSysParamNew.getDeptId().equals(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId())){
//                    TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                    tSysParamNewValues.setParValues(realValues.toString());
//                    tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                }
//              /*  if (isEmptyMedicalcommunity(diagnosisParamNewVO)) {
//                    TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(saveParameter.getKey());
//                    tSysParamNewValues.setParValues(realValues);
//                    tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//                } */else {
//                    intNewp(diagnosisParamNewVO, saveParameter, tSysParamNew, realValues);
//                }
//            }
//        }
//        return ResEntity.entity(true, Constant.SUCCESS_DX, diagnosisParamNewVO);
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    void intNewp(DiagnosisParamNewVO diagnosisParamNewVO, SaveParameter saveParameter, TSysParamNew tSysParamNew, String realValues) {
//
//        TSysParamNew tSysParamNew2 = new TSysParamNew();
//        tSysParamNew2.setAppId(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId());
//        tSysParamNew2.setInsCode(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode());
//        tSysParamNew2.setDeptId(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId());
//        tSysParamNew2.setParCode(tSysParamNew.getParCode());
//        TSysParamNew tSysParamNew3 = tSysParamNewMapper.selectParamByCondition(tSysParamNew2);
//        if (null == tSysParamNew3) {
//            TSysParamNew tSysParamNewn = new TSysParamNew();
//            tSysParamNewn.setParId(IDUtil.getID());
//            tSysParamNewn.setAppId(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId());
//            tSysParamNewn.setInsCode(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode());
//            tSysParamNewn.setDeptId(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId());
//            tSysParamNewn.setCreateDate(new Date());
//            tSysParamNewn.setParCode(tSysParamNew.getParCode());
//            tSysParamNewn.setParValues(realValues);
//            tSysParamNewn.setIsGlobal(Constant.BASIC_STRING_ONE);
//            tSysParamNewn.setMenuId(tSysParamNew.getMenuId());
//            tSysParamNewn.setParamDesc(tSysParamNew.getParamDesc());
//            tSysParamNewn.setStatus(Constant.BASIC_STRING_ZERO);
//            tSysParamNewn.setParamType(tSysParamNew.getParamType());
//            tSysParamNewn.setParName(tSysParamNew.getParName());
//            tSysParamNewn.setSort(tSysParamNew.getSort());
//            tSysParamNewn.setParamInitValue(tSysParamNew.getParamInitValue());
//            tSysParamNewn.setCreateUser(AdminUtils.getCurrentHr().getUserId());
//            tSysParamNewn.setParNumber(tSysParamNew.getParNumber());
//            tSysParamNewn.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
//            tSysParamNewn.setParameterDiagram(tSysParamNew.getParameterDiagram());
//            tSysParamNewMapper.insert(tSysParamNewn);
//            //获取参数的选项集合
//            List<TSysParamInitDesc> tSysParamInitDescsList = tSysParamInitDescMapper.selectTSysParamInitDesc(saveParameter.getKey());
//            for (TSysParamInitDesc tSysParamInitDesc : tSysParamInitDescsList) {
//                TSysParamInitDesc tSysParamInitDesc1 = new TSysParamInitDesc();
//                tSysParamInitDesc1.setParamId(tSysParamNewn.getParId());
//                tSysParamInitDesc1.setParamInitName(tSysParamInitDesc.getParamInitName());
//                tSysParamInitDesc1.setParamInitCode(tSysParamInitDesc.getParamInitCode());
//                tSysParamInitDesc1.setOptionDiagram(tSysParamInitDesc.getOptionDiagram());
//                tSysParamInitDesc1.setSort(tSysParamInitDesc.getSort());
//                tSysParamInitDescMapper.insert(tSysParamInitDesc1);
//            }
//        } else {
//            //更新
//            tSysParamNew3.setParValues(diagnosisParamNewVO.getParValues());
//            tSysParamNewMapper.updateByPrimaryKey(tSysParamNew3);
//        }
//    }
//
//    /**
//     * @param diagnosisParamNewVO
//     * @param paramValues         参数值集合
//     * @param paramId
//     */
//    @Transactional(rollbackFor = Exception.class)
//    void singleOptionProcessing(DiagnosisParamNewVO diagnosisParamNewVO, Map<String, Object> paramValues, String paramId) {
//        //根据参数ID查询参数选项，用判断新增参数还是某个机构修改参数
//        TSysParamNew objectById = tSysParamNewMapper.getObjectById(paramId);
//        if (StringUtils.isBlank(diagnosisParamNewVO.getAppId()) && StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) && StringUtils.isBlank(diagnosisParamNewVO.getDeptId())) {
//            //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//            TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(paramId);
//            String value = paramValues.get(paramId).toString();
//            tSysParamNewValues.setParValues(value);
//            tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//            //判断和原数据的医共体、机构、部门是否一致，不一致就新增
//        } else if (objectById.getAppId().equals(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId()) &&
//                objectById.getInsCode().equals(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode()) &&
//                objectById.getDeptId().equals(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId())){
//            TSysParamNew tSysParamNewValues = tSysParamNewMapper.getObjectById(paramId);
//            String value = paramValues.get(paramId).toString();
//            tSysParamNewValues.setParValues(value);
//            tSysParamNewMapper.updateByPrimaryKey(tSysParamNewValues);
//        }else {
//            String value = paramValues.get(paramId).toString();
//            TSysParamNew tSysParamNew2 = new TSysParamNew();
//            tSysParamNew2.setAppId(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId());
//            tSysParamNew2.setInsCode(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode());
//            tSysParamNew2.setDeptId(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId());
//            TSysParamNew getParCode = tSysParamNewMapper.selectParam(paramId);
//            tSysParamNew2.setParCode(getParCode.getParCode());
//            TSysParamNew tSysParamNew3 = tSysParamNewMapper.selectParamByCondition(tSysParamNew2);
//            if (null == tSysParamNew3) {
//                tSysParamNew3 = new TSysParamNew();
//                tSysParamNew3.setParId(IDUtil.getID());
//                tSysParamNew3.setAppId(StringUtils.isBlank(diagnosisParamNewVO.getAppId()) ? Constant.BASIC_APP_ID : diagnosisParamNewVO.getAppId());
//                tSysParamNew3.setInsCode(StringUtils.isBlank(diagnosisParamNewVO.getInsCode()) ? Constant.BASIC_INS_CODE : diagnosisParamNewVO.getInsCode());
//                tSysParamNew3.setDeptId(StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ? Constant.BASIC_DEPT_ID : diagnosisParamNewVO.getDeptId());
//                tSysParamNew3.setParCode(getParCode.getParCode());
//                tSysParamNew3.setCreateDate(new Date());
//                tSysParamNew3.setSort(getParCode.getSort());
//                tSysParamNew3.setParNumber(getParCode.getParNumber());
//                tSysParamNew3.setParName(getParCode.getParName());
//                tSysParamNew3.setParamDesc(getParCode.getParamDesc());
//                String paramType = getParCode.getParamType();
//                tSysParamNew3.setParamType(paramType);
//                tSysParamNew3.setParValues(value);
//                tSysParamNew3.setIsGlobal(Constant.BASIC_STRING_ONE);
//                tSysParamNew3.setMenuId(diagnosisParamNewVO.getMenuId());
//                tSysParamNew3.setParamDesc(getParCode.getParamDesc());
//                tSysParamNew3.setParNumber(getParCode.getParNumber());
//                tSysParamNew3.setStatus(Constant.BASIC_STRING_ZERO);
//                tSysParamNew3.setParamInitValue(getParCode.getParamInitValue());
//                tSysParamNew3.setCreateUser(AdminUtils.getCurrentHr().getUserId());
//                tSysParamNew3.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
//                tSysParamNewMapper.insert(tSysParamNew3);
//                List<TSysParamInitDesc> tSysParamInitDescsList = tSysParamInitDescMapper.selectTSysParamInitDesc(paramId);
//                for (TSysParamInitDesc tSysParamInitDesc : tSysParamInitDescsList) {
//                    TSysParamInitDesc tSysParamInitDesc1 = new TSysParamInitDesc();
//                    tSysParamInitDesc1.setParamId(tSysParamNew3.getParId());
//                    tSysParamInitDesc1.setParamInitName(tSysParamInitDesc.getParamInitName());
//                    tSysParamInitDesc1.setParamInitCode(tSysParamInitDesc.getParamInitCode());
//                    tSysParamInitDesc1.setOptionDiagram(tSysParamInitDesc.getOptionDiagram());
//                    tSysParamInitDesc1.setSort(tSysParamInitDesc.getSort());
//                    tSysParamInitDescMapper.insert(tSysParamInitDesc1);
//                }
//            } else {
//                //更新
//                tSysParamNew3.setParValues(diagnosisParamNewVO.getParValues());
//                tSysParamNewMapper.updateByPrimaryKey(tSysParamNew3);
//            }
//            //获取参数的选项集合
//        }
//    }
//
//    public JSONArray getJsonArray(List<TSysParamNew> obj) {
//        JSONObject jsonObject;
//        JSONObject pjsonObject;
//        JSONObject ppjsonObject = null;
//
//        //参数相同的第四级 菜单下 的 参数存放处
//        JSONArray ppChildren = new JSONArray();
//        //最外层数组
//        // JSONArray pppChildren = new JSONArray();
//        for (TSysParamNew tSysParam1 : obj) {
//            //参数存放处
//            JSONArray pChildren = new JSONArray();
//            //参数所属菜单是不显示菜单
//            JSONArray itemArray = new JSONArray();
//            String menuType = tSysParam1.getMenuType();
//            boolean isContains = false;
//            if (null == ppjsonObject) {
//                ppjsonObject = new JSONObject();
//            }
//            //判断下这个第四季菜单的菜单名是否存在了
//            if (Constant.BASIC_STRING_TWO.equals(menuType)) {
//                //存在不显示菜单，并且当前菜单名字没被添加到数组中。
//                for (Object ppChild : ppChildren) {
//                    JSONObject a = (JSONObject) ppChild;
//                    boolean b = a.containsValue(tSysParam1.getMenuName());
//                    if (b) {
//                        //存在了，覆盖 ppjsonObject。
//                        isContains = true;
//                        ppjsonObject = a;
//                        break;
//                    }
//                }
//                if (!isContains) {
//                    ppjsonObject = new JSONObject();
//                    ppjsonObject.put("menuName", tSysParam1.getMenuName());
//                    ppjsonObject.put("menuId", tSysParam1.getMenuId());
//                }
//            } else {
//                //虽然不是不显示菜单，但是需要构建一个虚拟的。
//                for (Object ppChild : ppChildren) {
//
//                    boolean b = ((JSONObject) ppChild).containsValue(" ");
//                    if (b) {
//                        //存在了，覆盖 ppjsonObject。
//                        isContains = true;
//                        ppjsonObject = (JSONObject) ppChild;
//                        break;
//                    }
//                }
//                if (!isContains) {
//                    ppjsonObject = new JSONObject();
//                    ppjsonObject.put("menuName", " ");
//                    ppjsonObject.put("menuId", " ");
//                }
//            }
//
//            jsonObject = new JSONObject();
//            pjsonObject = new JSONObject();
//            pjsonObject.put("parId", tSysParam1.getParId());
//            pjsonObject.put("parNumber", tSysParam1.getParNumber());
//            pjsonObject.put("parName", tSysParam1.getParName());
//            jsonObject.put("parId", tSysParam1.getParId() + "-1");
//            jsonObject.put("parName", "列表");
//            jsonObject.put("paramType", tSysParam1.getParamType());
//            jsonObject.put("sort", tSysParam1.getSort());
//            jsonObject.put("isGlobal", tSysParam1.getIsGlobal());
//            jsonObject.put("parValues", tSysParam1.getParValues());
//            //系统默认初始值
//            jsonObject.put("initValue", tSysParam1.getParamInitValue());
//            jsonObject.put("parameterDiagramURL", StringUtils.isBlank(tSysParam1.getParameterDiagram()) ? " " : tSysParam1.getParameterDiagram());
//            //jsonObject.put("paramDesc", tSysParam1.getParamDesc());
//            //选中的值
//            //获取参数代码选项
//            String paramType = tSysParam1.getParamType();
//            //先处理可以通用的处理
//            if (Constant.BASIC_STRING_ONE.equals(paramType) ||
//                    Constant.BASIC_STRING_TWO.equals(paramType) ||
//                    Constant.BASIC_STRING_THREE.equals(paramType) ||
//                    Constant.BASIC_STRING_FIVE.equals(paramType) ||
//                    "21".equals(paramType)
//            ) {
//                addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 0);
//
//            } else if (Constant.BASIC_STRING_FOUR.equals(paramType)) {
//                pjsonObject.put("children", itemArray);
//                pChildren.add(pjsonObject);
//                if (!ppjsonObject.containsKey("params")) {
//                    ppjsonObject.put("params", pChildren);
//                    ppChildren.add(ppjsonObject);
//                } else {
//                    JSONArray ddd = (JSONArray) ppjsonObject.get("params");
//                    ddd.add(pjsonObject);
//                }
//            } else if ("31".equals(paramType) && !("INPATIENT_ADVICE_DISPLAY".equals(tSysParam1.getParCode()))) {
//                pjsonObject.put("children", itemArray);
//                pChildren.add(pjsonObject);
//                if (!ppjsonObject.containsKey("params")) {
//                    ppjsonObject.put("params", pChildren);
//                    ppChildren.add(ppjsonObject);
//                } else {
//                    JSONArray ddd = (JSONArray) ppjsonObject.get("params");
//                    ddd.add(pjsonObject);
//                }
//            } else if ("41".equals(paramType) && !("PRESCRIPTION_SEARCH_RECIPE_OPTIONS".equals(tSysParam1.getParCode()))) {
//                addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 0);
//            } else {
//                //组合类型
//                //代码
//                //搜索(协定方|方剂|配方)处方配置
//                if ("PRESCRIPTION_SEARCH_RECIPE_OPTIONS".equals(tSysParam1.getParCode())) {
//                    pjsonObject.put("paramType", tSysParam1.getParamType());
//                    jsonObject.remove("initValue");
//                    jsonObject.remove("parValues");
//                    jsonObject.remove("paramType");
//                    jsonObject.remove("parName", "列表");
//                    jsonObject.remove("sort");
//                    String parValues = tSysParam1.getParValues();
//                    String replace = parValues.replace(":T", "");
//                    String realValues = replace.replace("|", ",");
//                    jsonObject.put("parValues", realValues);
//                    jsonObject.put("paramType", "4");
//                    addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 1);
//                }
//                //住院医嘱显示项
//                else if ("INPATIENT_ADVICE_DISPLAY".equals(tSysParam1.getParCode())) {
//                    pjsonObject.put("paramType", tSysParam1.getParamType());
//                    jsonObject.remove("initValue");
//                    jsonObject.remove("parValues");
//                    jsonObject.remove("paramType");
//                    jsonObject.remove("parName", "列表");
//                    jsonObject.remove("sort");
//                    if (tSysParam1.getParValues().equals("[]")) {
//                        jsonObject.put("parValues", "");
//                    } else {
//                        String parValues = tSysParam1.getParValues();
//                        JSONArray jsonArrayParValues = JSONArray.fromObject(parValues);
//                        String temp="";
//                        for (Object jsonArrayParValue : jsonArrayParValues) {
//                            String value = JSONObject.fromObject(jsonArrayParValue).get("value").toString();
//                            temp=temp+value+",";
//                        }
//                        //String values1 = jsonArrayParValues.getJSONObject(1).get("value").toString();
//                        //String total = values + "," + values1;
//                        jsonObject.put("parValues", temp);
//                    }
//                    jsonObject.put("paramType", "3");
//                    addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 1);
//                    /*jsonObject.put("paramType", "3");
//                    addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 1);*/
//
//                }
//                //外用中药方显示项目（拆成两个参数）
//                else if (
//                        "PRESCRIPTION_EXTERNAL_COLUMN".equals(tSysParam1.getParCode()) ||
//                                "PRESCRIPTION_INTERNAL_COLUMN".equals(tSysParam1.getParCode()) ||
//                                "PRESCRIPTION_PATENT_COLUMN".equals(tSysParam1.getParCode()) ||
//                                "PRESCRIPTION_PREPARATION_COLUMN".equals(tSysParam1.getParCode()) ||
//                                "PRESCRIPTION_ACUPOINT_COLUMN".equals(tSysParam1.getParCode())
//                ) {
//                    String defaultT = Constant.BASIC_STRING_THREE;
//                    if ("PRESCRIPTION_ACUPOINT_COLUMN".equals(tSysParam1.getParCode())) {
//                        defaultT = Constant.BASIC_STRING_TWO;
//                    }
//                    jsonObject.remove("parValues");
//                    //1,2,3|3|4|8|9|10,4,5,6,7,13,8,9,10
//                    StringBuilder parValues = new StringBuilder("");
//                    StringBuilder parValuesT = new StringBuilder("");
//                    String[] split = tSysParam1.getParValues().split(",");
//                    for (String s : split) {
//                        String s1 = s.split("\\|")[0];
//                        parValues.append(s1).append(",");
//                        //只有3 有弹窗
//                        if (defaultT.equals(s1)) {
//                            String[] split1 = s.split("\\|");
//                            for (int i = 1; i < split1.length; i++) {
//                                parValuesT.append(split1[i]).append(",");
//                            }
//                        }
//                    }
//                    String s = parValues.toString();
//                    jsonObject.remove("parValues");
//                    jsonObject.put("parValues", s.substring(0, s.length() - 1));
//                    jsonObject.remove("paramType");
//                    jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
//
//                    JSONObject jsonObject1 = new JSONObject();
//                    jsonObject1.putAll(jsonObject);
//                    jsonObject1.remove("parName");
//                    jsonObject1.put("parName", "弹窗");
//                    jsonObject1.remove("paramType");
//                    jsonObject1.remove("parValues");
//                    String sT = parValuesT.toString();
//                    jsonObject1.put("parValues", sT.length() > 1 ? sT.substring(0, sT.length() - 1) : sT);
//                    jsonObject1.put("paramType", tSysParam1.getParamType().split(",")[1]);
//                    jsonObject1.put("parId", tSysParam1.getParId() + "-2");
//                    TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
//                    tSysParamInitDesc.setParamId(tSysParam1.getParId());
//                    List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
//                    //JSONObject j2 = new JSONObject();
//                    JSONArray jsonArray = new JSONArray();
//                    JSONObject j = new JSONObject();
//                    for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
//                        j.put("label", tSysParamInitDesc1.getParamInitName());
//                        j.put("value", tSysParamInitDesc1.getParamInitCode());
//                        j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
//                        jsonArray.add(j);
//                    }
//                    jsonObject1.put("paramInitDesc", jsonArray);
//                    itemArray.add(jsonObject1);
//                    addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 1);
//                    //内服中草药方处方金额限制最高价格 类型是5+5
//                } else if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode())) {
//                    String[] split = tSysParam1.getParValues().split("\\|");
//
//                    jsonObject.remove("paramType");
//                    jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
//                    jsonObject.remove("parName");
//                    jsonObject.put("parName", "选项");
//                    jsonObject.remove("parValues");
//                    jsonObject.put("parValues", split[0]);
//                    JSONObject jsonObject1 = new JSONObject();
//                    jsonObject1.putAll(jsonObject);
//                    jsonObject1.remove("paramType");
//                    jsonObject1.put("parValues", split[1]);
//                    jsonObject1.put("paramType", tSysParam1.getParamType().split(",")[1]);
//                    TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
//                    tSysParamInitDesc.setParamId(tSysParam1.getParId());
//                    List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
//                    //JSONObject j2 = new JSONObject();
//                    JSONArray jsonArray = new JSONArray();
//                    JSONObject j = new JSONObject();
//                    // for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
//                    j.put("label", pageListByObj.get(1).getParamInitName());
//                    j.put("value", pageListByObj.get(1).getParamInitCode());
//                    j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(1).getOptionDiagram()) ? " " : pageListByObj.get(1).getOptionDiagram());
//                    jsonArray.add(j);
//                    //}
//                    jsonObject1.put("parId", tSysParam1.getParId() + "-2");
//                    jsonObject1.put("paramInitDesc", jsonArray);
//                    itemArray.add(jsonObject1);
//                    addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 1);
//
//                } else {
//                    addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, 0);
//                }
//
//            }
//
//        }
//        return ppChildren;
//    }
//
//    public void addParams(TSysParamNew tSysParam1, JSONObject jsonObject, JSONArray itemArray,
//                          JSONObject pjsonObject, JSONArray pChildren, JSONObject ppjsonObject, JSONArray ppChildren, int isMoreParameters) {
//        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
//        tSysParamInitDesc.setParamId(tSysParam1.getParId());
//        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
//        //JSONObject j2 = new JSONObject();
//        JSONArray jsonArray = new JSONArray();
//        JSONObject j = new JSONObject();
//
//        JSONObject jsonObject1 = new JSONObject();
//        //
//        if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode())) {
//            j.put("label", pageListByObj.get(0).getParamInitName());
//            j.put("value", pageListByObj.get(0).getParamInitCode());
//            j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
//            jsonArray.add(j);
//            //住院医嘱显示项
//        } else if ("INPATIENT_ADVICE_DISPLAY".equals(tSysParam1.getParCode())) {
//            jsonObject1.put("parId", tSysParam1.getParId() + "-2");
//            if ("[]".equals(tSysParam1.getParValues())) {
//                jsonObject1.put("parValues", "");
//            } else {
//                String parValues = tSysParam1.getParValues();
//                JSONArray jsonArrayParValues = JSONArray.fromObject(parValues);
//
//                for (Object jsonArrayParValue : jsonArrayParValues) {
//                    Boolean checked = (Boolean) JSONObject.fromObject(jsonArrayParValue).get("checked");
//                    if(checked){
//                        jsonObject1.put("parValues",JSONObject.fromObject(jsonArrayParValue).get("value"));
//                    }
//                }
//            /*    String checked = jsonArrayParValues.getJSONObject(0).get("checked").toString();
//                if ("true".equals(checked)){
//                    jsonObject1.put("parValues", "1");
//                }else {
//                    jsonObject1.put("parValues", "2");
//                }*/
//
//            }
//            jsonObject1.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
//            jsonObject1.put("paramType", "21");
//
//            for (TSysParamInitDesc sysParamInitDesc : pageListByObj) {
//                j.put("label", sysParamInitDesc.getParamInitName());
//                j.put("value", sysParamInitDesc.getParamInitCode());
//                jsonArray.add(j);
//            }
//            jsonObject1.put("paramInitDesc", jsonArray);
//        } else if ("PRESCRIPTION_SEARCH_RECIPE_OPTIONS".equals(tSysParam1.getParCode())) {
//            jsonObject1.put("parId", tSysParam1.getParId() + "-2");
//            String parValues = tSysParam1.getParValues();
//            String replace = parValues.replace("|", ",");
//            String[] split = replace.split(",");
//            String s1 = null;
//            for (String s : split) {
//                if (s.contains(":T")) {
//                    s1 = s;
//                }
//            }
//            String replace1 = s1.replace(":T", "");
//            jsonObject1.put("parValues", replace1);
//            jsonObject1.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
//            jsonObject1.put("paramType", "21");
//            for (TSysParamInitDesc sysParamInitDesc : pageListByObj) {
//
//                j.put("label", sysParamInitDesc.getParamInitName());
//                j.put("value", sysParamInitDesc.getParamInitCode());
//                jsonArray.add(j);
//            }
//            jsonObject1.put("paramInitDesc", jsonArray);
//
//        } else {
//            for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
//                j.put("label", tSysParamInitDesc1.getParamInitName());
//                j.put("value", tSysParamInitDesc1.getParamInitCode());
//                j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
//                jsonArray.add(j);
//            }
//        }
//        jsonObject.put("paramInitDesc", jsonArray);
//        if (isMoreParameters > 0) {
//            //选项图标
//            //jsonObject.put("optionDiagramURL", j2);
//            itemArray.add(0, jsonObject);
//            if ("INPATIENT_ADVICE_DISPLAY".equals(tSysParam1.getParCode())) {
//                itemArray.add(1, jsonObject1);
//            }
//            if ("PRESCRIPTION_SEARCH_RECIPE_OPTIONS".equals(tSysParam1.getParCode())) {
//                itemArray.add(1, jsonObject1);
//            }
//            //children 一个参数参数下多个 的话 就继续：itemArray.add(jsonObject);
//            pjsonObject.put("children", itemArray);
//        } else {
//            jsonObject.remove("parId");
//            jsonObject.remove("parName");
//            pjsonObject.putAll(jsonObject);
//        }
//        pChildren.add(pjsonObject);
//        if (!ppjsonObject.containsKey("params")) {
//            ppjsonObject.put("params", pChildren);
//            ppChildren.add(ppjsonObject);
//        } else {
//            JSONArray ddd = (JSONArray) ppjsonObject.get("params");
//            ddd.add(pjsonObject);
//        }
//    }
//
//
//    /**
//     * 判断医共体ID、医疗机构代码、科室id是否都为空
//     *
//     * @param diagnosisParamNewVO
//     * @return
//     */
//    private boolean isEmptyMedicalcommunity(DiagnosisParamNewVO diagnosisParamNewVO) {
//        return StringUtils.isBlank(diagnosisParamNewVO.getAppId())
//                && StringUtils.isBlank(diagnosisParamNewVO.getInsCode())
//                && StringUtils.isBlank(diagnosisParamNewVO.getDeptId()) ||
//                (Constant.BASIC_APP_ID.equals(diagnosisParamNewVO.getAppId())
//                        && Constant.BASIC_INS_CODE.equals(diagnosisParamNewVO.getInsCode()) &&
//                        Constant.BASIC_DEPT_ID.equals(diagnosisParamNewVO.getDeptId()));
//    }
//
//    /**
//     * 判断参数类型
//     *
//     * @param diagnosisParamNewVO
//     * @return
//     */
//    private boolean isSimpleParameter(DiagnosisParamNewVO diagnosisParamNewVO) {
//        return Constant.BASIC_STRING_ONE.equals(diagnosisParamNewVO.getParamType()) ||
//                Constant.BASIC_STRING_TWO.equals(diagnosisParamNewVO.getParamType()) ||
//                Constant.BASIC_STRING_THREE.equals(diagnosisParamNewVO.getParamType());
//    }
//
//    /*TSysParamNew tSysParamNew = tSysParamNewMapper.getObjectById(tSysParamVO.getParId());
//        if (StringUtils.isBlank(tSysParamVO.getAppId()) && StringUtils.isBlank(tSysParamVO.getInsCode()) && StringUtils.isBlank(tSysParamVO.getDeptId())) {
//            //都为空，说明使用默认值 000000，直接修改 tSysParamNew
//            if (Constant.BASIC_STRING_ONE.equals(tSysParamVO.getParamType()) || Constant.BASIC_STRING_TWO.equals(tSysParamVO.getParamType())) {
//                tSysParamNew.setParValues(tSysParamVO.getParValues());
//                tSysParamNewMapper.updateByPrimaryKey(tSysParamNew);
//                return ResEntity.entity(true, Constant.SUCCESS_DX, tSysParamNew);
//            }
//        } else {
//            //不为空，则判断原数据是否是 000000，不是则直接修改，是则需要新插入一条数据，因为需要保留原始的默认值。
//            if (Constant.BASIC_APP_ID.equals(tSysParamNew.getAppId()) &&
//                    Constant.BASIC_INS_CODE.equals(tSysParamNew.getInsCode()) &&
//                    Constant.BASIC_DEPT_ID.equals(tSysParamNew.getDeptId())) {
//                if (Constant.BASIC_STRING_ONE.equals(tSysParamVO.getParamType()) ||
//                        Constant.BASIC_STRING_TWO.equals(tSysParamVO.getParamType())) {
//                    TSysParamNew tSysParamNew1 = new TSysParamNew();
//                    tSysParamNew1.setParId(IDUtil.getID());
//                    tSysParamNew1.setAppId(tSysParamVO.getAppId());
//                    tSysParamNew1.setInsCode(tSysParamVO.getInsCode());
//                    tSysParamNew1.setDeptId(tSysParamVO.getDeptId());
//                    tSysParamNew1.setCreateDate(new Date());
//                    tSysParamNew1.setParValues(tSysParamVO.getParValues());
//                    tSysParamNew1.setIsGlobal(Constant.BASIC_STRING_ONE);
//                    tSysParamNew1.setMenuId(tSysParamNew.getMenuId());
//                    tSysParamNew1.setParamDesc(tSysParamNew.getParamDesc());
//                    tSysParamNew1.setStatus(Constant.BASIC_STRING_ONE);
//                    tSysParamNew1.setSort(tSysParamNew.getSort() + 1);
//                    tSysParamNew1.setParamInitValue(tSysParamNew.getParamInitValue());
//                    tSysParamNew1.setCreateUser(AdminUtils.getCurrentHr().getUserId());
//                    tSysParamNew1.setCreateUsername(AdminUtils.getCurrentHr().getUsername());
//                    tSysParamNew1.setParameterDiagram("");
//                    tSysParamNew.setOptionDiagram("");
//                    tSysParamNewMapper.insert(tSysParamNew1);
//                    return ResEntity.entity(true, Constant.SUCCESS_DX, tSysParamNew1);
//                }
//            } else {
//                if (Constant.BASIC_STRING_ONE.equals(tSysParamVO.getParamType()) || Constant.BASIC_STRING_TWO.equals(tSysParamVO.getParamType())) {
//                    tSysParamNew.setParValues(tSysParamVO.getParValues());
//                    tSysParamNewMapper.updateByPrimaryKey(tSysParamNew);
//                }
//            }
//        }*/
//
//}
