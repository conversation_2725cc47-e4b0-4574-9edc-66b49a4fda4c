package com.jiuzhekan.cbkj.service.sysService;

import com.jiuzhekan.cbkj.beans.sysBeans.*;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminInfoMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminMenuMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminRuleMapper;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysModualMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AdminRuleService {


    private final AdminRuleMapper adminRuleMapper;


    private final RedisService redisService;


    private final AdminMenuMapper adminMenuMapper;


    private final AdminMenuService adminMenuService;


    private final AdminInfoMapper adminInfoMapper;


    private final SysModualMapper sysModualMapper;

    @Autowired
    AdminRuleService(AdminRuleMapper adminRuleMapper, RedisService redisService,
                     AdminMenuMapper adminMenuMapper, AdminMenuService adminMenuService,
                     AdminInfoMapper adminInfoMapper, SysModualMapper sysModualMapper) {
        this.adminRuleMapper = adminRuleMapper;
        this.redisService = redisService;
        this.adminMenuMapper = adminMenuMapper;
        this.adminInfoMapper = adminInfoMapper;
        this.adminMenuService = adminMenuService;
        this.sysModualMapper = sysModualMapper;
    }

    /**
     * 分页查询角色
     *
     * @param keyWord
     * @param page
     * @return
     */
    public Object getPageDatas(String keyWord, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AdminRule> allAdminRulesByKeyWord = adminRuleMapper.getAllAdminRulesByKeyWord(keyWord);
        return Page.getLayUiTablePageData(allAdminRulesByKeyWord);
    }

    /**
     * 新增角色
     *
     * @param adminRuleVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(AdminRuleVo adminRuleVo) {

        if(StringUtils.isBlank(adminRuleVo.getRoleName())){
            return new ResEntity(false, "缺少参数！", null);
        }
        if(StringUtils.isBlank(adminRuleVo.getRnameZh())){
            return new ResEntity(false, "缺少参数！", null);
        }
        //判断新增的角色名是否重复
        AdminRule adminRule = new AdminRule();
        adminRule.setRoleName(adminRuleVo.getRoleName());
        adminRule.setRnameZh(adminRuleVo.getRnameZh());
        List<AdminRule> repeatNameS = adminRuleMapper.getRepeatNameS(adminRule);
        if (null != repeatNameS && !repeatNameS.isEmpty()) {
            return ResEntity.entity(false, "角色名称重复", null);
        }
        adminRule.setRoleId(IDUtil.getID());
        adminRule.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        adminRule.setRoleDesc(adminRuleVo.getRoleDesc());
        adminRule.setCreateDate(new Date());
        long rows = adminRuleMapper.insert(adminRule);
        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, rows);
        }
        return new ResEntity(false, "数据库异常", null);
    }

    /**
     * 获取某个角色
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {
        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "缺少参数！", null);
        }
        Object obj = adminRuleMapper.selectByPrimaryKey(id);
        return new ResEntity(true, Constant.SUCCESS_DX, obj);
    }

    /**
     * 修改角色
     *
     * @param adminRuleVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(AdminRuleVo adminRuleVo) {
        if (StringUtils.isBlank(adminRuleVo.getRoleId())) {
            return new ResEntity(false, "缺少参数！", null);
        }
        if(StringUtils.isBlank(adminRuleVo.getRoleId())){
            return new ResEntity(false, "缺少参数！", null);
        }
        if(StringUtils.isBlank(adminRuleVo.getRnameZh())){
            return new ResEntity(false, "缺少参数！", null);
        }
        AdminRule adminRule = new AdminRule();
        adminRule.setRoleId(adminRuleVo.getRoleId());
        adminRule.setRoleName(adminRuleVo.getRoleName());
        adminRule.setRnameZh(adminRuleVo.getRnameZh());
        adminRule.setRoleDesc(adminRuleVo.getRoleDesc());
        //判断有没有重复的名称
        List<AdminRule> repeatNames = adminRuleMapper.getRepeatNameById(adminRuleVo.getRoleId());
        for (AdminRule repeatName : repeatNames) {
            if (repeatName.getRoleName().equals(adminRule.getRoleName())||repeatName.getRnameZh().equals(adminRule.getRnameZh())) {
                return ResEntity.entity(false, "修改失败名称重复", null);
            }
        }
        long rows = adminRuleMapper.updateByPrimaryKey(adminRule);
        AdminRule adminRuleById = adminRuleMapper.getAdminRuleById(adminRuleVo.getRoleId());
        return new ResEntity(true, Constant.SUCCESS_DX, adminRuleById);
    }

    /**
     * 删除角色
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) throws Exception {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "缺少参数！", null);
        }
        //获取角色的用户数量
        long rows = adminRuleMapper.getRuleRelesCount(ids);
        if (rows > 0) {
            return new ResEntity(false, "当前角色正在被使用，不能删除", null);
        } else {
            long rowA = adminRuleMapper.deleteByPrimaryKey(ids);
            //删除角色下配置的用户
            long rowB = adminRuleMapper.deleteRuleMenuByRid(ids);
            //删除角色下配置的菜单
            long rowC = adminRuleMapper.deleteRuleMenuByRids(ids);
            return new ResEntity(true, Constant.SUCCESS_DX, rowA);
        }
    }

    public ResEntity getSystemMenu(String modelCode) {
        //查询不同系统的菜单
        if (StringUtils.isBlank(modelCode)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        List<AdminMenu> allMenuByModelCode = adminMenuMapper.getAllMenuByModelCode(modelCode);

        List<AdminMenu> menuList1 = new ArrayList<AdminMenu>();
        String superMenuNum = "";
        if (Constant.BASIC_STRING_TWO.equals(modelCode)) {
            //组装云系统菜单树
            superMenuNum = "00020";
            assemblyMenu(allMenuByModelCode, menuList1, superMenuNum);
        } else if (Constant.BASIC_STRING_ONE.equals(modelCode)) {
            //组装配置平台菜单树
            superMenuNum = "0";
            assemblyMenu(allMenuByModelCode, menuList1, superMenuNum);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, menuList1);
    }

    /**
     * 组装菜单
     *
     * @param allMenuByModelCode
     * @param menuList1
     * @param superMenuNum
     */
    private void assemblyMenu(List<AdminMenu> allMenuByModelCode, List<AdminMenu> menuList1, String superMenuNum) {
        if (allMenuByModelCode != null && !allMenuByModelCode.isEmpty()) {
            //大菜单
            for (AdminMenu menu : allMenuByModelCode) {
                if (superMenuNum.equals(menu.getParentMenuId())) {
                    menuList1.add(menu);
                }
            }
            //二级菜单
            if (!menuList1.isEmpty()) {
                for (AdminMenu menu1 : menuList1) {
                    List<AdminMenu> menuList2 = new ArrayList<AdminMenu>();
                    for (AdminMenu menu : allMenuByModelCode) {
                        if (menu1.getMenuId().equals(menu.getParentMenuId())) {
                            menuList2.add(menu);
                        }
                    }
                    //按钮
                    if (!menuList2.isEmpty()) {
                        for (AdminMenu menu2 : menuList2) {
                            List<AdminMenu> menuList3 = new ArrayList<AdminMenu>();
                            for (AdminMenu menu : allMenuByModelCode) {
                                if (menu2.getMenuId().equals(menu.getParentMenuId())) {
                                    menuList3.add(menu);
                                }
                            }
                            if (!menuList3.isEmpty()) {
                                menu2.setChildList(menuList3);
                            }
                            if (!menuList3.isEmpty()){
                                for (AdminMenu menu4 : menuList3) {
                                    List<AdminMenu> menuList4 = new ArrayList<AdminMenu>();
                                    for (AdminMenu menu : allMenuByModelCode) {
                                        if (menu4.getMenuId().equals(menu.getParentMenuId())) {
                                            menuList4.add(menu);
                                        }
                                    }
                                    if (!menuList4.isEmpty()) {
                                        menu4.setChildList(menuList4);
                                    }
                                }
                            }

                        }
                        menu1.setChildList(menuList2);
                    }
                }
            }
        }
    }

    /**
     * 获取角色保存菜单信息
     *
     * @param roleId
     * @return
     */
    public ResEntity getAdminRuleMenu(String roleId) {
        if (StringUtils.isBlank(roleId)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        //返回所有平台，平台菜单保存的数据
        List<SysAdminRuleMenu> allAdminRuleMenuById = adminRuleMapper.getAllAdminRuleMenuByRoleId(roleId);
        List<SysModual> allSysModualList = sysModualMapper.getAllSysModual();
        ArrayList<AdminRuleMenuVo> resultList = new ArrayList<>(1000);
        //获取平台数据
        for (SysModual sysModual : allSysModualList) {
            AdminRuleMenuVo adminRuleMenuVo = new AdminRuleMenuVo();
            adminRuleMenuVo.setModualCode(sysModual.getModualCode());
            adminRuleMenuVo.setModualName(sysModual.getModualName());
            resultList.add(adminRuleMenuVo);
        }
        //保存不同平台的菜单数据
        for (AdminRuleMenuVo adminRuleMenuVo : resultList) {
            List<String> menuData = new ArrayList<>(500);
            for (SysAdminRuleMenu sysAdminRuleMenu : allAdminRuleMenuById) {
                AdminMenu adminMenu = adminMenuMapper.selectByPrimaryKey(sysAdminRuleMenu.getMenuId());
                if(null==adminMenu){
                    continue;
                }
                if (adminMenu.getModualCode().equals(adminRuleMenuVo.getModualCode())) {
                    menuData.add(adminMenu.getMenuId());
                    adminRuleMenuVo.setMenuId(menuData);
                }
            }
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, resultList);
    }

    /**
     * 保存角色菜单数据
     *
     * @param adminRuleMenuVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "parameter::menu",allEntries = true)
    public ResEntity saveAdminRuleMenu(AdminRuleMenuVo adminRuleMenuVo) {
        if (StringUtils.isBlank(adminRuleMenuVo.getRoleId())) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        //先删除角色菜单表中的数据
        adminRuleMapper.deleteRuleMenuByRids(adminRuleMenuVo.getRoleId());
        ArrayList<SysAdminRuleMenu> list = new ArrayList<>();
        List<String> menuIds = adminRuleMenuVo.getMenuId();
        for (String menuId : menuIds) {
            SysAdminRuleMenu sysAdminRuleMenu = new SysAdminRuleMenu();
            sysAdminRuleMenu.setRoleId(adminRuleMenuVo.getRoleId());
            sysAdminRuleMenu.setMenuId(menuId);
            list.add(sysAdminRuleMenu);
        }
        try {
            long row = adminRuleMapper.insertSysAdminRuleMenuList(list);
        } catch (Exception e) {
            return ResEntity.entity(false, "保存失败", null);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }

    /**
     * 根据某种业务类型返回一个树
     *
     * @param id
     * @param type 1:菜单ID 2:角色ID
     * @return
     */
    public ResEntity findMenuByMID(String id, String type, boolean isBtn) {

        List<AdminMenu> allList = redisService.getAllEnableMenu();

        List<AdminMenu> menuList = new ArrayList<>();
        //返回结果集合
        List<AdminMenu> selList = null;

        List<Map<String, Object>> resultList = null;

        menuList = allList;

        if (menuList.size() > 0) {

            if (Constant.BASIC_STRING_ONE.equals(type)) {
                selList = adminMenuService.getObjByID(id);
            } else if (type.equals("2")) {
                selList = adminMenuService.getListObjByRID(id);
            }

            Map<String, Object> ruleM = new HashMap<>();
            Map<String, String> parentList = new HashMap<>();
            if (null != selList) {
                for (AdminMenu adminMenu : selList) {
                    ruleM.put(adminMenu.getMenuId(), adminMenu.getMenuName());
                }
            }
            for (AdminMenu menu : allList) {
                parentList.put(menu.getParentMenuId(), menu.getMenuId());
            }
            resultList = getChildrenMenu3(menuList, "0", ruleM, parentList, isBtn);
        }


        return new ResEntity(true, Constant.SUCCESS_DX, resultList);
    }

    /**
     * 返回一个满足于layui 扩展插件的 树
     *
     * @param allList
     * @param parentID
     * @param ruleM
     * @param parentList
     * @return
     */
    public List<Map<String, Object>> getChildrenMenu2(List<AdminMenu> allList, String parentID, Map<String, Object> ruleM, Map<String, String> parentList, boolean isBtn) {

        List<Map<String, Object>> resultList = new ArrayList<>();

        for (AdminMenu adminMenu : allList) {

            if (!isBtn && "2".equals(adminMenu.getMenuType())) {
                continue;
            }

            if (null != adminMenu) {

                String pid = adminMenu.getParentMenuId();
                if (pid.equals(parentID)) {

                    String title = adminMenu.getMenuName();
                    String value = adminMenu.getMenuId();

                    Map<String, Object> m = new HashMap<>();

                    m.put("title", title);
                    m.put("value", value);

                    if (ruleM.containsKey(value)) {
                        m.put("checked", true);
                    } else {
                        m.put("checked", false);
                    }

                    List<Map<String, Object>> dataLis = new ArrayList<>();

                    if (parentList.containsKey(value)) {

                        dataLis = getChildrenMenu2(allList, value, ruleM, parentList, isBtn);
                    }
                    if (null != dataLis && dataLis.size() > 0) {
                        m.put("data", dataLis);
                    } else {
                        m.put("data", dataLis);
                    }
                    resultList.add(m);
                }
            }

        }
        return resultList;
    }

    //@CacheEvict(value = "pre-parameter-menu", allEntries = true)
    public ResEntity insertauthority(String menuIds, String roleId) {

        long rows = adminMenuMapper.deleteRmByRid(roleId);
        if (!StringUtils.isBlank(menuIds)) {
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (String mid : menuIds.split(",")) {
                Map<String, Object> m = new HashMap<>(16);
                m.put("roleId", roleId);
                m.put("menuId", mid);
                resultList.add(m);
            }
            adminMenuMapper.insertListM(resultList);
        }
        return new ResEntity(true, Constant.SUCCESS_DX, rows);

    }

    /**
     * eleTree树
     *
     * @param allList    所有的
     * @param parentID   0
     * @param ruleM      当前角色有的
     * @param parentList 所有 父节点
     * @param isBtn
     * @return
     */
    public List<Map<String, Object>> getChildrenMenu3(List<AdminMenu> allList, String parentID, Map<String, Object> ruleM, Map<String, String> parentList, boolean isBtn) {

        List<Map<String, Object>> resultList = new ArrayList<>();

        Map<String, Object> selM;
        Map<String, Object> m;
        Map<Integer, Integer> existsMid = new HashMap<>();
        List<Map<String, Object>> dataLis;

        for (AdminMenu adminMenu : allList) {

            if (!isBtn && "2".equals(adminMenu.getMenuType())) {
                continue;
            }

            String pid = adminMenu.getParentMenuId();

            if (!StringUtils.isBlank(pid) && !StringUtils.isBlank(parentID) && pid.equals(parentID)) {

                String title = adminMenu.getMenuName();
                String value = adminMenu.getMenuId();

                m = new HashMap<>();

                m.put("title", title);
                m.put("value", value);
                m.put("isLeaf", false);

                if (ruleM.containsKey(value)) {
                    m.put("checked", true);
                } else {
                    m.put("checked", false);
                }
//最后节点
                if (!parentList.containsKey(value)) {
                    m.put("isLeaf", true);
                } else {
                    m.put("isLeaf", false);
                }

                dataLis = new ArrayList<>();

                if (parentList.containsKey(value)) {

                    dataLis = getChildrenMenu3(allList, value, ruleM, parentList, isBtn);
                }
                m.put("data", dataLis);
                resultList.add(m);
            }
        }
        return resultList;
    }

    /**
     * @param adminRule :
     * @return :
     * @Description : 保存的时候判断中文名或者英文名是否已经存在
     * <AUTHOR> xhq
     * @updateTime : 2020/3/25 10:49
     */
    public ResEntity removeRepeatName(AdminRule adminRule) {
        String rname = adminRule.getRoleName();
        String rnameZh = adminRule.getRnameZh();
        String rid = StringUtils.isBlank(adminRule.getRoleId()) ? "" : adminRule.getRoleId();
        if (StringUtils.isBlank(rname)) {
            return ResEntity.entity(false, "保存失败,英文名为空!", adminRule);
        }
        if (StringUtils.isBlank(rnameZh)) {
            return ResEntity.entity(false, "保存失败,中文名为空!", adminRule);
        }
        List<AdminRule> ruleList = adminRuleMapper.getRepeatNameS(adminRule);
        Boolean status = true;
        String messge = "SUCCESS!";
        if (null == ruleList || ruleList.isEmpty()) {
            return new ResEntity(status, messge, adminRule);
        } else {
            // 修改时的判重和新增时的判重不一致
            for (AdminRule rule : ruleList) {
                if (rname.equals(rule.getRoleName()) && !rid.equals(rule.getRoleId())) {
                    status = false;
                    messge = "保存失败,英文名重复了!";
                    break;

                }
                if (rnameZh.equals(rule.getRnameZh()) && !rid.equals(rule.getRoleId())) {
                    status = false;
                    messge = "保存失败,中文名重复了!";
                    break;
                }
           /*
                else if(rname.equals(rule.getRname()) && StringUtils.isBlank(rid)){
                    status = false;
                    messge = "保存失败,英文名重复了!";
                    break;
                }else if(rnameZh.equals(rule.getRnameZh()) && StringUtils.isBlank(rid)){
                    status = false;
                    messge = "保存失败,中文名重复了!";
                    break;
                }*/
            }
            return ResEntity.entity(status, messge, adminRule);
        }
    }

}