package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

@Service("PRESCRIPTION_LIMIT_POINT")
public class PrescriptionLimitPointStrategy implements ParamStrategy {

    private final TSysParamInitDescMapper tSysParamInitDescMapper;

    @Autowired
    PrescriptionLimitPointStrategy(TSysParamInitDescMapper tSysParamInitDescMapper) {
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
    }

    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        String paramInitValue = tSysParam1.getParamInitValue();
        String[] split1 = paramInitValue.split(",");
        String[] split = tSysParam1.getParValues().split(",");
        //getParamInitValue 选项名字 getParValues 选项值。要对应。
        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
        JSONObject j = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
            j.put("label", tSysParamInitDesc1.getParamInitName());
            j.put("value", tSysParamInitDesc1.getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
            jsonArray.add(j);
        }
        if ("PRESCRIPTION_LIMIT_POINT".equals(tSysParam1.getParCode())) {

            jsonObject.remove("parName");
            jsonObject.put("parName", "散装饮片");
            jsonObject.remove("paramType");
            jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
            jsonObject.remove("parValues");
            jsonObject.put("parValues", split.length == 1 ? "0" : split[0]);


            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.putAll(jsonObject);
            jsonObject1.remove("parName");
            jsonObject1.put("parName", "散装颗粒");
            jsonObject1.put("parId", tSysParam1.getParId() + "-2");
            jsonObject1.put("paramInitDesc", jsonArray);
            jsonObject1.put("parValues", split.length == 1 ? "0" : split[1]);
            itemArray.add(jsonObject1);

            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.putAll(jsonObject);
            jsonObject2.remove("parName");
            jsonObject2.put("parName", "小包装饮片");
            jsonObject2.put("parId", tSysParam1.getParId() + "-3");
            jsonObject2.put("paramInitDesc", jsonArray);
            jsonObject2.put("parValues", split.length == 1 ? "0" : split[2]);
            itemArray.add(jsonObject2);

            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.putAll(jsonObject);
            jsonObject3.remove("parName");
            jsonObject3.put("parName", "小包装颗粒");
            jsonObject3.put("parId", tSysParam1.getParId() + "-4");
            jsonObject3.put("paramInitDesc", jsonArray);
            jsonObject3.put("parValues", split.length == 1 ? "0" : split[3]);
            itemArray.add(jsonObject3);
        } else {
            for (int i = 0; i < split1.length; i++) {
                if (i == 0) {
                    jsonObject.remove("parName");
                    jsonObject.put("parName", split1[0]);
                    jsonObject.remove("paramType");
                    jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
                    jsonObject.remove("parValues");
                    jsonObject.put("parValues", split.length == 1 ? "0" : split[0]);
                } else {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.putAll(jsonObject);
                    jsonObject1.remove("parName");
                    jsonObject1.put("parName", split1[i]);
                    jsonObject1.put("parId", tSysParam1.getParId() + "-" + (i + 1));
                    jsonObject1.put("paramInitDesc", jsonArray);
                    jsonObject1.put("parValues", split.length == 1 ? "0" : split[i]);
                    itemArray.add(jsonObject1);
                }
            }
        }


        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO, TSysParamNew tSysParamNew) {
        if (!StringUtils.isBlank(saveParameter.getValue6())) {
            tSysParamVO.setParValues(saveParameter.getValue1() + "," + saveParameter.getValue2() + "," + saveParameter.getValue3() + "," + saveParameter.getValue4() + "," + saveParameter.getValue5() + "," + saveParameter.getValue6());
        } else if (!StringUtils.isBlank(saveParameter.getValue5())) {
            tSysParamVO.setParValues(saveParameter.getValue1() + "," + saveParameter.getValue2() + "," + saveParameter.getValue3() + "," + saveParameter.getValue4() + "," + saveParameter.getValue5());
        } else if (!StringUtils.isBlank(saveParameter.getValue4())) {
            tSysParamVO.setParValues(saveParameter.getValue1() + "," + saveParameter.getValue2() + "," + saveParameter.getValue3() + "," + saveParameter.getValue4());
        } else if (!StringUtils.isBlank(saveParameter.getValue3())) {
            tSysParamVO.setParValues(saveParameter.getValue1() + "," + saveParameter.getValue2() + "," + saveParameter.getValue3());
        } else if (!StringUtils.isBlank(saveParameter.getValue2())) {
            tSysParamVO.setParValues(saveParameter.getValue1() + "," + saveParameter.getValue2());
        } else {
            tSysParamVO.setParValues(saveParameter.getValue1());
        }
    }
}
