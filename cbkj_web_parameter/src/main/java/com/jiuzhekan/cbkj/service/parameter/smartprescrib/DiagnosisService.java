package com.jiuzhekan.cbkj.service.parameter.smartprescrib;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysParam.*;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminMenuMapper;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamNewMapper;
import com.jiuzhekan.cbkj.service.common.TimeConverter;
import com.jiuzhekan.cbkj.service.parameter.strategy.factory.NormalParamStrategyFactory;
import com.jiuzhekan.cbkj.service.parameter.strategy.factory.ParamStrategyFactory;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断
 *
 * <AUTHOR>
 */
@Service
public class DiagnosisService {
    private TSysParamNewMapper tSysParamNewMapper;
    private TSysParamInitDescMapper tSysParamInitDescMapper;

    private AdminMenuMapper adminMenuMapper;
    private ParamStrategyFactory paramStrategyFactory;

    private NormalParamStrategyFactory normalParamStrategyFactory;

    @Autowired
    public DiagnosisService(TSysParamNewMapper tSysParamNewMapper,
                            TSysParamInitDescMapper tSysParamInitDescMapper,
                            AdminMenuMapper adminMenuMapper,
                            ParamStrategyFactory paramStrategyFactory,
                            NormalParamStrategyFactory normalParamStrategyFactory) {
        this.tSysParamNewMapper = tSysParamNewMapper;
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
        this.adminMenuMapper = adminMenuMapper;
        this.paramStrategyFactory = paramStrategyFactory;
        this.normalParamStrategyFactory = normalParamStrategyFactory;
    }

    /**
     * 获取参数列表
     *
     * @param tSysParam
     * @return
     */
    public ResEntity getParameterList(DiagnosisParamDTO tSysParam) {
        DiagnosisParamDTO diagnosisParamDTO = new DiagnosisParamDTO();
        diagnosisParamDTO.setMenuId(tSysParam.getMenuId());
        if (StringUtils.isBlank(tSysParam.getAppId())) {
            tSysParam.setAppId(Constant.BASIC_APP_ID);
        }
        if (StringUtils.isBlank(tSysParam.getInsCode())) {
            tSysParam.setInsCode(Constant.BASIC_INS_CODE);
        }
        if (StringUtils.isBlank(tSysParam.getDeptId())) {
            tSysParam.setDeptId(Constant.BASIC_DEPT_ID);
        }

        Map<String, Object> params = new HashMap<>();
        params.put("uid", AdminUtils.getCurrentHr().getUserId());
        params.put("modualCode", Constant.BASIC_STRING_ONE);
        params.put("parentMenuId", tSysParam.getMenuId());
        params.put("menuTypes", "1,2");
        //查看当前菜单id是否有子菜单，即第四级不显示菜单。
        StringBuilder menuIds = new StringBuilder(tSysParam.getMenuId());
        List<AdminMenu> menuByPid = adminMenuMapper.getMenuByPID(params);
        if (menuByPid.size() > 0) {
            menuIds = new StringBuilder();
            for (AdminMenu adminMenu : menuByPid) {
                menuIds.append(adminMenu.getMenuId()).append(",");
            }
            tSysParam.setMenuIds(menuIds.append(tSysParam.getMenuId()).toString());
            tSysParam.setMenuId("");
            diagnosisParamDTO.setMenuIds(menuIds.append(tSysParam.getMenuId()).toString());
            diagnosisParamDTO.setMenuId(null);
        }
        List<TSysParamNew> newT = new ArrayList<>();

        List<TSysParamNew> obj = tSysParamNewMapper.getDiagnosisParameterList(tSysParam);

//       List<TSysParamNew> obj2 = tSysParamNewMapper.getDiagnosisParameterList(diagnosisParamDTO);
        List<TSysParamNew> obj2 = tSysParamNewMapper.getDiagnosisParameterListAll(diagnosisParamDTO);
        for (TSysParamNew tSysParamNew : obj2) {
            String parCode = tSysParamNew.getParCode();
            boolean pT = false;
            for (TSysParamNew sysParamNew : obj) {
                boolean equals = sysParamNew.getParCode().equals(parCode);
                if (equals) {
                    //存在了，不需要额外添加
                    pT = true;
                    break;
                }
            }
            if (!pT) {
                newT.add(tSysParamNew);
            }
        }

        obj.addAll(newT);
        JSONArray jsonArray = getJsonArray(obj);
        return ResEntity.entity(true, Constant.SUCCESS_DX, jsonArray);
    }

    /**
     * 更新或插入
     *
     * @param tSysParamVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"parameter::params"}, allEntries = true)
    public ResEntity saveParameterList(DiagnosisParamNewVO tSysParamVO) {
        Map<String, Object> data = tSysParamVO.getData();
        ArrayList<SaveParameter> saveParameters = new ArrayList<>();
        data.forEach(
                (k, v) -> {
                    TSysParamNew tSysParamNew = null;
                    boolean contains = k.contains("-");
                    if (contains) {
                        SaveParameter saveParameter = null;
                        String mainKey = k.substring(0, k.length() - 2);
                        for (SaveParameter saveParameter2 : saveParameters) {
                            boolean equals = saveParameter2.getKey().equals(mainKey);
                            if (equals) {
                                saveParameter = saveParameter2;
                                break;
                            }
                        }
                        if (null == saveParameter) {
                            saveParameter = new SaveParameter();
                            saveParameters.add(saveParameter);
                        }
                        boolean a = k.endsWith("-1");
                        boolean b = k.endsWith("-2");
                        boolean c = k.endsWith("-3");
                        boolean d = k.endsWith("-4");
                        boolean dd = k.endsWith("-5");
                        boolean ddd = k.endsWith("-6");
                        saveParameter.setKey(mainKey);
                        if (a) {
                            saveParameter.setValue1((null == v) ? "" : v.toString());
                        } else if (b) {
                            saveParameter.setValue2((null == v) ? "" : v.toString());
                        } else if (c) {
                            saveParameter.setValue3((null == v) ? "" : v.toString());
                        } else if (d) {
                            saveParameter.setValue4((null == v) ? "" : v.toString());
                        }else if (dd) {
                            saveParameter.setValue5((null == v) ? "" : v.toString());
                        }else if (ddd) {
                            saveParameter.setValue6((null == v) ? "" : v.toString());
                        }
                    } else {
                        tSysParamVO.setParId(k);
                        tSysParamNew = tSysParamNewMapper.getObjectById(tSysParamVO.getParId());
                        //先设置这个值
                        tSysParamVO.setParValues(v.toString());
                        //策略去判断ParValues是否重新修改。
                        normalParamStrategyFactory.getNormalParamStrategy(tSysParamNew.getParCode()).setParam(tSysParamVO, v);
                    }
                    saveJson(tSysParamVO, tSysParamNew);
                }
        );
        //处理特殊values值
        for (SaveParameter saveParameter : saveParameters) {
            TSysParamNew tSysParamNew = tSysParamNewMapper.getObjectById(saveParameter.getKey());
            if (null == tSysParamNew) {
                continue;
            }
            paramStrategyFactory.getParamStrategy(tSysParamNew.getParCode()).setParam(saveParameter, tSysParamVO, tSysParamNew);
            saveJson(tSysParamVO, tSysParamNew);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, tSysParamVO);

    }


    public JSONArray getJsonArray(List<TSysParamNew> obj) {
        JSONObject jsonObject;
        JSONObject pjsonObject;
        JSONObject ppjsonObject = null;
        //参数相同的第四级 菜单下 的 参数存放处
        JSONArray ppChildren = new JSONArray();
        //最外层数组
        // JSONArray pppChildren = new JSONArray();
        for (TSysParamNew tSysParam1 : obj) {
            //参数存放处
            JSONArray pChildren = new JSONArray();
            //参数所属菜单是不显示菜单
            JSONArray itemArray = new JSONArray();
            String menuType = tSysParam1.getMenuType();
            boolean isContains = false;
            if (null == ppjsonObject) {
                ppjsonObject = new JSONObject();
            }
            //判断下这个第四季菜单的菜单名是否存在了
            if (Constant.BASIC_STRING_TWO.equals(menuType)) {
                //存在不显示菜单，并且当前菜单名字没被添加到数组中。
                for (Object ppChild : ppChildren) {
                    JSONObject a = (JSONObject) ppChild;
                    boolean b = a.containsValue(tSysParam1.getMenuName());
                    if (b) {
                        //存在了，覆盖 ppjsonObject。
                        isContains = true;
                        ppjsonObject = a;
                        break;
                    }
                }
                if (!isContains) {
                    ppjsonObject = new JSONObject();
                    ppjsonObject.put("menuName", tSysParam1.getMenuName());
                    ppjsonObject.put("menuId", tSysParam1.getMenuId());
                }
            } else {
                //虽然不是不显示菜单，但是需要构建一个虚拟的。
                for (Object ppChild : ppChildren) {

                    boolean b = ((JSONObject) ppChild).containsValue(" ");
                    if (b) {
                        //存在了，覆盖 ppjsonObject。
                        isContains = true;
                        ppjsonObject = (JSONObject) ppChild;
                        break;
                    }
                }
                if (!isContains) {
                    ppjsonObject = new JSONObject();
                    ppjsonObject.put("menuName", " ");
                    ppjsonObject.put("menuId", " ");
                }
            }

            jsonObject = new JSONObject();
            pjsonObject = new JSONObject();
            pjsonObject.put("parId", tSysParam1.getParId());
            pjsonObject.put("parNumber", tSysParam1.getParNumber());
            pjsonObject.put("parName", tSysParam1.getParName());
            //补加字段
            pjsonObject.put("parCode", tSysParam1.getParCode());
            List<TSysParamNew> paramsByCodes = tSysParamNewMapper.getParamsByCode(tSysParam1.getParCode());
            pjsonObject.put("checked", paramsByCodes.size() > 0 ? true : false);

            jsonObject.put("parId", tSysParam1.getParId() + "-1");
            jsonObject.put("parName", "列表");
            jsonObject.put("paramType", tSysParam1.getParamType());
            jsonObject.put("sort", tSysParam1.getSort());
            jsonObject.put("isGlobal", tSysParam1.getIsGlobal());
            jsonObject.put("paramDesc", tSysParam1.getParamDesc());

            //系统默认初始值
            jsonObject.put("initValue", tSysParam1.getParamInitValue());
            jsonObject.put("parameterDiagramURL", StringUtils.isBlank(tSysParam1.getParameterDiagram()) ? " " : tSysParam1.getParameterDiagram());
            //根据参数编码-处理参数
            if ("SYS_CHECK_WORK_TIME".equals(tSysParam1.getParCode())){
                jsonObject.put("parValues", TimeConverter.convertTimeToUIStr(tSysParam1.getParValues()));
            }else {
                //选中的值
                jsonObject.put("parValues", tSysParam1.getParValues());
            }
            String s = paramStrategyFactory.getParamStrategy(tSysParam1.getParCode()).getParam(pjsonObject, jsonObject, tSysParam1, itemArray);
            addParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, ppjsonObject, ppChildren, Integer.parseInt(s));
        }
        return ppChildren;
    }

    public void addParams(TSysParamNew tSysParam1, JSONObject jsonObject, JSONArray itemArray,
                          JSONObject pjsonObject, JSONArray pChildren, JSONObject ppjsonObject, JSONArray ppChildren, int isMoreParameters) {
        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
        //JSONObject j2 = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject j = new JSONObject();
//        JSONObject jsonObject1 = new JSONObject();
        if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode()) ||
                "PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS".equals(tSysParam1.getParCode())) {
            j.put("label", pageListByObj.get(0).getParamInitName());
            j.put("value", pageListByObj.get(0).getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
            jsonArray.add(j);
        } else {
            for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
                j.put("label", tSysParamInitDesc1.getParamInitName());
                j.put("value", tSysParamInitDesc1.getParamInitCode());
                j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
                jsonArray.add(j);
            }
        }
        //通用类型，因为分隔不是通用方式，所以特殊处理
        if ("PRESCRIPTION_TRANSFER_ILLNESS_OPTIONS".equals(tSysParam1.getParCode())) {
            //转方操作-转病证配置 值没有用逗号分隔，需要处理
            jsonObject.remove("parValues");
            StringBuilder values = new StringBuilder("");
            for (int i = 0; i < tSysParam1.getParValues().length(); i++) {
                String substring = tSysParam1.getParValues().substring(i, i + 1);
                if ("c".equals(substring)) {
                    values.append(i + 1).append(",");
                }
            }
            jsonObject.put("parValues", values.length() < 1 ? "" : values.toString().substring(0, values.toString().length() - 1));
        }
        //通用类型-病历打印按钮是否显示
//        if ("PRINT_RECORD_SHOW".equals(tSysParam1.getParCode())) {
//            //病历打印按钮是否显示 类型是3，但是保存的值不是选项值
//            jsonObject.remove("parValues");
//            StringBuilder values = new StringBuilder("");
//            for (int i = 0; i < tSysParam1.getParValues().split(",").length; i++) {
//                String substring = tSysParam1.getParValues().split(",")[i];
//                if ("1".equals(substring)) {
//                    values.append(i + 1).append(",");
//                }
//            }
//            jsonObject.put("parValues", values.length() < 1 ? "" : values.toString().substring(0, values.toString().length() - 1));
//        }
        jsonObject.put("paramInitDesc", jsonArray);

        //处理要拆分成两个参数选项。正常类型只有一个参数选项（个别正常类型也拆成两个参数选项）。
        if (isMoreParameters > 0) {
            //选项图标
            itemArray.add(0, jsonObject);
            pjsonObject.put("children", itemArray);
        } else {
            jsonObject.remove("parId");
            jsonObject.remove("parName");
            pjsonObject.putAll(jsonObject);
        }
        pChildren.add(pjsonObject);

        if (!ppjsonObject.containsKey("params")) {
            ppjsonObject.put("params", pChildren);
            ppChildren.add(ppjsonObject);
        } else {
            JSONArray ddd = (JSONArray) ppjsonObject.get("params");
            ddd.add(pjsonObject);
        }
    }

    public TSysParamNew saveJson(DiagnosisParamNewVO tSysParamVO, TSysParamNew tSysParamNew) {
        if (null == tSysParamNew) {
            return tSysParamNew;
        }
        if (StringUtils.isBlank(tSysParamVO.getAppId()) && StringUtils.isBlank(tSysParamVO.getInsCode()) && StringUtils.isBlank(tSysParamVO.getDeptId())) {
            //都为空，说明使用默认值 000000，直接修改 tSysParamNew
            tSysParamNew.setParValues(tSysParamVO.getParValues());
            tSysParamNewMapper.updateByPrimaryKey(tSysParamNew);
            return tSysParamNew;
        } else {
            //判断和原数据的医共体、机构、部门是否一致，不一致就新增
            if (tSysParamNew.getAppId().equals(StringUtils.isBlank(tSysParamVO.getAppId()) ? Constant.BASIC_APP_ID : tSysParamVO.getAppId()) &&
                    tSysParamNew.getInsCode().equals(StringUtils.isBlank(tSysParamVO.getInsCode()) ? Constant.BASIC_INS_CODE : tSysParamVO.getInsCode()) &&
                    tSysParamNew.getDeptId().equals(StringUtils.isBlank(tSysParamVO.getDeptId()) ? Constant.BASIC_DEPT_ID : tSysParamVO.getDeptId())) {
                tSysParamNew.setParValues(tSysParamVO.getParValues());
                tSysParamNewMapper.updateByPrimaryKey(tSysParamNew);
                return tSysParamNew;
            } else {
                TSysParamNew tSysParamNew2 = new TSysParamNew();
                tSysParamNew2.setAppId(StringUtils.isBlank(tSysParamVO.getAppId()) ? Constant.BASIC_APP_ID : tSysParamVO.getAppId());
                tSysParamNew2.setInsCode(StringUtils.isBlank(tSysParamVO.getInsCode()) ? Constant.BASIC_INS_CODE : tSysParamVO.getInsCode());
                tSysParamNew2.setDeptId(StringUtils.isBlank(tSysParamVO.getDeptId()) ? Constant.BASIC_DEPT_ID : tSysParamVO.getDeptId());
                tSysParamNew2.setParCode(tSysParamNew.getParCode());
                TSysParamNew tSysParamNew3 = tSysParamNewMapper.selectParamByCondition(tSysParamNew2);
                if (null == tSysParamNew3) {
                    //通用对象赋值
                    TSysParamNew tSysParamNew1 = gettSysParamNew(tSysParamVO, tSysParamNew);
                    tSysParamNewMapper.insert(tSysParamNew1);

                    //插入数据到选项表
                    TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
                    tSysParamInitDesc.setParamId(tSysParamNew.getParId());
                    List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
                    for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
                        tSysParamInitDesc1.setParamId(tSysParamNew1.getParId());
                        tSysParamInitDescMapper.insert(tSysParamInitDesc1);
                    }
                    return tSysParamNew1;
                } else {
                    //更新
                    tSysParamNew3.setParValues(tSysParamVO.getParValues());
                    tSysParamNewMapper.updateByPrimaryKey(tSysParamNew3);
                    return tSysParamNew3;
                }
            }
        }
        //return tSysParamNew;
    }

    /**
     * 搜索药品
     *
     * @param sysParamSearch
     * @param page
     * @return
     */
    public Object searchParam(SysParamSearch sysParamSearch, Page page) {
        if (StringUtils.isBlank(sysParamSearch.getAppId())) {
            sysParamSearch.setAppId(Constant.BASIC_APP_ID);
        }
        if (StringUtils.isBlank(sysParamSearch.getInsCode())) {
            sysParamSearch.setInsCode(Constant.BASIC_INS_CODE);
        }
        if (StringUtils.isBlank(sysParamSearch.getDeptId())) {
            sysParamSearch.setDeptId(Constant.BASIC_DEPT_ID);
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        sysParamSearch.setUserId(AdminUtils.getCurrentHr().getUserId());
        List<TSysParamNew> tSysParamNews = tSysParamNewMapper.searchParam(sysParamSearch);

        for (TSysParamNew tSysParamNew : tSysParamNews) {
            AdminMenu adminMenu = adminMenuMapper.selectByPrimaryKey(tSysParamNew.getParentMenuId());
            if (Constant.ADMIN_EXT_PERSONAL_SHARE == adminMenu.getMenuLevel()) {
                AdminMenu parentMenuId = adminMenuMapper.selectByPrimaryKey(adminMenu.getParentMenuId());
                tSysParamNew.setParentMenuName(parentMenuId.getMenuName() + "-" + adminMenu.getMenuName());
            } else {
                tSysParamNew.setParentMenuName(adminMenu.getMenuName());
            }
            //判断这个参数是否存在个性配置
            List<TSysParamNew> paramsByCodes = tSysParamNewMapper.getParamsByCode(tSysParamNew.getParCode());
            tSysParamNew.setChecked(paramsByCodes.size() > 0 ? true : false);
        }

        return Page.getLayUiTablePageData(tSysParamNews);
    }

    /**
     * 参数个性化配置
     *
     * @param parCode
     * @return
     */
    public Object getPersonalityParameters(String parCode) {
        if (StringUtils.isBlank(parCode)) {
            return ResEntity.error("缺少参数");
        }
        List<TSysParamNew> paramsByCodes = tSysParamNewMapper.getParamsByCode(parCode);
        JSONArray jsonArray = detailParams(paramsByCodes);

        return ResEntity.entity(true, "", jsonArray);
    }


    public JSONArray detailParams(List<TSysParamNew> obj) {
        JSONObject jsonObject;
        //存放参数名称,CODE
        JSONObject pjsonObject;

        JSONArray pChildren = new JSONArray();


        for (TSysParamNew tSysParam1 : obj) {

            JSONArray itemArray = new JSONArray();
            //存放医共体信息层面的JSON
            JSONObject finalJson = new JSONObject();

            jsonObject = new JSONObject();
            pjsonObject = new JSONObject();
            finalJson.put("appId", tSysParam1.getAppId());
            finalJson.put("appName", tSysParam1.getAppName());
            finalJson.put("insCode", tSysParam1.getInsCode());
            finalJson.put("insName", tSysParam1.getInsName());
            finalJson.put("deptId", tSysParam1.getDeptId());
            finalJson.put("deptName", tSysParam1.getDeptName());

            pjsonObject.put("parId", tSysParam1.getParId());
            pjsonObject.put("parNumber", tSysParam1.getParNumber());
            pjsonObject.put("parName", tSysParam1.getParName());
            //补加字段
            pjsonObject.put("parCode", tSysParam1.getParCode());

            jsonObject.put("parId", tSysParam1.getParId() + "-1");
            jsonObject.put("parName", "列表");
            jsonObject.put("paramType", tSysParam1.getParamType());
            jsonObject.put("sort", tSysParam1.getSort());
            jsonObject.put("isGlobal", tSysParam1.getIsGlobal());
            jsonObject.put("paramDesc", tSysParam1.getParamDesc());
            //选中的值
            jsonObject.put("parValues", tSysParam1.getParValues());
            //系统默认初始值
            jsonObject.put("initValue", tSysParam1.getParamInitValue());
            jsonObject.put("parameterDiagramURL", StringUtils.isBlank(tSysParam1.getParameterDiagram()) ? " " : tSysParam1.getParameterDiagram());
            //根据参数编码-处理参数
            String s = paramStrategyFactory.getParamStrategy(tSysParam1.getParCode()).getParam(pjsonObject, jsonObject, tSysParam1, itemArray);
            detailAddParams(tSysParam1, jsonObject, itemArray, pjsonObject, pChildren, finalJson, Integer.parseInt(s));
        }

        return pChildren;
    }

    public void detailAddParams(TSysParamNew tSysParam1, JSONObject jsonObject, JSONArray itemArray,
                                JSONObject pjsonObject, JSONArray pChildren, JSONObject finalJson, int isMoreParameters) {

        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);

        JSONArray jsonArray = new JSONArray();
        JSONObject j = new JSONObject();

        if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(tSysParam1.getParCode()) ||
                "PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS".equals(tSysParam1.getParCode())) {
            j.put("label", pageListByObj.get(0).getParamInitName());
            j.put("value", pageListByObj.get(0).getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
            jsonArray.add(j);
        } else {
            for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
                j.put("label", tSysParamInitDesc1.getParamInitName());
                j.put("value", tSysParamInitDesc1.getParamInitCode());
                j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
                jsonArray.add(j);
            }
        }
        //通用类型，因为分隔不是通用方式，所以特殊处理
        if ("PRESCRIPTION_TRANSFER_ILLNESS_OPTIONS".equals(tSysParam1.getParCode())) {
            //转方操作-转病证配置 值没有用逗号分隔，需要处理
            jsonObject.remove("parValues");
            StringBuilder values = new StringBuilder("");
            for (int i = 0; i < tSysParam1.getParValues().length(); i++) {
                String substring = tSysParam1.getParValues().substring(i, i + 1);
                if ("c".equals(substring)) {
                    values.append(i + 1).append(",");
                }
            }
            jsonObject.put("parValues", values.length() < 1 ? "" : values.toString().substring(0, values.toString().length() - 1));
        }
        //通用类型-病历打印按钮是否显示
        if ("PRINT_RECORD_SHOW".equals(tSysParam1.getParCode())) {
            //病历打印按钮是否显示 类型是3，但是保存的值不是选项值
            jsonObject.remove("parValues");
            StringBuilder values = new StringBuilder("");
            for (int i = 0; i < tSysParam1.getParValues().split(",").length; i++) {
                String substring = tSysParam1.getParValues().split(",")[i];
                if ("1".equals(substring)) {
                    values.append(i + 1).append(",");
                }
            }
            jsonObject.put("parValues", values.length() < 1 ? "" : values.toString().substring(0, values.toString().length() - 1));
        }
        jsonObject.put("paramInitDesc", jsonArray);
        //处理要拆分成两个参数选项。正常类型只有一个参数选项（个别正常类型也拆成两个参数选项）。
        if (isMoreParameters > 0) {
            //选项图标
            itemArray.add(0, jsonObject);
            pjsonObject.put("children", itemArray);
        } else {
            jsonObject.remove("parId");
            jsonObject.remove("parName");
            pjsonObject.putAll(jsonObject);
        }

        finalJson.put("data", pjsonObject);
        pChildren.add(finalJson);

    }

    /**
     * 个性化参数配置
     *
     * @param persionalityParams
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object savePersonalityParameters(PersionalityParams persionalityParams) {
        //先把参数下面配置过的全删
        String parCode = persionalityParams.getParCode();
        if(StringUtils.isBlank(parCode)){
            return ResEntity.error("参数代码不能为空");
        }
        List<DiagnosisParamNewVO> tSysParamVOs = persionalityParams.getTSysParamVOs();
        if(tSysParamVOs==null||tSysParamVOs.size()<=0){
            tSysParamNewMapper.deleteParamByCode(parCode);
            return ResEntity.success("成功");
        }

        tSysParamNewMapper.deleteParamByCode(parCode);

        for (DiagnosisParamNewVO tSysParamVO : tSysParamVOs) {
            //保存参数值对象
            Map<String, Object> data = tSysParamVO.getData();
            ArrayList<SaveParameter> saveParameters = new ArrayList<>();
            data.forEach(
                    (k, v) -> {
                        TSysParamNew tSysParamNew1 = new TSysParamNew();
                        //包含“-”是特殊参数另外处理
                        boolean contains = k.contains("-");
                        if (contains) {
                            //存要保存的值
                            SaveParameter saveParameter = null;
                            String mainKey = k.substring(0, k.length() - 2);
                            for (SaveParameter saveParameter2 : saveParameters) {
                                boolean equals = saveParameter2.getKey().equals(mainKey);
                                if (equals) {
                                    saveParameter = saveParameter2;
                                    break;
                                }
                            }
                            if (null == saveParameter) {
                                saveParameter = new SaveParameter();
                                saveParameters.add(saveParameter);
                            }
                            boolean a = k.endsWith("-1");
                            boolean b = k.endsWith("-2");
                            boolean c = k.endsWith("-3");
                            boolean d = k.endsWith("-4");
                            boolean dd = k.endsWith("-5");
                            boolean ddd = k.endsWith("-6");
                            saveParameter.setKey(mainKey);
                            if (a) {
                                saveParameter.setValue1((null == v) ? "" : v.toString());
                            } else if (b) {
                                saveParameter.setValue2((null == v) ? "" : v.toString());
                            } else if (c) {
                                saveParameter.setValue3((null == v) ? "" : v.toString());
                            } else if (d) {
                                saveParameter.setValue4((null == v) ? "" : v.toString());
                            }else if (dd) {
                                saveParameter.setValue5((null == v) ? "" : v.toString());
                            }else if (ddd) {
                                saveParameter.setValue6((null == v) ? "" : v.toString());
                            }
                            saveParameter.setParCode(tSysParamVO.getParCode());
                        } else {
                            tSysParamVO.setParId(k);
                            //
                            tSysParamNew1 = tSysParamNewMapper.getSystemParamByCode(parCode);
                            tSysParamVO.setParValues(v.toString());
                            normalParamStrategyFactory.getNormalParamStrategy(parCode).setParam(tSysParamVO,v);
                            savePersonaLity(tSysParamVO, tSysParamNew1);
                        }
                    }
            );

            for (SaveParameter saveParameter : saveParameters) {

                TSysParamNew systemParamByCode = tSysParamNewMapper.getSystemParamByCode(parCode);
                if (null == systemParamByCode) {
                    continue;
                }
                paramStrategyFactory.getParamStrategy(systemParamByCode.getParCode()).setParam(saveParameter, tSysParamVO, systemParamByCode);
                savePersonaLity(tSysParamVO, systemParamByCode);
            }
        }
        return ResEntity.success("成功");

    }

    @Transactional(rollbackFor = Exception.class)
    public TSysParamNew savePersonaLity(DiagnosisParamNewVO tSysParamVO, TSysParamNew tSysParamNew) {
        if (null == tSysParamNew) {
            return tSysParamNew;
        }
//        if (tSysParamNew.getAppId().equals(StringUtils.isBlank(tSysParamVO.getAppId()) ? Constant.BASIC_APP_ID : tSysParamVO.getAppId()) &&
//                tSysParamNew.getInsCode().equals(StringUtils.isBlank(tSysParamVO.getInsCode()) ? Constant.BASIC_INS_CODE : tSysParamVO.getInsCode()) &&
//                tSysParamNew.getDeptId().equals(StringUtils.isBlank(tSysParamVO.getDeptId()) ? Constant.BASIC_DEPT_ID : tSysParamVO.getDeptId())) {
//            tSysParamNew.setParValues(tSysParamVO.getParValues());
//            tSysParamNewMapper.updateByPrimaryKey(tSysParamNew);
//            return tSysParamNew;
//        } else {
            String initParId = tSysParamNew.getParId();
            //赋值
            TSysParamNew tSysParamNew1 = gettSysParamNew(tSysParamVO, tSysParamNew);
            tSysParamNewMapper.insert(tSysParamNew1);
            //插入数据到选项表
            TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
            tSysParamInitDesc.setParamId(initParId);
            List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
            for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
                tSysParamInitDesc1.setParamId(tSysParamNew1.getParId());
                tSysParamInitDescMapper.insert(tSysParamInitDesc1);
            }
            return tSysParamNew;
       // }

    }

    /**
     * 参数对象赋值
     *
     * @param tSysParamVO
     * @param tSysParamNew
     * @return
     */
    private TSysParamNew gettSysParamNew(DiagnosisParamNewVO tSysParamVO, TSysParamNew tSysParamNew) {
        TSysParamNew tSysParamNew1 = new TSysParamNew();
        tSysParamNew1.setParId(IDUtil.getID());
        tSysParamNew1.setAppId(StringUtils.isBlank(tSysParamVO.getAppId()) ? Constant.BASIC_APP_ID : tSysParamVO.getAppId());
        tSysParamNew1.setInsCode(StringUtils.isBlank(tSysParamVO.getInsCode()) ? Constant.BASIC_INS_CODE : tSysParamVO.getInsCode());
        tSysParamNew1.setDeptId(StringUtils.isBlank(tSysParamVO.getDeptId()) ? Constant.BASIC_DEPT_ID : tSysParamVO.getDeptId());
        tSysParamNew1.setCreateDate(new Date());
        tSysParamNew1.setParCode(tSysParamNew.getParCode());
        tSysParamNew1.setParValues(tSysParamVO.getParValues());
        tSysParamNew1.setIsGlobal(Constant.BASIC_STRING_ONE);
        tSysParamNew1.setMenuId(tSysParamNew.getMenuId());
        tSysParamNew1.setParamDesc(tSysParamNew.getParamDesc());
        tSysParamNew1.setStatus(Constant.BASIC_STRING_ZERO);
        tSysParamNew1.setParamType(tSysParamNew.getParamType());
        tSysParamNew1.setParName(tSysParamNew.getParName());
        tSysParamNew1.setSort(tSysParamNew.getSort());
        tSysParamNew1.setParamInitValue(tSysParamNew.getParamInitValue());
        tSysParamNew1.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        tSysParamNew1.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
        tSysParamNew1.setParameterDiagram(tSysParamNew.getParameterDiagram());
        tSysParamNew1.setParNumber(tSysParamNew.getParNumber());
        return tSysParamNew1;
    }
}


