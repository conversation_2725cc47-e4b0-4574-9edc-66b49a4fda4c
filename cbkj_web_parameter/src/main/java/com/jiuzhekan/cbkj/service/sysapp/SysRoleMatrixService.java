package com.jiuzhekan.cbkj.service.sysapp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix;
import com.jiuzhekan.cbkj.beans.sysExt.SysRoleMatrix;
import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.StreamUtils;
import com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysRoleMatrixMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysRoleMatrixService {

    @Autowired
    private SysRoleMatrixMapper sysRoleMatrixMapper;

    @Autowired
    private SysProductMatrixService sysProductMatrixService;

    @Autowired
    private DicBaseMapper dicBaseMapper;

    /**
     * @description  根据角色ID获取产品矩阵树形结构 （带选中状态）
     * <AUTHOR>
     * @date    2025/8/25 10:03
     * @param	roleId
     * @return  java.util.List<com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix>
    */ 
    public List<String> listMatrixIdsByRoleId(String roleId){
        if (StrUtil.isBlank(roleId)){
            ExceptionUtils.throwErrorCustomRuntimeException("参数roleId不能为空");
        }
        // 查询出角色列表拥有的 产品矩阵列IDS
        List<String> matrixIds = sysRoleMatrixMapper.listMatrixIdsByRoleIds(Collections.singletonList(roleId));
        return matrixIds;
    }

    private void sortProductMatrixByCategory(List<SysProductMatrix> treeList) {
        // 根据字典 ，填充一级分类，顺序
        List<TDicBase> catoryDictList = dicBaseMapper.getCareer(Constant.DICT_PRODUCT_MATRIX_CATEGORY);
        for (SysProductMatrix sysProductMatrix : treeList) {
            for (TDicBase dicBase : catoryDictList) {
                if (dicBase.getDicCode().equals(sysProductMatrix.getCategory())){
                    sysProductMatrix.setCategoryOrderNum(dicBase.getSort());
                    break;
                }
            }
        }

        // 一级分类排序， 空值排在最后
        treeList.sort(Comparator.comparing(
                SysProductMatrix::getCategoryOrderNum,
                Comparator.nullsLast(Comparator.naturalOrder())
        ));
    }


    /**
     * @description 根据用户ID 获取其拥有的产品矩阵树形结构
     * <AUTHOR>
     * @date    2025/8/25 10:26
     * @param	userId
     * @return  java.util.List<com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix>
    */
    public List<SysProductMatrix> getTreeByUserId(String userId){
        // 查询出角色所拥有的产品矩阵列表 （已排好序
        List<SysProductMatrix> matrixList = sysProductMatrixService.listByUserId(userId);
        if (CollUtil.isEmpty(matrixList)){
            return null;
        }

        // 系统类型字典
        List<TDicBase> cateGoryList = dicBaseMapper.getCareer(Constant.DICT_PRODUCT_MATRIX_CATEGORY);
        Map<String, TDicBase> cateGoryMap = StreamUtils.toMap(cateGoryList,TDicBase::getDicCode);

        // 按分类分组
        Map<String, List<SysProductMatrix>> categoryMap = StreamUtils.
                toGroupLinkedMap(matrixList,SysProductMatrix::getCategory);

        // 组织成树形结构
        List<SysProductMatrix> treeList = categoryMap.entrySet().stream()
                .map(entry -> {
                    String category = entry.getKey();
                    List<SysProductMatrix> childList = entry.getValue();

                    // 创建分类节点
                    SysProductMatrix categoryNode = new SysProductMatrix();
                    categoryNode.setProductMatrixId(Constant.VIRTUAL + "_" + category);
                    categoryNode.setCategory(category);
                    categoryNode.setName(Optional.ofNullable(cateGoryMap.get(category))
                            .map(TDicBase::getDicName).orElse(category));
                    categoryNode.setChildList(childList);

                    return categoryNode;
                })
                .collect(Collectors.toList());

        return treeList;
    }

    /**
     * @description 删除后新增 角色所关联得 产品矩阵
     * <AUTHOR>
     * @date    2025/8/25 11:18
     * @param	roleId
     * @param	productMatrixIdList
     * @return  boolean
    */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBeforeDel(String roleId, List<String> productMatrixIdList){
        if (StrUtil.isEmpty(roleId)){
            ExceptionUtils.throwErrorCustomRuntimeException("角色ID不能为空");
        }

        // 抽取出被勾上的产品矩阵，构建待新增列表
        List<SysRoleMatrix> saveList = new ArrayList<>();
        for (String productMatrixId : Optional.ofNullable(productMatrixIdList)
                .orElse(new ArrayList<>())) {
            if (productMatrixId.startsWith(Constant.VIRTUAL)){
                continue;
            }
            SysRoleMatrix save = new SysRoleMatrix();
            save.setRoleMatrixId(IdUtil.getSnowflake().nextIdStr());
            save.setRoleId(roleId);
            save.setMatrixId(productMatrixId);
            saveList.add(save);
        }

        // 先删除，后新增
        this.deleteByRoleIds(Collections.singletonList(roleId));

        // 新增
        if (CollUtil.isNotEmpty(saveList)){
            sysRoleMatrixMapper.insertList(saveList);
        }

        return true;
    }

    /**
     * @description 根据角色ID删除
     * <AUTHOR>
     * @date    2025/8/25 11:10
     * @param	roleIdList
     * @return  boolean
    */
    public boolean deleteByRoleIds(List<String> roleIdList){
        if (CollUtil.isEmpty(roleIdList)){
            ExceptionUtils.throwErrorCustomRuntimeException("角色IDS不能为空");
        }
        return sysRoleMatrixMapper.deleteByRoleIds(roleIdList) > 0;
    }

}
