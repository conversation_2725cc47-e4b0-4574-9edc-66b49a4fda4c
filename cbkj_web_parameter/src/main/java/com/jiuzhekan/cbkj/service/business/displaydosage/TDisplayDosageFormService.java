package com.jiuzhekan.cbkj.service.business.displaydosage;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.business.displaydosage.*;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.controller.business.displaydosage.DisplayDosageFormList;
import com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageCostMapper;
import com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageDescribeMapper;
import com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageFormMapper;
import com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class TDisplayDosageFormService {


    private final TDisplayDosageFormMapper tDisplayDosageFormMapper;

    private final TDisplayDosageCostMapper tDisplayDosageCostMapper;

    private final TDisplayDosageDescribeMapper tDisplayDosageDescribeMapper;

    private final DicBaseMapper dicBaseMapper;

    public TDisplayDosageFormService(TDisplayDosageFormMapper tDisplayDosageFormMapper, TDisplayDosageCostMapper tDisplayDosageCostMapper, TDisplayDosageDescribeMapper tDisplayDosageDescribeMapper, DicBaseMapper dicBaseMapper) {
        this.tDisplayDosageFormMapper = tDisplayDosageFormMapper;
        this.tDisplayDosageCostMapper = tDisplayDosageCostMapper;
        this.tDisplayDosageDescribeMapper = tDisplayDosageDescribeMapper;
        this.dicBaseMapper = dicBaseMapper;
    }


    public Object getDisplayDosageFormList() {
        return ResEntity.success(DisplayDosageFormList.getAllCodeAndName());
    }

    public Object getDisplayDosageFormListByDicId(GetDisplayDosageFormListByDicId dicId) {
        if (StringUtils.isBlank(dicId.getDicId())) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        if (dicId.getDicType() == null) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        List<TDisplayDosageCost> list = tDisplayDosageCostMapper.getDisplayDosageFormListByBean(dicId);
        return ResEntity.success(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object saveDisplayDosageForm(SaveDisplayDosageForm tDisplayDosageFormVo) {
        List<TDisplayDosageCost> tDisplayDosageCostList = tDisplayDosageFormVo.getTDisplayDosageCostList();
        if (tDisplayDosageCostList == null || tDisplayDosageCostList.isEmpty()) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        tDisplayDosageCostList.forEach(tDisplayDosageCost -> {
            tDisplayDosageCost.setDosageCostId(IDUtil.getID());
            //校验字段
            if (StringUtils.isBlank(tDisplayDosageCost.getDicId())) {
                ExceptionUtils.throwErrorCustomRuntimeException("参数错误：dicId不能为空");
            }
            if (tDisplayDosageCost.getDicType() == null) {
                ExceptionUtils.throwErrorCustomRuntimeException("参数错误：dicType不能为空");
            }
            if (StringUtils.isBlank(tDisplayDosageCost.getCostCatalogueId())) {
                ExceptionUtils.throwErrorCustomRuntimeException("参数错误：costCatalogueId不能为空");
            }
            if (tDisplayDosageCost.getPricingMethod() == null) {
                ExceptionUtils.throwErrorCustomRuntimeException("参数错误：pricingMethod不能为空");
            }
            if (tDisplayDosageCost.getDoctorEdit() == null) {
                ExceptionUtils.throwErrorCustomRuntimeException("参数错误：doctorEdit不能为空");
            }
        });
        //删除旧的 数据 根据dicId和dicType
        tDisplayDosageCostMapper.deleteByDicIdAndDicType(tDisplayDosageCostList.get(0).getDicId(), tDisplayDosageCostList.get(0).getDicType());
        tDisplayDosageCostMapper.insertList(tDisplayDosageCostList);
        return ResEntity.success(tDisplayDosageCostList);
    }

    public Object getPharmacyDosageInfo(String displayId) {
        if (StringUtils.isBlank(displayId)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        TDisplayDosageP tDisplayDosageP = new TDisplayDosageP();
        List<TDisplayDosageForm> list = tDisplayDosageCostMapper.getPharmacyDosageInfo(displayId);
        if (list != null && !list.isEmpty())
        {
            list.forEach(tDisplayDosageForm -> {
                if (Constant.BASIC_STRING_ONE.equals(tDisplayDosageForm.getOutpatientOrHospitalization())) {
                    tDisplayDosageP.setOutpatient(tDisplayDosageForm);
                } else {
                    tDisplayDosageP.setHospitalization(tDisplayDosageForm);
                }
            });
        }
        if (tDisplayDosageP.getHospitalization() == null){
            TDisplayDosageForm tDisplayDosageForm = new TDisplayDosageForm();
            tDisplayDosageForm.setStatus(1);
            tDisplayDosageForm.setExpenseEntry(1);
            tDisplayDosageForm.setDisplayId(displayId);
            tDisplayDosageForm.setOutpatientOrHospitalization("2");
            tDisplayDosageP.setHospitalization(tDisplayDosageForm);
        }
        if (tDisplayDosageP.getOutpatient() == null){
            TDisplayDosageForm tDisplayDosageForm = new TDisplayDosageForm();
            tDisplayDosageForm.setStatus(1);
            tDisplayDosageForm.setExpenseEntry(1);
            tDisplayDosageForm.setDisplayId(displayId);
            tDisplayDosageForm.setOutpatientOrHospitalization("1");
            tDisplayDosageP.setOutpatient(tDisplayDosageForm);
        }
        return tDisplayDosageP;
    }

    public ResEntity updateOrInsert(TDisplayDosageP tDisplayDosageCost) {
        TDisplayDosageForm hospitalization = tDisplayDosageCost.getHospitalization();


        TDisplayDosageForm outpatient = tDisplayDosageCost.getOutpatient();
        if (null == hospitalization.getExpenseEntry() || null == hospitalization.getStatus()) {
            return ResEntity.error("请配置状态或费用录入入口");
        }
        if (null == outpatient.getExpenseEntry() || null == outpatient.getStatus()) {
            return ResEntity.error("请配置状态或费用录入入口");
        }
        if (StringUtils.isBlank(hospitalization.getDisplayId()) || StringUtils.isBlank(outpatient.getDisplayId())){
            return ResEntity.error("缺少displayId");
        }
        hospitalization.setCreateDate( new Date());
        outpatient.setCreateDate( new Date());
        outpatient.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        hospitalization.setCreateUser(AdminUtils.getCurrentHr().getUserId());
//删除旧的
        tDisplayDosageFormMapper.deleteByDisplayId(hospitalization.getDisplayId());
        tDisplayDosageFormMapper.deleteByDisplayId(outpatient.getDisplayId());
//        if (outpatient.getSetId() == null){
            outpatient.setSetId(IDUtil.getID());
            tDisplayDosageFormMapper.insert(outpatient);
//        }else {
//            tDisplayDosageFormMapper.updateByPrimaryKey(outpatient);
//        }
//        if (hospitalization.getSetId() == null){
            hospitalization.setSetId(IDUtil.getID());
            tDisplayDosageFormMapper.insert(hospitalization);
//        }else {
//            tDisplayDosageFormMapper.updateByPrimaryKey(hospitalization);
//        }

        return ResEntity.success(tDisplayDosageCost);

    }


    public Object getDosageDescribeInfo(GetDisplayDosageFormListByDicId dicId) {
        if (StringUtils.isBlank(dicId.getDicId()) || null == dicId.getDicType() ){
            return ResEntity.entity(false, "缺少参数", null);
        }
        TDisplayDosageDescribe info = tDisplayDosageDescribeMapper.getDosageDescribeInfo(dicId);
        if (info == null){
            info = new TDisplayDosageDescribe();
            info.setDicId(dicId.getDicId());
            info.setDicType(dicId.getDicType());
            //默认都是1
            info.setDailyDosageShow(1);
            info.setDailyDosageRequired(1);
            info.setDailyPreparationsShow(1);
            info.setDailyPreparationsRequired(1);
            info.setDailyProcessingShow(1);
            info.setDailyProcessingRequired(1);
            info.setEachTimeShow(1);
            info.setEachTimeRequired(1);

        }
        return ResEntity.success(info);
    }

    public Object saveDosageDescribeInfo(TDisplayDosageDescribe tDisplayDosageDescribe) {

        TDicBase objectById = dicBaseMapper.getObjectById(tDisplayDosageDescribe.getDicId());
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();


        StringJoiner extendDisplay = new StringJoiner(",");
        StringJoiner hideProject = new StringJoiner(",");
        StringJoiner required = new StringJoiner(",");
        if (tDisplayDosageDescribe.getDailyDosageShow() == 0){
            extendDisplay.add("dailyDosage");
        }else {
            hideProject.add("dailyDosage");
        }

        if (tDisplayDosageDescribe.getDailyPreparationsShow() == 0){
            extendDisplay.add("dailyPreparations");
        }else {
            hideProject.add("dailyPreparations");
        }

        if (tDisplayDosageDescribe.getDailyProcessingShow() == 0){
            extendDisplay.add("dailyProcessing");
        }else {
            hideProject.add("dailyProcessing");
        }

        if (tDisplayDosageDescribe.getEachTimeShow() == 0){
            extendDisplay.add("eachTime");
        }else {
            hideProject.add("eachTime");
        }

        if (tDisplayDosageDescribe.getDailyDosageRequired() == 0){
            required.add("dailyDosage");
        }
        if (tDisplayDosageDescribe.getDailyPreparationsRequired() == 0){
            required.add("dailyPreparations");
        }
        if (tDisplayDosageDescribe.getDailyProcessingRequired() == 0){
            required.add("dailyProcessing");
        }
        if (tDisplayDosageDescribe.getEachTimeRequired() == 0){
            required.add("eachTime");
        }

        stringObjectHashMap.put("extendDisplay", extendDisplay.toString());
        stringObjectHashMap.put("hideProject", hideProject.toString());
        stringObjectHashMap.put("required", required.toString());

        objectById.setOtherJson(JSON.toJSONString(stringObjectHashMap));
        dicBaseMapper.updateByPrimaryKey(objectById);
        if (StringUtils.isBlank(tDisplayDosageDescribe.getDosageDescribeId())){
            //新增
            tDisplayDosageDescribe.setDosageDescribeId(IDUtil.getID());
            tDisplayDosageDescribeMapper.insert(tDisplayDosageDescribe);
        }else {
            tDisplayDosageDescribeMapper.updateByPrimaryKey(tDisplayDosageDescribe);
        }
        return ResEntity.success(tDisplayDosageDescribe);
    }
}
