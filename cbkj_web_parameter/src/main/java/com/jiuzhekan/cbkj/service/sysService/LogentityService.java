package com.jiuzhekan.cbkj.service.sysService;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysmapper.LogentityMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(rollbackFor = Exception.class)
public class LogentityService {

    @Autowired
    private LogentityMapper logentityMapper;

    /**
     * 加载分页数据
     *
     * @param logentity
     * @param page
     * @return
     */
    public Object getPageDatas(Logentity logentity, Page page) {
        String sysIdByName = logentityMapper.getSysIdByName(logentity.getCreateName());
        logentity.setCreateId(sysIdByName);
        PageHelper.startPage(page.getPage(), page.getLimit());
        if(StringUtils.isNotBlank(logentity.getBeginTime())){
            logentity.setBeginTime(logentity.getBeginTime()+" 00:00:00");
        }
        if(StringUtils.isNotBlank(logentity.getEndTime())){
            logentity.setEndTime(logentity.getEndTime()+" 23:59:59");
        }
        List<Map<String, Object>> lis = logentityMapper.getPageDatas(logentity);
        for (Map map : lis) {
            String name = logentityMapper.getSysNameById((String) map.get("create_id"));
            map.put("createName", name);
        }
        Object result = Page.getLayUiTablePageData(lis);
        return result;
    }

    /**
     * 插入新数据
     *
     * @param logentity
     * @return
     * @throws Exception
     */
    public ResEntity insert(Logentity logentity) {

        long rows = logentityMapper.insert(logentity);
        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, logentity);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param logentity
     * @return
     */
    public ResEntity update(Logentity logentity) {

        long rows = logentityMapper.updateByPrimaryKey(logentity);

        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, null);
        }

        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        Logentity objM = logentityMapper.getObjectById(id);
        if (objM != null) {
            String name = logentityMapper.getSysNameById((objM.createId));
            objM.setCreateName(name);
        }
        return new ResEntity(true, Constant.SUCCESS_DX, objM);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = logentityMapper.deleteBylist(ids.split(","));

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);

    }

    /**
     * 标记异常日志
     *
     * @param ids
     * @return
     */
    public ResEntity changeStatus(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("okID", AdminUtils.getCurrentHr().getUserId());
        params.put("ids", ids.split(","));
        long rowsR = logentityMapper.changeStatus(params);

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }
}
