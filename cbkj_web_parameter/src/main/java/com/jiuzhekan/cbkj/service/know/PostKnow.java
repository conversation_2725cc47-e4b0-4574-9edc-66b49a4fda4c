package com.jiuzhekan.cbkj.service.know;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.business.knowledge.TInterfaceKnowledge;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.SHA1;
import com.jiuzhekan.cbkj.service.sysService.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2024/11/7 9:32
 */
@Service
@Slf4j
public class PostKnow {


    @Autowired
    private RedisService redisService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${address.know.interface.url}")
    private String knowInterfaceUrl;


    public ResEntity postKnow(String str) {

        Map<String, Object> map = new HashMap<>();
        map.put("pres", str);
        map.put("gravidity", "N");
        map.put("gender", "M");
        map.put("age", "29");
        map.put("showType", "2");
        map.put("disid", "");
        map.put("symid", "");
        ResEntity entity = null;
        try {
            entity = postKnow2("check/pres", map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResEntity.error("调知识库接口出错了！");
        }
        if (entity.getStatus()) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) entity.getData();
            log.info("知识经数据"+JSON.toJSONString(list));
            return ResEntity.success(list);
        }
        return entity;
    }


    public ResEntity postKnow2(String url, Map<String, Object> params) {
        TInterfaceKnowledge interfaceKnowledge = getInitInterfaceKnowledge();

        if (interfaceKnowledge == null) {
            log.error("知识库接口表配置错误，没有找到app_id:100001,app_pwd:100001的配置");
            return ResEntity.entity(false, "知识库接口表配置错误，没有找到app_id:100001,app_pwd:100001的配置", null);
        }

        if (interfaceKnowledge.getEffectiveTime() == null
                || interfaceKnowledge.getEffectiveTime().getTime() - System.currentTimeMillis() < 60 * 1000) {
            redisService.clearRedisCache("know::token", null);
            interfaceKnowledge = getInitInterfaceKnowledge();
        }
        params.put("app_id", Constant.KNOW_APP_ID);
        params.put("app_pwd", Constant.KNOW_APP_PWD);
        params.put("format", "json");
        params.put("token", interfaceKnowledge.getToken());
        params.put("timestamp", System.currentTimeMillis() + "");


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        params.forEach(map::add);
        map.add("sign", SHA1.getSign(map));
        HttpEntity<MultiValueMap<String, Object>> request1 = new HttpEntity<>(map, headers);

        log.info("【知识库接口】URL：{}, 参数：{}", interfaceKnowledge.getUrl() + url, map);
        long time1 = System.currentTimeMillis();
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(interfaceKnowledge.getUrl() + url, request1, JSONObject.class);
        long time2 = System.currentTimeMillis();
        log.info("【知识库接口耗时】：{}===ms", (time2 - time1));

        JSONObject result = response.getBody();
        if (result != null) {
            String code = result.getString("code");
            String message = result.getString("message");
            if ("0".equals(code)) {
                //成功
                Object obj = result.get("data");
               // log.info("obj====" + JSON.toJSONString(obj));
                return ResEntity.entity(true, Constant.SUCCESS_DX, obj);
            } else {
                //1002 token过期, 重新获取token
                redisService.clearRedisCache("know::token", null);
                interfaceKnowledge = getInitInterfaceKnowledge();
                //重新请求接口
                response = restTemplate.postForEntity(interfaceKnowledge.getUrl() + url, request1, JSONObject.class);
                result = response.getBody();
                if (result != null) {
                    code = result.getString("code");
                    message = result.getString("message");
                    if ("0".equals(code)) {
                        Object obj = result.get("data");
                        log.info("obj====" + JSON.toJSONString(obj));
                        return ResEntity.entity(true, Constant.SUCCESS_DX, obj);
                    } else {
                        log.error(message);
                    }
                }
            }

            return ResEntity.entity(false, message, null);
        }


        return ResEntity.entity(false, "【知识库】服务异常", null);
    }

    /**
     * 获取知识库接口
     *
     * <AUTHOR>
     * @date 2020/10/26
     */
    @Cacheable(value = "know::token", keyGenerator = "cacheKeyGenerator")
    public TInterfaceKnowledge getInitInterfaceKnowledge() {

        TInterfaceKnowledge interfaceKnowledge = new TInterfaceKnowledge();
        interfaceKnowledge.setAppId(Constant.KNOW_APP_ID);
        interfaceKnowledge.setAppPwd(Constant.KNOW_APP_PWD);
        interfaceKnowledge.setUrl(knowInterfaceUrl);

        String url = interfaceKnowledge.getUrl() + "token?app_id=" + interfaceKnowledge.getAppId()
                + "&app_pwd=" + interfaceKnowledge.getAppPwd();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(headers);

        try {
            log.info("restTemplate:know: " + url);
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(url, request, JSONObject.class);
            JSONObject result = response.getBody();
            if (result != null) {
                JSONObject data = result.getJSONObject("data");
                String token = data.getString("token");
                String effective = data.getString("effective");
                interfaceKnowledge.setToken(token);
                interfaceKnowledge.setEffectiveTime(DateUtil.getDateFormatd(effective, DateUtil.date1));
//                tInterfaceKnowledgeMapper.updateByPrimaryKey(interfaceKnowledge);
            }
        } catch (Exception rcx) {
            rcx.printStackTrace();
            log.error("知识库接口访问失败！ " + url);
        }
        return interfaceKnowledge;
    }
}
