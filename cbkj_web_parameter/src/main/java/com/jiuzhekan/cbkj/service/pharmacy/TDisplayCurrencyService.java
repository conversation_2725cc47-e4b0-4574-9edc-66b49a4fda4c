package com.jiuzhekan.cbkj.service.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayCurrency;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayCurrencyList;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayCurrencyMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class TDisplayCurrencyService {

    @Autowired
    private TDisplayCurrencyMapper tDisplayCurrencyMapper;

    /**
     * 查询通用配置信息
     *
     * @param displayId
     * @return
     */
    public Object getTDisplayCurrencySetting(String displayId) {
        TDisplayCurrency tDisplayCurrency = tDisplayCurrencyMapper.getTDisplayCurrency(displayId);
        if(tDisplayCurrency==null){
            tDisplayCurrency=new TDisplayCurrency(null,displayId,Constant.BASIC_STRING_ZERO,Constant.BASIC_STRING_ZERO,new Date(),AdminUtils.getCurrentHr().getUsername(),0,1,0);
        }
        return ResEntity.entity(true, "", tDisplayCurrency);
    }

    /**
     * @param tDisplayCurrency
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity saveTDisplayCurrencySetting(TDisplayCurrency tDisplayCurrency) {
        String displayId = tDisplayCurrency.getDisplayId();
        tDisplayCurrencyMapper.deleteByDisplayId(displayId);
        tDisplayCurrency.setDisplayCurrencyId(IDUtil.getID());
        tDisplayCurrency.setCreateDate(new Date());
        tDisplayCurrencyMapper.insert(tDisplayCurrency);
        return ResEntity.entity(true, "", tDisplayCurrency);
    }


    @Transactional(rollbackFor = Exception.class)
    public ResEntity saveTDisplayCurrencySettingList(TDisplayCurrencyList tDisplayCurrencyList) {
        String[] split = new String[0];
        if (StringUtils.isNotBlank(tDisplayCurrencyList.getPharmacyDisplayList())){
            split = tDisplayCurrencyList.getPharmacyDisplayList().split(",");
        }
        List<String> displayList = Arrays.asList(split);
        tDisplayCurrencyMapper.deleteBylistByDisplayId(displayList);
        TDisplayCurrency tDisplayCurrency = tDisplayCurrencyList.getTDisplayCurrency();
        ArrayList<TDisplayCurrency> tDisplayCurrencies = new ArrayList<>();
        for (String s : displayList) {
            TDisplayCurrency tDisplayCurrency1 = new TDisplayCurrency(IDUtil.getID(), s, tDisplayCurrency.getWithholdSwitch(), tDisplayCurrency.getPreStockSwitch(), new Date(), AdminUtils.getCurrentHr().getUsername(), tDisplayCurrency.getManySpeSwitch(),tDisplayCurrency.getOralMedicationTimeSwitch(),tDisplayCurrency.getUrgentSign());
            tDisplayCurrencies.add(tDisplayCurrency1);
        }
        int i = tDisplayCurrencyMapper.insertList(tDisplayCurrencies);
        if (i > Constant.ADMIN_EXT_ZERO) {
            return ResEntity.entity(true, "", i);
        }
        return ResEntity.entity(false, "批量插入失败", null);

    }
}
