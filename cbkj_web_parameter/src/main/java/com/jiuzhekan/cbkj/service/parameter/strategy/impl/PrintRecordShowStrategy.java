package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.service.parameter.strategy.NormalParamStrategy;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service("PRINT_RECORD_SHOW")
public class PrintRecordShowStrategy implements NormalParamStrategy {
    @Override
    public void getParam() {

    }

    @Override
    public void setParam(DiagnosisParamNewVO tSysParamVO, Object v) {
// 多选，特殊处理。保存的值，不是选项值。
        //1显示 0不显示（英文逗号, 分隔）
        //   - 第一位： 中医电子病历；（默认值 不显示）
        //   - 第二位： 今日病人/我的历史病历/病历管理-查看订单 （默认值 显示）
//        if (!StringUtils.isBlank(v.toString())) {
//            String[] split = v.toString().split(",");
//            String a = "0";
//            String b = "0";
//            for (String s : split) {
//                if ("1".equals(s)) {
//                    a = "1";
//                }
//                if ("2".equals(s)) {
//                    b = "1";
//                }
//            }
//            tSysParamVO.setParValues(a + "," + b);
//        } else {
//            tSysParamVO.setParValues("0,0");
//        }
    }
}
