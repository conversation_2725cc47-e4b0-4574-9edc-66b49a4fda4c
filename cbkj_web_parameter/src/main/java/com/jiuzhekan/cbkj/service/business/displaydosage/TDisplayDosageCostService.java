package com.jiuzhekan.cbkj.service.business.displaydosage;


import com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageCost;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageCostMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class TDisplayDosageCostService {

    @Autowired
    private TDisplayDosageCostMapper tDisplayDosageCostMapper;

    /**
     * 加载分页数据
     *
     * @param tDisplayDosageCost 药房应用配置-制剂费用绑定表（字典中）
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2025-08-06
     */
    public Object getPageDatas(TDisplayDosageCost tDisplayDosageCost, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDisplayDosageCost> list = tDisplayDosageCostMapper.getPageListByObj(tDisplayDosageCost);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param dosageCostId 药房应用配置-制剂费用绑定表（字典中）
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    public ResEntity findObj(String dosageCostId) {

        if (StringUtils.isBlank(dosageCostId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TDisplayDosageCost tDisplayDosageCost = tDisplayDosageCostMapper.getObjectById(dosageCostId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDisplayDosageCost);
    }


    /**
     * 插入新数据
     *
     * @param tDisplayDosageCost 药房应用配置-制剂费用绑定表（字典中）
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDisplayDosageCost tDisplayDosageCost){

        tDisplayDosageCost.setDosageCostId(IDUtil.getID());
        long rows = tDisplayDosageCostMapper.insert(tDisplayDosageCost);

        return ResEntity.success(tDisplayDosageCost);
    }


    /**
     * 修改
     *
     * @param tDisplayDosageCost 药房应用配置-制剂费用绑定表（字典中）
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDisplayDosageCost tDisplayDosageCost) {

        long rows = tDisplayDosageCostMapper.updateByPrimaryKey(tDisplayDosageCost);

        return ResEntity.success(tDisplayDosageCost);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tDisplayDosageCostMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
