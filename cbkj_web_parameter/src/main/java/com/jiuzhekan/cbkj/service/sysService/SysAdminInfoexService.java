package com.jiuzhekan.cbkj.service.sysService;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysAdminInfoexMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SysAdminInfoexService {

    @Autowired
    private SysAdminInfoexMapper sysAdminInfoexMapper;

    /**
     * 加载分页数据
     *
     * @param sysAdminInfoex 用户扩展表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-22
     */
    public Object getPageDatas(SysAdminInfoex sysAdminInfoex, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysAdminInfoex> list = sysAdminInfoexMapper.getPageListByObj(sysAdminInfoex);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param adminInfoexId 用户扩展表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-22
     */
    public ResEntity findObj(String adminInfoexId) {

        if (StringUtils.isBlank(adminInfoexId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        SysAdminInfoex sysAdminInfoex = sysAdminInfoexMapper.getObjectById(adminInfoexId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysAdminInfoex);
    }


    /**
     * 插入新数据
     *
     * @param sysAdminInfoex 用户扩展表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-22
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(SysAdminInfoex sysAdminInfoex){

        //sysAdminInfoex.setAdminInfoexId(IDUtil.getID());
        long rows = sysAdminInfoexMapper.insert(sysAdminInfoex);

        return ResEntity.success(sysAdminInfoex);
    }


    /**
     * 修改
     *
     * @param sysAdminInfoex 用户扩展表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-22
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(SysAdminInfoex sysAdminInfoex) {

        long rows = sysAdminInfoexMapper.updateByPrimaryKey(sysAdminInfoex);

        return ResEntity.success(sysAdminInfoex);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-22
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = sysAdminInfoexMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
