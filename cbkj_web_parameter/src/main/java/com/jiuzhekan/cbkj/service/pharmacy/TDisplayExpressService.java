package com.jiuzhekan.cbkj.service.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayExpress;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting;
import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayExpressP;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayExpressMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayMoneySettingMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TDisplayExpressService {

    private final TDisplayExpressMapper tDisplayExpressMapper;
    private final TDisplayMoneySettingMapper tDisplayMoneySettingMapper;
    private final TDisplayMoneySettingService tDisplayMoneySettingService;

    @Autowired
    TDisplayExpressService(TDisplayExpressMapper tDisplayExpressMapper, TDisplayMoneySettingMapper tDisplayMoneySettingMapper, TDisplayMoneySettingService tDisplayMoneySettingService) {

        this.tDisplayExpressMapper = tDisplayExpressMapper;
        this.tDisplayMoneySettingMapper = tDisplayMoneySettingMapper;
        this.tDisplayMoneySettingService = tDisplayMoneySettingService;
    }

    /**
     * 加载某条数据
     *
     * @param displayId 药房配置配送信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    public ResEntity findObj(String displayId) {
        TDisplayExpressP tDisplayProductionP = new TDisplayExpressP();
        String ZERO = Constant.BASIC_STRING_ZERO;
        String ONE = Constant.BASIC_STRING_ONE;
        //门诊
        TDisplayExpress outpatient = new TDisplayExpress(displayId, Constant.BASIC_STRING_ONE, ZERO, ONE, ONE, ZERO);
        //住院
        TDisplayExpress hospitalization = new TDisplayExpress(displayId, Constant.BASIC_STRING_TWO, ZERO, ONE, ONE, ZERO);
        if (!StringUtils.isBlank(displayId)) {
            //存在则覆盖。
            TDisplayExpress outpatient2 = tDisplayExpressMapper.getByDisplayId(new TDisplayExpress(displayId, Constant.BASIC_STRING_ONE));
            if (null != outpatient2) {
                outpatient = outpatient2;
            }
            TDisplayExpress hospitalization2 = tDisplayExpressMapper.getByDisplayId(new TDisplayExpress(displayId, Constant.BASIC_STRING_TWO));
            if (null != hospitalization2) {
                hospitalization = hospitalization2;
            }
        }
        //门诊：配置默认费用配置参数-通用
        //List<TDisplayMoneySetting> psfx = tDisplayMoneySettingMapper.getByDicCode(new TDisplayMoneySetting(StringUtils.isBlank(outpatient.getSetId()) ? IDUtil.getID() : outpatient.getSetId(), Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE, "psfx", null, null));

        //住院：配置默认费用配置参数
        //List<TDisplayMoneySetting> psfx2 = tDisplayMoneySettingMapper.getByDicCode(new TDisplayMoneySetting(StringUtils.isBlank(hospitalization.getSetId()) ? IDUtil.getID() : hospitalization.getSetId(), Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE, "psfx", null, null));
//        setDefault(psfx, Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE);
//        setDefault(psfx2, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE);
        //        outpatient.setShowDefaultList(psfx);
//        hospitalization.setShowDefaultList(psfx2);

        TDisplayMoneySetting defaultMoneySetting = new TDisplayMoneySetting(null, Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE, "00000000", "不限制", ZERO);
        TDisplayMoneySetting defaultMoneySetting2 = new TDisplayMoneySetting(null, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE, "00000000", "不限制", ZERO);
        ArrayList<TDisplayMoneySetting> list = new ArrayList<>();
        list.add(defaultMoneySetting);
        outpatient.setShowDefaultList(list);
        ArrayList<TDisplayMoneySetting> list2 = new ArrayList<>();
        list2.add(defaultMoneySetting2);
        hospitalization.setShowDefaultList(list2);
        if (!StringUtils.isBlank(outpatient.getSetId())) {
            //门诊-通用制膏
            TDisplayMoneySetting tDisplayMoneySetting = new TDisplayMoneySetting(outpatient.getSetId(), Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE, null, null, null);
            tDisplayMoneySetting.setDicId("00000000");
            List<TDisplayMoneySetting> pageListByObj = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting);
            if (pageListByObj.size() > 0) {
                outpatient.setShowDefaultList(pageListByObj);
            }
        }

        //从数据库获取通用的。
        if (!StringUtils.isBlank(hospitalization.getSetId())) {
            //住院-通用制膏
            TDisplayMoneySetting tDisplayMoneySetting = new TDisplayMoneySetting(hospitalization.getSetId(), Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_THREE, Constant.BASIC_STRING_ONE, null, null, null);
            tDisplayMoneySetting.setDicId("00000000");
            List<TDisplayMoneySetting> pageListByObj22 = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting);
            if (pageListByObj22.size() > 0) {
                hospitalization.setShowDefaultList(pageListByObj22);
            }

        }


        //设置进去
        tDisplayProductionP.setOutpatient(outpatient);
        tDisplayProductionP.setHospitalization(hospitalization);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDisplayProductionP);
    }

    public void setDefault(List<TDisplayMoneySetting> pageListByObj, String outpatientOrHospitalization
            , String decoctionOrProductionExpress, String currencyOrFormula) {
        for (TDisplayMoneySetting tDisplayMoneySetting : pageListByObj) {
            if (StringUtils.isBlank(tDisplayMoneySetting.getId())) {
                tDisplayMoneySetting.setOutpatientOrHospitalization(outpatientOrHospitalization);
                tDisplayMoneySetting.setDecoctionOrProductionExpress(decoctionOrProductionExpress);
                tDisplayMoneySetting.setCurrencyOrFormula(currencyOrFormula);
            }
        }
    }

    /**
     * 修改
     *
     * @param tDisplayExpressP 药房配置配送信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateOrInsert(TDisplayExpressP tDisplayExpressP) {
        TDisplayExpress outpatient = tDisplayExpressP.getOutpatient();
        TDisplayExpress hospitalization = tDisplayExpressP.getHospitalization();
        if (StringUtils.isBlank(outpatient.getDisplayId()) || StringUtils.isBlank(hospitalization.getDisplayId())) {
            return ResEntity.error("缺少displayId");
        }
        if (Constant.BASIC_STRING_ZERO.equals(outpatient.getShowExpress())) {
            boolean b = outpatient.getShowDefaultList().stream().anyMatch(tDisplayMoneySetting -> tDisplayMoneySetting.getIsShowSetting().equals(Constant.BASIC_STRING_ZERO));
            if (!b) {
                return ResEntity.entity(false, "请勾选配送费用配置", tDisplayExpressP);
            }
        }
        if (Constant.BASIC_STRING_ZERO.equals(hospitalization.getShowExpress())) {
            boolean b = hospitalization.getShowDefaultList().stream().anyMatch(tDisplayMoneySetting -> tDisplayMoneySetting.getIsShowSetting().equals(Constant.BASIC_STRING_ZERO));
            if (!b) {
                return ResEntity.entity(false, "请勾选配送费用配置", tDisplayExpressP);
            }
        }
        if (!StringUtils.isBlank(outpatient.getDisplayId())) {
            tDisplayExpressMapper.deleteByCondition(outpatient);
        }
//        if (StringUtils.isBlank(outpatient.getSetId())) {
            //插入制膏的门诊和住院信息
            outpatient.setSetId(IDUtil.getID());
            outpatient.initDefaultValue();
            tDisplayExpressMapper.insert(outpatient);
//        } else {
//            tDisplayExpressMapper.updateByPrimaryKey(outpatient);
//        }
        if (!StringUtils.isBlank(hospitalization.getDisplayId())) {
            tDisplayExpressMapper.deleteByCondition(hospitalization);
        }
//        if (StringUtils.isBlank(hospitalization.getSetId())) {
            //插入制膏的门诊和住院信息
            hospitalization.setSetId(IDUtil.getID());
            hospitalization.initDefaultValue();
            tDisplayExpressMapper.insert(hospitalization);
//        } else {
//            tDisplayExpressMapper.updateByPrimaryKey(hospitalization);
//        }

        //处理制膏的门诊和住院。费用配置信息，
        tDisplayMoneySettingService.saveExpressSetting(outpatient, outpatient.getSetId());
        tDisplayMoneySettingService.saveExpressSetting(hospitalization, hospitalization.getSetId());
//        tDisplayMoneySettingMapper.insert()
        return ResEntity.success(tDisplayExpressP);
    }


}
