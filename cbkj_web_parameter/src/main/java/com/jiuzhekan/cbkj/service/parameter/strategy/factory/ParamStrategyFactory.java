package com.jiuzhekan.cbkj.service.parameter.strategy.factory;

import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
public class ParamStrategyFactory {

    Map<String, ParamStrategy> bases;

    @Autowired
    public ParamStrategyFactory(Map<String, ParamStrategy> bases) {
        Assert.notNull(bases, "ParamStrategy must not be null!");
        this.bases = bases;
    }

    public ParamStrategy getParamStrategy(String type) {
        if (
                "PRESCRIPTION_EXTERNAL_COLUMN".equals(type) ||
                        "PRESCRIPTION_INTERNAL_COLUMN".equals(type) ||
                        "PRESCRIPTION_PATENT_COLUMN".equals(type) ||
                        "PRESCRIPTION_PREPARATION_COLUMN".equals(type) ||
                        "PRESCRIPTION_ACUPOINT_COLUMN".equals(type)||
                "SUITABLE_TECHNICAL_DISPLAY_ITEMS".equals(type)
        ) {
            type = "PRESCRIPTION_EXTERNAL_COLUMN";
        }
        else if ("PRESCRIPTION_INTERNAL_HIGHEST_MONEY".equals(type) ||
                "PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS".equals(type)) {
            type = "PRESCRIPTION_INTERNAL_HIGHEST_MONEY";
        }else if("PRESCRIPTION_SEARCH_RECIPE_OPTIONS_2".equals(type)){
            type = "PRESCRIPTION_SEARCH_RECIPE_OPTIONS";
        } else if ("SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME".equals(type)) {
            type = "PRESCRIPTION_LIMIT_POINT";
        } else if ("SYS_CHECK_WORK_TIME".equals(type)) {
            type = "SYS_CHECK_WORK_TIME";
        }
        if (null == bases.get(type)){
            return bases.get("otherStrategy");
        }
        return bases.get(type);
    }
}
