package com.jiuzhekan.cbkj.service.business;

import com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemDetailListByChargeItemId;
import com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemList;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetail;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetailDept;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.TChargeItemDetailDeptMapper;
import com.jiuzhekan.cbkj.mapper.business.TChargeItemDetailMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Date;
import java.util.stream.Collectors;

@Service
public class TChargeItemDetailService {


    private final TChargeItemDetailMapper tChargeItemDetailMapper;
    private final TChargeItemDetailDeptMapper tChargeItemDetailDeptMapper;

    public TChargeItemDetailService(TChargeItemDetailMapper tChargeItemDetailMapper, TChargeItemDetailDeptMapper tChargeItemDetailDeptMapper) {
        this.tChargeItemDetailMapper = tChargeItemDetailMapper;
        this.tChargeItemDetailDeptMapper = tChargeItemDetailDeptMapper;
    }

    /**
     * 加载分页数据
     *
     * @param tChargeItemDetail 收费项目管理明细
     * @param page              分页
     * @return Object
     * <AUTHOR>
     * @date 2025-08-05
     */
    public Object getPageDatas(TChargeItemDetail tChargeItemDetail, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TChargeItemDetail> list = tChargeItemDetailMapper.getPageListByObj(tChargeItemDetail);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param chargeItemDetailId 收费项目管理明细
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    public ResEntity findObj(String chargeItemDetailId) {

        if (StringUtils.isBlank(chargeItemDetailId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TChargeItemDetail tChargeItemDetail = tChargeItemDetailMapper.getObjectById(chargeItemDetailId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tChargeItemDetail);
    }


    /**
     * 根据收费项目ID获取详情列表
     *
     * @param chargeItemId 收费项目ID
     * @return ResEntity
     */
    public Object getChargeItemDetailList(GetChargeItemList getChargeItemList) {
        try {

//            PageHelper.startPage(getChargeItemList.getPage(), getChargeItemList.getLimit());
            //计算总数
            Integer i = tChargeItemDetailMapper.countDetailsByGetChargeItemList(getChargeItemList);
            //计算是否有下一页
            boolean hasNext = i > getChargeItemList.getPage()*getChargeItemList.getLimit() ;
            List<TChargeItemDetail> list = tChargeItemDetailMapper.getDetailsByGetChargeItemList(getChargeItemList);
            if (list != null && !list.isEmpty()){

                for (TChargeItemDetail tChargeItemDetail : list) {
                    List<TChargeItemDetailDept> itemDetailDepts = tChargeItemDetail.getItemDetailDepts();
                    if (itemDetailDepts != null && !itemDetailDepts.isEmpty()){
                        String collect = itemDetailDepts.stream().map(TChargeItemDetailDept::getDeptName).collect(Collectors.joining(","));
                        tChargeItemDetail.setDeptNames(collect);
                    }
                }
            }
//            Object layUiTablePageData = Page.getLayUiTablePageData(list);
//            PageHelper.clearPage();
            Object layuiData = Page.getLayuiData(true, "获取收费项目列表成功", i, hasNext, list);
            return layuiData;
        } catch (Exception e) {
            return ResEntity.error("获取收费项目详情列表失败：" + e.getMessage());
        }
    }

    /**
     * 新增收费项目详情
     *
     * @param tChargeItemDetail 收费项目详情信息
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertChargeItemDetail(TChargeItemDetail tChargeItemDetail) {
        if (tChargeItemDetail == null) {
            return ResEntity.error("收费项目详情信息不能为空");
        }

        if (StringUtils.isBlank(tChargeItemDetail.getChargeItemId())) {
            return ResEntity.error("收费项目ID不能为空");
        }

        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailName())) {
            return ResEntity.error("项目名称不能为空");
        }

        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailCode())) {
            return ResEntity.error("收费编码不能为空");
        }
        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailType())) {
            return ResEntity.error("itemDetailType不能为空");
        }
        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailTypeName())) {
            return ResEntity.error("itemDetailTypeName不能为空");
        }
        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailSingleUnit())) {
            return ResEntity.error("itemDetailSingleUnit不能为空");
        }
        //单价不能为空
        if (tChargeItemDetail.getItemDetailSinglePrice() == null) {
            return ResEntity.error("itemDetailSinglePrice不能为空");
        }
        //拼音和五笔不能为空
        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailPy()) || StringUtils.isBlank(tChargeItemDetail.getItemDetailWb())) {
            return ResEntity.error("itemDetailPy或itemDetailWb不能为空");
        }

        // 检查编码是否重复
        if (tChargeItemDetailMapper.checkDetailCodeExists(tChargeItemDetail.getItemDetailCode()) > 0) {
            return ResEntity.error("收费编码已存在");
        }
        //itemDetailStatus不能为空
        if (tChargeItemDetail.getItemDetailStatus() == null) {
            return ResEntity.error("itemDetailStatus不能为空");
        }

        // 验证科室数据
        String deptValidationResult = validateChargeItemDetailDepts(tChargeItemDetail);
        if (deptValidationResult != null) {
            return ResEntity.error(deptValidationResult);
        }

        AdminInfo currentHr = AdminUtils.getCurrentHr();

        // 设置默认值
        tChargeItemDetail.setChargeItemDetailId(IDUtil.getID());
        tChargeItemDetail.setItemDetailStatus(0); // 0启用
        tChargeItemDetail.setCreateTime(new Date());
        tChargeItemDetail.setCreateUserId(currentHr.getUserId());
        tChargeItemDetail.setCreateUserName(currentHr.getNameZh());

        int rows = tChargeItemDetailMapper.insert(tChargeItemDetail);
        if (rows > 0) {
            List<TChargeItemDetailDept> itemDetailDepts = prepareChargeItemDetailDepts(tChargeItemDetail);
            if (itemDetailDepts != null && !itemDetailDepts.isEmpty()) {
                tChargeItemDetailDeptMapper.insertList(itemDetailDepts);
            }
            return ResEntity.success(tChargeItemDetail);
        } else {
            throw new RuntimeException("新增收费项目详情失败");
        }
    }

    /**
     * 验证收费项目详情的科室数据
     *
     * @param tChargeItemDetail 收费项目详情
     * @return 验证错误信息，如果验证通过返回null
     */
    private String validateChargeItemDetailDepts(TChargeItemDetail tChargeItemDetail) {
        List<TChargeItemDetailDept> itemDetailDepts = tChargeItemDetail.getItemDetailDepts();
        if (itemDetailDepts != null && !itemDetailDepts.isEmpty()) {
            for (TChargeItemDetailDept itemDetailDept : itemDetailDepts) {
                if (StringUtils.isBlank(itemDetailDept.getDeptId()) || StringUtils.isBlank(itemDetailDept.getDeptName())) {
                    return "科室ID和名称不能为空";
                }
            }
        }
        return null;
    }

    /**
     * 准备收费项目详情的科室数据
     *
     * @param tChargeItemDetail 收费项目详情
     * @return 准备好的科室数据列表
     */
    private List<TChargeItemDetailDept> prepareChargeItemDetailDepts(TChargeItemDetail tChargeItemDetail) {
        List<TChargeItemDetailDept> itemDetailDepts = tChargeItemDetail.getItemDetailDepts();
        if (itemDetailDepts != null && !itemDetailDepts.isEmpty()) {
            itemDetailDepts.forEach(itemDetailDept -> {
                itemDetailDept.setChargeItemDetailId(tChargeItemDetail.getChargeItemDetailId());
            });
        }
        return itemDetailDepts;
    }

    /**
     * 修改收费项目详情
     *
     * @param tChargeItemDetail 收费项目详情信息
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateChargeItemDetail(TChargeItemDetail tChargeItemDetail) {
        if (tChargeItemDetail == null || StringUtils.isBlank(tChargeItemDetail.getChargeItemDetailId())) {
            return ResEntity.error("收费项目详情ID不能为空");
        }

        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailName())) {
            return ResEntity.error("项目名称不能为空");
        }

        if (StringUtils.isBlank(tChargeItemDetail.getItemDetailCode())) {
            return ResEntity.error("收费编码不能为空");
        }

        // 检查编码是否重复（排除自己）
        if (tChargeItemDetailMapper.checkDetailCodeExistsExcludeSelf(
                tChargeItemDetail.getItemDetailCode(),
                tChargeItemDetail.getChargeItemDetailId()) > 0) {
            return ResEntity.error("收费编码已存在");
        }

        // 验证科室数据
        String deptValidationResult = validateChargeItemDetailDepts(tChargeItemDetail);
        if (deptValidationResult != null) {
            return ResEntity.error(deptValidationResult);
        }

        AdminInfo currentHr = AdminUtils.getCurrentHr();

        tChargeItemDetail.setUpdateTime(new Date());
        tChargeItemDetail.setUpdateUserId(currentHr.getUserId());
        tChargeItemDetail.setUpdateUserName(currentHr.getNameZh());

        int rows = tChargeItemDetailMapper.updateByPrimaryKey(tChargeItemDetail);
        if (rows > 0) {
            //先删除旧的数据TChargeItemDetailDept
            tChargeItemDetailDeptMapper.deleteBylist(new String[]{tChargeItemDetail.getChargeItemDetailId()});
            List<TChargeItemDetailDept> itemDetailDepts = prepareChargeItemDetailDepts(tChargeItemDetail);
            if (itemDetailDepts != null && !itemDetailDepts.isEmpty()) {
                tChargeItemDetailDeptMapper.insertList(itemDetailDepts);
            }
            return ResEntity.success(tChargeItemDetail);
        } else {
            throw new RuntimeException("修改收费项目详情失败");
        }
    }

    /**
     * 删除收费项目详情
     *
     * @param chargeItemDetailId 收费项目详情ID
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteChargeItemDetail(String chargeItemDetailId) {
        if (StringUtils.isBlank(chargeItemDetailId)) {
            return ResEntity.error("收费项目详情ID不能为空");
        }

        // 检查收费项目详情是否存在
        TChargeItemDetail existDetail = tChargeItemDetailMapper.getObjectById(chargeItemDetailId);
        if (existDetail == null) {
            return ResEntity.error("收费项目详情不存在");
        }

        int rows = tChargeItemDetailMapper.deleteByPrimaryKey(existDetail);
        if (rows > 0) {
            // 同时删除相关的科室映射数据
            tChargeItemDetailDeptMapper.deleteBylist(new String[]{chargeItemDetailId});
            return ResEntity.success("删除成功");
        } else {
            throw new RuntimeException("删除收费项目详情失败");
        }
    }

    /**
     * @param chargeItemId
     * @return
     * @descript 根据收费目录id或者关键字获取目录下的所有收费项目列表
     */
    public ResEntity getChargeItemDetailListByChargeItemId(GetChargeItemDetailListByChargeItemId chargeItemId) {
        //入参字段不能都为空
        if (StringUtils.isBlank(chargeItemId.getChargeItemId()) && StringUtils.isBlank(chargeItemId.getSearchKey())) {
            return ResEntity.error("参数不能都为空");
        }
        List<TChargeItemDetail> list = tChargeItemDetailMapper.getChargeItemDetailListByChargeItemId(chargeItemId);
        return ResEntity.success(list);
    }
}
