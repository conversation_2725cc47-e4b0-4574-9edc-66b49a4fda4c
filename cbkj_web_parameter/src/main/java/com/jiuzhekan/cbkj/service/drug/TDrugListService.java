package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.TDrugList;
import com.jiuzhekan.cbkj.beans.drug.TDrugListVo;
import com.jiuzhekan.cbkj.beans.drug.TMaterial;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.drug.TDrugListMapper;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.drug.TMaterialMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class TDrugListService {

    @Autowired
    private TDrugListMapper tDrugListMapper;

    @Autowired
    private TMaterialMapper tMaterialMapper;

    public Object getPageDatas(TDrugList tDrugList, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDrugList> list = tDrugListMapper.getDrugPageList(tDrugList);
        return Page.getLayUiTablePageData(list);
    }

    public Object getPageDatas2(TDrugList tDrugList) {
        List<TDrugList> list = tDrugListMapper.getPageListByObj2(tDrugList);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 字典管理药房管理获取药品目录
     * @param tDrugList
     * @return
     */
    public Object getPageDatas3(TDrugList tDrugList) {
        List<TDrugList> list = tDrugListMapper.getPageListByObj2(tDrugList);
        return ResEntity.entity(true,"",list);
    }
    /**
     * 加载某条数据
     *
     * @param drugId 药品目录主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String drugId) {

        if (StringUtils.isBlank(drugId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TDrugList tDrugList = tDrugListMapper.getObjectById(drugId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDrugList);
    }


    /**
     * 插入新数据
     *
     * @param tDrugListVo 药品目录主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDrugListVo tDrugListVo) {
        if (StringUtils.isBlank(tDrugListVo.getDrugCode())) {
            return ResEntity.error("缺少药品目录代码");
        }
        TDrugList drugByDrugCode = tDrugListMapper.getDrugByDrugCode(tDrugListVo.getDrugCode());
        if (null != drugByDrugCode) {
            return ResEntity.error("药品目录代码已存在");
        }
        TDrugList tDrugList = new TDrugList();

        
        tDrugList.setDrugId(IDUtil.getID());
        tDrugList.setDrugCode(tDrugListVo.getDrugCode());
        tDrugList.setDrugName(tDrugListVo.getDrugName());
        tDrugList.setDrugDesc(tDrugListVo.getDrugDesc());
        tDrugList.setDrugType(tDrugListVo.getDrugType());
        if (StringUtils.isNotBlank(tDrugListVo.getHisId())) {
            tDrugList.setHisId(tDrugListVo.getHisId());
        }
        tDrugList.setStatus("0");
        tDrugList.setCreateDate(new Date());
        tDrugList.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        tDrugList.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        try {
            long rows = tDrugListMapper.insert(tDrugList);
        } catch (Exception e) {
            return ResEntity.error("缺少参数");
        }
        return ResEntity.success(tDrugList);
    }


    /**
     * 修改
     *
     * @param tDrugListVo 药品目录主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDrugListVo tDrugListVo) {
        TDrugList tDrugList = new TDrugList();
        tDrugList.setDrugId(tDrugListVo.getDrugId());
        tDrugList.setDrugCode(tDrugListVo.getDrugCode());
        tDrugList.setDrugName(tDrugListVo.getDrugName());
        tDrugList.setDrugType(tDrugListVo.getDrugType());
        tDrugList.setDrugDesc(tDrugListVo.getDrugDesc());
        tDrugList.setHisId(tDrugListVo.getHisId());
        try {
            long rows = tDrugListMapper.updateByPrimaryKey(tDrugList);
        } catch (Exception e) {
            return ResEntity.error("更新失败");
        }
        TDrugList objectById = tDrugListMapper.getObjectById(tDrugListVo.getDrugId());
        return ResEntity.success(objectById);
    }

    /**
     * 删除
     *
     * @param id
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        //根据药品目录获取药品，用于判断该药品目录能否删除
/*        List<TMaterial> materialByDrugId = tMaterialMapper.getMaterialByDrugId(id);
        if(materialByDrugId.size()>0){
            return ResEntity.entity(false,"该药品目录所属药品信息未删除",null);
        }*/
        long rowsR = tDrugListMapper.deleteByDrugId(id);

        return ResEntity.success(rowsR);
    }

}
