package com.jiuzhekan.cbkj.service.parameter.strategy.factory;

import com.jiuzhekan.cbkj.service.parameter.strategy.NormalParamStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class NormalParamStrategyFactory {

    Map<String, NormalParamStrategy> bases;

    @Autowired
    public NormalParamStrategyFactory(Map<String, NormalParamStrategy> bases) {
        Assert.notNull(bases, "NormalParamStrategyFactory must not be null!");
        this.bases = bases;
    }

    public NormalParamStrategy getNormalParamStrategy(String type) {
        if (null == bases.get(type)){
            //如果不存在返回一个默认。
            return bases.get("otherNormalStrategy");
        }
        return bases.get(type);
    }
}
