package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.BMatSynonym;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.drug.BMatSynonymMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class BMatSynonymService {

    @Autowired
    private BMatSynonymMapper bMatSynonymMapper;

    /**
     * 加载分页数据
     *
     * @param bMatSynonym 知识库药品别名
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-20
     */
    public Object getPageDatas(BMatSynonym bMatSynonym, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<BMatSynonym> list = bMatSynonymMapper.getPageListByObj(bMatSynonym);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param matSynId 知识库药品别名
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    public ResEntity findObj(String matSynId) {

        if (StringUtils.isBlank(matSynId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        BMatSynonym bMatSynonym = bMatSynonymMapper.getObjectById(matSynId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, bMatSynonym);
    }


    /**
     * 插入新数据
     *
     * @param bMatSynonym 知识库药品别名
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(BMatSynonym bMatSynonym){

        bMatSynonym.setMatSynId(IDUtil.getID());
        long rows = bMatSynonymMapper.insert(bMatSynonym);

        return ResEntity.success(bMatSynonym);
    }


    /**
     * 修改
     *
     * @param bMatSynonym 知识库药品别名
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(BMatSynonym bMatSynonym) {

        long rows = bMatSynonymMapper.updateByPrimaryKey(bMatSynonym);

        return ResEntity.success(bMatSynonym);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-20
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = bMatSynonymMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
