package com.jiuzhekan.cbkj.service.pharmacy;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.pharmacy.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.DisplayMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.DisplayMappingMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.PharmacyMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
/**
 * <AUTHOR>
 */
@Slf4j
public class PharmacyService {


    private PharmacyMapper pharmacyMapper;


    private DisplayMapper displayMapper;


    private DisplayMappingMapper displayMappingMapper;

    private DicBaseMapper dicBaseMapper;

    @Autowired
    PharmacyService(PharmacyMapper pharmacyMapper,
                    DisplayMapper displayMapper,
                    DisplayMappingMapper displayMappingMapper,
                    DicBaseMapper dicBaseMapper) {
        this.pharmacyMapper = pharmacyMapper;
        this.displayMapper = displayMapper;
        this.displayMappingMapper = displayMappingMapper;
        this.dicBaseMapper = dicBaseMapper;
    }

    /**
     * 加载分页数据
     *
     * @param tPharmacy 药房表
     * @param page      分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-13
     */
    public Object getPageDatas(TPharmacy tPharmacy, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPharmacy> list = pharmacyMapper.getPageListByObj(tPharmacy);
        list.stream().forEach(Pharmacy -> {
            if (Pharmacy.getIsAlter() > 0) {
                Pharmacy.setIsAlter(1);
            } else {
                Pharmacy.setIsAlter(0);
            }
        });
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 获取上级药房
     *
     * @param phaId
     * @return
     */
    public Object getSuperiorPharmacy(String phaId) {
        List<TPharmacy> superiorPharmacy = null;
        if (!StringUtils.isBlank(phaId)) {
            superiorPharmacy = pharmacyMapper.getSuperiorPharmacy(phaId);
        } else {
            superiorPharmacy = pharmacyMapper.getSuperiorPharmacyInsert();
        }

        return superiorPharmacy;
    }

    /**
     * 加载某条数据
     *
     * @param phaId 药房表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-13
     */
    public ResEntity findObj(String phaId) {

        if (StringUtils.isBlank(phaId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TPharmacy tPharmacy = pharmacyMapper.getObjectById(phaId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPharmacy);
    }


    /**
     * 插入新数据
     *
     * @param tPharmacyVo 药房表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-13
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::pha", "parameter::display", "parameter::dic"}, allEntries = true)
    public ResEntity insert(TPharmacyVo tPharmacyVo) {
        if (StringUtils.isBlank(tPharmacyVo.getDrugId())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        if (StringUtils.isBlank(tPharmacyVo.getPhaName())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        if (StringUtils.isBlank(tPharmacyVo.getPhaId())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        TPharmacy tPharmacy = new TPharmacy();
        tPharmacy.setPhaId(tPharmacyVo.getPhaId());
        tPharmacy.setPharmacyCode(tPharmacyVo.getPharmacyCode());
        tPharmacy.setPhaType(tPharmacyVo.getPhaType());
        tPharmacy.setPhaPid(tPharmacyVo.getPhaPid());
        tPharmacy.setDrugId(tPharmacyVo.getDrugId());
        tPharmacy.setSortNum(tPharmacyVo.getSortNum());
        tPharmacy.setPhaName(tPharmacyVo.getPhaName());
        tPharmacy.setStatus(tPharmacyVo.getStatus());
        tPharmacy.setPhaAddress(tPharmacyVo.getPhaAddress());
        if (StringUtils.isNotBlank(tPharmacyVo.getAppId())) {
            tPharmacy.setAppId(tPharmacyVo.getAppId());
        }
        if (StringUtils.isNotBlank(tPharmacyVo.getInsCode())) {
            tPharmacy.setInsCode(tPharmacyVo.getInsCode());
        }
        if (StringUtils.isNotBlank(tPharmacyVo.getDeptId())) {
            tPharmacy.setDeptId(tPharmacyVo.getDeptId());
        }
        if (StringUtils.isNotBlank(tPharmacyVo.getPhaAddressNumber())) {
            tPharmacy.setPhaAddressNumber(tPharmacyVo.getPhaAddressNumber());
        }
        tPharmacy.setCreateDate(new Date());
        tPharmacy.setCreateUser(AdminUtils.getCurrentHr().getCreateUser());
        tPharmacy.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());


        try {
            long rows = pharmacyMapper.insert(tPharmacy);
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResEntity.entity(false, e.getMessage(), null);
        } finally {
        }

        return ResEntity.success(tPharmacy);
    }


    /**
     * 修改
     *
     * @param tPharmacyVo 药房表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-13
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::pha", "parameter::display", "parameter::dic"}, allEntries = true)
    public ResEntity update(TPharmacyVo tPharmacyVo) {

        if (StringUtils.isBlank(tPharmacyVo.getPhaName())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        TPharmacy tPharmacy = new TPharmacy();
        tPharmacy.setPharmacyCode(tPharmacyVo.getPharmacyCode());
        tPharmacy.setPhaId(tPharmacyVo.getPhaId());
        tPharmacy.setPhaType(tPharmacyVo.getPhaType());
        tPharmacy.setPhaPid(tPharmacyVo.getPhaPid());
        tPharmacy.setDrugId(tPharmacyVo.getDrugId());
        if (StringUtils.isNotBlank(tPharmacyVo.getAppId())) {
            tPharmacy.setAppId(tPharmacyVo.getAppId());
        }
        if (StringUtils.isNotBlank(tPharmacyVo.getInsCode())) {
            tPharmacy.setInsCode(tPharmacyVo.getInsCode());
        }
        if (StringUtils.isNotBlank(tPharmacyVo.getDeptId())) {
            tPharmacy.setDeptId(tPharmacyVo.getDeptId());
        }
        if (StringUtils.isNotBlank(tPharmacyVo.getPhaAddressNumber())) {
            tPharmacy.setPhaAddressNumber(tPharmacyVo.getPhaAddressNumber());
        }
        tPharmacy.setSortNum(tPharmacyVo.getSortNum());
        tPharmacy.setPhaName(tPharmacyVo.getPhaName());
        tPharmacy.setPhaAddress(tPharmacyVo.getPhaAddress());
        tPharmacy.setStatus(tPharmacyVo.getStatus());
        if (Constant.BASIC_STRING_TWO.equals(tPharmacyVo.getStatus())) {
            tPharmacy.setDisableDate(new Date());
            tPharmacy.setDisableUser(AdminUtils.getCurrentHr().getUserId());
            tPharmacy.setDisableUsername(AdminUtils.getCurrentHr().getNameZh());
        }
        try {
            long rows = pharmacyMapper.updateByPrimaryKey(tPharmacy);
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResEntity.entity(false, e.getMessage(), null);
        }
        TPharmacy tPharmacy1 = pharmacyMapper.getTPharmacy(tPharmacyVo.getPhaId());
        return ResEntity.success(tPharmacy1);
    }

    /**
     * 获取药房最大序号
     *
     * @param drugId
     * @return
     */
    public ResEntity getMaxNum(String drugId) {
        if (StringUtils.isBlank(drugId)) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        TPharmacy tPharmacy = new TPharmacy();

        Integer maxNum = pharmacyMapper.getMaxNum(drugId);
        if (null == maxNum) {
            tPharmacy.setSortNum(1);
            return ResEntity.entity(true, "", tPharmacy);
        }
        tPharmacy.setSortNum(maxNum + 1);
        return ResEntity.entity(true, "", tPharmacy);
    }

    /**
     * 删除
     *
     * @param id ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-13
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::pha", "parameter::display", "parameter::dic"}, allEntries = true)
    public ResEntity deleteLis(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        List<TDisplay> matType = displayMapper.getMatType(id);
        pharmacyMapper.deleteByPhaId(id);
        displayMapper.deleteByPhaId1(id);
        for (TDisplay tDisplay : matType) {
            displayMappingMapper.deleteById(tDisplay.getId());
        }
        return ResEntity.entity(true, "", null);
    }

    /**
     * 获取应用配置数据
     *
     * @param phaId
     * @return
     */
    public ResEntity getPharmacyConfig(String phaId) {
        if (StringUtils.isBlank(phaId)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        //获取药房下的所有药房配置信息
        TPharmacy objectById = pharmacyMapper.getObjectById(phaId);
        if (Constant.BASIC_STRING_TWO.equals(objectById.getStatus())) {
            return ResEntity.entity(false, "该药房已被禁用无法配置", null);
        }
        List<TDisplay> tDisplays = displayMapper.getMatType(phaId);

        List<PharmacyConfig> pharmacyConfigList = new ArrayList<>();
        //每种剂型保存到配置中
        for (TDisplay tDisplay : tDisplays) {
            PharmacyConfig pharmacyConfig = new PharmacyConfig();
            pharmacyConfig.setMatType(tDisplay.getMatType());
            pharmacyConfig.setId(tDisplay.getId());
            pharmacyConfig.setDisplayName(tDisplay.getDisplayName());
            pharmacyConfig.setDeptLists(new ArrayList<>());
            pharmacyConfig.setSort(tDisplay.getSort());
            List<TDisplayMapping> configApp = displayMappingMapper.getConfigApp(tDisplay.getId());
            pharmacyConfig.setDeptLists(configApp);
            pharmacyConfigList.add(pharmacyConfig);
        }
        TDisplayVo tDisplayVo = new TDisplayVo();
        tDisplayVo.setPhaId(phaId);
        tDisplayVo.setPharmacyConfigList(pharmacyConfigList);
        return ResEntity.entity(true, "", tDisplayVo);
    }

    /**
     * 保存应用配置
     *
     * @param tDisplayVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"org::pha", "parameter::display", "parameter::dic"}, allEntries = true)
    public ResEntity savePharmacyConfig(TDisplayVo tDisplayVo) {
        if (StringUtils.isBlank(tDisplayVo.getPhaId())) {
            return ResEntity.error("参数不能为空");
        }
        List<PharmacyConfig> pharmacyConfigList = tDisplayVo.getPharmacyConfigList();

/*
        //判断配置集合中是否存在同一中类型的中药类型
        ArrayList<String> matTypeList = new ArrayList<>();
        for (PharmacyConfig pharmacyConfig : pharmacyConfigList) {
            matTypeList.add(pharmacyConfig.getMatType());
        }
        //判断是否存在相同的中药类型
        long distinctMatTypeCount = matTypeList.stream().distinct().count();
        long matTypeCount = matTypeList.stream().count();
        if (matTypeCount > distinctMatTypeCount) {
            return ResEntity.entity(false, "同一药房不能配置多个相同的中药类型", null);
        }
*/

        List<TDisplay> tDisplays = displayMapper.getMatType(tDisplayVo.getPhaId());
        ArrayList<String> dicIds = new ArrayList<>();
        for (PharmacyConfig pharmacyConfig : pharmacyConfigList) {
            if (StringUtils.isNotBlank(pharmacyConfig.getId())) {
                dicIds.add(pharmacyConfig.getId());
            }
        }
        for (TDisplay tDisplay : tDisplays) {
            if (!dicIds.contains(tDisplay.getId())) {
                displayMapper.deleteByPrimaryKeyL(tDisplay.getId());
            }
            displayMappingMapper.deleteById(tDisplay.getId());
        }
        //获取药房的医疗机构
        TPharmacy tPharmacy = pharmacyMapper.getTPharmacy(tDisplayVo.getPhaId());
        //List<TDisplay> tDisplays = displayMapper.getMatType(tDisplayVo.getPhaId());
        int temp = 0;
        Integer tPharmacyLastSort = pharmacyMapper.getTPharmacyLastSort();
        for (PharmacyConfig pharmacyConfig : pharmacyConfigList) {
            if (StringUtils.isBlank(pharmacyConfig.getId())) {
                TDisplay tDisplay = new TDisplay();
                String id = IDUtil.getID();
                tDisplay.setId(id);
                tDisplay.setDisplayName(pharmacyConfig.getDisplayName());
                tDisplay.setAppId(tPharmacy.getAppId());
                tDisplay.setInsCode(tPharmacy.getInsCode());
                tDisplay.setDeptId(tPharmacy.getDeptId());
                tDisplay.setMatType(pharmacyConfig.getMatType());
                tDisplay.setPhaId(tDisplayVo.getPhaId());
                tDisplay.setStatus("0");
                if (pharmacyConfig.getSort() == null) {

                    temp = temp + 1;
                    tDisplay.setSort(tPharmacyLastSort + temp);
                } else {
                    tDisplay.setSort(pharmacyConfig.getSort());
                }

                tDisplay.setPhaName(tPharmacy.getPhaName());
                Date date = new Date();
                tDisplay.setCreateDate(date);
                tDisplay.setCreateUser(AdminUtils.getCurrentHr().getUserId());
                List<TDisplayMapping> deptLists = pharmacyConfig.getDeptLists();
                for (TDisplayMapping deptList : deptLists) {
                    deptList.setDisplayId(id);
                    if (StringUtils.isBlank(deptList.getDeptId())) {
                        deptList.setDeptId("000000");
                    }
                    if (StringUtils.isBlank(deptList.getInsCode())) {
                        deptList.setInsCode("000000");
                    }
                    deptList.setCreateDate(date);
                    deptList.setCreateUser(AdminUtils.getCurrentHr().getNameZh());
                }
                displayMapper.insert(tDisplay);
                //保存应用范围集合
                displayMappingMapper.insertList(deptLists);
            } else {
                TDisplay tDisplay = new TDisplay();
                tDisplay.setId(pharmacyConfig.getId());
                tDisplay.setMatType(pharmacyConfig.getMatType());
                tDisplay.setDisplayName(pharmacyConfig.getDisplayName());
                List<TDisplayMapping> deptLists = pharmacyConfig.getDeptLists();
                for (TDisplayMapping deptList : deptLists) {
                    deptList.setDisplayId(pharmacyConfig.getId());
                    if (StringUtils.isBlank(deptList.getDeptId())) {
                        deptList.setDeptId("000000");
                    }
                    if (StringUtils.isBlank(deptList.getInsCode())) {
                        deptList.setInsCode("000000");
                    }
                    deptList.setCreateDate(new Date());
                    deptList.setCreateUser(AdminUtils.getCurrentHr().getNameZh());
                }
                if (pharmacyConfig.getSort() == null) {

                    temp = temp + 1;
                    tDisplay.setSort(tPharmacyLastSort + temp);
                } else {
                    tDisplay.setSort(pharmacyConfig.getSort());
                }
                displayMapper.updateByPrimaryKey(tDisplay);
                //保存应用范围集合
                displayMappingMapper.insertList(deptLists);
            }
        }
        return ResEntity.entity(true, "", null);
    }

    public Object getAllPharmacy() {
        List<TPharmacy> superiorPharmacyInsert = pharmacyMapper.getSuperiorPharmacyInsert();
        return ResEntity.entity(true, Constant.SUCCESS_DX, superiorPharmacyInsert);
    }

    /**
     * 获取所有药房剂型配置信息
     *
     * @return
     */
    public Object getPharmacyItemList(String parentId) {
        List<TDisplay> pharmacyItemList = displayMapper.getPharmacyItemList();
        for (TDisplay tDisplay : pharmacyItemList) {
            TDicBase tDicBase = new TDicBase();
            tDicBase.setDisplayId(tDisplay.getId());
            tDicBase.setParentId(parentId);
            List<TDicBase> pharmacyItem = dicBaseMapper.getPharmacyItem(tDicBase);
            if (pharmacyItem.size() > 0) {
                tDisplay.setDisplayChecked("true");
            } else {
                tDisplay.setDisplayChecked("false");
            }
        }
        return ResEntity.entity(true, "", pharmacyItemList);
    }

    public Object getDisplayList(String phaId) {
        if (StringUtils.isBlank(phaId)) {
            return ResEntity.entity(false, "药房ID不能为空", null);
        }
        TPharmacy objectById = pharmacyMapper.getObjectById(phaId);
        if (null == objectById) {
            return ResEntity.entity(false, "该药房不存在", null);
        }
        if (Constant.BASIC_STRING_TWO.equals(objectById.getStatus())) {
            return ResEntity.entity(false, "该药房已被禁用无法配置", null);
        }
        List<TDisplay> displayMatTypes = displayMapper.getMatType(phaId);
        if (displayMatTypes.size() == Constant.ADMIN_EXT_ZERO) {
            return ResEntity.entity(false, "请先配置应用配置", null);
        }
        return ResEntity.entity(true, "", displayMatTypes);
    }

    /**
     * 获取物理要放下的剂型
     *
     * @return
     */
    public ResEntity getAllPharmacyDisplayList() {
        List<TPharmacy> allPharmacyDisplayList = displayMapper.getAllPharmacyDisplayList();
        return ResEntity.entity(true, "", allPharmacyDisplayList);
    }
}
