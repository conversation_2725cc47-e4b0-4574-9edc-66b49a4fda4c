package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.drug.BMatSynonymMapper;
import com.jiuzhekan.cbkj.mapper.drug.TAppMaterialMappingMapper;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.drug.TDrugListMapper;
import com.jiuzhekan.cbkj.mapper.drug.TMaterialPriceMapper;
import com.jiuzhekan.cbkj.service.know.PostKnow;
import org.apache.commons.lang.StringUtils;
//import org.odftoolkit.odfdom.dom.attribute.style.StyleNumFormatAttribute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.management.ObjectName;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

//import static org.odftoolkit.odfdom.dom.attribute.style.StyleNumFormatAttribute.Value.i;

@Service
public class TAppMaterialMappingService {

    @Autowired
    private TAppMaterialMappingMapper tAppMaterialMappingMapper;
    @Autowired
    private TDrugListMapper tDrugListMapper;
    @Autowired
    private BMatSynonymMapper bMatSynonymMapper;

    @Autowired
    private TMaterialPriceMapper tMaterialPriceMapper;

    @Autowired
    private PostKnow postKnow;

    @Autowired
    private DataSource dataSource;

    @Value("${address.know.drug.mapping}")
    private boolean status;

    /**
     * 加载分页数据
     *
     * @param tAppMaterialMapping 药房的药品和知识库药品映射关系表
     * @param page                分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TAppMaterialMapping tAppMaterialMapping, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TAppMaterialMapping> list = tAppMaterialMappingMapper.getPageListByObj(tAppMaterialMapping);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 药房的药品和知识库药品映射关系表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TAppMaterialMapping tAppMaterialMapping = tAppMaterialMappingMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tAppMaterialMapping);
    }


    /**
     * 插入新数据
     *
     * @param tKnowMaterialMappingVo 药房的药品和知识库药品映射关系表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TKnowMaterialMappingVo tKnowMaterialMappingVo) {
        if (StringUtils.isBlank(tKnowMaterialMappingVo.getKMatId())) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(tKnowMaterialMappingVo.getKMatName())) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        List<TMaterialKnowMapping> tMaterials = tKnowMaterialMappingVo.getTMaterials();
        ArrayList<TAppMaterialMapping> list = new ArrayList<>();
        for (TMaterialKnowMapping tMaterialKnowMapping : tMaterials) {
            if (StringUtils.isBlank(tMaterialKnowMapping.getDrugId())) {
                return ResEntity.entity(false, "参数错误(缺少参数)！", null);
            }
            if (StringUtils.isBlank(tMaterialKnowMapping.getMatPriceId())) {
                return ResEntity.entity(false, "参数错误(缺少参数)！", null);
            }
            TMaterialKnowVO tMaterialKnowVO = new TMaterialKnowVO();
            tMaterialKnowVO.setMatPriceId(tMaterialKnowMapping.getMatPriceId());
            tMaterialKnowVO.setkMatId(tKnowMaterialMappingVo.getKMatId());
            tMaterialKnowVO.setDrugId(tMaterialKnowMapping.getDrugId());
            long l = tAppMaterialMappingMapper.selectCountData(tMaterialKnowVO);
            if (l > 0) {
                continue;
            }
            TAppMaterialMapping tAppMaterialMapping = new TAppMaterialMapping();
            tAppMaterialMapping.setKMatId(tKnowMaterialMappingVo.getKMatId());
            tAppMaterialMapping.setMatPriceId(tMaterialKnowMapping.getMatPriceId());
            tAppMaterialMapping.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
            tAppMaterialMapping.setKMatName(tKnowMaterialMappingVo.getKMatName());
            tAppMaterialMapping.setId(IDUtil.getID());
            tAppMaterialMapping.setCreateDate(new Date());
            tAppMaterialMapping.setDrugId(tMaterialKnowMapping.getDrugId());
            tAppMaterialMapping.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            list.add(tAppMaterialMapping);
        }
        if (list.size() > 0) {
            long rows = tAppMaterialMappingMapper.insertList(list);
            if (status) {
                ResEntity mappingOne = getMappingOne(tKnowMaterialMappingVo);
            }
            return ResEntity.success(rows);

        }
        return ResEntity.success(0);
    }


    /**
     * 修改
     *
     * @param tAppMaterialMapping 药房的药品和知识库药品映射关系表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TAppMaterialMapping tAppMaterialMapping) {

        long rows = tAppMaterialMappingMapper.updateByPrimaryKey(tAppMaterialMapping);

        return ResEntity.success(tAppMaterialMapping);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tAppMaterialMappingMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public Object getDataPage(TMaterialKnowVO tMaterialKnowVO, Page page) {
        if (StringUtils.isBlank(tMaterialKnowVO.getIsMapping())) {
            return ResEntity.entity(false, "参数不能为空哦(isMapping)", null);
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TMaterialKnowVO> list = tAppMaterialMappingMapper.getDataPage(tMaterialKnowVO);
        return Page.getLayUiTablePageData(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity autoMapping(StandTMAutoMappingVO standTmAutoMappingVO) {
        try (Connection connection = dataSource.getConnection()) {
            if (StringUtils.isBlank(standTmAutoMappingVO.getDrugId())) {
                return new ResEntity(false, "药品目录ID不能为空！", null);
            }
//        TDrugList objectById = tDrugListMapper.getObjectById(standTmAutoMappingVO.getDrugId());
//        if (!Constant.BASIC_STRING_ZERO.equals(objectById.getDrugType())) {
//            return new ResEntity(false, "请同步本地药品目录！", null);
//        }
            standTmAutoMappingVO.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            standTmAutoMappingVO.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
            int mappingNumber = tAppMaterialMappingMapper.insertautoMapping(standTmAutoMappingVO);
            long noMappingNumber = tAppMaterialMappingMapper.selectautoMappingCount(standTmAutoMappingVO);
            HashMap<String, Object> map = new HashMap<>();
            map.put("mappingNumber", mappingNumber);
            map.put("noMappingNumber", noMappingNumber);

            if (status) {
                ResEntity mappingByDrugId = getMappingByDrugId(standTmAutoMappingVO);
            }
            return new ResEntity(true, "映射完成！！", map);
        } catch (SQLException e) {
            e.printStackTrace();
            return ResEntity.error(e.getMessage());
        }
    }


    public ResEntity getMappingOne(TKnowMaterialMappingVo tKnowMaterialMappingVo) {

        ResEntity resEntity = postKnow.postKnow(tKnowMaterialMappingVo.getKMatId());
        if (resEntity.getStatus()) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) resEntity.getData();
            if (data.size() > 0) {
                List<TMaterialPrice> list = new ArrayList<>();
                for (TMaterialKnowMapping tMaterialKnowMapping : tKnowMaterialMappingVo.getTMaterials()) {
                    TMaterialPrice tMaterialPrice = tMaterialPriceMapper.getMaterialPrice(tMaterialKnowMapping);
                    if (null != tMaterialPrice) {
                        TMaterialPrice tMaterialPrice1 = new TMaterialPrice();

                        for (Map<String, Object> map : data) {
                            if (null == tMaterialPrice.getMaxdose()) {
                                if (map.containsKey("matmaxdosage")) {
                                    Object o = map.get("matmaxdosage");
                                    if (o != null  && StringUtils.isNumeric(o.toString())) {
                                        Double matmaxdosage= null;
                                        try {
                                             matmaxdosage = Double.parseDouble(map.get("matmaxdosage").toString());
                                        } catch (Exception e) {
                                        }

                                        tMaterialPrice1.setMaxdose(matmaxdosage);
                                    }
                                }
                            }
                            if (null == tMaterialPrice.getMindose()) {
                                if (map.containsKey("matmindosage")) {
                                    Object o = map.get("matmindosage");
                                    if (o != null && StringUtils.isNumeric(o.toString())) {
                                        Double matmaxdosage= null;
                                        try {
                                            matmaxdosage = Double.parseDouble(map.get("matmindosage").toString());
                                        } catch (Exception e) {
                                        }
                                        tMaterialPrice1.setMindose(matmaxdosage);
                                    }

                                }
                            }
                            if (StringUtils.isBlank(tMaterialPrice.getToxicity())) {
                                if (map.containsKey("matdxsm")) {
                                    Object matdxsmObj = map.get("matdxsm");
                                    if (null != matdxsmObj) {
                                        String matdxsm = matdxsmObj.toString();
                                        if (matdxsm.contains("大毒")) {
                                            tMaterialPrice1.setToxicity("大毒");
                                        } else if (matdxsm.contains("小毒")) {
                                            tMaterialPrice1.setToxicity("小毒");
                                        } else if (matdxsm.contains("有毒")) {
                                            tMaterialPrice1.setToxicity("有毒");
                                        }
                                    }
                                }
                            }

                            if (StringUtils.isBlank(tMaterialPrice.getMotherTaboos())) {
                                if (map.containsKey("matyfsjj") && StringUtils.isNotBlank(map.get("matyfsjj").toString())) {
                                    tMaterialPrice1.setMotherTaboos(map.get("matyfsjj").toString());
                                }
                            }

                            if (StringUtils.isBlank(tMaterialPrice.getSpecialUsages())) {
                                String key = "specialUsage";
                                if (map.containsKey(key)) {
                                    if (null != map.get(key)) {
                                        tMaterialPrice1.setSpecialUsages(map.get(key).toString());
                                    }
                                }
                            }
                        }
                        if (tMaterialPrice1.getMaxdose() != null || tMaterialPrice1.getMindose() != null ||
                                StringUtils.isNotBlank(tMaterialPrice1.getToxicity()) || StringUtils.isNotBlank(tMaterialPrice1.getMotherTaboos())
                                || StringUtils.isNotBlank(tMaterialPrice1.getSpecialUsages())) {
                            tMaterialPrice1.setMatPriceId(tMaterialKnowMapping.getMatPriceId());
                            tMaterialPrice1.setDrugId(tMaterialKnowMapping.getDrugId());
                            list.add(tMaterialPrice1);
                        }
                        if (list.size() > 0) {
                            tMaterialPriceMapper.updateList(list);
                            return ResEntity.success("成功");
                        }

                    }
                }
                return ResEntity.success("成功");
            }
            return ResEntity.error("查询知识库数据为空！");
        }
        return ResEntity.error(resEntity.getMessage());
    }


    @Async
    public ResEntity getMappingByDrugId(StandTMAutoMappingVO standTmAutoMappingVO) {

        List<TAppMaterial> objectByDrugId = tAppMaterialMappingMapper.getObjectByDrugId(standTmAutoMappingVO);

        if (objectByDrugId.size() > 0) {
            String collect = objectByDrugId.stream().map(TAppMaterial::getKMatId).collect(Collectors.joining(","));
            ResEntity resEntity = postKnow.postKnow(collect);
            if (resEntity.getStatus()) {
                List<Map<String, Object>> data = (List<Map<String, Object>>) resEntity.getData();
                if (data.size() > 0) {
                    List<TMaterialPrice> list = new ArrayList<>();
                    for (TAppMaterial tAppMaterial : objectByDrugId) {
                        for (Map<String, Object> map : data) {
                            if (tAppMaterial.getKMatId().equals(map.get("matid").toString())) {
                                TMaterialPrice tMaterialPrice1 = new TMaterialPrice();
                                if (null == tAppMaterial.getMaxdose()) {
                                    if (map.containsKey("matmaxdosage") && StringUtils.isNotBlank(map.get("matmaxdosage").toString())) {
                                        tMaterialPrice1.setMaxdose(Double.parseDouble(map.get("matmaxdosage").toString()));
                                    }
                                }
                                if (null == tAppMaterial.getMindose()) {
                                    if (map.containsKey("matmindosage") && StringUtils.isNotBlank(map.get("matmindosage").toString())) {
                                        tMaterialPrice1.setMindose(Double.parseDouble(map.get("matmindosage").toString()));
                                    }
                                }
                                if (StringUtils.isBlank(tAppMaterial.getToxicity())) {
                                    if (map.containsKey("matdxsm") && map.get("matdxsm") != null && map.get("matdxsm").toString().contains("大毒")) {
                                        tMaterialPrice1.setToxicity("大毒");
                                    } else if (map.containsKey("matdxsm") && map.get("matdxsm") != null && map.get("matdxsm").toString().contains("小毒")) {
                                        tMaterialPrice1.setToxicity("小毒");
                                    } else if (map.containsKey("matdxsm") && map.get("matdxsm") != null && map.get("matdxsm").toString().contains("有毒")) {
                                        tMaterialPrice1.setToxicity("有毒");
                                    }
                                }
                                if (StringUtils.isBlank(tAppMaterial.getMotherTaboos())) {
                                    if (map.containsKey("matyfsjj") && StringUtils.isNotBlank(map.get("matyfsjj").toString())) {
                                        tMaterialPrice1.setMotherTaboos(map.get("matyfsjj").toString());
                                    }
                                }
                                if (StringUtils.isBlank(tAppMaterial.getSpecialUsages())) {
                                    String key = "specialUsage";
                                    if (map.containsKey(key)) {
                                        if (null != map.get(key)) {
                                            tMaterialPrice1.setSpecialUsages(map.get(key).toString());
                                        }
                                    }
                                }
                                if (tMaterialPrice1.getMaxdose() != null || tMaterialPrice1.getMindose() != null ||
                                        StringUtils.isNotBlank(tMaterialPrice1.getToxicity()) || StringUtils.isNotBlank(tMaterialPrice1.getMotherTaboos())
                                        || StringUtils.isNotBlank(tMaterialPrice1.getSpecialUsages())) {
                                    tMaterialPrice1.setMatPriceId(tAppMaterial.getMatPriceId());
                                    tMaterialPrice1.setDrugId(tAppMaterial.getDrugId());
                                    list.add(tMaterialPrice1);
                                }
                            }
                        }
                    }
                    if (list.size() > 0) {
                        tMaterialPriceMapper.updateList(list);
                        return ResEntity.success("成功");
                    }
                    return ResEntity.success("成功");
                }
                return ResEntity.error("查询知识库数据为空！");
            }
            return ResEntity.error(resEntity.getMessage());
        }
        return ResEntity.error("未查询到映射的数据！");
    }


    public static void main(String[] args) {

        Map<String, Object> map = new HashMap<>();
        map.put("matyfsjj", "1");
        String key = "specialUsage";
        System.out.println(map.containsKey(key));
    }


}
