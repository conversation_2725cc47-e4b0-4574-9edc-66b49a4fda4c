package com.jiuzhekan.cbkj.service.business;

import com.jiuzhekan.cbkj.beans.business.chargeitem.InsertChargeItemMapping;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItem;
import com.jiuzhekan.cbkj.beans.business.chargeitem.ChargeItemIdNameDTO;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemMapping;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.TChargeItemMapper;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.TChargeItemMappingMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Date;

@Service
public class TChargeItemService {


    private final TChargeItemMapper tChargeItemMapper;
    private final TChargeItemMappingMapper tChargeItemMappingMapper;

    public TChargeItemService(TChargeItemMapper tChargeItemMapper, TChargeItemMappingMapper tChargeItemMappingMapper) {
        this.tChargeItemMapper = tChargeItemMapper;
        this.tChargeItemMappingMapper = tChargeItemMappingMapper;
    }

    /**
     * 加载分页数据
     *
     * @param tChargeItem 收费项目管理主表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2025-08-05
     */
    public Object getPageDatas(TChargeItem tChargeItem, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TChargeItem> list = tChargeItemMapper.getPageListByObj(tChargeItem);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param chargeItemId 收费项目管理主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    public ResEntity findObj(String chargeItemId) {

        if (StringUtils.isBlank(chargeItemId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TChargeItem tChargeItem = tChargeItemMapper.getObjectById(chargeItemId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tChargeItem);
    }


    /**
     * 插入新数据
     *
     * @param tChargeItem 收费项目管理主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TChargeItem tChargeItem){

        tChargeItem.setChargeItemId(IDUtil.getID());
        long rows = tChargeItemMapper.insert(tChargeItem);

        return ResEntity.success(tChargeItem);
    }


    /**
     * 修改
     *
     * @param tChargeItem 收费项目管理主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TChargeItem tChargeItem) {

        long rows = tChargeItemMapper.updateByPrimaryKey(tChargeItem);

        return ResEntity.success(tChargeItem);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tChargeItemMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 获取收费项目目录列表
     *
     * @param chargeItemName 收费项目名称（可选，用于模糊查询）
     * @return ResEntity
     */
    public Object getChargeItemList(String chargeItemName,Page page) {
        try {
            PageHelper.startPage(page.getPage(), page.getLimit());
            List<TChargeItem> list = tChargeItemMapper.getChargeItemList(chargeItemName);
            Object layUiTablePageData = Page.getLayUiTablePageData(list);
            PageHelper.clearPage();
            return layUiTablePageData;
        } catch (Exception e) {
            return ResEntity.error("获取收费项目列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有的收费目录的charge_item_id和charge_item_name
     *
     * @return ResEntity
     */
    public ResEntity getChargeItemIdAndName() {
        try {
            List<ChargeItemIdNameDTO> list = tChargeItemMapper.getChargeItemIdAndName();
            return ResEntity.success(list);
        } catch (Exception e) {
            return ResEntity.error("获取收费项目ID和名称失败：" + e.getMessage());
        }
    }

    /**
     * 新增收费目录
     *
     * @param tChargeItem 收费项目信息
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertChargeItem(TChargeItem tChargeItem) {
        if (tChargeItem == null) {
            return ResEntity.error("收费项目信息不能为空");
        }

        if (StringUtils.isBlank(tChargeItem.getChargeItemName())) {
            return ResEntity.error("收费项目名称不能为空");
        }

        if (StringUtils.isBlank(tChargeItem.getChargeItemCode())) {
            return ResEntity.error("收费项目代码不能为空");
        }

        // 检查代码是否重复
        if (tChargeItemMapper.checkChargeItemCodeExists(tChargeItem.getChargeItemCode()) > 0) {
            return ResEntity.error("收费项目代码已存在");
        }

        AdminInfo currentHr = AdminUtils.getCurrentHr();

        // 设置默认值
        tChargeItem.setChargeItemId(IDUtil.getID());
        tChargeItem.setStatus(0); // 0正常
        tChargeItem.setCreateTime(new java.util.Date());
        tChargeItem.setCreateUserName(currentHr.getNameZh());
        tChargeItem.setCreateUserId(currentHr.getUserId());

        int rows = tChargeItemMapper.insert(tChargeItem);
        if (rows > 0) {
            return ResEntity.success(tChargeItem);
        } else {
            throw new RuntimeException("新增收费项目失败");
        }
    }

    /**
     * 修改收费目录
     *
     * @param tChargeItem 收费项目信息
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateChargeItem(TChargeItem tChargeItem) {
        if (tChargeItem == null || StringUtils.isBlank(tChargeItem.getChargeItemId())) {
            return ResEntity.error("收费项目ID不能为空");
        }

        if (StringUtils.isBlank(tChargeItem.getChargeItemName())) {
            return ResEntity.error("收费项目名称不能为空");
        }

        if (StringUtils.isBlank(tChargeItem.getChargeItemCode())) {
            return ResEntity.error("收费项目代码不能为空");
        }

        // 检查代码是否重复（排除自己）
        if (tChargeItemMapper.checkChargeItemCodeExistsExcludeSelf(tChargeItem.getChargeItemCode(), tChargeItem.getChargeItemId()) > 0) {
            return ResEntity.error("收费项目代码已存在");
        }

        int rows = tChargeItemMapper.updateByPrimaryKey(tChargeItem);
        if (rows > 0) {
            return ResEntity.success(tChargeItem);
        } else {
            throw new RuntimeException("修改收费项目失败");
        }
    }

    /**
     * 删除收费目录（仅对状态修改）
     *
     * @param chargeItemId 收费项目ID
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteChargeItem(String chargeItemId) {
        if (StringUtils.isBlank(chargeItemId)) {
            return ResEntity.error("收费项目ID不能为空");
        }

        // 检查收费项目是否存在
        TChargeItem existItem = tChargeItemMapper.getObjectById(chargeItemId);
        if (existItem == null) {
            return ResEntity.error("收费项目不存在");
        }

        // 逻辑删除，将状态设置为1
        int rows = tChargeItemMapper.deleteChargeItemLogically(chargeItemId);
        if (rows > 0) {
            return ResEntity.success("删除成功");
        } else {
            throw new RuntimeException("删除收费项目失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertChargeItemMapping(InsertChargeItemMapping insertChargeItemMapping) {
        String chargeItemId = insertChargeItemMapping.getChargeItemId();
        if (StringUtils.isBlank(chargeItemId)) {
            return ResEntity.error("收费项目ID不能为空");
        }
        //删除映射表数据根据收费项目ID
        tChargeItemMappingMapper.deleteByChargeItemId(chargeItemId);
        //重新插入
        List<TChargeItemMapping> chargeItemMappingList = insertChargeItemMapping.getChargeItemMappingList();
        chargeItemMappingList.forEach(chargeItemMapping -> {
            if (StringUtils.isBlank(chargeItemMapping.getInsCode())){
                chargeItemMapping.setInsCode("000000");
            }
            if (StringUtils.isBlank(chargeItemMapping.getDeptId())){
                chargeItemMapping.setDeptId("000000");
            }
            chargeItemMapping.setCreateDate(new Date());
            chargeItemMapping.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            chargeItemMapping.setChargeItemId(insertChargeItemMapping.getChargeItemId());
        });
        tChargeItemMappingMapper.insertList(chargeItemMappingList);
        return ResEntity.success(insertChargeItemMapping);
    }

    public ResEntity getChargeItemMappingList(String chargeItemId) {
        if (StringUtils.isBlank(chargeItemId)){
            return ResEntity.error("收费项目ID不能为空");
        }
        InsertChargeItemMapping insertChargeItemMapping = new InsertChargeItemMapping();
        List<TChargeItemMapping> list = tChargeItemMappingMapper.getChargeItemMappingList(chargeItemId);
        insertChargeItemMapping.setChargeItemId(chargeItemId);
        insertChargeItemMapping.setChargeItemMappingList(list);
        return ResEntity.success(insertChargeItemMapping);
    }
}
