package com.jiuzhekan.cbkj.service.drug;


import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.drug.TMaterialPriceMapper;
import com.jiuzhekan.cbkj.mapper.drug.CenterYpmlmxMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
/**
 * <AUTHOR>
 */
public class CenterYpmlmxService {


    private CenterYpmlmxMapper centerYpmlmxMapper;


    private TMaterialPriceMapper tMaterialPriceMapper;

    @Autowired
    CenterYpmlmxService(CenterYpmlmxMapper centerYpmlmxMapper, TMaterialPriceMapper tMaterialPriceMapper) {
        this.centerYpmlmxMapper = centerYpmlmxMapper;
        this.tMaterialPriceMapper = tMaterialPriceMapper;
    }

    /**
     * 加载分页数据
     *
     * @param vCenterYpmlmx 药品目录明细
     * @param page          分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-18
     */
    public Object getPageDatas(VCenterYpmlmx vCenterYpmlmx, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        String sdkTagsCodes = vCenterYpmlmx.getSdkTagsCodes();
        if (StringUtils.isNotBlank(sdkTagsCodes))
        {
            String[] split = sdkTagsCodes.split(",");
            vCenterYpmlmx.setSdkTagsCodesArray(split);
        }
        List<VCenterYpmlmx> list = centerYpmlmxMapper.getPageListByObj(vCenterYpmlmx);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param matPriceId 药品目录明细
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-18
     */
    public ResEntity findObj(String matPriceId) {

        if (StringUtils.isBlank(matPriceId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        VCenterYpmlmxDetails objectByIdDetail = centerYpmlmxMapper.getObjectByIdDetail(matPriceId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, objectByIdDetail);
    }


    /**
     * 医保配置
     *
     * @param vCenterYpmlmxVo 药品价格
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-18
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateInsurance(VCenterYpmlmxVo vCenterYpmlmxVo) {
        if (StringUtils.isBlank(vCenterYpmlmxVo.getDrugId())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        if (StringUtils.isBlank(vCenterYpmlmxVo.getMatPriceId())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        TMaterialPrice tMaterialPrice = new TMaterialPrice();
        tMaterialPrice.setMatPriceId(vCenterYpmlmxVo.getMatPriceId());
        tMaterialPrice.setDrugId(vCenterYpmlmxVo.getDrugId());
        tMaterialPrice.setDailyMaxDoseIn(vCenterYpmlmxVo.getDailyMaxDoseIn());
        tMaterialPrice.setDailyMaxDoseExt(vCenterYpmlmxVo.getDailyMaxDoseExt());
        tMaterialPrice.setNotPayAlone(vCenterYpmlmxVo.getNotPayAlone());
        tMaterialPrice.setNotPayInFund(vCenterYpmlmxVo.getNotPayInFund());
        tMaterialPrice.setToxicityOverdoseMultiple(vCenterYpmlmxVo.getToxicityOverdoseMultiple());
        tMaterialPrice.setExternalUseOnly(vCenterYpmlmxVo.getExternalUseOnly());
        tMaterialPrice.setMaxdose(vCenterYpmlmxVo.getMaxdose());
        tMaterialPrice.setMindose(vCenterYpmlmxVo.getMindose());

        tMaterialPrice.setToxicity(vCenterYpmlmxVo.getToxicity());
        tMaterialPrice.setMotherTaboos(vCenterYpmlmxVo.getMotherTaboos());
        tMaterialPrice.setExternalUseOrally(vCenterYpmlmxVo.getExternalUseOrally());
        tMaterialPrice.setExternalMarusan(vCenterYpmlmxVo.getExternalMarusan());
        tMaterialPrice.setExternalMarusanName(vCenterYpmlmxVo.getExternalMarusanName());
        tMaterialPrice.setSpecialUsages(vCenterYpmlmxVo.getSpecialUsages());
        tMaterialPrice.setUsagesAstrict(vCenterYpmlmxVo.getUsagesAstrict());
        tMaterialPrice.setSdkTagsCodes(vCenterYpmlmxVo.getSdkTagsCodes());

        tMaterialPriceMapper.updateByPrimaryKey(tMaterialPrice);
        return ResEntity.success(tMaterialPrice);
    }

}
