package com.jiuzhekan.cbkj.service.business.asyncTask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItem;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItemQuery;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskItemMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AsyncTaskItemService {

    private final AsyncTaskItemMapper asyncTaskItemMapper;

    @Autowired
    public AsyncTaskItemService(AsyncTaskItemMapper asyncTaskItemMapper) {
        Assert.notNull(asyncTaskItemMapper, "MyCollaborator must not be null!");
        this.asyncTaskItemMapper = asyncTaskItemMapper;
    }

    /**
     * 加载分页数据
     *
     * @param asyncTaskItem 任务明细表
     * @param page          分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-15
     */
    public Object getPageData(AsyncTaskItemQuery asyncTaskItem, Page page) {
        if (!StringUtils.isBlank(asyncTaskItem.getStatus())) {
            //查全部，置空
            if (Constant.BASIC_STRING_MINUS_ONE.equals(asyncTaskItem.getStatus())) {
                asyncTaskItem.setStatus(null);
            }
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AsyncTaskItem> list = asyncTaskItemMapper.getPageListByObj2(asyncTaskItem);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param taskItemId 任务明细表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    public ResEntity findObj(String taskItemId) {

        if (StringUtils.isBlank(taskItemId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        AsyncTaskItem asyncTaskItem = asyncTaskItemMapper.getObjectById(taskItemId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, asyncTaskItem);
    }


    /**
     * 插入新数据
     *
     * @param asyncTaskItem 任务明细表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(AsyncTaskItem asyncTaskItem) {

        long rows = asyncTaskItemMapper.insert(asyncTaskItem);

        return ResEntity.success(asyncTaskItem);
    }


    /**
     * 修改
     *
     * @param asyncTaskItem 任务明细表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(AsyncTaskItem asyncTaskItem) {

        long rows = asyncTaskItemMapper.updateByPrimaryKey(asyncTaskItem);

        return ResEntity.success(asyncTaskItem);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = asyncTaskItemMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
