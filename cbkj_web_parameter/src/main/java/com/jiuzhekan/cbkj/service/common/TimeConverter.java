package com.jiuzhekan.cbkj.service.common;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/25 16:51
 * @Version 1.0
 */
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;

public class TimeConverter {
    /**
     *
     * @param args
     */
    public static void main(String[] args) {
        String time = "星期一:14:00:00-21:41:11;星期一:07:47:56-16:41:05;星期二:06:40:58-07:41:00";
        String timeA = convertTime(time);
        System.out.println(timeA);
        String s = convertTimeToUIStr(timeA);
        System.out.println(s);
    }

    public static String convertTime(String time) {
        if (StringUtils.isBlank(time)){
            return "";
        }
        try {
            // 使用Map按星期几存储时间段
            Map<String, List<String>> weekDayToTimeRanges = new LinkedHashMap<>();
            // 分割时间段
            String[] timeRanges = time.split(";");
            for (String timeRange : timeRanges) {
                String[] parts = timeRange.split(":");
                String weekDay = parts[0];
                String[] timeSlots = parts[1].split("-");
                 ;
                weekDayToTimeRanges
                        .computeIfAbsent(weekDay, k -> new ArrayList<>())
                        .add(parts[1]+":"+parts[2] + "-"+parts[3].split("-")[1]+":"+parts[4] );
            }
            StringBuilder result = new StringBuilder();
            // 合并时间段并格式化
            for (Map.Entry<String, List<String>> entry : weekDayToTimeRanges.entrySet()) {
                String weekWord = entry.getKey();
                result.append(weekWord).append("：");
                for (String s : entry.getValue()) {
                    result.append(s).append("|");
                }
                String substring = result.substring(0, result.length() - 1);
                //重新把substring替换result
                result = new StringBuilder(substring+"；");
            }
            return result.toString();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 星期一：8:00-11:00；星期三：8:00-10:00|14:00-16:00；星期五：8:00-11:00|14:00-18:00； 转成三条
     * 星期一：8:00-11:00；星期三：8:00-10:00|14:00-16:00；星期四：8:00-11:00|14:00-17:00；
     * 星期一：13:02-20:43|07:47-11:43；星期二：06:40-07:41；
     * @param time
     * @return
     */
    public static String convertTimeToUIStr(String time) {
        if (StringUtils.isBlank(time)){
            return "";
        }
        try {
            StringBuilder returnStr = new StringBuilder();
            String substring = time.substring(0, time.length() - 1);
            String[] split = substring.split("；");
            for (int i = 0; i < split.length; i++) {
                String[] split1 = split[i].split("：");
                returnStr.append(split1[0]).append(":");
                String[] split2 = split1[1].split("\\|");
                if (split2.length >= 2 ){
                    for (int i1 = 0; i1 < split2.length; i1++) {
                        returnStr.append(split2[i1]).append(";").append(split1[0]).append(":");
                    }
                    returnStr = new StringBuilder(returnStr.substring(0, returnStr.length() - 4));
                }else {
                    returnStr.append(split2[0]).append(";");
                }


            }

            return returnStr.toString();
        } catch (Exception e) {
            return "";
        }

    }
}
