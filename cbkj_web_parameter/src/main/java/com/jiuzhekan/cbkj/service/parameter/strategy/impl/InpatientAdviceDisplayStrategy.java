package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Service("INPATIENT_ADVICE_DISPLAY")
public class InpatientAdviceDisplayStrategy implements ParamStrategy {
    private final TSysParamInitDescMapper tSysParamInitDescMapper;
    public InpatientAdviceDisplayStrategy(TSysParamInitDescMapper tSysParamInitDescMapper){
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
    }
    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1,JSONArray itemArray) {
        pjsonObject.put("paramType", tSysParam1.getParamType());
        jsonObject.remove("initValue");
        jsonObject.remove("parValues");
        jsonObject.remove("paramType");
        jsonObject.remove("parName", "列表");
        jsonObject.remove("sort");
        if ("[]".equals(tSysParam1.getParValues())) {
            jsonObject.put("parValues", "");
        } else if (StringUtils.isBlank(tSysParam1.getParValues())) {
            jsonObject.put("parValues", "");
        } else {
            String parValues = tSysParam1.getParValues();
            JSONArray jsonArrayParValues = JSONArray.fromObject(parValues);
            StringBuilder temp = new StringBuilder("");
            for (Object jsonArrayParValue : jsonArrayParValues) {
                if (jsonArrayParValue != null){

                    JSONObject jsonObject1 = JSONObject.fromObject(jsonArrayParValue);
                    if (null != jsonObject1){
                        Object value1 = jsonObject1.get("value");
                        temp.append(value1).append(",");
                    }else {
                        temp.append(" ").append(",");
                    }
                }else {
                    temp.append(" ").append(",");
                }
            }
            jsonObject.put("parValues", temp.toString());
        }
        jsonObject.put("paramType", "3");
        JSONObject j = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("parId", tSysParam1.getParId() + "-2");
        if ("[]".equals(tSysParam1.getParValues())) {
            jsonObject1.put("parValues", "");
        } else if (StringUtils.isBlank(tSysParam1.getParValues())) {
            jsonObject1.put("parValues", "");
        } else {
            String parValues = tSysParam1.getParValues();
            JSONArray jsonArrayParValues = JSONArray.fromObject(parValues);

            for (Object jsonArrayParValue : jsonArrayParValues) {
                Boolean checked = (Boolean) JSONObject.fromObject(jsonArrayParValue).get("checked");
                if (checked) {
                    jsonObject1.put("parValues", JSONObject.fromObject(jsonArrayParValue).get("value"));
                }
            }
        }
        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);

        jsonObject1.put("parameterDiagramURL", StringUtils.isBlank(tSysParam1.getParameterDiagram()) ? " " : tSysParam1.getParameterDiagram());
        jsonObject1.put("paramType", "21");

        for (TSysParamInitDesc sysParamInitDesc : pageListByObj) {
            j.put("label", sysParamInitDesc.getParamInitName());
            j.put("value", sysParamInitDesc.getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(pageListByObj.get(0).getOptionDiagram()) ? " " : pageListByObj.get(0).getOptionDiagram());
            jsonArray.add(j);

        }
        jsonObject1.put("paramInitDesc", jsonArray);
        itemArray.add(jsonObject1);
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew) {
        //[{"label": "临嘱","value": "1","checked": true},{"label": "长嘱","value": "2","checked": false}]
        String value1 = saveParameter.getValue1();
        String value2 = saveParameter.getValue2();
        if (!StringUtils.isBlank(value1)) {
            String[] split = value1.split(",");
            JSONArray jsonArray = new JSONArray();
            for (String s : split) {
                TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
                tSysParamInitDesc.setParamId(tSysParamNew.getParId());
                tSysParamInitDesc.setParamInitCode(s);
                TSysParamInitDesc initByCondition = tSysParamInitDescMapper.getInitByCondition(tSysParamInitDesc);
                if (null != initByCondition) {
                    JSONObject object = new JSONObject();
                    object.put("label", initByCondition.getParamInitName());
                    object.put("value", initByCondition.getParamInitCode());
                    if (!StringUtils.isBlank(value2) && value2.equals(initByCondition.getParamInitCode())) {
                        //默认选中
                        object.put("checked", true);
                    } else {
                        object.put("checked", false);
                    }
                    jsonArray.add(object);
                }

            }
            tSysParamVO.setParValues(jsonArray.toString());
        } else {
//            tSysParamVO.setParValues("[]");
            tSysParamVO.setParValues("");
        }

    }
}
