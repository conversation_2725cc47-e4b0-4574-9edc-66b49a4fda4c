package com.jiuzhekan.cbkj.service.sysService;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.sysBeans.*;
import com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.common.utils.encry.RSAEncryption;
import com.jiuzhekan.cbkj.controller.sysService.vo.SysAdminPracticeVo;
import com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.AdminInfoMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysAdminGrayscaleMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysAdminInfoexMapper;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysDoctorMultipointMapper;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AdminService implements UserDetailsService {


    @Autowired
    private AdminInfoMapper adminInfoMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RSAEncryption rsaEncryption;

    @Value("${rsa.privateKey}")
    private String privateKey;

    @Autowired
    private SysDoctorMultipointService sysDoctorMultipointService;

    @Autowired
    private DicBaseMapper dicBaseMapper;

    @Autowired
    private SysDoctorMultipointMapper sysDoctorMultipointMapper;
    //    @Autowired
//    private AdminRuleMapper adminRuleMapper;
    @Autowired
    private SysDepartmentMapper sysDepartmentMapper;
    @Autowired
    private SysAdminInfoexMapper sysAdminInfoexMapper;

    @Autowired
    private SysAdminGrayscaleMapper sysAdminGrayscaleMapper;

    public ResEntity getUserEx(String userId) {
        SysAdminInfoex infoex1 = new SysAdminInfoex();
        infoex1.setUserId(userId);
        List<SysAdminInfoex> infoexList = sysAdminInfoexMapper.getObjectByUserId(infoex1);
        AdminInfo adminInfo = adminInfoMapper.selectByPrimaryKey(userId);
        if (null != adminInfo && infoexList.size() == 1) {
            infoexList.get(0).setIsQualifier(adminInfo.getIsQualifier());
            return new ResEntity(true, Constant.SUCCESS_DX, infoexList.get(0));
        }
        return new ResEntity(true, Constant.SUCCESS_DX, new SysAdminInfoex());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity setUserEx(SysAdminInfoex sysAdminInfoex) {
        if (StringUtils.isBlank(sysAdminInfoex.getUserId())) {
            return new ResEntity(false, "用户id必填！！", null);
        }
        if (StringUtils.isBlank(sysAdminInfoex.getIsQualifier())) {
            return new ResEntity(false, "中医资质必填！！", null);
        }
        SysAdminInfoex infoex = new SysAdminInfoex();
        infoex.setUserId(sysAdminInfoex.getUserId());
        long countByObj = sysAdminInfoexMapper.getCountByObj(infoex);
        if (countByObj > 0) {
            //修改
//            if (StringUtils.isBlank(sysAdminInfoex.getAdminInfoexId())){
//                return new ResEntity(false, "用户扩展表ID必填！！", null);
//            }
            SysAdminInfoex adminInfoex = new SysAdminInfoex();
            adminInfoex.setUserId(sysAdminInfoex.getUserId());
            List<SysAdminInfoex> objectByUserId = sysAdminInfoexMapper.getObjectByUserId(adminInfoex);
            if (objectByUserId.size() == 1) {
                //sysAdminInfoex.setAdminInfoexId(objectByUserId.get(0).getAdminInfoexId());
                sysAdminInfoex.setUpdateDate(new Date());
                //修改医生扩展信息
                sysAdminInfoexMapper.updateByPrimaryKey(sysAdminInfoex);
            }

        } else {
            //新增
            //sysAdminInfoex.setAdminInfoexId(IDUtil.getID());
            sysAdminInfoexMapper.insert(sysAdminInfoex);
        }
        AdminInfo adminInfo = new AdminInfo();
        adminInfo.setUserId(sysAdminInfoex.getUserId());
        adminInfo.setIsQualifier(sysAdminInfoex.getIsQualifier());
        //修改中医资质，在用户表中
        adminInfoMapper.updateByPrimaryKey(adminInfo);
        return new ResEntity(true, Constant.SUCCESS_DX, sysAdminInfoex);
    }

    public void turnSysAdminInfoEx(SysAdminInfoex sysAdminInfoex) {
        //病历模板权限
        if (!StringUtils.isBlank(sysAdminInfoex.getDescriptionShare())) {
            String[] shareTypes = new String[]{"0", "1", "2", "3", "4"};
            String[] shareTexts = Constant.TEMPLATE_SHARE_TEXT;
            String[] types = sysAdminInfoex.getDescriptionShare().split(",");
            StringBuilder temp = new StringBuilder("");
            for (String shareType : types) {
                if (Arrays.asList(shareTypes).contains(shareType)) {
                    temp.append(shareTexts[Integer.parseInt(shareType)]).append(",");
                }
            }
            sysAdminInfoex.setUserExText(temp.toString());
        }
    }

    /**
     * 根据用户名加载用户信息
     * 用户名唯一 使用手机号
     *
     * <AUTHOR>
     * @date 2019/12/13 15:01
     */
    @Override
//    @Cacheable(value = "pre-parameter-user", key = "#username")
    public AdminInfo loadUserByUsername(String username) throws UsernameNotFoundException {
        AdminInfo admin = adminInfoMapper.loadUserByUsername(username);
        return loadUser(admin);
    }

    public AdminInfo loadUser(AdminInfo admin) {
        if (null == admin) {
            throw new UsernameNotFoundException("用户名或密码错误！");
        }
        if (admin.getExpireDate() != null && admin.getExpireDate().before(new Date())) {
            throw new UsernameNotFoundException("用户已过期");
        }


        return admin;
    }

    /**
     * 加载分页数据
     *
     * @param admin
     * @param page
     * @return
     */
    public Object getPageDatas(AdminInfo admin, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AppsInfo> lis = adminInfoMapper.getPageDatas(admin);
        for (int i = 0; i < lis.size(); i++) {
            if (StringUtils.isNotBlank(lis.get(i).getRnamess()) ){
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> rnamess = Arrays.stream(lis.get(i).getRnamess().split(",")).distinct().collect(Collectors.toList());
                rnamess.forEach(r -> temp.set(temp + r + ","));
                lis.get(i).setRnamess(temp.toString());
            }
            if (StringUtils.isNotBlank(lis.get(i).getInsNames()) ){
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> rnamess = Arrays.stream(lis.get(i).getInsNames().split(",")).distinct().collect(Collectors.toList());
                rnamess.forEach(r -> temp.set(temp + r + ","));
                lis.get(i).setInsNames( temp.toString());
            }
            if (StringUtils.isNotBlank(lis.get(i).getDoctorMultipoint()) ) {
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> rnamess = Arrays.stream(lis.get(i).getDoctorMultipoint().split(",")).distinct().collect(Collectors.toList());
                rnamess.forEach(r -> temp.set(temp + r + ","));
                lis.get(i).setDoctorMultipoint( temp.toString());
            }
        }
        Object result = Page.getLayUiTablePageData(lis);
        return result;
    }

    /**
     * 插入新的管理员
     *
     * @param adminInfo
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"client::user::userId", "parameter::params::all::userId"}, allEntries = true)
    public ResEntity insert(AdminInfo adminInfo) {

        if (StringUtils.isBlank(adminInfo.getUsername())) {
            return new ResEntity(false, "账号必填！！", null);
        }
        if (StringUtils.isBlank(adminInfo.getNameZh())) {
            return new ResEntity(false, "中文名必填！！", null);
        }
        if (null == adminInfo.getRoles() || adminInfo.getRoles().size() <= 0) {
            return new ResEntity(false, "请选择角色！！", null);
        }

        Map<String, Object> param = new HashMap<>();
        param.put("id", adminInfo.getUserId());
        param.put("name", adminInfo.getUsername());

        List<Map<String, Object>> infos = adminInfoMapper.validateParam(param);
        if (null != infos && !infos.isEmpty()) {
            return ResEntity.entity(false, "用户名已存在，请重新输入", null);
        }

        adminInfo.setUserId(IDUtil.getID());
        adminInfo.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        adminInfo.setCreateUsername(AdminUtils.getCurrentHr().getNameZh());
        adminInfo.setCreateDate(new Date());
        adminInfo.setAppId(null == adminInfo.getAppId() ? Constant.BASIC_APP_ID : adminInfo.getAppId());
        adminInfo.setInsCode(null == adminInfo.getInsCode() ? Constant.BASIC_INS_CODE : adminInfo.getInsCode());
        adminInfo.setDeptId(null == adminInfo.getDeptId() ? Constant.BASIC_DEPT_ID : adminInfo.getDeptId());

        //adminInfo.setStatus(Constant.BASIC_STRING_ONE);
        if (StringUtils.isBlank(adminInfo.getPassword())) {
            //默认密码123456
            adminInfo.setPassword(MD5Util.encode("123456"));
        } else {
            adminInfo.setPassword(MD5Util.encode(adminInfo.getPassword()));
        }
        adminInfo.setIsQualifier(Constant.BASIC_STRING_ONE);
        long rows = adminInfoMapper.insert(adminInfo);
        if (rows > 0) {
            List<AdminInfoRule> adminRuleList = new ArrayList<>();
            getAdminInfoRuleList(adminInfo, adminRuleList);
            adminInfoMapper.insertList(adminRuleList);
            SysAdminPracticeVo sysDoctorMultipointVo = adminInfo.getSysDoctorMultipointVo();
            sysDoctorMultipointVo.setUserId(adminInfo.getUserId());
            List<SysDoctorMultipoint> practiceList = sysDoctorMultipointVo.getPracticeList();
            for (SysDoctorMultipoint sysDoctorMultipoint : practiceList) {
                if (null != sysDoctorMultipoint.getDeptId()) {
                    SysDepartment sysDepartment = new SysDepartment();
                    sysDepartment.setAppId(sysDoctorMultipoint.getAppId());
                    sysDepartment.setInsCode(sysDoctorMultipoint.getInsCode());
                    sysDepartment.setDeptId(sysDoctorMultipoint.getDeptId());
                    String a = sysDepartmentMapper.getDeptNameByAppInCodeDeptOriginId(sysDepartment);
                    if (null != a) {
                        sysDoctorMultipoint.setDeptName(a);
                    } else {
                        String name = sysDepartmentMapper.getDeptNameByAppInCodeDeptID(sysDepartment);
                        if (null != name) {
                            sysDoctorMultipoint.setDeptName(name);
                        }
                    }
                }
                String employeeId = sysDoctorMultipoint.getEmployeeId();
                //工号id支持用户对象输入，也支持跟随每个执业机构输入。
                if (StringUtils.isBlank(employeeId)) {
//                    if (!StringUtils.isBlank(adminInfo.getEmployeeId())) {
//                        if (checkUserEmployeeId(sysDoctorMultipoint.getAppId(),sysDoctorMultipoint.getInsCode(),adminInfo.getEmployeeId(),null)){
//                            return ResEntity.entity(false, "工号已存在，请重新输入", null);
//                        }
//                        sysDoctorMultipoint.setEmployeeId(adminInfo.getEmployeeId());
//                    }
                } else {
                    if (checkUserEmployeeId(sysDoctorMultipoint.getAppId(), sysDoctorMultipoint.getInsCode(), employeeId, null)) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return ResEntity.entity(false, "工号已存在，请重新输入", null);
                    }
                }
                sysDoctorMultipoint.setStatus("0");
            }

            sysDoctorMultipointService.save(sysDoctorMultipointVo);
            //配置医生权限
            SysAdminInfoex sysAdminInfoex = new SysAdminInfoex();
            sysAdminInfoex.setSpecialDrugsUsePermission(Constant.BASIC_STRING_ONE);
            sysAdminInfoex.setPrescriptionShare(Constant.BASIC_STRING_ZERO + "," + Constant.BASIC_STRING_ONE);
            sysAdminInfoex.setDescriptionShare(Constant.BASIC_STRING_ZERO + "," + Constant.BASIC_STRING_ONE);
//            sysAdminInfoex.setDefaultPrescriptionShare(Constant.BASIC_STRING_ONE + "," + Constant.BASIC_STRING_TWO + "," + Constant.BASIC_STRING_FOUR);
            sysAdminInfoex.setDefaultPrescriptionShare(Constant.BASIC_STRING_ONE);
            //sysAdminInfoex.setAdminInfoexId(IDUtil.getID());
            sysAdminInfoex.setUserId(adminInfo.getUserId());
            sysAdminInfoexMapper.insert(sysAdminInfoex);
            return new ResEntity(true, Constant.SUCCESS_DX, null);
        }

        return new ResEntity(false, "用户名不能重复哦", null);
    }

    /**
     * @param appId
     * @param insCode
     * @param employeeId
     * @return 如果同意机构有重复就返回ture
     */
    public boolean checkUserEmployeeId(String appId, String insCode, String employeeId, String userId) {
        if (StringUtils.isBlank(employeeId)) {
            return false;
        }
        return sysDoctorMultipointService.checkUserEmployeeId(appId, insCode, employeeId, userId);
    }

    /**
     * 修改管理员
     *
     * @param adminInfo
     * @return
     */
//    @CacheEvict(value = "pre-parameter-user", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"client::user::userId", "parameter::params::all::userId"}, allEntries = true)
    public ResEntity update(AdminInfoVO adminInfo) {
        if (!StringUtils.isBlank(adminInfo.getPassword())) {
            String newPwd = URLDecoder.decode(adminInfo.getPassword());
            try {
                newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
            } catch (Exception e) {
                return new ResEntity(false, "加密错误", null);
            }
            newPwd = MD5Util.encode(newPwd);
            adminInfo.setPassword(newPwd);
        }
        if (StringUtils.isBlank(adminInfo.getUserId())) {
            return new ResEntity(false, "用户userId不能为空！！", null);
        }
        if (StringUtils.isBlank(adminInfo.getUserName())) {
            return new ResEntity(false, "账号必填！！", null);
        }
        if (StringUtils.isBlank(adminInfo.getNameZh())) {
            return new ResEntity(false, "中文名必填！！", null);
        }
//        if (StringUtils.isBlank(adminInfo.getPhone())) {
//            return new ResEntity(false, "手机号必填！！", null);
//        }
        if (null == adminInfo.getRoles() || adminInfo.getRoles().size() <= 0) {
            return new ResEntity(false, "请选择角色！！", null);
        }

        Map<String, Object> param = new HashMap<>(16);
        param.put("id", adminInfo.getUserId());
        param.put("name", adminInfo.getUserName());

        List<Map<String, Object>> infos = adminInfoMapper.validateParam(param);
        if (null != infos && !infos.isEmpty()) {
            return ResEntity.entity(false, "用户名已存在，请重新输入", null);
        }
        Date update = new Date();
        //adminInfo.setUpdateTime(new Date());
        adminInfo.setUpdateUser(AdminUtils.getCurrentHr().getUserId());
        adminInfo.setUpdateUserName(AdminUtils.getCurrentHr().getUsername());
        adminInfo.setUpdateDate(update);
        AdminInfo adminInfo1 = new AdminInfo();
        BeanUtils.copyProperties(adminInfo, adminInfo1);
        adminInfo1.setUserName(adminInfo.getUserName());
        adminInfo1.setCertificate(adminInfo.getCertificate());
        //adminInfo1.setUpdateDate(update);
        adminInfoMapper.updateByPrimaryKey(adminInfo1);
        adminInfoMapper.deleteAdminInfoRuleByAdminId(adminInfo.getUserId());
        List<AdminInfoRule> adminRuleList = new ArrayList<>();
        getAdminInfoRuleList(adminInfo1, adminRuleList);
        adminInfoMapper.insertList(adminRuleList);
        SysAdminPracticeVo sysDoctorMultipointVo = adminInfo.getSysDoctorMultipointVo();
        List<SysDoctorMultipoint> practiceList = sysDoctorMultipointVo.getPracticeList();
        for (SysDoctorMultipoint sysDoctorMultipoint : practiceList) {
            if (null != sysDoctorMultipoint.getDeptId()) {
                SysDepartment sysDepartment = new SysDepartment();
                sysDepartment.setAppId(sysDoctorMultipoint.getAppId());
                sysDepartment.setInsCode(sysDoctorMultipoint.getInsCode());
                sysDepartment.setDeptId(sysDoctorMultipoint.getDeptId());
                String a = sysDepartmentMapper.getDeptNameByAppInCodeDeptID(sysDepartment);
                if (null != a) {
                    sysDoctorMultipoint.setDeptName(a);
                } else {
                    String name = sysDepartmentMapper.getDeptNameByAppInCodeDeptOriginId(sysDepartment);
                    if (null != name) {
                        sysDoctorMultipoint.setDeptName(name);
                    }
                }
            }
            sysDoctorMultipoint.setStatus("0");
            String employeeId = sysDoctorMultipoint.getEmployeeId();

            if (StringUtils.isBlank(sysDoctorMultipoint.getEmployeeId())) {
//                if (checkUserEmployeeId(sysDoctorMultipoint.getAppId(),sysDoctorMultipoint.getInsCode(),adminInfo.getEmployeeId(),adminInfo.getUserId())){
//                    return ResEntity.entity(false, "工号已存在，请重新输入", null);
//                }
//                sysDoctorMultipoint.setEmployeeId(adminInfo.getEmployeeId());
            } else {
                if (checkUserEmployeeId(sysDoctorMultipoint.getAppId(), sysDoctorMultipoint.getInsCode(), employeeId, adminInfo.getUserId())) {
                    return ResEntity.entity(false, "工号已存在，请重新输入", null);
                }
            }
        }
        sysDoctorMultipointService.save(adminInfo.getSysDoctorMultipointVo());
        AdminInfo adminInfo2 = new AdminInfo();
        adminInfo2.setUserId(adminInfo.getUserId());
        List<AppsInfo> lis = adminInfoMapper.getPageDatas(adminInfo2);
        if (lis.size() == 1) {
            AppsInfo stringObjectMap = lis.get(0);
            if (StringUtils.isNotBlank(stringObjectMap.getRnamess()) ) {
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> rnamess = Arrays.stream(stringObjectMap.getRnamess().split(",")).distinct().collect(Collectors.toList());
                rnamess.forEach(r -> temp.set(temp + r + ","));
                adminInfo.setRnamess(temp.toString());
            }
            if (StringUtils.isNotBlank(stringObjectMap.getDoctorMultipoint()) ){
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> doctorMultipoint = Arrays.stream(stringObjectMap.getDoctorMultipoint().toString().split(",")).distinct().collect(Collectors.toList());
                doctorMultipoint.forEach(r -> temp.set(temp + r + ","));
                adminInfo.setDoctorMultipoint(temp.toString());
            }
        }

        redisService.clearRedisCache("pre-ai-params:allParams::" + adminInfo.getUserId(), null);
        adminInfo.setRoles(null);
        return new ResEntity(true, Constant.SUCCESS_DX, adminInfo);
    }


    /**
     * 获取当前系统所有角色
     *
     * @return
     */
    public Object getRoles() {
        AdminRule adminRule = new AdminRule();

        List<Map<String, Object>> list = adminInfoMapper.getRoles(adminRule);
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    private void getAdminInfoRuleList(AdminInfo adminInfo, List<AdminInfoRule> adminRuleList) {
        for (AdminRule adminRule : adminInfo.getRoles()) {
            AdminInfoRule adminInfoRule = new AdminInfoRule();
//            adminInfoRule.setIrID(IDUtil.getID());
            adminInfoRule.setUserId(adminInfo.getUserId());
            adminInfoRule.setRoleId(adminRule.getRoleId());
            adminRuleList.add(adminInfoRule);
        }
    }

    /**
     * 加载某个管理员
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {
        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "管理主键不能为空哦", null);
        }
        AdminInfo adminInfo = adminInfoMapper.selectAdminRoleById(id);
        if (null == adminInfo) {
            return new ResEntity(true, Constant.SUCCESS_DX, null);
        }
        if (StringUtils.isNotBlank(adminInfo.getQualifierPicPath())) {
            adminInfo.setPicList(Arrays.asList(adminInfo.getQualifierPicPath().split(",")));
        }
        AdminInfoVO adminInfoVO = new AdminInfoVO();
        BeanUtils.copyProperties(adminInfo, adminInfoVO);
        adminInfoVO.setUserName(adminInfo.getUsername());
        SysAdminPracticeVo multiPoint = sysDoctorMultipointService.getMultiPoint(id, null, null);
        adminInfoVO.setSysDoctorMultipointVo(multiPoint);

        SysAdminInfoex infoex = new SysAdminInfoex();
        infoex.setUserId(id);
        List<SysAdminInfoex> list = sysAdminInfoexMapper.getObjectByUserId(infoex);
        if (list.size() == 1) {
            list.get(0).setIsQualifier(adminInfoVO.getIsQualifier());
            adminInfoVO.setSysAdminInfoex(list.get(0));
        }
        return new ResEntity(true, Constant.SUCCESS_DX, adminInfoVO);
    }

    /**
     * 重置密码
     *
     * @param ids
     * @param newPwd
     * @return
     */
//    @CacheEvict(value = "pre-parameter-user", allEntries = true)
    public ResEntity resetPwd(String ids, String newPwd) {

        if (StringUtils.isBlank(ids) || StringUtils.isBlank(newPwd)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }

        try {
            newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
        } catch (Exception e) {
            return new ResEntity(false, "加密错误", null);
        }

        if (!this.checkPassword(newPwd)) {
            return new ResEntity(false, "密码需为数字、字母、特殊符号中的最少2种，最小8位，最大18位！", null);
        }
        newPwd = MD5Util.encode(newPwd);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("newPwd", newPwd);
        params.put("ids", ids.split(","));
        params.put("last_update_pwd", new Date());

        long rows = adminInfoMapper.updatePwd(params);
        return new ResEntity(true, Constant.SUCCESS_DX, rows);
    }

    /**
     * 修改密码
     *
     * @param oldPwd oldPwd
     * @param newPwd newPwd
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = "pre-parameter-user", allEntries = true)
    public ResEntity updatePwd(String oldPwd, String newPwd) {

        String id = AdminUtils.getCurrentHr().getUserId();

        if (StringUtils.isBlank(id) || StringUtils.isBlank(oldPwd) || StringUtils.isBlank(newPwd)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }

        try {
            oldPwd = rsaEncryption.dencryptByPrivateKey(oldPwd, privateKey);
            newPwd = rsaEncryption.dencryptByPrivateKey(newPwd, privateKey);
        } catch (Exception e) {
            return new ResEntity(false, "加密错误", null);
        }

        if (!this.checkPassword(newPwd)) {
            return new ResEntity(false, "密码需为数字、字母、特殊符号中的最少2种，最小8位，最大18位！", null);
        }

        AdminInfo admin = adminInfoMapper.selectByPrimaryKey(id);
        if (admin == null) {
            return new ResEntity(false, "当前用户不存在！", null);
        }
        if (!MD5Util.encode(oldPwd).equals(admin.getPassword())) {
            return new ResEntity(false, "原密码错误！", null);
        }

        newPwd = MD5Util.encode(newPwd);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("newPwd", newPwd);
        params.put("ids", id.split(","));
        params.put("last_update_pwd", new Date());

        long rows = adminInfoMapper.updatePwd(params);
        return new ResEntity(true, Constant.SUCCESS_DX, rows);
    }

    /**
     * 禁用/启用管理员
     *
     * @param id
     * @param status
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = "pre-parameter-user", allEntries = true)
    public ResEntity updateStatus(String id, String status) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(status)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("id", id);
        params.put("status", status);
        long rows = adminInfoMapper.updateStatus(params);
        return new ResEntity(true, Constant.SUCCESS_DX, rows);
    }

    /**
     * 删除管理员
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = "pre-parameter-user", allEntries = true)
    public ResEntity deleteLis(String ids) throws Exception {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = adminInfoMapper.deleteAdminRole(ids.split(","));
        long rowsA = 0;

        Map<String, Object> params = new HashMap<>();
        //params.put("delUser", AdminUtils.getCurrentHr().getUserId());
        //params.put("delUsername", AdminUtils.getCurrentHr().getNameZh());
        params.put("array", ids.split(","));
        rowsA = adminInfoMapper.deleteBylis(params);
        sysDoctorMultipointMapper.deleteByAdminIds(params);


        return new ResEntity(true, Constant.SUCCESS_DX, rowsA);

    }


    /**
     * 验证登录名
     *
     * @param appId
     * @param name
     * @return
     */
    public ResEntity validateParam(String id, String appId, String name) {
        //验证登录名
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("appId", appId);
        param.put("name", name);

        List<Map<String, Object>> infos = adminInfoMapper.validateParam(param);
        if (null != infos && !infos.isEmpty()) {
            Object zhO = infos.get(0).get("name_zh");
            String name_zh = null != zhO ? zhO.toString() : "";
            return ResEntity.entity(false, "登录名与  " + name_zh + " 的相同，请重新输入", null);
        }
        //验证工号
        Map<String, Object> param2 = new HashMap<>();
        param2.put("id", id);
        param2.put("appId", appId);
        List<Map<String, Object>> infos2 = adminInfoMapper.validateParam(param2);
        if (null != infos2 && !infos2.isEmpty()) {
            Object zhO = infos.get(0).get("name_zh");
            String name_zh = null != zhO ? zhO.toString() : "";
            return ResEntity.entity(false, "工号与  " + name_zh + " 的相同，请重新输入", null);
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, null);


    }


    /**
     * 获取手机号
     *
     * @param id
     * @return
     */
    public ResEntity findPhone(String id) {

        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "管理主键不能为空哦", null);
        }
        AdminInfo adminInfo = adminInfoMapper.selectByPrimaryKey(id);

        return new ResEntity(true, Constant.SUCCESS_DX, adminInfo);
    }


    /**
     * 密码规则校验
     *
     * @param str
     * @return boolean
     * <AUTHOR>
     * @date 2020/8/21
     */
    private boolean checkPassword(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        int a = 0;//是否是数字
        int b = 0;//师傅是字母
        int c = 0;//是否是其他符号
        for (int i = 0; i < str.length(); i++) {
            if (Character.isWhitespace(str.charAt(i))) {//是否是有空白符，不允许有空白符
                return false;
            } else if (Character.isDigit(str.charAt(i))) {
                a = 1;
            } else if (Character.isLetter(str.charAt(i))) {
                b = 1;
            } else {
                c = 1;
            }
        }

        boolean checkRule = false;
        int sum = a + b + c;
        if (sum >= 2) {
            checkRule = true;
        }
        boolean checkLength = false;
        int length = str.length();
        if (length >= 8 && length <= 18) {
            checkLength = true;
        }

        if (checkRule && checkLength) {
            return true;
        }
        return false;
    }

    /**
     * EXCEL导入用户数据
     *
     * @param file
     * @return
     */
    public Object importIns(MultipartFile file) {
        InputStream inputStream = null;
        List<Map<String, Object>> excelList = null;
        try {
            inputStream = file.getInputStream();
            excelList = ExcelUtils.readXlsxFirstSheet(inputStream, 1);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        Map<String, Object> stringObjectMap = importInsData(excelList);
        return ResEntity.entity(true, Constant.SUCCESS_DX, stringObjectMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importInsData(List<Map<String, Object>> excelList) {
        StringBuilder errorCount = new StringBuilder();

        Integer errorCounts = 0;
        //真实总条数
        Integer realCounts = 0;
        if (excelList != null && excelList.size() > 0) {
            AdminInfo ins;
            AdminInfoRule adminInfoRule;
            SysDoctorMultipoint sysDoctorMultipoint;
            Date newDate = new Date();
            AdminInfo admin = AdminUtils.getCurrentHr();

            for (int i = 0; i < excelList.size(); i++) {
                List<AdminInfoRule> adminInfoRules = new ArrayList<>();
                List<SysDoctorMultipoint> sysDoctorMultipoints = new ArrayList<>();
                if (null == excelList.get(i)) {
                    //realCounts = realCounts+1;
                    continue;
                }
                Map<String, Object> om = excelList.get(i);
                if (om.size() == 0) {
                    continue;
                }
                realCounts = realCounts + 1;

                String sort = om.get("*序号").toString();
                String userName = om.get("*登录名").toString();
                String userNameZh = om.get("*姓名").toString();
                String sex = om.get("*性别").toString();
                String employeeId = om.get("工号").toString();
                String idCard = om.get("*身份证号").toString();
                String phone = om.get("*手机号码").toString();
                String email = null == om.get("邮箱") ? null : om.get("邮箱").toString();
                String address = null == om.get("住址") ? null : om.get("住址").toString();
                String expireDate = null == om.get("账号有效期") ? null : om.get("账号有效期").toString();
                String roesIds = om.get("*角色").toString();
                String doctorMultipoint = om.get("*执业机构").toString();
                String professionalNames = om.get("*职称").toString();
                if (!StringUtils.isBlank(doctorMultipoint) &&
                        doctorMultipoint.split(",").length >= 3 &&
                        !StringUtils.isBlank(professionalNames) &&
                        !StringUtils.isBlank(sort) &&
                        !StringUtils.isBlank(userName) &&
                        !StringUtils.isBlank(userNameZh) &&
                        !StringUtils.isBlank(sex) &&
                        !StringUtils.isBlank(idCard) &&
                        !StringUtils.isBlank(phone) &&
                        !StringUtils.isBlank(roesIds) &&
                        !StringUtils.isBlank(doctorMultipoint)

                ) {

                } else {
                    errorCount.append("第").append(i + 1).append("行导入失败，请检查数据").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }
                Map<String, Object> param = new HashMap<>();

                param.put("name", userName);

                List<Map<String, Object>> infos = adminInfoMapper.validateParam(param);
                if (null != infos && !infos.isEmpty()) {
                    errorCount.append("第").append(i + 1).append("行导入重复，请检查数据").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }
                String[] split2 = professionalNames.split(",");
                int tempdic = 0;
                String professionalCodes = "";
                for (String value : split2) {
                    List<TDicBase> dicByName = dicBaseMapper.getDicByName(value);
                    if (dicByName.size() == 1) {
                        professionalCodes = professionalCodes + dicByName.get(0).getDicCode() + ",";
                    } else {
                        tempdic = 1;
                        break;
                    }
                }
                if (tempdic == 1) {
                    errorCount.append("第").append(i + 1).append("行导入失败，请检查职称数据（含有不存在的职称）").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }
                ins = new AdminInfo();
                ins.setAppId(Constant.BASIC_APP_ID);
                ins.setInsCode(Constant.BASIC_INS_CODE);
                ins.setDeptId(Constant.BASIC_DEPT_ID);
                ins.setPassword(MD5Util.encode("123456"));
                if (professionalCodes.contains(",")) {
                    ins.setProfessional(professionalCodes.substring(0, professionalCodes.length() - 1));
                } else {
                    ins.setProfessional(professionalCodes);
                }

                ins.setUserId(IDUtil.getID());
                ins.setSort(sort);
                ins.setUserName(userName);
                ins.setNameZh(userNameZh);

                ins.setSex("男".equals(sex) ? "M" : "F");
                ins.setCertificate(idCard);
                ins.setPhone(phone);
                ins.setEmail(email);
                ins.setAddress(StringUtils.isBlank(address) ? "" : address);
                if (null != expireDate) {
                    DateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                    try {
                        Date parse = sdf.parse(expireDate);
                        ins.setExpireDate(parse);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                ins.setCreateDate(newDate);
                ins.setCreateUser(admin.getUserId());
                ins.setCreateUsername(admin.getNameZh());
                ins.setStatus(Constant.BASIC_STRING_ZERO);
                ins.setSort(sort);

                if (!StringUtils.isBlank(roesIds)) {
                    String[] split = roesIds.split(",");
                    for (String s : split) {
                        adminInfoRule = new AdminInfoRule();
                        adminInfoRule.setUserId(ins.getUserId());
                        adminInfoRule.setRoleId(s);
                        adminInfoRules.add(adminInfoRule);
                    }
                } else {
                    errorCount.append("第").append(i + 1).append("行导入失败，请检查角色数据").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }
                if (!StringUtils.isBlank(doctorMultipoint) && doctorMultipoint.split(",").length >= 3) {

                    String[] split = doctorMultipoint.split(";");
                    HashSet<String> strings = new HashSet<>();
                    for (String s : split) {
                        sysDoctorMultipoint = new SysDoctorMultipoint();
                        String[] split1 = s.split(",");
                        sysDoctorMultipoint.setAppId(split1[0]);
                        sysDoctorMultipoint.setInsCode(split1[1]);

                        SysDepartment sysDepartment = new SysDepartment();
                        sysDepartment.setAppId(split1[0]);
                        sysDepartment.setInsCode(split1[1]);
                        sysDepartment.setDepOriginId(split1[2]);

                        SysDepartment objectByDeptOriginId = sysDepartmentMapper.getObjectByDeptOriginId(sysDepartment);
                        if (null != objectByDeptOriginId) {
                            if (!StringUtils.isBlank(objectByDeptOriginId.getDepOriginId())) {
                                sysDoctorMultipoint.setDeptId(objectByDeptOriginId.getDepOriginId());
                            } else {
                                sysDoctorMultipoint.setDeptId(objectByDeptOriginId.getDeptId());
                            }
                        }
                        sysDoctorMultipoint.setUserId(ins.getUserId());
                        sysDoctorMultipoint.setStatus(Constant.BASIC_STRING_ZERO);
                        sysDoctorMultipoint.setEmployeeId(StringUtils.isBlank(employeeId) ? "" : employeeId);
                        sysDoctorMultipoint.setCreateDate(newDate);
                        sysDoctorMultipoint.setCreateUser(admin.getUserId());
                        sysDoctorMultipoints.add(sysDoctorMultipoint);
                        strings.add(split1[0] + split1[1]);
                    }
                    if (strings.size() != split.length) {
                        errorCount.append("第").append(i + 1).append("行导入失败，请检查执业机构（医共体和机构不允许重复）").append(",");
                        errorCounts = errorCounts + 1;
                        continue;
                    }
                }

                //adminInfos.add(ins);
                //单个导入
                ins.setIsQualifier(Constant.BASIC_STRING_ONE);
                adminInfoMapper.insert(ins);
                //导入角色
                adminInfoMapper.insertList(adminInfoRules);
                //导入多执业机构
                sysDoctorMultipointMapper.insertList(sysDoctorMultipoints);
                //增加用户权限配置
                SysAdminInfoex sysAdminInfoex = new SysAdminInfoex();
                sysAdminInfoex.setSpecialDrugsUsePermission(Constant.BASIC_STRING_ONE);
                sysAdminInfoex.setPrescriptionShare(Constant.BASIC_STRING_ZERO + "," + Constant.BASIC_STRING_ONE);
                sysAdminInfoex.setDescriptionShare(Constant.BASIC_STRING_ZERO + "," + Constant.BASIC_STRING_ONE);
                sysAdminInfoex.setDefaultPrescriptionShare(Constant.BASIC_STRING_ONE + "," + Constant.BASIC_STRING_TWO + "," + Constant.BASIC_STRING_FOUR);
                //sysAdminInfoex.setAdminInfoexId(IDUtil.getID());
                sysAdminInfoex.setUserId(ins.getUserId());
                sysAdminInfoexMapper.insert(sysAdminInfoex);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("errorNumber", errorCounts);
        String[] split = errorCount.toString().split(",");
        map.put("description", split);
        map.put("total", realCounts);
        map.put("successNumber", realCounts - errorCounts);
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object setGrayscale(String userIds) {
        String[] split = userIds.split(",");
        ArrayList<AdminGrayscale> adminGrayscales = new ArrayList<>();
        ArrayList<JSONObject> adminInfos = new ArrayList<>();
        for (String s : split) {
            AdminInfo userNameById = adminInfoMapper.getGrayscaleUserById(s);
            if (null == userNameById) {
                AdminGrayscale adminGrayscale = new AdminGrayscale(s, 0, new Date(), AdminUtils.getCurrentHr().getUserId(), AdminUtils.getCurrentHr().getNameZh());
                adminGrayscales.add(adminGrayscale);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("userId", userNameById.getUserId());
                jsonObject.put("nameZh", userNameById.getNameZh());
                jsonObject.put("userName", userNameById.getUsername());
                adminInfos.add(jsonObject);
            }
        }
        JSONObject jsonObject = new JSONObject();
        if (adminGrayscales.size() > 0) {
            sysAdminGrayscaleMapper.insertList(adminGrayscales);
        }

        jsonObject.put("successAdmin", adminGrayscales.size());
        jsonObject.put("failAdmin", adminInfos.toString());

        return ResEntity.entity(true, "", jsonObject);
    }

    public Object getGrayscale(AdminInfo adminInfo, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<Grayscale> grayscaleStatus = adminInfoMapper.getGrayscaleStatus(adminInfo);
        return Page.getLayUiTablePageData(grayscaleStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object deleteGrayscale(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.error("参数不能为空");
        }
        int i = 0;
        if (Constant.YES.equals(id)) {
            i = sysAdminGrayscaleMapper.deleteAll();
            return ResEntity.success(i);
        }
        AdminGrayscale adminGrayscale = new AdminGrayscale();
        adminGrayscale.setUserId(id);
        i = sysAdminGrayscaleMapper.deleteByPrimaryKey(adminGrayscale);
        return ResEntity.success(i);
    }
}