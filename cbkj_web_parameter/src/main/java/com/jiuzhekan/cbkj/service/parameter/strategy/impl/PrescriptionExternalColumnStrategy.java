package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamNewMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service("PRESCRIPTION_EXTERNAL_COLUMN")
public class PrescriptionExternalColumnStrategy implements ParamStrategy {
    private final TSysParamInitDescMapper tSysParamInitDescMapper;

    private final static ArrayList<String> SUITABLETECHNICALDISPLAYLIST = new ArrayList<String>() {{add("11");add("2");add("4");add("5");add("7");add("10");add("12");}};

    private final static String SUITABLE_TECHNICAL_DISPLAY_ITEMS="SUITABLE_TECHNICAL_DISPLAY_ITEMS";
    @Autowired
    PrescriptionExternalColumnStrategy(TSysParamInitDescMapper tSysParamInitDescMapper,
                                       TSysParamNewMapper tSysParamNewMapper) {
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        Assert.notNull(tSysParamNewMapper, "tSysParamNewMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;

    }

    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        String defaultT = Constant.BASIC_STRING_THREE;
        if ("PRESCRIPTION_ACUPOINT_COLUMN".equals(tSysParam1.getParCode())) {
            defaultT = Constant.BASIC_STRING_TWO;
        }
        if ("SUITABLE_TECHNICAL_DISPLAY_ITEMS".equals(tSysParam1.getParCode())) {
            defaultT = Constant.BASIC_STRING_TWO;
        }
        jsonObject.remove("parValues");
        //1,2,3|3|4|8|9|10,4,5,6,7,13,8,9,10
        StringBuilder parValues = new StringBuilder("");
        StringBuilder parValuesT = new StringBuilder("");
        String[] split = tSysParam1.getParValues().split(",");
        for (String s : split) {
            String s1 = s.split("\\|")[0];
            parValues.append(s1).append(",");
            //只有3 有弹窗
            if (defaultT.equals(s1)) {
                String[] split1 = s.split("\\|");
                for (int i = 1; i < split1.length; i++) {
                    parValuesT.append(split1[i]).append(",");
                }
            }
        }
        String s = parValues.toString();
        jsonObject.remove("parValues");
        jsonObject.put("parValues", s.substring(0, s.length() - 1));
        jsonObject.remove("paramType");
        jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);

        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.putAll(jsonObject);
        jsonObject1.remove("parName");
        jsonObject1.put("parName", "弹窗");
        jsonObject1.remove("paramType");
        jsonObject1.remove("parValues");
        String sT = parValuesT.toString();
        jsonObject1.put("parValues", sT.length() > 1 ? sT.substring(0, sT.length() - 1) : sT);
        jsonObject1.put("paramType", tSysParam1.getParamType().split(",")[1]);
        jsonObject1.put("parId", tSysParam1.getParId() + "-2");
        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        if (
                "PRESCRIPTION_EXTERNAL_COLUMN".equals(tSysParam1.getParCode()) ||
                        "PRESCRIPTION_INTERNAL_COLUMN".equals(tSysParam1.getParCode())) {
            tSysParamInitDesc.setParamInitCodes("1,3,4,6,8,9,10");
        }
        //1序号 3药品名称 4规格 5数量 6包装单位 7用法 9单价 11自费 12总金额 13备注 14天数 15每次用量 16单位 17频次
        //中药制剂：弹窗选项只有：序号，药品名称，规格，用法，频次，单位，包装单位，单价，产地，库存
        if ("PRESCRIPTION_PREPARATION_COLUMN".equals(tSysParam1.getParCode())) {
            tSysParamInitDesc.setParamInitCodes("1,3,4,6,7,8,9,10,16,17");
        }
        if ("PRESCRIPTION_ACUPOINT_COLUMN".equals(tSysParam1.getParCode())) {
            tSysParamInitDesc.setParamInitCodes("1,2,4");
        }
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
        //JSONObject j2 = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject j = new JSONObject();
        //内服、外用中药方显示项目：
        //弹窗选项只有：序号，药品名称，规格，单位，单价，产地，库存 对应序号：
        for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
            j.put("label", tSysParamInitDesc1.getParamInitName());
            j.put("value", tSysParamInitDesc1.getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
            jsonArray.add(j);
        }

        if ("SUITABLE_TECHNICAL_DISPLAY_ITEMS".equals(tSysParam1.getParCode())) {
            jsonArray.clear();
            for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
                if (SUITABLETECHNICALDISPLAYLIST.contains(tSysParamInitDesc1.getParamInitCode())) {
                    j.put("label", tSysParamInitDesc1.getParamInitName());
                    j.put("value", tSysParamInitDesc1.getParamInitCode());
                    j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
                    jsonArray.add(j);
                }
            }
        }

        jsonObject1.put("paramInitDesc", jsonArray);
        itemArray.add(jsonObject1);
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO, TSysParamNew tSysParamNew) {
        String value1 = saveParameter.getValue1();
        String value2 = saveParameter.getValue2();
        char ss = '3';
        if ("PRESCRIPTION_ACUPOINT_COLUMN".equals(tSysParamNew.getParCode())||SUITABLE_TECHNICAL_DISPLAY_ITEMS.equals(tSysParamNew.getParCode())) {
            ss = '2';
        }
        if (!StringUtils.isBlank(value1)) {
            if (!StringUtils.isBlank(value2)) {
                //第二个值不为空
                String replace = "|" + value2.replace(",", "|");
                int i = value1.indexOf(ss);
                if (i < 0) {
                    tSysParamVO.setParValues(value1);
                } else {
                    tSysParamVO.setParValues(value1.substring(0, i) + value1.substring(i, i + 1) + replace + value1.substring(i + 1, value1.length()));
                }
            } else {
                tSysParamVO.setParValues(value1);
            }
        } else {
            //第一个值就是空的，直接写空
            tSysParamVO.setParValues("");
        }
    }
}
