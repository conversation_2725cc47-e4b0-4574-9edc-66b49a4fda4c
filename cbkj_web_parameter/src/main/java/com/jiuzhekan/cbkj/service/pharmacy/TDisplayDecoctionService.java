package com.jiuzhekan.cbkj.service.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting;
import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayDecoctionP;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayDecoctionMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayMoneySettingMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TDisplayDecoctionService {


    private final TDisplayDecoctionMapper tDisplayDecoctionMapper;


    private final TDisplayMoneySettingMapper tDisplayMoneySettingMapper;


    private final TDisplayMoneySettingService tDisplayMoneySettingService;

    @Autowired
    public TDisplayDecoctionService(TDisplayDecoctionMapper tDisplayDecoctionMapper, TDisplayMoneySettingMapper tDisplayMoneySettingMapper, TDisplayMoneySettingService tDisplayMoneySettingService) {
        this.tDisplayDecoctionMapper = tDisplayDecoctionMapper;
        this.tDisplayMoneySettingMapper = tDisplayMoneySettingMapper;
        this.tDisplayMoneySettingService = tDisplayMoneySettingService;
    }

    /**
     * 加载某条数据
     *
     * @param displayId 药房配置代煎信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    public ResEntity findObj(String displayId) {
        TDisplayDecoctionP tDisplayDecoctionP = new TDisplayDecoctionP();
        String ZERO = Constant.BASIC_STRING_ZERO;
        //门诊
        TDisplayDecoction outpatientDecoction = new TDisplayDecoction(displayId, "1", ZERO, ZERO, ZERO, ZERO, Integer.parseInt(ZERO), ZERO, ZERO);
        //住院
        TDisplayDecoction hospitalizationDecoction = new TDisplayDecoction(displayId, "2", ZERO, ZERO, ZERO, ZERO, Integer.parseInt(ZERO), ZERO, ZERO);
        if (!StringUtils.isBlank(displayId)) {
            //门诊
            TDisplayDecoction outpatientByDisplayId = tDisplayDecoctionMapper.getTDisplayDecoctionByDisplayId(new TDisplayDecoction(displayId, Constant.BASIC_STRING_ONE));
            if (null != outpatientByDisplayId) {
                outpatientDecoction = outpatientByDisplayId;
            }
            //住院
            TDisplayDecoction hospitalizationByDisplayId = tDisplayDecoctionMapper.getTDisplayDecoctionByDisplayId(new TDisplayDecoction(displayId, Constant.BASIC_STRING_TWO));
            if (null != hospitalizationByDisplayId) {
                hospitalizationDecoction = hospitalizationByDisplayId;
            }
        }
        //门诊通用代煎默认无数据情况
        TDisplayMoneySetting notSetting = new TDisplayMoneySetting(null, "1", "1", "1", "00000000", "通用煎药方式", "0");
        //门诊配置
        TDisplayMoneySetting inSetting = new TDisplayMoneySetting(null, "1", "1", "1", "jyfx", "通用煎药方式", "1");
        List<TDisplayMoneySetting> notSetByDicCode = new ArrayList<>();
        List<TDisplayMoneySetting> inSetByDicCode = new ArrayList<>();
        if (!StringUtils.isBlank(outpatientDecoction.getSetId())) {
            notSetting.setSetId(outpatientDecoction.getSetId());
            notSetting.setDicId("00000000");
            notSetByDicCode = tDisplayMoneySettingMapper.getPageListByObj(notSetting);
            if (notSetByDicCode.size() == Constant.ADMIN_EXT_ZERO) {
                notSetByDicCode.add(notSetting);
            }
            inSetting.setSetId(outpatientDecoction.getSetId());
            inSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(inSetting);
        } else {
            notSetByDicCode.add(notSetting);
            inSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(inSetting);
        }
        for (TDisplayMoneySetting tDisplayMoneySetting : inSetByDicCode) {
            tDisplayMoneySetting.setOutpatientOrHospitalization("1");
            tDisplayMoneySetting.setCurrencyOrFormula("1");
            tDisplayMoneySetting.setDecoctionOrProductionExpress("1");
        }
        outpatientDecoction.setShowDefaultList(notSetByDicCode);
        outpatientDecoction.setShowDicList(inSetByDicCode);
        //门诊配方默认
        TDisplayMoneySetting formulaNotSetting = new TDisplayMoneySetting(null, "1", "1", "2", "00000000", "配方煎药方式", "0");
        TDisplayMoneySetting formulaInSetting = new TDisplayMoneySetting(null, "1", "1", "2", "jyfx", "通用煎药方式", "1");
        List<TDisplayMoneySetting> formulaNotSetByDicCode = new ArrayList<>();
        List<TDisplayMoneySetting> formulaInSetByDicCode = new ArrayList<>();
        if (!StringUtils.isBlank(outpatientDecoction.getSetId())) {
            formulaNotSetting.setSetId(outpatientDecoction.getSetId());
            formulaNotSetting.setDicId("00000000");
            formulaNotSetByDicCode = tDisplayMoneySettingMapper.getPageListByObj(formulaNotSetting);
            if (formulaNotSetByDicCode.size() == Constant.ADMIN_EXT_ZERO) {
                formulaNotSetByDicCode.add(formulaNotSetting);
            }
            formulaInSetting.setSetId(outpatientDecoction.getSetId());
            formulaInSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(formulaInSetting);
        } else {
            formulaNotSetByDicCode.add(formulaNotSetting);
            formulaInSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(formulaInSetting);
        }
        for (TDisplayMoneySetting tDisplayMoneySetting : formulaInSetByDicCode) {
            tDisplayMoneySetting.setOutpatientOrHospitalization("1");
            tDisplayMoneySetting.setCurrencyOrFormula("2");
            tDisplayMoneySetting.setDecoctionOrProductionExpress("1");
        }
        outpatientDecoction.setFormulaDefaultList(formulaNotSetByDicCode);
        outpatientDecoction.setFormulaDicList(formulaInSetByDicCode);

        //住院配置
        TDisplayMoneySetting hnotSetting = new TDisplayMoneySetting(null, "2", "1", "1", "00000000", "通用煎药方式", "0");
        TDisplayMoneySetting hinSetting = new TDisplayMoneySetting(null, "2", "1", "1", "jyfx", "通用煎药方式", "1");
        List<TDisplayMoneySetting> hnotSetByDicCode = new ArrayList<>();
        List<TDisplayMoneySetting> hinSetByDicCode = new ArrayList<>();
        if (!StringUtils.isBlank(hospitalizationDecoction.getSetId())) {
            hnotSetting.setSetId(hospitalizationDecoction.getSetId());
            hnotSetting.setDicId("00000000");
            hnotSetByDicCode = tDisplayMoneySettingMapper.getPageListByObj(hnotSetting);
            if (hnotSetByDicCode.size() == Constant.ADMIN_EXT_ZERO) {
                hnotSetByDicCode.add(hnotSetting);
            }
            hinSetting.setSetId(hospitalizationDecoction.getSetId());
            hinSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(hinSetting);
        } else {
            hnotSetByDicCode.add(hnotSetting);
            hinSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(hinSetting);
        }
        for (TDisplayMoneySetting tDisplayMoneySetting : hinSetByDicCode) {
            tDisplayMoneySetting.setOutpatientOrHospitalization("2");
            tDisplayMoneySetting.setCurrencyOrFormula("1");
            tDisplayMoneySetting.setDecoctionOrProductionExpress("1");
        }
        hospitalizationDecoction.setShowDefaultList(hnotSetByDicCode);
        hospitalizationDecoction.setShowDicList(hinSetByDicCode);
        //住院配方默认
        TDisplayMoneySetting hformulaNotSetting = new TDisplayMoneySetting(null, "2", "1", "2", "00000000", "配方煎药方式", "0");
        TDisplayMoneySetting hformulaInSetting = new TDisplayMoneySetting(null, "2", "1", "2", "jyfx", "通用煎药方式", "1");
        List<TDisplayMoneySetting> hformulaNotSetByDicCode = new ArrayList<>();
        List<TDisplayMoneySetting> hformulaInSetByDicCode = new ArrayList<>();
        if (!StringUtils.isBlank(hospitalizationDecoction.getSetId())) {
            hformulaNotSetting.setSetId(hospitalizationDecoction.getSetId());
            hformulaNotSetting.setDicId("00000000");
            hformulaNotSetByDicCode = tDisplayMoneySettingMapper.getPageListByObj(hformulaNotSetting);
            if (hformulaNotSetByDicCode.size() == Constant.ADMIN_EXT_ZERO) {
                hformulaNotSetByDicCode.add(hformulaNotSetting);
            }
            hformulaInSetting.setSetId(hospitalizationDecoction.getSetId());
            hformulaInSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(hformulaInSetting);
        } else {
            hformulaNotSetByDicCode.add(hformulaNotSetting);
            hformulaInSetByDicCode = tDisplayMoneySettingMapper.getByDicCode(hformulaInSetting);
        }
        for (TDisplayMoneySetting tDisplayMoneySetting : hformulaInSetByDicCode) {
            tDisplayMoneySetting.setOutpatientOrHospitalization("2");
            tDisplayMoneySetting.setCurrencyOrFormula("2");
            tDisplayMoneySetting.setDecoctionOrProductionExpress("1");
        }
        hospitalizationDecoction.setFormulaDefaultList(hformulaNotSetByDicCode);
        hospitalizationDecoction.setFormulaDicList(hformulaInSetByDicCode);

        tDisplayDecoctionP.setOutpatient(outpatientDecoction);
        tDisplayDecoctionP.setHospitalization(hospitalizationDecoction);
        return ResEntity.entity(true, "", tDisplayDecoctionP);
    }


    /**
     * 插入新数据
     *
     * @param tDisplayDecoctionP 药房配置代煎信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertOrUpdate(TDisplayDecoctionP tDisplayDecoctionP) {
        TDisplayDecoction outpatient = tDisplayDecoctionP.getOutpatient();
        TDisplayDecoction hospitalization = tDisplayDecoctionP.getHospitalization();

        if (Constant.BASIC_STRING_ONE.equals(outpatient.getUsuallyDecoctionSet()) ||
                Constant.BASIC_STRING_ONE.equals(hospitalization.getUsuallyDecoctionSet())) {
            if (null == outpatient.getShowDicList() || outpatient.getShowDicList().size() == 0) {
                return ResEntity.error("请配置通用制膏费用配置字典数据");
            }
            if (null == hospitalization.getShowDicList() || hospitalization.getShowDicList().size() == 0) {
                return ResEntity.error("请配置通用制膏费用配置字典数据");
            }
        }

        if (Constant.BASIC_STRING_ONE.equals(outpatient.getFormulaDecoctionSet()) ||
                Constant.BASIC_STRING_ONE.equals(hospitalization.getFormulaDecoctionSet())) {
            if (null == outpatient.getFormulaDicList() || outpatient.getFormulaDicList().size() == 0) {
                return ResEntity.error("请配置配方制膏费用配置字典数据");
            }
            if (null == hospitalization.getFormulaDicList() || hospitalization.getFormulaDicList().size() == 0) {
                return ResEntity.error("请配置配方制膏费用配置字典数据");
            }
        }
        if (StringUtils.isBlank(outpatient.getDisplayId()) || StringUtils.isBlank(hospitalization.getDisplayId())) {
            return ResEntity.entity(false, "displayID不能为空", null);
        }
        if (StringUtils.isBlank(outpatient.getUsuallyDecoctionSet()) || StringUtils.isBlank(hospitalization.getUsuallyDecoctionSet())) {
            return ResEntity.error("缺少usuallyProductionSet");
        }
        if (StringUtils.isBlank(outpatient.getFormulaDecoctionSet()) || StringUtils.isBlank(hospitalization.getFormulaDecoctionSet())) {
            return ResEntity.error("缺少formulaProductionSet");
        }
        if (StringUtils.isBlank(outpatient.getOutpatientOrHospitalization()) || StringUtils.isBlank(hospitalization.getOutpatientOrHospitalization())) {
            return ResEntity.error("缺少outpatientOrHospitalization");
        }
        //判断是否最少勾选一个费用配置
        if(getTDisplayDecoctionList(outpatient)){
            return ResEntity.error("最少选一个费用配置");
        }
        if(getTDisplayDecoctionList(hospitalization)){
            return ResEntity.error("最少选一个费用配置");
        }
        if (!StringUtils.isBlank(outpatient.getDisplayId())) {
            int i = tDisplayDecoctionMapper.deleteByCondition(outpatient);
            int i1 = tDisplayDecoctionMapper.deleteByCondition(hospitalization);
        }
        //if (StringUtils.isBlank(outpatient.getSetId())) {
            outpatient.setSetId(IDUtil.getID());
            outpatient.initDefaultValue();
            tDisplayDecoctionMapper.insert(outpatient);
            hospitalization.setSetId(IDUtil.getID());
            hospitalization.initDefaultValue();
            tDisplayDecoctionMapper.insert(hospitalization);
//        } else {
//            tDisplayDecoctionMapper.updateByPrimaryKey(outpatient);
//            tDisplayDecoctionMapper.updateByPrimaryKey(hospitalization);
//        }
        //处理保存费用配置
        tDisplayMoneySettingService.saveDecoctionSetting(outpatient, outpatient.getSetId());
        tDisplayMoneySettingService.saveDecoctionSetting(hospitalization, hospitalization.getSetId());
        return ResEntity.entity(true, "", tDisplayDecoctionP);
    }
    private Boolean getTDisplayDecoctionList(TDisplayDecoction tDisplayDecoction){
        if(Constant.BASIC_DEL_NO.equals(tDisplayDecoction.getShowDecoction())){
            List<TDisplayMoneySetting> usuallySeting=new ArrayList<>();
            List<TDisplayMoneySetting> formularSeting=new ArrayList<>();
            ArrayList<Boolean> booleans = new ArrayList<>();
            if(Constant.BASIC_STRING_ZERO.equals(tDisplayDecoction.getUsuallyDecoctionSet())){
                usuallySeting = tDisplayDecoction.getShowDefaultList();
            }else {
                usuallySeting = tDisplayDecoction.getShowDicList();
            }
            if(Constant.BASIC_STRING_ZERO.equals(tDisplayDecoction.getFormulaDecoctionSet())){
                formularSeting = tDisplayDecoction.getFormulaDefaultList();
            }else {
                formularSeting = tDisplayDecoction.getFormulaDicList();
            }
            booleans.add(isUsuallyMust(usuallySeting));
            booleans.add(isUsuallyMust(formularSeting));
            boolean contains = booleans.contains(false);
            return contains;
        }
        return false;
    }

    /**
     * 判断是否配置了费用集合
     * @param tDisplayMoneySettingList
     * @return
     */
    private Boolean isUsuallyMust(List<TDisplayMoneySetting> tDisplayMoneySettingList){
        int i=0;
        for (TDisplayMoneySetting tDisplayMoneySetting : tDisplayMoneySettingList) {
            if(Constant.BASIC_STRING_ZERO.equals(tDisplayMoneySetting.getIsShowSetting())){
                i=i+1;
            }
        }
        if(i<Constant.ADMIN_EXT_SHURUMA){
            return false;
        }
        long count = tDisplayMoneySettingList.stream().filter(tDisplayMoneySetting -> tDisplayMoneySetting.getPrice() == null).count();
        if(count>Constant.ADMIN_ZERO){
            return false;
        }

        return true;
    }
}
