package com.jiuzhekan.cbkj.service.common;

import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.ExcelUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * ImportInsService
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2020/10/9 16:50
 */
@Service
public class ImportInsService {

    @Autowired
    private SysInstitutionMapper sysInstitutionMapper;

    @Autowired
    private SysDepartmentMapper sysDepartmentMapper;

    /**
     * 科室文件导入
     *
     * @param file file
     * <AUTHOR>
     * @date 2020/10/13
     */
    @CacheEvict(value = {"org::app","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity importIns(MultipartFile file) {
        InputStream inputStream = null;
        List<Map<String, Object>> excelList = null;
        try {
            inputStream = file.getInputStream();
            // excelList = ExcelUtils.readXlsFirstSheet(inputStream);
            excelList = ExcelUtils.readXlsxFirstSheet(inputStream, 1);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //机构导入方法
        Map<String, Object> stringObjectMap = importInsData(excelList);

        return ResEntity.entity(true, Constant.SUCCESS_DX, stringObjectMap);
    }
    @CacheEvict(value = {"org::dept","parameter::params","parameter::display","parameter::dic","client::token","client::user","client::register"},allEntries = true)
    public ResEntity importDept(MultipartFile file) {
        InputStream inputStream = null;
        List<Map<String, Object>> excelList = null;
        try {
            inputStream = file.getInputStream();
            // excelList = ExcelUtils.readXlsFirstSheet(inputStream);
            excelList = ExcelUtils.readXlsxFirstSheet(inputStream, 1);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //科室导入方法
        Map<String, Object> stringObjectMap = importDeptData(excelList);

        return ResEntity.entity(true, Constant.SUCCESS_DX, stringObjectMap);
    }

    /**
     * 医疗机构导入方法
     * @param excelList
     * @return
     */
    private Map<String, Object> importInsData(List<Map<String, Object>> excelList) {
        StringBuilder errorCount = new StringBuilder();
        Integer errorCounts = 0;
        //真实总条数
        Integer realCounts = 0;
        if (excelList != null && excelList.size() > 0) {
            SysInstitution ins;

            AdminInfo admin = AdminUtils.getCurrentHr();

            for (int i = 0; i < excelList.size(); i++) {
                Map<String, Object> om = excelList.get(i);
                if (om.size() == 0) {
                    continue;
                }
                if (null == excelList.get(i)) {
                    continue;
                }
                String insName = om.get("*医疗机构名称").toString();
                String insCategory = om.get("*医疗机构类别").toString();
                String insPcode = om.get("上级医疗机构代码").toString();
                String insAddress = om.get("*医疗机构地址").toString();
                String insCode = om.get("*医疗机构代码").toString();
                String appID = om.get("*医共体代码").toString();
                realCounts = realCounts + 1;
                SysInstitution sysInstitutionByInsCode = sysInstitutionMapper.getSysInstitutionByInsCodeAndAppId(insCode,appID);
                if(null!=sysInstitutionByInsCode){
                    errorCount.append("第").append(i + 1).append("行导入重复，请检查数据").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }

                if (StringUtils.isBlank(insName)
                        || StringUtils.isBlank(insCategory)
                        || StringUtils.isBlank(insAddress)
                        || StringUtils.isBlank(insCode)
                        || StringUtils.isBlank(appID)) {
                    errorCount.append("第").append(i + 1).append("行导入失败，请检查数据").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }
                ins = new SysInstitution();
                ins.setInsId(IDUtil.getID());
                ins.setAppId(appID);
                ins.setInsCode(insCode);
                ins.setInsName(insName);
                ins.setInsParentCode(StringUtils.isBlank(insPcode) ? "" : insPcode);
                ins.setInsAddress(StringUtils.isBlank(insAddress) ? "" : insAddress);
                if ("医院".equals(insCategory)) {
                    ins.setInsCategory("1");
                } else if ("乡镇卫生院".equals(insCategory)) {
                    ins.setInsCategory("2");
                } else if ("卫生站".equals(insCategory)) {
                    ins.setInsCategory("3");
                } else {
                    ins.setInsCategory("");
                }
                ins.setCreateDate(new Date());
                ins.setCreateUser(admin.getUserId());
                ins.setCreateUserName(admin.getNameZh());
                ins.setStatus(Constant.BASIC_STRING_ZERO);
                ins.setSort(i);
                if (StringUtils.isBlank(insPcode)) {
                    ins.setInsIslast(1);
                } else {
                    ins.setInsIslast(0);
                }
                //单个导入
                sysInstitutionMapper.insert(ins);
            }
            //sysInstitutionMapper.insertList(insList);

            excelList.clear();
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("errorNumber", errorCounts);
        String[] split = errorCount.toString().split(",");
        map.put("description", split);
        map.put("total", realCounts);
        map.put("successNumber", realCounts - errorCounts);
        return map;
    }

    /**
     * 科室导入方法
     *
     * @param excelList
     * @return
     */
    private Map<String, Object> importDeptData(List<Map<String, Object>> excelList) {
        StringBuilder errorCount = new StringBuilder();
        Integer errorCounts = 0;
        //真实总条数
        Integer realCounts = 0;
        if (excelList != null && excelList.size() > 0) {
            List<SysDepartment> departmentList = new ArrayList<>();
            SysDepartment department;
            AdminInfo admin = AdminUtils.getCurrentHr();

            for (int i = 0; i < excelList.size(); i++) {
                Map<String, Object> om = excelList.get(i);
                if (om.size() == 0) {
                    continue;
                }
                if (null == excelList.get(i)) {
                    continue;
                }
                String appID = om.get("*医共体代码").toString();
                String insCode = om.get("*医疗机构代码").toString();
                //第三方科室ID
                String deptOriginId = om.get("*科室/病区编码").toString();
                String deptName = om.get("*科室/病区名称").toString();
                String deptType = om.get("*科室类型").toString();
                String sort = om.get("*序号").toString();
                if(StringUtils.isNotBlank(appID)
                        &&StringUtils.isNotBlank(insCode)
                        &&StringUtils.isNotBlank(deptName)){
                    SysDepartment department1 = new SysDepartment();
                    department1.setAppId(appID);
                    department1.setInsCode(insCode);
                    department1.setDeptName(deptName);
                    Integer count = sysDepartmentMapper.getDepCountByDepOriginId(department1);
                    if (count>0){
                        errorCount.append("第").append(i + 1).append("行导入重复，请检查数据").append(",");
                        errorCounts = errorCounts + 1;
                        continue;
                    }
                }

                realCounts = realCounts + 1;
                if (StringUtils.isBlank(appID)
                        || StringUtils.isBlank(insCode)
                        || StringUtils.isBlank(deptOriginId)
                        || StringUtils.isBlank(deptName)
                        || StringUtils.isBlank(deptType)
                        || StringUtils.isBlank(sort)) {
                    errorCount.append("第").append(i + 1).append("行导入失败，请检查数据").append(",");
                    errorCounts = errorCounts + 1;
                    continue;
                }
                department = new SysDepartment();
                department.setDeptId(IDUtil.getID());
                if ("病区".equals(deptType)) {
                    department.setDeptType("2");
                } else if ("科室".equals(deptType)) {
                    department.setDeptType("1");
                }

                department.setAppId(appID);
                department.setInsCode(insCode);
                department.setDeptName(deptName);
                department.setDepOriginId(deptOriginId);
                department.setSort(Integer.parseInt(sort));
                department.setCreateDate(new Date());
                department.setCreateUser(admin.getUserId());
                department.setCreateUserName(admin.getNameZh());
                department.setStatus("0");
                //单个导入

                sysDepartmentMapper.insert(department);
            }
            excelList.clear();
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("errorNumber", errorCounts);
        String[] split = errorCount.toString().split(",");
        map.put("description", split);
        map.put("total", realCounts);
        map.put("successNumber", realCounts - errorCounts);
        return map;
    }

}