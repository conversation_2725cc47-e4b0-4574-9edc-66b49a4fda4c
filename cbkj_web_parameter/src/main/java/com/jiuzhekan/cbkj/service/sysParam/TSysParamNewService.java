package com.jiuzhekan.cbkj.service.sysParam;

import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamNewMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class TSysParamNewService {

    @Autowired
    private TSysParamNewMapper tSysParamNewMapper;

    /**
     * 加载分页数据
     *
     * @param tSysParam 系统参数表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-02-28
     */
    public Object getPageDatas(TSysParamNew tSysParam, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TSysParamNew> list = tSysParamNewMapper.getPageListByObj(tSysParam);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param parId 系统参数表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-28
     */
    public ResEntity findObj(String parId) {

        if (StringUtils.isBlank(parId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TSysParamNew tSysParam = tSysParamNewMapper.getObjectById(parId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tSysParam);
    }


    /**
     * 插入新数据
     *
     * @param tSysParam 系统参数表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-28
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TSysParamNew tSysParam){

        tSysParam.setParId(IDUtil.getID());
        long rows = tSysParamNewMapper.insert(tSysParam);

        return ResEntity.success(tSysParam);
    }


    /**
     * 修改
     *
     * @param tSysParam 系统参数表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-28
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TSysParamNew tSysParam) {

        long rows = tSysParamNewMapper.updateByPrimaryKey(tSysParam);

        return ResEntity.success(tSysParam);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-02-28
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tSysParamNewMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
