package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Service("DIALECTICAL_TAB")
public class DialecticalTabStrategy implements ParamStrategy {
    private final TSysParamInitDescMapper tSysParamInitDescMapper;
    @Autowired
    DialecticalTabStrategy(TSysParamInitDescMapper tSysParamInitDescMapper){
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
    }
    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        String[] split = tSysParam1.getParValues().split("\\|");
        StringBuilder codes = new StringBuilder("");
        String selectT = "";
        for (String s : split) {
            String[] split1 = s.split(":");
            codes.append(split1[0]).append(",");
            if (split1.length == 3) {
                selectT = split1[0];
            }
        }

        jsonObject.remove("parName");
        jsonObject.put("parName", "");
        jsonObject.put("paramType", "3");
        pjsonObject.put("paramType", "31");
        jsonObject.remove("parValues");
        jsonObject.put("parValues", codes.length() > 1 ? codes.toString().substring(0, codes.toString().length() - 1) : codes.toString());
        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
        JSONObject j = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.putAll(jsonObject);
        for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
            j.put("label", tSysParamInitDesc1.getParamInitName());
            j.put("value", tSysParamInitDesc1.getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
            jsonArray.add(j);
            if (selectT.equals(tSysParamInitDesc1.getParamInitCode())) {
                jsonObject1.put("parValues", selectT);
            }
        }
        jsonObject1.put("paramType", "21");
        jsonObject1.put("paramInitDesc", jsonArray);
        jsonObject1.put("parId", tSysParam1.getParId() + "-2");
        jsonObject1.put("parName", "默认选中");
        itemArray.add(jsonObject1);
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew) {
//ai:AI 辨证:T|experiential:经方辨证:|expert:国医大师辨证:
        //ai,experiential,expert 是 InitCode,就是value值
        // 获取的value值 就是 initcode
        String value1 = saveParameter.getValue1();
        String value2 = saveParameter.getValue2();
        if (!StringUtils.isBlank(value1)) {
            String[] split = value1.split(",");
            for (int i = 0; i < split.length; i++) {
                TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
                tSysParamInitDesc.setParamId(tSysParamNew.getParId());
                tSysParamInitDesc.setParamInitCode(split[i]);
                TSysParamInitDesc initByCondition = tSysParamInitDescMapper.getInitByCondition(tSysParamInitDesc);
                if (null != initByCondition) {
                    split[i] = initByCondition.getParamInitCode() + ":" + initByCondition.getParamInitName() + ":";
                    if (!StringUtils.isBlank(value2) && value2.equals(tSysParamInitDesc.getParamInitCode())) {
                        //默认选中不为空
                        split[i] = split[i] + "T" + "|";
                    } else {
                        split[i] = split[i] + "|";
                    }
                }

            }
            StringBuilder stringBuilder = new StringBuilder("");
            for (String s : split) {
                stringBuilder.append(s);
            }
            tSysParamVO.setParValues(stringBuilder.toString().substring(0, stringBuilder.length() - 2));
        } else {
            tSysParamVO.setParValues("");
        }
    }
}
