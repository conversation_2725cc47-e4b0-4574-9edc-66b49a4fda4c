package com.jiuzhekan.cbkj.service.business.banner;

import com.jiuzhekan.cbkj.beans.business.banner.SysBanner;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.banner.SysBannerMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class SysBannerService {

    @Autowired
    private SysBannerMapper sysBannerMapper;

    /**
     * 加载分页数据
     *
     * @param sysBanner 系统登录页轮播图
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-11
     */
    public Object getPageDatas(SysBanner sysBanner, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysBanner> list = sysBannerMapper.getPageListByObj(sysBanner);
        return Page.getLayUiTablePageData(list);
    }

    public Object getBannerList() {
        List<SysBanner> list = sysBannerMapper.getBannerList();
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * 加载某条数据
     *
     * @param bannerId 系统登录页轮播图
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-11
     */
    public ResEntity findObj(String bannerId) {

        if (StringUtils.isBlank(bannerId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        SysBanner sysBanner = sysBannerMapper.getObjectById(bannerId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysBanner);
    }


    /**
     * 插入新数据
     *
     * @param sysBanner 系统登录页轮播图
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-11
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(SysBanner sysBanner){
        sysBanner.setStatus("1");
        sysBanner.setBannerId(IDUtil.getID());
        long rows = sysBannerMapper.insert(sysBanner);

        return ResEntity.success(sysBanner);
    }


    /**
     * 修改
     *
     * @param sysBanner 系统登录页轮播图
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-11
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(SysBanner sysBanner) {

        long rows = sysBannerMapper.updateByPrimaryKey(sysBanner);

        return ResEntity.success(sysBanner);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-11
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = sysBannerMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
