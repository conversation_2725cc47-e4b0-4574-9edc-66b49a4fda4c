package com.jiuzhekan.cbkj.service.business.displaydosage;

import com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageDescribe;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageDescribeMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class TDisplayDosageDescribeService {

    @Autowired
    private TDisplayDosageDescribeMapper tDisplayDosageDescribeMapper;

    /**
     * 加载分页数据
     *
     * @param tDisplayDosageDescribe 药房应用配置-制剂说明表（字典中）
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2025-08-06
     */
    public Object getPageDatas(TDisplayDosageDescribe tDisplayDosageDescribe, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDisplayDosageDescribe> list = tDisplayDosageDescribeMapper.getPageListByObj(tDisplayDosageDescribe);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param dosageDescribeId 药房应用配置-制剂说明表（字典中）
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    public ResEntity findObj(String dosageDescribeId) {

        if (StringUtils.isBlank(dosageDescribeId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TDisplayDosageDescribe tDisplayDosageDescribe = tDisplayDosageDescribeMapper.getObjectById(dosageDescribeId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDisplayDosageDescribe);
    }


    /**
     * 插入新数据
     *
     * @param tDisplayDosageDescribe 药房应用配置-制剂说明表（字典中）
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDisplayDosageDescribe tDisplayDosageDescribe){

        tDisplayDosageDescribe.setDosageDescribeId(IDUtil.getID());
        long rows = tDisplayDosageDescribeMapper.insert(tDisplayDosageDescribe);

        return ResEntity.success(tDisplayDosageDescribe);
    }


    /**
     * 修改
     *
     * @param tDisplayDosageDescribe 药房应用配置-制剂说明表（字典中）
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDisplayDosageDescribe tDisplayDosageDescribe) {

        long rows = tDisplayDosageDescribeMapper.updateByPrimaryKey(tDisplayDosageDescribe);

        return ResEntity.success(tDisplayDosageDescribe);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-06
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tDisplayDosageDescribeMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
