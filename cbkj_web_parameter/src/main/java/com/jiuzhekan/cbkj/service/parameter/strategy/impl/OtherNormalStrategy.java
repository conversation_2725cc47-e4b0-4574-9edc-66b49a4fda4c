package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.service.parameter.strategy.NormalParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service("otherNormalStrategy")
public class OtherNormalStrategy implements NormalParamStrategy {

    @Override
    public void getParam() {

    }

    @Override
    public void setParam(DiagnosisParamNewVO tSysParamVO, Object v) {

    }
}
