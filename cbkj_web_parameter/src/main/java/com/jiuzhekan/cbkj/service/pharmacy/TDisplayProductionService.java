package com.jiuzhekan.cbkj.service.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction;
import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayProductionP;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayMoneySettingMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayProductionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TDisplayProductionService {


    private final TDisplayProductionMapper tDisplayProductionMapper;
    private final TDisplayMoneySettingMapper tDisplayMoneySettingMapper;

    private final TDisplayMoneySettingService tDisplayMoneySettingService;

    @Autowired
    TDisplayProductionService(TDisplayProductionMapper tDisplayProductionMapper, TDisplayMoneySettingMapper tDisplayMoneySettingMapper, TDisplayMoneySettingService tDisplayMoneySettingService) {
        this.tDisplayProductionMapper = tDisplayProductionMapper;
        this.tDisplayMoneySettingMapper = tDisplayMoneySettingMapper;
        this.tDisplayMoneySettingService = tDisplayMoneySettingService;
    }


    /**
     * 加载某条数据
     *
     * @param displayId 药房配置制膏信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    public ResEntity findObj(String displayId) {
        TDisplayProductionP tDisplayProductionP = new TDisplayProductionP();
        String ZERO = Constant.BASIC_STRING_ZERO;
        //门诊
        TDisplayProduction outpatient = new TDisplayProduction(displayId, Constant.BASIC_STRING_ONE, ZERO, ZERO, "1", ZERO, ZERO,ZERO);
        //住院
        TDisplayProduction hospitalization = new TDisplayProduction(displayId, Constant.BASIC_STRING_TWO, ZERO, ZERO, "1", ZERO, ZERO,ZERO);
        if (!StringUtils.isBlank(displayId)) {
            //存在则覆盖。
            TDisplayProduction outpatient2 = tDisplayProductionMapper.getByDisplayId(new TDisplayProduction(displayId, Constant.BASIC_STRING_ONE));
            if (null != outpatient2) {
                outpatient = outpatient2;
            }
            TDisplayProduction hospitalization2 = tDisplayProductionMapper.getByDisplayId(new TDisplayProduction(displayId, Constant.BASIC_STRING_TWO));
            if (null != hospitalization2) {
                hospitalization = hospitalization2;
            }
        }
        //门诊：配置默认费用配置参数-通用
        TDisplayMoneySetting packingCommon = new TDisplayMoneySetting(null, Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_ONE, "00000000", "通用制膏", ZERO);
        TDisplayMoneySetting formulaProductionCommon = new TDisplayMoneySetting(null, Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, "00000000", "配方制膏", ZERO);
        ArrayList<TDisplayMoneySetting> list = new ArrayList<>();
        list.add(packingCommon);
        outpatient.setShowDefaultList(list);
        ArrayList<TDisplayMoneySetting> list2 = new ArrayList<>();
        list2.add(formulaProductionCommon);
        outpatient.setFormulaDefaultList(list2);
        //todo 从数据库获取通用的。
        if (!StringUtils.isBlank(outpatient.getSetId())) {
            //门诊-通用制膏
            TDisplayMoneySetting tDisplayMoneySetting = new TDisplayMoneySetting(outpatient.getSetId(), Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_ONE, null, null, null);
            tDisplayMoneySetting.setDicId("00000000");
            List<TDisplayMoneySetting> pageListByObj = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting);
            if (pageListByObj.size() > 0) {
                outpatient.setShowDefaultList(pageListByObj);
            }
            //门诊-配方制膏
            TDisplayMoneySetting tDisplayMoneySetting1 = new TDisplayMoneySetting(outpatient.getSetId(), Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, null, null, null);
            tDisplayMoneySetting1.setDicId("00000000");
            List<TDisplayMoneySetting> pageListByObj2 = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting1);
            if (pageListByObj2.size() > 0) {
                outpatient.setFormulaDefaultList(pageListByObj2);
            }
        }
        //获取门诊费用配置信息-非通用
        List<TDisplayMoneySetting> pageListByObj = tDisplayMoneySettingMapper.getByDicCode(new TDisplayMoneySetting(outpatient.getSetId(), Constant.BASIC_STRING_ONE, null, Constant.BASIC_STRING_ONE, "bzfs", null, null));
        List<TDisplayMoneySetting> pageListByObj2 = tDisplayMoneySettingMapper.getByDicCode(new TDisplayMoneySetting(outpatient.getSetId(), Constant.BASIC_STRING_ONE, null, Constant.BASIC_STRING_TWO, "bzfs", null, null));
        //配置默认值
        setDefault(pageListByObj,Constant.BASIC_STRING_ONE,Constant.BASIC_STRING_TWO,Constant.BASIC_STRING_ONE);
        setDefault(pageListByObj2,Constant.BASIC_STRING_ONE,Constant.BASIC_STRING_TWO,Constant.BASIC_STRING_TWO);
        outpatient.setShowDicList(pageListByObj);
        outpatient.setFormulaDicList(pageListByObj2);

        //住院：配置默认费用配置参数
        TDisplayMoneySetting packingCommon2 = new TDisplayMoneySetting(null, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_ONE, "00000000", "通用制膏", ZERO);
        TDisplayMoneySetting formulaProductionCommon2 = new TDisplayMoneySetting(null, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, "00000000", "配方制膏", ZERO);
        ArrayList<TDisplayMoneySetting> list3 = new ArrayList<>();
        list3.add(packingCommon2);
        hospitalization.setShowDefaultList(list3);
        ArrayList<TDisplayMoneySetting> list4 = new ArrayList<>();
        list4.add(formulaProductionCommon2);
        hospitalization.setFormulaDefaultList(list4);
        //todo 从数据库获取通用的。
        if (!StringUtils.isBlank(hospitalization.getSetId())) {
            //门诊-通用制膏
            TDisplayMoneySetting tDisplayMoneySetting = new TDisplayMoneySetting(hospitalization.getSetId(), Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_ONE, null, null, null);
            tDisplayMoneySetting.setDicId("00000000");
            List<TDisplayMoneySetting> pageListByObj22 = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting);
            if (pageListByObj22.size() > 0) {
                hospitalization.setShowDefaultList(pageListByObj22);
            }
            //门诊-配方制膏
            TDisplayMoneySetting tDisplayMoneySetting1 = new TDisplayMoneySetting(hospitalization.getSetId(), Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, Constant.BASIC_STRING_TWO, null, null, null);
            tDisplayMoneySetting1.setDicId("00000000");
            List<TDisplayMoneySetting> pageListByObj33 = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting1);
            if (pageListByObj33.size() > 0) {
                hospitalization.setFormulaDefaultList(pageListByObj33);
            }
        }
        //获取住院费用配置信息
        List<TDisplayMoneySetting> pageListByObj3 = tDisplayMoneySettingMapper.getByDicCode(new TDisplayMoneySetting(hospitalization.getSetId(), Constant.BASIC_STRING_TWO, null, Constant.BASIC_STRING_ONE, "bzfs", null, null));
        List<TDisplayMoneySetting> pageListByObj4 = tDisplayMoneySettingMapper.getByDicCode(new TDisplayMoneySetting(hospitalization.getSetId(), Constant.BASIC_STRING_TWO, null, Constant.BASIC_STRING_TWO, "bzfs", null, null));
        setDefault(pageListByObj3,Constant.BASIC_STRING_TWO,Constant.BASIC_STRING_TWO,Constant.BASIC_STRING_ONE);
        setDefault(pageListByObj4,Constant.BASIC_STRING_TWO,Constant.BASIC_STRING_TWO,Constant.BASIC_STRING_TWO);
        hospitalization.setShowDicList(pageListByObj3);
        hospitalization.setFormulaDicList(pageListByObj4);
        //设置进去
        tDisplayProductionP.setOutpatient(outpatient);
        tDisplayProductionP.setHospitalization(hospitalization);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDisplayProductionP);
    }


    /**
     * 插入新数据
     *
     * @param tDisplayProductionP 药房配置制膏信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertOrUpdate(TDisplayProductionP tDisplayProductionP) {
        TDisplayProduction outpatient = tDisplayProductionP.getOutpatient();
        TDisplayProduction hospitalization = tDisplayProductionP.getHospitalization();

        if (Constant.BASIC_STRING_ONE.equals(outpatient.getUsuallyProductionSet()) ||
                Constant.BASIC_STRING_ONE.equals(hospitalization.getUsuallyProductionSet())){
            if (null == outpatient.getShowDicList() || outpatient.getShowDicList().size() == 0){
                return ResEntity.error("请配置通用制膏费用配置字典数据");
            }
            if (null == hospitalization.getShowDicList() || hospitalization.getShowDicList().size() == 0){
                return ResEntity.error("请配置通用制膏费用配置字典数据");
            }
        }
        if (Constant.BASIC_STRING_ONE.equals(outpatient.getFormulaProductionSet()) ||
                Constant.BASIC_STRING_ONE.equals(hospitalization.getFormulaProductionSet())){
            if (null == outpatient.getFormulaDicList() || outpatient.getFormulaDicList().size() == 0){
                return ResEntity.error("请配置配方制膏费用配置字典数据");
            }if (null == hospitalization.getFormulaDicList() || hospitalization.getFormulaDicList().size() == 0){
                return ResEntity.error("请配置配方制膏费用配置字典数据");
            }
        }



        if (StringUtils.isBlank(outpatient.getDisplayId()) || StringUtils.isBlank(hospitalization.getDisplayId())) {
            return ResEntity.error("缺少displayId");
        }
        if (StringUtils.isBlank(outpatient.getUsuallyProductionSet()) || StringUtils.isBlank(hospitalization.getUsuallyProductionSet())) {
            return ResEntity.error("缺少usuallyProductionSet");
        }
        if (StringUtils.isBlank(outpatient.getFormulaProductionSet()) || StringUtils.isBlank(hospitalization.getFormulaProductionSet())) {
            return ResEntity.error("缺少formulaProductionSet");
        }
        if (StringUtils.isBlank(outpatient.getOutpatientOrHospitalization()) || StringUtils.isBlank(hospitalization.getOutpatientOrHospitalization())) {
            return ResEntity.error("缺少outpatientOrHospitalization");
        }

        if ( Constant.BASIC_STRING_ZERO.equals(outpatient.getShowProduction())){
            if (mustHaveOne(outpatient)){
                return ResEntity.entity(false,"最少选一个费用配置",tDisplayProductionP);
            }
        }
        if (Constant.BASIC_STRING_ZERO.equals(hospitalization.getShowProduction())){
            if (mustHaveOne(hospitalization)){
                return ResEntity.entity(false,"最少选一个费用配置",tDisplayProductionP);
            }
        }
        if (!StringUtils.isBlank(outpatient.getDisplayId())) {
            int i = tDisplayProductionMapper.deleteByCondition(hospitalization);
            int i1 = tDisplayProductionMapper.deleteByCondition(outpatient);
        }
//        if (StringUtils.isBlank(outpatient.getSetId())) {
            //插入制膏的门诊和住院信息
            outpatient.setSetId(IDUtil.getID());
            outpatient.initDefaultValue();
            tDisplayProductionMapper.insert(outpatient);
            hospitalization.setSetId(IDUtil.getID());
            hospitalization.initDefaultValue();
            tDisplayProductionMapper.insert(hospitalization);
//        }else {
//            tDisplayProductionMapper.updateByPrimaryKey(outpatient);
//            tDisplayProductionMapper.updateByPrimaryKey(hospitalization);
//        }

        //处理制膏的门诊和住院。费用配置信息，
        tDisplayMoneySettingService.saveSetting(outpatient,outpatient.getSetId());
        tDisplayMoneySettingService.saveSetting(hospitalization,hospitalization.getSetId());
//        tDisplayMoneySettingMapper.insert()
        return ResEntity.success(tDisplayProductionP);
    }

    public void setDefault(List<TDisplayMoneySetting> pageListByObj, String outpatientOrHospitalization
            , String decoctionOrProductionExpress, String currencyOrFormula) {
        for (TDisplayMoneySetting tDisplayMoneySetting : pageListByObj) {
            if (StringUtils.isBlank(tDisplayMoneySetting.getId())) {
                tDisplayMoneySetting.setOutpatientOrHospitalization(outpatientOrHospitalization);
                tDisplayMoneySetting.setDecoctionOrProductionExpress(decoctionOrProductionExpress);
                tDisplayMoneySetting.setCurrencyOrFormula(currencyOrFormula);
            }
        }
    }


    public boolean mustHaveOne(TDisplayProduction outpatientOrHospitalization){
        //门诊/住院-通用-配置
        if (Constant.BASIC_STRING_ONE.equals(outpatientOrHospitalization.getUsuallyProductionSet())){
            if (outpatientOrHospitalization.getShowDicList().stream().noneMatch(a -> Constant.BASIC_STRING_ZERO.equals(a.getIsShowSetting()))){
                return true;
            }
        }
        //门诊/住院-通用-默认
        else if(Constant.BASIC_STRING_ZERO.equals(outpatientOrHospitalization.getUsuallyProductionSet())){
            if (outpatientOrHospitalization.getShowDefaultList().stream().noneMatch(a -> Constant.BASIC_STRING_ZERO.equals(a.getIsShowSetting()))){
                return true;
            }
        }

        //门诊/住院-制膏-配置
        if (Constant.BASIC_STRING_ONE.equals(outpatientOrHospitalization.getFormulaProductionSet())){
             if (outpatientOrHospitalization.getFormulaDicList().stream().noneMatch(a -> Constant.BASIC_STRING_ZERO.equals(a.getIsShowSetting()))){
                 return true;
             }
        }
        //门诊/住院-制膏-默认
        else if(Constant.BASIC_STRING_ZERO.equals(outpatientOrHospitalization.getFormulaProductionSet())){
            if ( outpatientOrHospitalization.getFormulaDefaultList().stream().noneMatch(a -> Constant.BASIC_STRING_ZERO.equals(a.getIsShowSetting()))){
                return true;
            }
        }
        return false;
    }
}
