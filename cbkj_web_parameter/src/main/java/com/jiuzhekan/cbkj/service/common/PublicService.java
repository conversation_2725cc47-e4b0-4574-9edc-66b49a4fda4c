package com.jiuzhekan.cbkj.service.common;


import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;

import com.jiuzhekan.cbkj.mapper.sysapp.SysAppMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PublicService {

    @Autowired
    private SysAppMapper sysAppMapper;

    @Autowired
    private SysInstitutionMapper sysInstitutionMapper;

    @Autowired
    private SysDepartmentMapper sysDepartmentMapper;

    /**
     * @return : java.util.List<SysApp>
     * @Add : xhq on 2019/7/18 9:37
     * @Modify : guowei 2020/08/01
     * @Description :  获取当前登录人的医联体，000000时查出所有医联体，最前面加一条请选择. 有点不通用。
     */
    public List<SysApp> getAppList() {
        List<SysApp> list = new ArrayList<>();
        String appId = AdminUtils.getCurrentAppId();
        SysApp sysApp = new SysApp();
        if (!Constant.BASIC_APP_ID.equals(appId)) {
            sysApp.setAppId(appId);
        } else {
//            SysApp base = new SysApp();
//            base.setAppId(Constant.BASIC_APP_ID);
//            base.setAppName("全部医联体");
//            list.add(base);
        }
        list.addAll(sysAppMapper.getApplist(sysApp));
        return list;
    }






    /**
     * 获取医疗机构树
     *
     * @param appId appId
     * @return java.util.List
     * <AUTHOR>
     * @date 2021/7/19
     */
    public List getInsTree(String appId) {

        String adminAppId = AdminUtils.getCurrentAppId();
        if (adminAppId != null && !Constant.BASIC_APP_ID.equals(adminAppId)) {
            appId = adminAppId;
        }

        return getInsTree2(appId);
    }

    private List getInsTree2(String appId) {

        SysInstitution insParam = new SysInstitution();
        insParam.setAppId(appId);
        List<SysInstitution> insList = sysInstitutionMapper.getPageListByObj(insParam);

        LinkedHashMap<String, SysApp> appMap = new LinkedHashMap<>();

        for (SysInstitution ins : insList) {
            SysApp app = appMap.computeIfAbsent(ins.getAppId(), k -> {
                SysApp a = new SysApp();
                a.setAppId(ins.getAppId());
                a.setAppName(ins.getAppName());
                a.setInsList(new ArrayList<>());
                return a;
            });
            app.getInsList().add(ins);
        }

        List<SysApp> appList = new ArrayList<>();

        appMap.forEach((appKey, app) -> {
            if (!Constant.BASIC_APP_ID.equals(app.getAppId())) {
                appList.add(app);
            }
        });

        return appList;
    }

    /**
     * 获取科室树
     *
     * @param appId   appId
     * @param insCode insCode
     * @return java.util.List
     * <AUTHOR>
     * @date 2021/3/16
     */
    public List getDeptTree(String appId, String insCode) {
        return getDeptTree2(appId, insCode);
    }

    private List getDeptTree2(String appId, String insCode) {

        SysDepartment department = new SysDepartment();
        department.setAppId(appId);
        department.setInsCode(insCode);
        //List<SysDepartment> deptList = sysDepartmentMapper.getPageListByObj(department);
        List<SysDepartment> deptList = sysDepartmentMapper.getPageListByObjTree(department);

        LinkedHashMap<String, SysInstitution> insMap = new LinkedHashMap<>();

        for (SysDepartment sd : deptList) {
            SysInstitution ins = insMap.computeIfAbsent(sd.getInsCode(), k -> {
                SysInstitution i = new SysInstitution();
                i.setAppId(sd.getAppId());
                i.setAppName(sd.getAppName());
                i.setInsCode(sd.getInsCode());
                i.setInsName(sd.getInsName());
                i.setDeptList(new ArrayList<>());
                return i;
            });

            if (StringUtils.isNotBlank(sd.getDepOriginId())) {
                sd.setDeptId(sd.getDepOriginId());
            }
            ins.getDeptList().add(sd);
        }

        LinkedHashMap<String, SysApp> appMap = new LinkedHashMap<>();

        insMap.forEach((insKey, ins) -> {
            SysApp app = appMap.computeIfAbsent(ins.getAppId(), k -> {
                SysApp a = new SysApp();
                a.setAppId(ins.getAppId());
                a.setAppName(ins.getAppName());
                a.setInsList(new ArrayList<>());
                return a;
            });
            app.getInsList().add(ins);
        });

        List<SysApp> appList = new ArrayList<>();

        appMap.forEach((appKey, app) -> {
            if (!Constant.BASIC_APP_ID.equals(app.getAppId())) {
                appList.add(app);
            }
        });

        return appList;
    }

}