package com.jiuzhekan.cbkj.service.dic;

import com.jiuzhekan.cbkj.beans.dic.DicData;
import com.jiuzhekan.cbkj.beans.dic.DicNode;
import com.jiuzhekan.cbkj.beans.dic.DicNodeDto;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplay;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.pharmacy.DisplayMapper;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayMoneySettingMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
/**
 * <AUTHOR>
 */
public class DicBaseService {

    private DicBaseMapper dicBaseMapper;


    private SysDepartmentMapper departmentMapper;

    private DisplayMapper displayMapper;

    private TDisplayMoneySettingMapper tDisplayMoneySettingMapper;
    //TODO
    public static final String FEEDICTIONARYPARENTID = "ff64f6eb241811edad8d00163f006620";

    @Autowired
    DicBaseService(DicBaseMapper dicBaseMapper,
                   SysDepartmentMapper departmentMapper,
                   DisplayMapper displayMapper,
                   TDisplayMoneySettingMapper tDisplayMoneySettingMapper) {
        this.dicBaseMapper = dicBaseMapper;
        this.departmentMapper = departmentMapper;
        this.displayMapper = displayMapper;
        this.tDisplayMoneySettingMapper = tDisplayMoneySettingMapper;
    }

    private final String dicId = "f80af795a9a311ecad8d00163f006620";

    /**
     * 加载分页数据
     *
     * @param tDicBase 数据字典表
     * @param page     分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TDicBase tDicBase, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        String mapping = tDicBase.getMapping();
        List<TDicBase> list = new ArrayList<>();
        //为1查询已映射
        //科室字典映射查询
        if (dicId.equals(tDicBase.getDicId())) {
            List<SysDepartment> sysDepartmentList;
            if (Constant.BASIC_STRING_ONE.equals(mapping)) {
                sysDepartmentList = departmentMapper.getAllDeptInMapping(tDicBase);
            } else {
                sysDepartmentList = departmentMapper.getAllDeptNotInMapping(tDicBase);
            }
            return Page.getLayUiTablePageData(sysDepartmentList);
        }
        //其他字典映射关系查询
        if (Constant.BASIC_STRING_ONE.equals(mapping)) {
            list = dicBaseMapper.getPageListInMapping(tDicBase);
        } else {
            list = dicBaseMapper.getPageListNotInMapping(tDicBase);
        }
        return Page.getLayUiTablePageData(list);
    }


    /**
     * 查询大类字典代码
     *
     * @param tDicBase
     * @param page
     * @return
     */
    public Object getParentName(TDicBase tDicBase, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDicBase> parentNameList = dicBaseMapper.getParentName(tDicBase);
//        for (TDicBase dicBase : parentNameList) {
//            TDicBase tDicBase1 = new TDicBase();
//            tDicBase1.setParentId(dicBase.getDicId());
//            List<TDicBase> systemCodeItem = dicBaseMapper.getSystemCodeItem(tDicBase1);
//            List<TDicBase> hisItem = dicBaseMapper.getHisItem(tDicBase1);
//            List<TDicBase> pharmacyItem = dicBaseMapper.getDisplayItem(tDicBase1);
//
//            dicBase.setSysChecked(!systemCodeItem.isEmpty() ? "true" : "false");
//            dicBase.setHisChecked(!hisItem.isEmpty() ? "true" : "false");
//            dicBase.setDisplayChecked(!pharmacyItem.isEmpty() ? "true" : "false");
//        }
        return Page.getLayUiTablePageData(parentNameList);
    }

    /**
     * 查询明细
     *
     * @param tDicBase
     * @param page
     * @return
     */
    public Object getSystemItem(TDicBase tDicBase, Page page) {
        if (StringUtils.isBlank(tDicBase.getParentId())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDicBase> tDicBases = new ArrayList<>();
        //系统明细
        if (Constant.BASIC_STRING_ONE.equals(tDicBase.getBusinessType())) {
            tDicBases = dicBaseMapper.getSystemCodeItem(tDicBase);
        }
        //his明细
        if (Constant.BASIC_STRING_TWO.equals(tDicBase.getBusinessType())) {
            tDicBases = dicBaseMapper.getHisItem(tDicBase);
        }
        //药房明细
        if (Constant.BASIC_STRING_THREE.equals(tDicBase.getBusinessType())) {
            tDicBases = dicBaseMapper.getPharmacyItem(tDicBase);
        }
        return Page.getLayUiTablePageData(tDicBases);
    }

    public Object getSystemItemList(TDicBase tDicBase) {
        if (StringUtils.isBlank(tDicBase.getParentId())) {
            return ResEntity.entity(false, "参数不能为空", null);
        }
        List<TDicBase> tDicBases = new ArrayList<>();
     //   if (Constant.BASIC_STRING_ONE.equals(tDicBase.getBusinessType())) {
            tDicBases = dicBaseMapper.getSystemCodeItem(tDicBase);
    //    }
        return tDicBases;
    }

    /**
     * 插入新数据系统明细和His明细
     *
     * @param tDicBase 数据字典表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"parameter::dic", "parameter::display"}, allEntries = true)
    public ResEntity insert(TDicBase tDicBase) {
        //入参校验
        if (InputVerification(tDicBase)) {
            return ResEntity.error("参数不能为空");
        }
        TDicBase newDicBase = new TDicBase();
        if (Constant.BASIC_STRING_ONE.equals(tDicBase.getBusinessType())) {
            newDicBase.setAppId(Constant.BASIC_APP_ID);
            newDicBase.setInsCode(Constant.BASIC_INS_CODE);
            newDicBase.setDeptId(Constant.BASIC_DEPT_ID);
            newDicBase.setDisplayId(Constant.BASIC_PHA_ID);
        }

        InputDetail(tDicBase, newDicBase);

        if (Constant.BASIC_STRING_THREE.equals(tDicBase.getBusinessType())) {
            newDicBase.setAppId(Constant.BASIC_APP_ID);
            newDicBase.setInsCode(Constant.BASIC_INS_CODE);
            newDicBase.setDeptId(Constant.BASIC_DEPT_ID);
            newDicBase.setDisplayId(tDicBase.getDisplayId());
        }
        //设置属性
        newDicBase.setDicCode(tDicBase.getDicCode());
        newDicBase.setParentId(tDicBase.getParentId());
        List<TDicBase> displayByDicCodeAndParentId = dicBaseMapper.getDisplayByDicCodeAndParentId(newDicBase);
        if (displayByDicCodeAndParentId.size() > 0) {
            return ResEntity.entity(false, "明细代码重复", null);
        }
        setProperty(tDicBase, newDicBase);
        dicBaseMapper.insert(newDicBase);
        return ResEntity.success(newDicBase);
    }

    /**
     * 数据字典更新接口
     *
     * @param tDicBase
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    //@CacheEvict(value = {"parameter::dic::dicCode","parameter::dic::all","parameter::display"},allEntries = true)
    @CacheEvict(value = {"parameter::dic", "parameter::display"}, allEntries = true)
    public ResEntity update(TDicBase tDicBase) {
        //入参校验
        if (InputVerification(tDicBase)) {
            return ResEntity.error("参数不能为空");
        }
        TDicBase newDicBase = new TDicBase();
        InputDetail(tDicBase, newDicBase);
        newDicBase.setDicCode(tDicBase.getDicCode());
        newDicBase.setParentId(tDicBase.getParentId());
        updateProperty(tDicBase, newDicBase);
        //该逻辑应用与药房服务配置
        if (Constant.BASIC_STRING_TWO.equals(newDicBase.getStatus())) {
            Integer byDicIds = tDisplayMoneySettingMapper.getMoneySettingByDicId(newDicBase.getDicId());
            if (byDicIds > Constant.ADMIN_EXT_ZERO) {
                return ResEntity.entity(false, "药房服务配置已应用无法禁用", null);
            }
        }
        dicBaseMapper.updateByPrimaryKey(newDicBase);
        TDicBase objectById = dicBaseMapper.getObjectById(tDicBase.getDicId());
        objectById.setBusinessType(tDicBase.getBusinessType());
        return ResEntity.success(objectById);
    }


    /**
     * 入参校验
     *
     * @param tDicBase
     * @return
     */
    private boolean InputVerification(TDicBase tDicBase) {
        if (StringUtils.isBlank(tDicBase.getDicName())) {
            return true;
        }
        if (StringUtils.isBlank(tDicBase.getDicCode())) {
            return true;
        }
        if (StringUtils.isBlank(tDicBase.getIsDefault())) {
            return true;
        }
        return false;
    }

    /**
     * 业务类型处理
     *
     * @param tDicBase
     * @param newDicBase
     */
    private void InputDetail(TDicBase tDicBase, TDicBase newDicBase) {
        if (Constant.BASIC_STRING_TWO.equals(tDicBase.getBusinessType())) {
            if (StringUtils.isBlank(tDicBase.getInsCode())) {
                newDicBase.setInsCode(Constant.BASIC_INS_CODE);
            } else {
                newDicBase.setInsCode(tDicBase.getInsCode());
            }
            newDicBase.setAppId(tDicBase.getAppId());
            newDicBase.setDeptId(Constant.BASIC_DEPT_ID);
            newDicBase.setDisplayId(Constant.BASIC_PHA_ID);
        }
    }

    private void setProperty(TDicBase tDicBase, TDicBase newTDicBase) {
        newTDicBase.setDicId(IDUtil.getID());
        newTDicBase.setDicName(tDicBase.getDicName());
        newTDicBase.setIsDefault(tDicBase.getIsDefault());
        newTDicBase.setSort(tDicBase.getSort());
        newTDicBase.setCreateDate(new Date());
        newTDicBase.setCreateUser(AdminUtils.getCurrentHr().getUserId());
        newTDicBase.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
        newTDicBase.setStatus(tDicBase.getStatus());

    }

    private void updateProperty(TDicBase tDicBase, TDicBase newDicBase) {
        newDicBase.setDicId(tDicBase.getDicId());
        newDicBase.setDicName(tDicBase.getDicName());
        newDicBase.setIsDefault(tDicBase.getIsDefault());
        newDicBase.setSort(tDicBase.getSort());
        newDicBase.setStatus(tDicBase.getStatus());
    }


    /**
     * 删除字典数据
     *
     * @param dicId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {/*"parameter::dic::dicCode",*/"parameter::dic", "parameter::display"}, allEntries = true)
    public Object delete(String dicId) {
        if (StringUtils.isBlank(dicId)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        TDicBase tDicBase = new TDicBase();
        tDicBase.setDicId(dicId);
        Integer byDicIds = tDisplayMoneySettingMapper.getMoneySettingByDicId(dicId);
        if (byDicIds > Constant.ADMIN_EXT_ZERO) {
            return ResEntity.entity(false, "药房服务配置已应用无法删除", null);
        }
        int i = dicBaseMapper.deleteByPrimaryKey(tDicBase);

        return ResEntity.entity(true, "", null);
    }

    /**
     * 获取名称
     *
     * @return
     */
    public ResEntity getFname() {
        List<TDicBase> fname = dicBaseMapper.getFname();
        return ResEntity.success(fname);
    }


    /**
     * 获取职业信息
     *
     * @param dicCode
     * @return
     */
    public ResEntity getCareer(String dicCode) {
        if (StringUtils.isBlank(dicCode)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        List<TDicBase> career = dicBaseMapper.getCareer(dicCode);
        return ResEntity.success(career);
    }

    /**
     * 禁用接口
     *
     * @param dicId
     * @param status
     * @return
     */
    @CacheEvict(value = {"parameter::dic", "parameter::display"}, allEntries = true)
    public Object disable(String dicId, String status) {
        if (StringUtils.isBlank(dicId)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        if (StringUtils.isBlank(status)) {
            return ResEntity.entity(false, "缺少参数", null);
        }
        TDicBase tDicBase = new TDicBase();
        tDicBase.setDicId(dicId);
        tDicBase.setStatus(status);
        //药房服务配置费用字典无法禁用
        TDicBase itemByParentId = dicBaseMapper.getItemByParentId(dicId);
        if ("jyfx".equals(itemByParentId.getDicCode()) ||
                "bzfs".equals(itemByParentId.getDicCode()) ||
                "psfx".equals(itemByParentId.getDicCode())) {
            return ResEntity.entity(false, "该大类应用于药房费用配置无法禁用", null);
        }
        dicBaseMapper.updateByPrimaryKey(tDicBase);
        TDicBase objectById = dicBaseMapper.getObjectById(dicId);
        return ResEntity.entity(true, "", objectById);
    }

    /**
     * 查询收费目录大类
     *
     * @return
     */
    public Object getFeeDictionaryParent() {

        List<TDicBase> feeDictionaryParent = dicBaseMapper.getFeeDictionaryParent(FEEDICTIONARYPARENTID);
        return feeDictionaryParent;
    }


    public ResEntity getList(DicData dicData) {

        if (StringUtils.isBlank(dicData.getDicCode())) {
            return ResEntity.error("字典编码不能为空");
        }

        DicNode dicNode = dicBaseMapper.getDetailByCode(dicData.getDicCode());

        if (dicNode == null) {
            return ResEntity.error("字典不存在");
        }


        DicNode param = new DicNode(dicData);
        List<DicNodeDto> children;

        //取值顺序：药房->科室->医疗机构->医共体->系统
        if (StringUtils.isNotBlank(dicData.getDisplayId())) {
            //药房
            DicNode displayParam = new DicNode();
            displayParam.setDicCode(param.getDicCode());
            displayParam.setDisplayId(param.getDisplayId());
            children = dicBaseMapper.getChildren(displayParam);

            if (children.isEmpty()) {
                //科室
                param.setDisplayId(Constant.BASIC_DEPT_ID);
                children = dicBaseMapper.getChildren(param);
            }

        } else {
            //科室
            children = dicBaseMapper.getChildren(param);
        }

        if (children.isEmpty()) {
            //医疗机构
            param.setDeptId(Constant.BASIC_INS_CODE);
            children = dicBaseMapper.getChildren(param);

            if (children.isEmpty()) {
                //医共体
                param.setInsCode(Constant.BASIC_APP_ID);
                children = dicBaseMapper.getChildren(param);

                if (children.isEmpty()) {
                    //系统
                    param.setAppId(Constant.BASIC_APP_ID);
                    children = dicBaseMapper.getChildren(param);
                }
            }
        }

        //   dicNode.setChildren(children);

        return ResEntity.success(children);
    }

}
