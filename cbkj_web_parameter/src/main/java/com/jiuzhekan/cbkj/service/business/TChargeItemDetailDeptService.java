package com.jiuzhekan.cbkj.service.business;

import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetailDept;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.TChargeItemDetailDeptMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class TChargeItemDetailDeptService {

    @Autowired
    private TChargeItemDetailDeptMapper tChargeItemDetailDeptMapper;

    /**
     * 加载分页数据
     *
     * @param tChargeItemDetailDept 收费项目管理明细科室映射表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2025-08-05
     */
    public Object getPageDatas(TChargeItemDetailDept tChargeItemDetailDept, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TChargeItemDetailDept> list = tChargeItemDetailDeptMapper.getPageListByObj(tChargeItemDetailDept);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 收费项目管理明细科室映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TChargeItemDetailDept tChargeItemDetailDept = tChargeItemDetailDeptMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tChargeItemDetailDept);
    }


    /**
     * 插入新数据
     *
     * @param tChargeItemDetailDept 收费项目管理明细科室映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TChargeItemDetailDept tChargeItemDetailDept){

//        tChargeItemDetailDept.setId(IDUtil.getID());
        long rows = tChargeItemDetailDeptMapper.insert(tChargeItemDetailDept);

        return ResEntity.success(tChargeItemDetailDept);
    }


    /**
     * 修改
     *
     * @param tChargeItemDetailDept 收费项目管理明细科室映射表
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TChargeItemDetailDept tChargeItemDetailDept) {

        long rows = tChargeItemDetailDeptMapper.updateByPrimaryKey(tChargeItemDetailDept);

        return ResEntity.success(tChargeItemDetailDept);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2025-08-05
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tChargeItemDetailDeptMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
