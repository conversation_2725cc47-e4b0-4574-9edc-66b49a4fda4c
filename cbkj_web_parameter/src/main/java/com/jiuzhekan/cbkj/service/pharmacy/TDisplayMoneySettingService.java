package com.jiuzhekan.cbkj.service.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayExpress;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayMoneySettingMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class TDisplayMoneySettingService {

    @Autowired
    private TDisplayMoneySettingMapper tDisplayMoneySettingMapper;

    /**
     * 加载分页数据
     *
     * @param tDisplayMoneySetting 药房费用配置信息表
     * @param page                 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-06-15
     */
    public Object getPageDatas(TDisplayMoneySetting tDisplayMoneySetting, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDisplayMoneySetting> list = tDisplayMoneySettingMapper.getPageListByObj(tDisplayMoneySetting);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 药房费用配置信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TDisplayMoneySetting tDisplayMoneySetting = tDisplayMoneySettingMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDisplayMoneySetting);
    }


    /**
     * 插入新数据
     *
     * @param tDisplayMoneySetting 药房费用配置信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity insert(TDisplayMoneySetting tDisplayMoneySetting) {
//
//        tDisplayMoneySetting.setId(IDUtil.getID());
//        long rows = tDisplayMoneySettingMapper.insert(tDisplayMoneySetting);
//
//        return ResEntity.success(tDisplayMoneySetting);
//    }


    /**
     * 修改
     *
     * @param tDisplayMoneySetting 药房费用配置信息表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDisplayMoneySetting tDisplayMoneySetting) {

        long rows = tDisplayMoneySettingMapper.updateByPrimaryKey(tDisplayMoneySetting);

        return ResEntity.success(tDisplayMoneySetting);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-06-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tDisplayMoneySettingMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveSetting(TDisplayProduction displayProduction, String setId) {
        //通用代煎费用配置
        List<TDisplayMoneySetting> packingCommonList = displayProduction.getShowDefaultList();
        List<TDisplayMoneySetting> packingList = displayProduction.getShowDicList();
        //配方代煎费用配置
        List<TDisplayMoneySetting> formulaProductionCommonSetList = displayProduction.getFormulaDefaultList();
        List<TDisplayMoneySetting> formulaProductionSetList = displayProduction.getFormulaDicList();
        //删除旧的费用配置
        tDisplayMoneySettingMapper.deleteBySetId(setId);
        if (Constant.BASIC_STRING_ONE.equals(displayProduction.getUsuallyProductionSet())) {
            insertOrUpdate(packingList, setId);
        } else {
            for (TDisplayMoneySetting tDisplayMoneySetting : packingCommonList) {
                tDisplayMoneySetting.setDicId("00000000");
            }
            insertOrUpdate(packingCommonList, setId);
        }
        if (Constant.BASIC_STRING_ONE.equals(displayProduction.getFormulaProductionSet())) {
            insertOrUpdate(formulaProductionSetList, setId);
        } else {
            for (TDisplayMoneySetting tDisplayMoneySetting : formulaProductionCommonSetList) {
                tDisplayMoneySetting.setDicId("00000000");
            }
            insertOrUpdate(formulaProductionCommonSetList, setId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveExpressSetting(TDisplayExpress displayProduction, String setId) {
//        //通用住院配送费用配置
//        List<TDisplayMoneySetting> packingCommonList = displayProduction.getShowDefaultList();
        //通用门诊配送费用配置
        List<TDisplayMoneySetting> formulaProductionCommonSetList = displayProduction.getShowDefaultList();

        //删除旧的费用配置
        tDisplayMoneySettingMapper.deleteBySetId(setId);
        if (Constant.BASIC_STRING_ONE.equals(displayProduction.getExpressSet())) {
            insertOrUpdate(formulaProductionCommonSetList, setId);
        } else {
            for (TDisplayMoneySetting tDisplayMoneySetting : formulaProductionCommonSetList) {
                tDisplayMoneySetting.setDicId("00000000");
            }
            insertOrUpdate(formulaProductionCommonSetList, setId);
        }
//        insertOrUpdate(packingCommonList, setId);
//        insertOrUpdate(formulaProductionCommonSetList, setId);

    }

    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdate(List<TDisplayMoneySetting> tDisplayMoneySettingList, String setId) {
        for (TDisplayMoneySetting tDisplayMoneySetting : tDisplayMoneySettingList) {
            tDisplayMoneySetting.setId(IDUtil.getID());
            tDisplayMoneySetting.setStatus(Constant.BASIC_STRING_ZERO);
            tDisplayMoneySetting.setSetId(setId);
            tDisplayMoneySetting.setCreateDate(new Date());
            tDisplayMoneySetting.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            tDisplayMoneySettingMapper.insert(tDisplayMoneySetting);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveDecoctionSetting(TDisplayDecoction displayDecoction, String setId) {
        //通用制膏费用配置
        List<TDisplayMoneySetting> currencyDecoctionSets = displayDecoction.getShowDefaultList();
        List<TDisplayMoneySetting> currencyPersonalityDecoctionSets = displayDecoction.getShowDicList();
        //配方制膏费用配置
        List<TDisplayMoneySetting> formulaDecoctionSets = displayDecoction.getFormulaDefaultList();
        List<TDisplayMoneySetting> formulaPersonalityDecoctionSets = displayDecoction.getFormulaDicList();

        tDisplayMoneySettingMapper.deleteBySetId(setId);
        if (Constant.BASIC_STRING_ONE.equals(displayDecoction.getUsuallyDecoctionSet())) {
            insertOrUpdate(currencyPersonalityDecoctionSets, setId);

        } else {
            for (TDisplayMoneySetting tDisplayMoneySetting : currencyDecoctionSets) {
                tDisplayMoneySetting.setDicId("00000000");
            }
            insertOrUpdate(currencyDecoctionSets, setId);
        }
        if (Constant.BASIC_STRING_ONE.equals(displayDecoction.getFormulaDecoctionSet())) {
            insertOrUpdate(formulaPersonalityDecoctionSets, setId);

        } else {
            for (TDisplayMoneySetting tDisplayMoneySetting : formulaDecoctionSets) {
                tDisplayMoneySetting.setDicId("00000000");
            }
            insertOrUpdate(formulaDecoctionSets, setId);
        }
    }
}
