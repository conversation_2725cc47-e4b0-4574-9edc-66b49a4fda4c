package com.jiuzhekan.cbkj.service.business.asyncTask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTask;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskApp;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskIsC;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskValue;
import com.jiuzhekan.cbkj.beans.drug.TDrugList;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskMapper;
import com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskValueMapper;
import com.jiuzhekan.cbkj.mapper.drug.TDrugListMapper;

import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;

@Service
public class AsyncTaskService {

    private final AsyncTaskMapper asyncTaskMapper;
    private final AsyncTaskValueMapper asyncTaskValueMapper;
    private final TDrugListMapper tDrugListMapper;

    private final SysInstitutionMapper sysInstitutionMapper;

    @Autowired
    public AsyncTaskService(AsyncTaskMapper asyncTaskMapper,
                            AsyncTaskValueMapper asyncTaskValueMapper,
                            TDrugListMapper tDrugListMapper,
                            SysInstitutionMapper sysInstitutionMapper
    ) {
        Assert.notNull(asyncTaskMapper, "asyncTaskMapper must not be null!");
        Assert.notNull(asyncTaskValueMapper, "asyncTaskValueMapper must not be null!");
        Assert.notNull(tDrugListMapper, "tDrugListMapper must not be null!");
        Assert.notNull(sysInstitutionMapper, "sysInstitutionMapper must not be null!");
        this.asyncTaskMapper = asyncTaskMapper;
        this.asyncTaskValueMapper = asyncTaskValueMapper;
        this.tDrugListMapper = tDrugListMapper;
        this.sysInstitutionMapper = sysInstitutionMapper;
    }

    /**
     * 加载分页数据
     *
     * @param asyncTask 任务主表
     * @param page      分页
     * @return Object
     * <AUTHOR>
     * @date 2022-04-15
     */
    public Object getPageDatas(AsyncTask asyncTask, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        if (StringUtils.isBlank(asyncTask.getStatus())) {
            asyncTask.setStatus("0,1,2");
            asyncTask.setStatus2("0,1");
        } else {
            asyncTask.setStatus2(asyncTask.getStatus());
        }
//        if (!StringUtils.isBlank(asyncTask.getAsyncExecType())){
//            //查全部，置空
//            if (Constant.BASIC_STRING_THREE.equals(asyncTask.getAsyncExecType())){
//                asyncTask.setAsyncExecType(null);
//            }
//        }
        List<AsyncTask> list = asyncTaskMapper.getPageListByObj(asyncTask);
        for (AsyncTask asyncTask1 : list) {
            AsyncTaskValue syncTaskValue = new AsyncTaskValue();
            syncTaskValue.setAsyncId(asyncTask1.getId());
            List<AsyncTaskValue> valueList = asyncTaskValueMapper.getPageListByObj(syncTaskValue);
            if (Constant.BASIC_STRING_ONE.equals(asyncTask1.getAsyncType()) || Constant.BASIC_STRING_TWO.equals(asyncTask1.getAsyncType())) {
                if (valueList.size() == 1) {
                    TDrugList objectById = tDrugListMapper.getObjectById(valueList.get(0).getDrugId());
                    if (null != objectById) {
                        asyncTask1.setTaskDetail(objectById.getDrugName());
                    }
                }

            } else {
                StringBuilder temp = new StringBuilder("");
                for (AsyncTaskValue taskValue : valueList) {
                    SysInstitution app = sysInstitutionMapper.getSysInstitutionByInsCodeAndAppId(taskValue.getInsCode(), taskValue.getAppId());
                    if (null != app) {
                        temp.append(app.getAppName()).append("+").append(app.getInsName()).append(";");
                    }
                }
                asyncTask1.setTaskDetail(temp.toString());
            }
        }
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 任务主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    public ResEntity findObj(String id) {
        AsyncTask asyncTask = asyncTaskMapper.getObjectById(id);
        String asyncType = asyncTask.getAsyncType();
        AsyncTaskValue syncTaskValue = new AsyncTaskValue();
        syncTaskValue.setAsyncId(asyncTask.getId());
        List<AsyncTaskValue> valueList = asyncTaskValueMapper.getPageListByObj(syncTaskValue);
        if (Constant.BASIC_STRING_ONE.equals(asyncType) || Constant.BASIC_STRING_TWO.equals(asyncType) || Constant.BASIC_STRING_EIGHT.equals(asyncType)) {
            if (valueList.size() == 1) {
                asyncTask.setDrugId(valueList.get(0).getDrugId());
            }
        } else {
            //任务分类：1.药品目录2.药品库存3.医生4.科室
            List<AsyncTaskApp> asyncTaskApps = new ArrayList<AsyncTaskApp>();
            for (AsyncTaskValue value : valueList) {
                AsyncTaskApp app = new AsyncTaskApp();
                app.setAppId(value.getAppId());
                app.setInsCode(value.getInsCode());
                asyncTaskApps.add(app);
            }
            asyncTask.setTaskAppList(asyncTaskApps);
        }


        return ResEntity.entity(true, Constant.SUCCESS_DX, asyncTask);
    }

    public ResEntity selectByCondition(AsyncTask asyncTask, AsyncTaskApp asyncTaskApp, String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        if (Constant.BASIC_STRING_ONE.equals(type)) {
            String[] split = asyncTask.getDrugId().split(",");
            for (String s : split) {
                AsyncTaskIsC asyncTaskIsC = new AsyncTaskIsC();
                asyncTaskIsC.setAsyncType(asyncTask.getAsyncType());
                asyncTaskIsC.setAsyncExecType(asyncTask.getAsyncExecType());
                asyncTaskIsC.setStatus(Constant.BASIC_STRING_ONE.equals(asyncTask.getAsyncExecType()) ? "0,1" : "0,1,2");
                asyncTaskIsC.setDrugId(s);
                List<AsyncTask> asyncTasks = asyncTaskMapper.selectByCondition(asyncTaskIsC);
                if (!asyncTasks.isEmpty()) {
                    if (null != asyncTask.getId() && asyncTasks.size() == 1 && asyncTasks.get(0).getId().equals(asyncTask.getId())) {
                        //继续
                    } else {
                        return ResEntity.entity(false, "已存在有效的相同任务！", null);
                    }
                }
            }
        } else {
            AsyncTaskIsC asyncTaskIsC = new AsyncTaskIsC();
            asyncTaskIsC.setAsyncType(asyncTask.getAsyncType());
            asyncTaskIsC.setAsyncExecType(asyncTask.getAsyncExecType());
            asyncTaskIsC.setStatus(asyncTask.getStatus());
            asyncTaskIsC.setAppId(asyncTaskApp.getAppId());
            asyncTaskIsC.setInsCode(asyncTaskApp.getInsCode());
            asyncTaskIsC.setStatus(Constant.BASIC_STRING_ONE.equals(asyncTask.getAsyncExecType()) ? "0,1" : "0,1,2");
            List<AsyncTask> asyncTasks = asyncTaskMapper.selectByCondition(asyncTaskIsC);
            if (asyncTasks.size() > 0) {
                if (null != asyncTask.getId() && asyncTasks.size() == 1 && asyncTasks.get(0).getId().equals(asyncTask.getId())) {
                    //继续
                } else {
                    return ResEntity.entity(false, "已存在有效的相同任务！", null);
                }
            }
        }

        return null;
    }

    /**
     * 插入新数据
     *
     * @param asyncTask 任务主表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertOrUpdate(AsyncTask asyncTask) {
        asyncTask.setUpdateDate(new Date());
        if (StringUtils.isBlank(asyncTask.getAsyncType())) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(asyncTask.getAsyncExecType())) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        //2022-05-07 zjh 修改全部都需要选择第三方his
        if (StringUtils.isBlank(asyncTask.getHisId())) {
            return ResEntity.entity(false, "参数错误(缺少参数HisId)！", null);
        }
        if (null == asyncTask.getId()) {
            asyncTask.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            asyncTask.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
            asyncTask.setCreateDate(new Date());
            asyncTask.setStatus(Constant.BASIC_STRING_ZERO);
        }
        if (Constant.BASIC_STRING_ONE.equals(asyncTask.getAsyncType()) ||
                Constant.BASIC_STRING_TWO.equals(asyncTask.getAsyncType()) ||
                Constant.BASIC_STRING_EIGHT.equals(asyncTask.getAsyncType())
        ) {
            //传的是目录或者是药品库存
            if (StringUtils.isBlank(asyncTask.getDrugId())) {
                return ResEntity.entity(false, "参数错误(缺少参数drugId)！", null);
            }

            ResEntity resEntity = selectByCondition(asyncTask, null, Constant.BASIC_STRING_ONE);
            if (null != resEntity) {
                return resEntity;
            }
            String[] split = asyncTask.getDrugId().split(",");
            for (String s : split) {
                AsyncTask asyncTask1 = new AsyncTask();
                BeanUtils.copyProperties(asyncTask, asyncTask1);
                if (null != asyncTask.getId()) {
                    TDrugList objectById = tDrugListMapper.getObjectById(s);
                    if ( (null != objectById) || Constant.BASIC_STRING_EIGHT.equals(asyncTask.getAsyncType())  ) {
                        //先删除
                        String[] a = {asyncTask.getId() + ""};
                        asyncTaskValueMapper.deleteBylist(a);
                        //在添加
                        AsyncTaskValue taskValue = new AsyncTaskValue();
                        taskValue.setAsyncId(asyncTask.getId());
                        taskValue.setDrugId(s);
                        taskValue.setAsyncType(asyncTask.getAsyncType());
                        asyncTaskValueMapper.insert(taskValue);
                        //更新的status从数据库获取返给前端。
                        AsyncTask objectById1 = asyncTaskMapper.getObjectById(asyncTask.getId() + "");
                        asyncTask.setHisName(objectById1.getHisName());
                        //asyncTask.setStatus(objectById1.getStatus());
                        //.setHisId(objectById.getHisId());
                        asyncTask.setHisId(asyncTask.getHisId());
                        //asyncTask.setUpdateDate(new Date());
                        asyncTask.setTaskDetail( null == objectById ? "收费目录" : objectById.getDrugName());
                        asyncTaskMapper.updateByPrimaryKey(asyncTask);
                    }
                } else {
//                    TDrugList objectById = tDrugListMapper.getObjectById(s);
//                    if (null != objectById  || Constant.BASIC_STRING_EIGHT.equals(asyncTask.getAsyncType())) {
                        asyncTask.setStatus(StringUtils.isBlank(asyncTask.getStatus()) ? Constant.BASIC_STRING_ZERO : asyncTask.getStatus());
//                        asyncTask.setHisId(objectById.getHisId());
                        if (Constant.BASIC_STRING_ONE.equals(asyncTask.getAsyncExecType()) &&
                                StringUtils.isBlank(asyncTask.getAsyncStartTime()) &&
                                StringUtils.isBlank(asyncTask.getAsyncEndTime()) &&
                                StringUtils.isBlank(asyncTask.getAsyncBetweenUnit())
                        ) {
                            //定时任务，未开启
                            asyncTask.setStatus(Constant.BASIC_STRING_TWO);
                        }
                        asyncTask.setHisId(asyncTask.getHisId());
                        asyncTaskMapper.insert(asyncTask);
                        AsyncTaskValue taskValue = new AsyncTaskValue();
                        taskValue.setAsyncId(asyncTask.getId());
                        taskValue.setDrugId(s);
                        taskValue.setAsyncType(asyncTask.getAsyncType());
                        asyncTaskValueMapper.insert(taskValue);
//                    }
                    asyncTask.setId(null);
                }
            }
        } else {
            //传的是医共体
            if (null != asyncTask.getTaskAppList() && asyncTask.getTaskAppList().size() > 0) {
                StringBuilder temp = new StringBuilder("");

                List<AsyncTaskApp> taskAppList = asyncTask.getTaskAppList();
                for (AsyncTaskApp asyncTaskApp : taskAppList) {
                    boolean a = asyncTaskApp.getAppId().contains(",");
                    boolean b = asyncTaskApp.getAppId().contains(";");
                    boolean c = asyncTaskApp.getInsCode().contains(",");
                    boolean d = asyncTaskApp.getInsCode().contains(";");
                    if (a || b || c || d) {
                        return ResEntity.entity(false, "机构id不能含有,和;符号", null);
                    }
                    ResEntity resEntity = selectByCondition(asyncTask, asyncTaskApp, Constant.BASIC_STRING_THREE);
                    if (null != resEntity) {
                        return resEntity;
                    }
                    temp.append(asyncTaskApp.getAppId()).append(",").append(asyncTaskApp.getInsCode()).append(";");

                }
                StringBuilder temp2 = new StringBuilder("");
                for (AsyncTaskApp taskValue : taskAppList) {
                    SysInstitution app = sysInstitutionMapper.getSysInstitutionByInsCodeAndAppId(taskValue.getInsCode(), taskValue.getAppId());
                    if (null != app) {
                        temp2.append(app.getAppName()).append("+").append(app.getInsName()).append(";");
                    }
                }

                if (null != asyncTask.getId()) {
                    //更新操作先删除
                    String[] a = {asyncTask.getId() + ""};
                    asyncTaskValueMapper.deleteBylist(a);
                    //在添加
                    for (AsyncTaskApp asyncTaskApp : taskAppList) {
                        AsyncTaskValue taskValue = new AsyncTaskValue();
                        taskValue.setAsyncId(asyncTask.getId());
                        taskValue.setAppId(asyncTaskApp.getAppId());
                        taskValue.setInsCode(asyncTaskApp.getInsCode());
                        taskValue.setAsyncType(asyncTask.getAsyncType());
                        asyncTaskValueMapper.insert(taskValue);
                    }
                    AsyncTask objectById1 = asyncTaskMapper.getObjectById(asyncTask.getId() + "");
                    asyncTask.setHisName(objectById1.getHisName());
                    //asyncTask.setStatus(objectById1.getStatus());
                    //asyncTask.setUpdateDate(new Date());
                    asyncTask.setTaskDetail(temp.toString());
                    asyncTaskMapper.updateByPrimaryKey(asyncTask);
                    asyncTask.setTaskDetail(temp2.toString());
                } else {
                    asyncTask.setStatus(StringUtils.isBlank(asyncTask.getStatus()) ? Constant.BASIC_STRING_ZERO : asyncTask.getStatus());
                    if (Constant.BASIC_STRING_ONE.equals(asyncTask.getAsyncExecType()) &&
                            StringUtils.isBlank(asyncTask.getAsyncStartTime()) &&
                            StringUtils.isBlank(asyncTask.getAsyncEndTime()) &&
                            StringUtils.isBlank(asyncTask.getAsyncBetweenUnit())
                    ) {
                        //定时任务，未开启
                        asyncTask.setStatus(Constant.BASIC_STRING_TWO);
                    }
                    asyncTaskMapper.insert(asyncTask);
                    //在添加
                    for (AsyncTaskApp asyncTaskApp : taskAppList) {
                        AsyncTaskValue taskValue = new AsyncTaskValue();
                        taskValue.setAsyncId(asyncTask.getId());
                        taskValue.setAppId(asyncTaskApp.getAppId());
                        taskValue.setInsCode(asyncTaskApp.getInsCode());
                        taskValue.setAsyncType(asyncTask.getAsyncType());
                        asyncTaskValueMapper.insert(taskValue);
                    }
                    //asyncTask.setId(null);
                }
            } else {
                return ResEntity.entity(false, "请选择机构", null);
            }
        }
        return ResEntity.success(asyncTask);
    }


    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-04-15
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        for (int i = 0; i < ids.split(Constant.ENGLISH_COMMA).length; i++) {
            HashMap<String, Object> map = new HashMap<>(16);
            map.put("id", ids.split(",")[i]);
            map.put("status", "3");
            long rowsR = asyncTaskMapper.updateStatusByIds(map);
        }
        return ResEntity.success(ids.split(",").length);
    }

    /**
     * 更新状态
     *
     * @param asyncId
     * @param status
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2022/4/27 13:21
     **/
    @Transactional(rollbackFor = Exception.class)
    public ResEntity statechange(String asyncId, String status) {
        if (StringUtils.isBlank(asyncId) || StringUtils.isBlank(status)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        HashMap<String, Object> map = new HashMap<>(16);
        map.put("id", asyncId);
        map.put("status", status);
        map.put("updateDate", new Date());
        asyncTaskMapper.updateStatusByIds(map);
        return ResEntity.success(map);
    }

    /**
     * 添加一次任务
     *
     * @param asyncTaskId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity addOnceTask(String asyncTaskId) {
        if (StringUtils.isBlank(asyncTaskId)) {
            return ResEntity.entity(false, "参数错误(缺少参数任务id)！", null);
        }
        AsyncTask asyncTask = asyncTaskMapper.getObjectById(asyncTaskId);
        if (null != asyncTask) {
            asyncTask.setId(null);
            asyncTask.setAsyncExecType(Constant.BASIC_STRING_TWO);
            asyncTask.setAsyncStartTime(null);
            asyncTask.setAsyncLastTime(null);
            asyncTask.setAsyncEndTime(null);
            asyncTask.setAsyncBetweenUnit(null);
            asyncTask.setUpdateDate(new Date());
            if (Constant.BASIC_STRING_ONE.equals(asyncTask.getAsyncType()) || Constant.BASIC_STRING_TWO.equals(asyncTask.getAsyncType())) {
                //设置药品目录
                List<AsyncTaskValue> asyncTaskValueList = asyncTaskValueMapper.getListByAsyncId(asyncTaskId);
                if (asyncTaskValueList.size() == 1 && !StringUtils.isBlank(asyncTaskValueList.get(0).getDrugId())) {
                    asyncTask.setDrugId(asyncTaskValueList.get(0).getDrugId());
                    return insertOrUpdate(asyncTask);
                }
            } else {
                //设置医生、科室
                List<AsyncTaskValue> asyncTaskValueList = asyncTaskValueMapper.getListByAsyncId(asyncTaskId);
                if (asyncTaskValueList.size() > 0) {
                    List<AsyncTaskApp> taskAppList = new ArrayList<>();
                    for (AsyncTaskValue asyncTaskValue : asyncTaskValueList) {
                        AsyncTaskApp asyncTaskApp = new AsyncTaskApp();
                        asyncTaskApp.setAppId(asyncTaskValue.getAppId());
                        asyncTaskApp.setInsCode(asyncTaskValue.getInsCode());
                        taskAppList.add(asyncTaskApp);
                    }
                    asyncTask.setTaskAppList(taskAppList);
                    return insertOrUpdate(asyncTask);
                }
            }
        }
        return ResEntity.success("");
    }
}
