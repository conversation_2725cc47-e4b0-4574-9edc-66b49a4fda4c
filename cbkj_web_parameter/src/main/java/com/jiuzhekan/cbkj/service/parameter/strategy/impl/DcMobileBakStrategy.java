package com.jiuzhekan.cbkj.service.parameter.strategy.impl;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SaveParameter;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper;
import com.jiuzhekan.cbkj.service.parameter.strategy.ParamStrategy;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Service("DC_MOBILE_BAK")
public class DcMobileBakStrategy implements ParamStrategy {
    private final TSysParamInitDescMapper tSysParamInitDescMapper;
    @Autowired
    DcMobileBakStrategy(TSysParamInitDescMapper tSysParamInitDescMapper){
        Assert.notNull(tSysParamInitDescMapper, "tSysParamInitDescMapper must not be null!");
        this.tSysParamInitDescMapper = tSysParamInitDescMapper;
    }
    @Override
    public String getParam(JSONObject pjsonObject, JSONObject jsonObject, TSysParamNew tSysParam1, JSONArray itemArray) {
        String[] split = tSysParam1.getParValues().split(",");
        jsonObject.remove("parName");
        jsonObject.put("parName", "备用号码的数量");
        jsonObject.remove("paramType");
        jsonObject.put("paramType", tSysParam1.getParamType().split(",")[0]);
        jsonObject.remove("parValues");
        jsonObject.put("parValues", split[0]);

        TSysParamInitDesc tSysParamInitDesc = new TSysParamInitDesc();
        tSysParamInitDesc.setParamId(tSysParam1.getParId());
        List<TSysParamInitDesc> pageListByObj = tSysParamInitDescMapper.getPageListByObj(tSysParamInitDesc);
        JSONObject j = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (TSysParamInitDesc tSysParamInitDesc1 : pageListByObj) {
            j.put("label", tSysParamInitDesc1.getParamInitName());
            j.put("value", tSysParamInitDesc1.getParamInitCode());
            j.put("optionDiagramURL", StringUtils.isBlank(tSysParamInitDesc1.getOptionDiagram()) ? " " : tSysParamInitDesc1.getOptionDiagram());
            jsonArray.add(j);
        }
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.putAll(jsonObject);
        jsonObject1.remove("parName");
        jsonObject1.put("parName", "备用号码必填数量");
        jsonObject1.put("parId", tSysParam1.getParId() + "-2");
        jsonObject1.put("paramInitDesc", jsonArray);
        jsonObject1.put("parValues", split.length == 1 ? "0" : split[1]);
        itemArray.add(jsonObject1);
        return Constant.BASIC_STRING_ONE;
    }

    @Override
    public void setParam(SaveParameter saveParameter, DiagnosisParamNewVO tSysParamVO,TSysParamNew tSysParamNew) {
        if (!StringUtils.isBlank(saveParameter.getValue2())) {
            tSysParamVO.setParValues(saveParameter.getValue1() + "," + saveParameter.getValue2());
        } else {
            tSysParamVO.setParValues(saveParameter.getValue1());
        }
    }
}
