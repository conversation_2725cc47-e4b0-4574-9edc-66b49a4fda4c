package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.TMaterial;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.drug.TMaterialMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class TMaterialService {

    @Autowired
    private TMaterialMapper tMaterialMapper;

    /**
     * 加载分页数据
     *
     * @param tMaterial 药品表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(TMaterial tMaterial, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TMaterial> list = tMaterialMapper.getPageListByObj(tMaterial);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param matId 药品表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String matId) {

        if (StringUtils.isBlank(matId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TMaterial tMaterial = tMaterialMapper.getObjectById(matId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tMaterial);
    }


    /**
     * 插入新数据
     *
     * @param tMaterial 药品表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TMaterial tMaterial){

        tMaterial.setMatId(IDUtil.getID());
        long rows = tMaterialMapper.insert(tMaterial);

        return ResEntity.success(tMaterial);
    }


    /**
     * 修改
     *
     * @param tMaterial 药品表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TMaterial tMaterial) {

        long rows = tMaterialMapper.updateByPrimaryKey(tMaterial);

        return ResEntity.success(tMaterial);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tMaterialMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
