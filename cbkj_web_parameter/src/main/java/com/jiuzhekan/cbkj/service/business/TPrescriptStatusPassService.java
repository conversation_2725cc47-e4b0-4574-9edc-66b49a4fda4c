package com.jiuzhekan.cbkj.service.business;

import com.jiuzhekan.cbkj.beans.business.TPrescriptStatusPass;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.InterfaceRestTemplate;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.TPrescriptStatusPassMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class TPrescriptStatusPassService {

    @Autowired
    private TPrescriptStatusPassMapper tPrescriptStatusPassMapper;

    @Autowired
    private InterfaceRestTemplate interfaceRestTemplate;
    /**
     * 加载分页数据
     *
     * @param tPrescriptStatusPass 药房处方状态补传表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-11-29
     */
    public Object getPageDatas(TPrescriptStatusPass tPrescriptStatusPass, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPrescriptStatusPass> list = tPrescriptStatusPassMapper.getPageListByObj(tPrescriptStatusPass);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param passId 药房处方状态补传表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-11-29
     */
    public ResEntity findObj(String passId) {

        if (StringUtils.isBlank(passId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TPrescriptStatusPass tPrescriptStatusPass = tPrescriptStatusPassMapper.getObjectById(passId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPrescriptStatusPass);
    }
    /**
     * 修改
     *
     * @param tPrescriptStatusPass 药房处方状态补传表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-11-29
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TPrescriptStatusPass tPrescriptStatusPass) {

        long rows = tPrescriptStatusPassMapper.updateByPrimaryKey(tPrescriptStatusPass);

        return ResEntity.success(tPrescriptStatusPass);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-11-29
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tPrescriptStatusPassMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 获取状态正常的并且补传次数五次以内的数据 interfaceRestTemplate
     * @return
     */
    public void passStatusScheduler(){
        List<TPrescriptStatusPass> allPass = tPrescriptStatusPassMapper.getAllPass(new TPrescriptStatusPass());
        interfaceRestTemplate.passStatus(allPass);
    }

}
