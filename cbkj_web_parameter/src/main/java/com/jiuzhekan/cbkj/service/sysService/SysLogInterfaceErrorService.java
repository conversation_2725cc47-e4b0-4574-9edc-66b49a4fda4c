package com.jiuzhekan.cbkj.service.sysService;

import com.jiuzhekan.cbkj.beans.sysBeans.SysLogInterfaceError;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysmapper.SysLogInterfaceErrorMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SysLogInterfaceErrorService {

    @Autowired
    private SysLogInterfaceErrorMapper sysLogInterfaceErrorMapper;

    /**
     * 加载分页数据
     *
     * @param sysLogInterfaceError 接口错误日志
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2021-06-21
     */
    public Object getPageDatas(SysLogInterfaceError sysLogInterfaceError, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        if(StringUtils.isNotBlank(sysLogInterfaceError.getBeginTime())){
            sysLogInterfaceError.setBeginTime(sysLogInterfaceError.getBeginTime()+" 00:00:00");
        }
        if(StringUtils.isNotBlank(sysLogInterfaceError.getEndTime())){
            sysLogInterfaceError.setEndTime(sysLogInterfaceError.getEndTime()+" 23:59:59");
        }
        List<SysLogInterfaceError> list = sysLogInterfaceErrorMapper.getPageListByObj(sysLogInterfaceError);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 插入新数据
     *
     * @param sysLogInterfaceError 接口错误日志
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(SysLogInterfaceError sysLogInterfaceError){

        sysLogInterfaceError.setId(IDUtil.getID());
        long rows = sysLogInterfaceErrorMapper.insert(sysLogInterfaceError);
        if(rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, sysLogInterfaceError);
        }
        return ResEntity.entity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param sysLogInterfaceError 接口错误日志
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(SysLogInterfaceError sysLogInterfaceError) {

        long rows = sysLogInterfaceErrorMapper.updateByPrimaryKey(sysLogInterfaceError);
        if(rows >0){
            return ResEntity.entity(true, Constant.SUCCESS_DX, sysLogInterfaceError);
        }
        return ResEntity.entity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param id 接口错误日志
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    public ResEntity findObj(String id) {

        if(StringUtils.isBlank(id)){
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        SysLogInterfaceError sysLogInterfaceError = sysLogInterfaceErrorMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysLogInterfaceError);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if(StringUtils.isBlank(ids)){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = sysLogInterfaceErrorMapper.deleteBylist(ids.split(","));

        return ResEntity.entity(true, Constant.SUCCESS_DX, rowsR);
    }

}
