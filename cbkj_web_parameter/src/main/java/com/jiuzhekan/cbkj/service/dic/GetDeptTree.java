package com.jiuzhekan.cbkj.service.dic;

import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysapp.SysAppMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


@Service
/**
 * <AUTHOR>
 */
public class GetDeptTree {

    private SysDepartmentMapper sysDepartmentMapper;

    @Autowired
    GetDeptTree(SysDepartmentMapper sysDepartmentMapper) {
        this.sysDepartmentMapper = sysDepartmentMapper;
    }

    /**
     * @return
     */
    public List getDeptTree() {
        String appId = "000000";
        String insCode = "000000";
        return getDeptTree2(appId, insCode);
    }

    public List getDeptTree1() {
        String appId = "000000";
        String insCode = "000000";
        return getDeptTree3(appId, insCode);
    }

    private List getDeptTree2(String appId, String insCode) {

        SysDepartment department = new SysDepartment();
        department.setAppId(appId);
        department.setInsCode(insCode);
        List<SysDepartment> deptList = sysDepartmentMapper.getPageListByObjTree(department);

        LinkedHashMap<String, SysInstitution> insMap = new LinkedHashMap<>();

        for (SysDepartment sd : deptList) {
            SysInstitution ins = insMap.computeIfAbsent(sd.getInsCode(), k -> {
                SysInstitution i = new SysInstitution();
                i.setAppId(sd.getAppId());
                i.setAppName(sd.getAppName());
                i.setInsCode(sd.getInsCode());
                i.setInsName(sd.getInsName());
                i.setDeptList(new ArrayList<>());
                return i;
            });

            if (StringUtils.isNotBlank(sd.getDepOriginId())) {
                sd.setDeptId(sd.getDepOriginId());
            }
            ins.getDeptList().add(sd);
        }

        LinkedHashMap<String, SysApp> appMap = new LinkedHashMap<>();

        insMap.forEach((insKey, ins) -> {
            SysApp app = appMap.computeIfAbsent(ins.getAppId(), k -> {
                SysApp a = new SysApp();
                a.setAppId(ins.getAppId());
                a.setAppName(ins.getAppName());
                a.setInsList(new ArrayList<>());
                return a;
            });
            app.getInsList().add(ins);
        });

        List<SysApp> appList = new ArrayList<>();


        appMap.forEach((appKey, app) -> {
            if (!Constant.BASIC_APP_ID.equals(app.getAppId())) {
                appList.add(app);
            }
        });

        return appList;
    }

    private List getDeptTree3(String appId, String insCode) {

        SysDepartment department = new SysDepartment();
        department.setAppId(appId);
        department.setInsCode(insCode);
        List<SysDepartment> deptList = sysDepartmentMapper.getPageListByObjTree(department);

        LinkedHashMap<String, SysInstitution> insMap = new LinkedHashMap<>();

        for (SysDepartment sd : deptList) {
            SysInstitution ins = insMap.computeIfAbsent(sd.getInsCode(), k -> {
                SysInstitution i = new SysInstitution();
                i.setAppId(sd.getAppId());
                i.setAppName(sd.getAppName());
                i.setInsCode(sd.getInsCode());
                i.setInsName(sd.getInsName());
                i.setDeptList(new ArrayList<>());
                return i;
            });

            if (StringUtils.isNotBlank(sd.getDepOriginId())) {
                sd.setDeptId(sd.getDepOriginId());
            }
            ins.getDeptList().add(sd);
        }

        LinkedHashMap<String, SysApp> appMap = new LinkedHashMap<>();

        insMap.forEach((insKey, ins) -> {
            SysApp app = appMap.computeIfAbsent(ins.getAppId(), k -> {
                SysApp a = new SysApp();
                a.setAppId(ins.getAppId());
                a.setAppName(ins.getAppName());
                a.setInsList(new ArrayList<>());
                return a;
            });
            app.getInsList().add(ins);
        });

        List<SysApp> appList = new ArrayList<>();


        appMap.forEach((appKey, app) -> {
            if (!Constant.BASIC_APP_ID.equals(app.getAppId())) {
                appList.add(app);
            }
        });

        return appList;
    }
}
