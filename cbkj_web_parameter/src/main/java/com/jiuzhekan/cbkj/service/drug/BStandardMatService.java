package com.jiuzhekan.cbkj.service.drug;

import com.jiuzhekan.cbkj.beans.drug.BStandardMat;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.drug.BStandardMatMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class BStandardMatService {
    private final BStandardMatMapper bStandardMatMapper;

    @Autowired
    BStandardMatService(BStandardMatMapper bStandardMatMapper){
        this.bStandardMatMapper = bStandardMatMapper;
    }
    /**
     * 加载分页数据
     *
     * @param bStandardMat 药品标准编码表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-03-21
     */
    public Object getPageDatas(BStandardMat bStandardMat, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<BStandardMat> list = bStandardMatMapper.getPageListByObj(bStandardMat);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param sId 药品标准编码表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    public ResEntity findObj(String sId) {

        if (StringUtils.isBlank(sId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        BStandardMat bStandardMat = bStandardMatMapper.getObjectById(sId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, bStandardMat);
    }


    /**
     * 插入新数据
     *
     * @param bStandardMat 药品标准编码表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(BStandardMat bStandardMat){
        if (StringUtils.isBlank(bStandardMat.getsMatType())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(bStandardMat.getsMatClass())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(bStandardMat.getsMatCode())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        if (StringUtils.isBlank(bStandardMat.getsMatName())){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        bStandardMat.setsId(IDUtil.getID());
        long rows = bStandardMatMapper.insert(bStandardMat);

        return ResEntity.success(bStandardMat);
    }


    /**
     * 修改
     *
     * @param bStandardMat 药品标准编码表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(BStandardMat bStandardMat) {

        long rows = bStandardMatMapper.updateByPrimaryKey(bStandardMat);

        return ResEntity.success(bStandardMat);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-03-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = bStandardMatMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

}
