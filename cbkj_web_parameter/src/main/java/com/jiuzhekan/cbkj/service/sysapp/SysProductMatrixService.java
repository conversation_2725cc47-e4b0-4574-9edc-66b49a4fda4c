package com.jiuzhekan.cbkj.service.sysapp;

import cn.hutool.core.util.StrUtil;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix;
import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.StreamUtils;
import com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper;
import com.jiuzhekan.cbkj.mapper.sysapp.SysProductMatrixMapper;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class SysProductMatrixService {

    @Autowired
    private SysProductMatrixMapper sysProductMatrixMapper;

    @Autowired
    private DicBaseMapper dicBaseMapper;

    /**
     * @description  分页查询产品矩阵信息
     * <AUTHOR>
     * @date    2025/8/29 10:22
     * @param	sysProductMatrix
     * @param	page
     * @return  java.lang.Object
    */
    public Object getPageDatas(SysProductMatrix sysProductMatrix, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysProductMatrix> list = sysProductMatrixMapper.getPageListByObj(sysProductMatrix);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * @description  check
     * <AUTHOR>
     * @date    2025/8/29 10:21
     * @param	sysProductMatrix	
     * @param	dbType	
     * @return  void
    */
    public void checkParam(SysProductMatrix sysProductMatrix,String dbType){

        if (sysProductMatrix == null){
            ExceptionUtils.throwErrorCustomRuntimeException("sysProductMatrix不能为空");
        }
        if (StrUtil.isBlank(sysProductMatrix.getCategory())){
            ExceptionUtils.throwErrorCustomRuntimeException("系统分类不能为空");
        }
        if (sysProductMatrix.getOrderNum() == null){
            ExceptionUtils.throwErrorCustomRuntimeException("序号不能为空");
        }
        if (StrUtil.isBlank(sysProductMatrix.getStatus())){
            ExceptionUtils.throwErrorCustomRuntimeException("状态不能为空");
        }
        if (StrUtil.isBlank(sysProductMatrix.getName())){
            ExceptionUtils.throwErrorCustomRuntimeException("系统名称不能为空");
        }
        if (StrUtil.isBlank(sysProductMatrix.getType())){
            ExceptionUtils.throwErrorCustomRuntimeException("系统类型不能为空");
        }
        if (StrUtil.isBlank(sysProductMatrix.getLogoUrl())){
            ExceptionUtils.throwErrorCustomRuntimeException("系统logo不能为空");
        }

        if (dbType.equals(Constant.DB_UPDATE)){
            if (StrUtil.isBlank(sysProductMatrix.getProductMatrixId())){
                ExceptionUtils.throwErrorCustomRuntimeException("产品矩阵ID不能为空");
            }
        }
    }
    
    /**
     * @description  新增产品矩阵信息
     * <AUTHOR>
     * @date    2025/8/29 10:21
     * @param	sysProductMatrix	
     * @return  boolean
    */
    public synchronized boolean insert(SysProductMatrix sysProductMatrix) {
        this.checkParam(sysProductMatrix,Constant.DB_SAVE);
        sysProductMatrix.fullOptData(Constant.DB_SAVE);
        sysProductMatrix.setProductMatrixId(IDUtil.getID());
        sysProductMatrix.setIsDel(Constant.BASIC_DEL_NO);

        // 如果序号为空、0 那么自动填充序号
        if (sysProductMatrix.getOrderNum() == null || sysProductMatrix.getOrderNum() == 0){
            int maxOrderNum = sysProductMatrixMapper.getMaxOrderNum(sysProductMatrix.getCategory());
            sysProductMatrix.setOrderNum(maxOrderNum + 1);
        }

        long rows = sysProductMatrixMapper.insert(sysProductMatrix);
        return rows > 0;
    }
    
    /**
     * @description  修改产品矩阵信息
     * <AUTHOR>
     * @date    2025/8/29 10:21
     * @param	sysProductMatrix
     * @return  com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix
    */
    public SysProductMatrix update(SysProductMatrix sysProductMatrix) {
        this.checkParam(sysProductMatrix,Constant.DB_UPDATE);
        sysProductMatrix.fullOptData(Constant.DB_UPDATE);
        long rows = sysProductMatrixMapper.updateByPrimaryKey(sysProductMatrix);

        // 如果更新成功，把新数据返回
        if(rows > 0){
            return sysProductMatrixMapper.getObjectById(sysProductMatrix.getProductMatrixId());
        }
        return null;
    }
    
    /**
     * @description  删除产品矩阵信息
     * <AUTHOR>
     * @date    2025/8/29 10:22
     * @param	id
     * @return  boolean
    */
    public boolean delete(String id) {
        if (StrUtil.isBlank(id)){
            ExceptionUtils.throwErrorCustomRuntimeException("id不能为空");
        }
        SysProductMatrix sysProductMatrix = new SysProductMatrix();
        sysProductMatrix.setProductMatrixId(id);
        sysProductMatrix.fullOptData(Constant.DB_DEL);
        long rows = sysProductMatrixMapper.deleteByPrimaryKey(sysProductMatrix);
        return rows > 0;
    }

    /** 根据ID获取产品矩阵详情
     * @description  
     * <AUTHOR>
     * @date    2025/8/29 10:22
     * @param	id	
     * @return  com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix
    */
    public SysProductMatrix getById(String id) {
        if (StrUtil.isBlank(id)){
            ExceptionUtils.throwErrorCustomRuntimeException("id不能为空");
        }
        SysProductMatrix sysProductMatrix = sysProductMatrixMapper.getObjectById(id);
        return sysProductMatrix;
    }

    /**
     * @description 查询所有开启的产品矩阵列表
     * <AUTHOR>
     * @date    2025/8/25 9:46
     * @param
     * @return  java.util.List<com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix>
    */
    public List<SysProductMatrix> listAllEnable(){
        SysProductMatrix param = new SysProductMatrix();
        param.setStatus(Constant.BASIC_STRING_ONE);
        return sysProductMatrixMapper.getPageListByObj(param);
    }

    /**
     * @description 根据用户ID 查询出 其拥有的产品矩阵列表
     * <AUTHOR>
     * @date    2025/8/25 10:18
     * @param	userId
     * @return  java.util.List<com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix>
    */
    public List<SysProductMatrix> listByUserId(String userId){
        if (StrUtil.isBlank(userId)){
            ExceptionUtils.throwErrorCustomRuntimeException("userId不能为空");
        }
        List<SysProductMatrix> matrixList = sysProductMatrixMapper.listByUserId(userId);
        return matrixList;
    }

    /**
     * @description 获取据矩阵树
     * <AUTHOR>
     * @date    2025/8/27 16:51
     * @param
     * @return  java.util.List<com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix>
    */
    public List<SysProductMatrix> getTree() {

        // 查询出所有产品矩阵列表，查出来就排好序了
        List<SysProductMatrix> matrixList = this.listAllEnable();

        // 系统分类字典
        List<TDicBase> matrixCategoryList = dicBaseMapper.getCareer(Constant.DICT_PRODUCT_MATRIX_CATEGORY);
        Map<String, TDicBase> matrixCategoryMap = StreamUtils.toMap(matrixCategoryList,TDicBase::getDicCode);

        // 按分类分组
        Map<String, List<SysProductMatrix>> categoryMap = StreamUtils
                .toGroupLinkedMap(matrixList,SysProductMatrix::getCategory);

        // 构建树形结构
        List<SysProductMatrix> treeList = new ArrayList<>();
        for (Map.Entry<String, List<SysProductMatrix>> entry : categoryMap.entrySet()) {
            String category = entry.getKey();
            List<SysProductMatrix> childList = entry.getValue();

            // 创建分类节点
            SysProductMatrix categoryNode = new SysProductMatrix();
            categoryNode.setProductMatrixId(Constant.VIRTUAL+"_" + category); // 为分类节点生成唯一ID
            categoryNode.setCategory(category);
            categoryNode.setName(Optional.ofNullable(matrixCategoryMap.get(category)).map(TDicBase::getDicName).orElse(category));
            categoryNode.setChildList(childList);

            treeList.add(categoryNode);
        }

        return treeList;
    }
}
