package com.jiuzhekan.cbkj;

import com.jiuzhekan.cbkj.common.multipleDataSource.DynamicDataSourceRegister;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@EnableAsync(proxyTargetClass = true)
@EnableCaching(proxyTargetClass = true)
@SpringBootApplication
@MapperScan("com.jiuzhekan.cbkj.mapper")
@Import({DynamicDataSourceRegister.class})
//@ServletComponentScan
public class PreApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(PreApiApplication.class, args);
    }
}