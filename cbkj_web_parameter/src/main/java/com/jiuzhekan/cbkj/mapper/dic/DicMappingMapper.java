package com.jiuzhekan.cbkj.mapper.dic;

import com.jiuzhekan.cbkj.beans.dic.TDicMapping;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

@Component
/**
 * <AUTHOR>
 */
public interface DicMappingMapper extends BaseMapper<TDicMapping>{
    /**
     * 根据映射ID删除
     * @param id
     * @return
     */
    int deletePrimaryKey(String id);

    /**
     * 查询已映射数据
     * @param tDicMapping
     * @return
     */
    TDicMapping getInMapping(TDicMapping tDicMapping);
}