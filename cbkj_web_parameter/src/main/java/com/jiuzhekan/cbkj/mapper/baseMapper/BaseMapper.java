package com.jiuzhekan.cbkj.mapper.baseMapper;

import java.util.List;

public interface BaseMapper<T> {
    /**
     * 根据主键删除单个
     * @param t
     * @return
     */
    int deleteByPrimaryKey(T t);

    /**
     * 根据主键批量删除
     * @param split
     * @return
     */
    int deleteBylist(String[] split);

    /**
     * 单条数据插入
     * @param t
     * @return
     */
    int insert(T t);

    /**
     * 批量数据插入
     * @param list
     * @return
     */
    int insertList(List<T> list);

    /**
     * 根据主键修改某条数据
     * @param t
     * @return
     */
    int updateByPrimaryKey(T t);


    /**
     * 根据主键返回单个对象
     * @param id
     * @return T
     */
    T getObjectById(String id);

    /**
     * 返回分页数据 List<Object>
     * @param t
     * @return T
     */
    List<T> getPageListByObj(T t);

    /**
     * 返回分页数量
     * @param t t
     * @return int
     */
    long getCountByObj(T t);
}