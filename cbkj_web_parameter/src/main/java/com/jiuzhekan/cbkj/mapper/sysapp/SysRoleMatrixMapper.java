package com.jiuzhekan.cbkj.mapper.sysapp;

import com.jiuzhekan.cbkj.beans.sysExt.SysRoleMatrix;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface SysRoleMatrixMapper extends BaseMapper<SysRoleMatrix> {
    
    /**
     * 查询所有角色矩阵关联信息
     * @return
     */
    List<SysRoleMatrix> selectAll();
    
    /**
     * 根据角色ID查询关联的矩阵
     * @param roleId
     * @return
     */
    List<SysRoleMatrix> selectByRoleId(@Param("roleId") String roleId);
    
    /**
     * 根据矩阵ID查询关联的角色
     * @param matrixId
     * @return
     */
    List<SysRoleMatrix> selectByMatrixId(@Param("matrixId") String matrixId);

    List<String> listMatrixIdsByRoleIds(@Param("roleIdList") List<String> roleIdList);

    int deleteByRoleIds(@Param("roleIdList") List<String> roleIdList);
}
