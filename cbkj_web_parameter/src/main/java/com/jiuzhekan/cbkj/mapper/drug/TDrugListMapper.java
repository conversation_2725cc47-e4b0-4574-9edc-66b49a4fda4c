package com.jiuzhekan.cbkj.mapper.drug;

import com.jiuzhekan.cbkj.beans.drug.TDrugList;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TDrugListMapper extends BaseMapper<TDrugList> {
    List<TDrugList> getPageListByObj2(TDrugList tDrugList);

    List<TDrugList> getDrugPageList(TDrugList tDrugList);

    //获取药品目录最大Id
    String getMaxDrugId();

    //判断药品目录代码是否存在
    TDrugList getDrugByDrugCode(String drugCode);

    long deleteByDrugId(String drugId);
}