package com.jiuzhekan.cbkj.mapper.business.interfacehis;

import com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface TInterfaceHisMapper extends BaseMapper<TInterfaceHis>{


    List<TInterfaceHis> getPageList(TInterfaceHis tInterfaceHis);

    List<TInterfaceHis> getAll();
}