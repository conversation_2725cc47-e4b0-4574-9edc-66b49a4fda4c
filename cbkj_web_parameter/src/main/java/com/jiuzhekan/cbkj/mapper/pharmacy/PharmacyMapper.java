package com.jiuzhekan.cbkj.mapper.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TPharmacy;
import org.apache.maven.lifecycle.internal.LifecycleStarter;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
/**
 * <AUTHOR>
 */
public interface PharmacyMapper extends BaseMapper<TPharmacy> {
    /**
     * 获取上级药房
     *
     * @param phaId
     * @return
     */
    List<TPharmacy> getSuperiorPharmacy(String phaId);

    /**
     * 获取所有药房
     *
     * @return
     */
    List<TPharmacy> getSuperiorPharmacyInsert();

    /**
     * 获取药房最大序号
     *
     * @param drugId
     * @return
     */
    Integer getMaxNum(String drugId);

    List<Integer> getSortNums(String drugId);

    /**
     * 根据药房ID查询药房信息
     *
     * @param phaId
     * @return
     */
    TPharmacy getTPharmacy(String phaId);

    /**
     * 找到sort最大值
     * @return
     */
    Integer getTPharmacyLastSort();

    /**
     * 根据Id删除药房
     *
     * @param phaId
     * @return
     */
    long deleteByPhaId(String phaId);


}