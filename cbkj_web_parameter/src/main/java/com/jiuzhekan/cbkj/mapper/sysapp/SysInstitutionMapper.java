package com.jiuzhekan.cbkj.mapper.sysapp;

import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface SysInstitutionMapper extends BaseMapper<SysInstitution> {
    List<SysInstitution> getSysInstitutionByOryCateGory(SysInstitution sysInstitution);

    Integer getInsCountByIns(SysInstitution sysInstitution);

    List<Map<String, Object>> getMapByAppId(Map<String, Object> appId);

//    List<SysInstitution> getInsListByYS(Map<String, Object> map);

    SysInstitution getInsByIns(SysInstitution sysInstitution);

    List<SysInstitution> getInsListByIns(SysInstitution sysInstitution);

    /**
     *根据appId查询机构集合
     * @param sysInstitution
     * @return
     * <AUTHOR>
     */
    List<SysInstitution> getInsByAppId(SysInstitution sysInstitution);

    /**
     * 根据appId查询该医联体的所有顶层机构
     * @param sysInstitution
     * @return
     */
    List<SysInstitution> getRootInsByAppId(SysInstitution sysInstitution);

    int updateByIns(SysInstitution sysInstitution);



    Map<String, String> getInsAddressByIns(SysInstitution sysInstitution);

    /**
     * 根据 pcode 查询子集机构
     * @param insPcode
     * @return
     */
    List<String> getSubIns(String insPcode);

//    List<Map<String, Object>> getYPMLCheckIns(Map<String, Object> map);
    SysInstitution getSysInstitutionByInsCode(String code);

    SysInstitution getSysInstitutionByInsCodeAndAppId(String insCode,String appId);

    List<SysInstitution> getInstitutionByAppId(String appId);

    SysInstitution getObjectById2(String id);

    List<SysInstitution> getAllInstitution();
}