package com.jiuzhekan.cbkj.mapper.business;

import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemMapping;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TChargeItemMappingMapper extends BaseMapper<TChargeItemMapping>{


    @Delete("delete from t_charge_item_mapping where charge_item_id = #{chargeItemId}")
    void deleteByChargeItemId(@Param("chargeItemId") String chargeItemId);

    @Select("select charge_item_id as chargeItemId,app_id as appId,ins_code as insCode,dept_id as deptId from t_charge_item_mapping where charge_item_id = #{chargeItemId}")
    List<TChargeItemMapping> getChargeItemMappingList(@Param("chargeItemId") String chargeItemId);
}