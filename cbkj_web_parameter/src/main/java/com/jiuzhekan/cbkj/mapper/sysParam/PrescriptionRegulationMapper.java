package com.jiuzhekan.cbkj.mapper.sysParam;

import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulation;
import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulationItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2024/9/11 10:45
 */
@Component
public interface PrescriptionRegulationMapper {

    List<Map<String,Object>> getCondition(@Param("name") String name);

    void prescriptionRegulationInsertOrUpdate( List<PrescriptionRegulation> list);

    void prescriptionRegulationItemInsertOrUpdate(List<PrescriptionRegulationItem> list);

    List<PrescriptionRegulation> getPrescriptionRegulation();

    List<Map<String, Object>> getEvent();

    void prescriptionRegulationDel();
}
