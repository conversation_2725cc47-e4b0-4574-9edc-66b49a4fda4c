package com.jiuzhekan.cbkj.mapper.pharmacy;


import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplay;
import com.jiuzhekan.cbkj.beans.pharmacy.TPharmacy;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface DisplayMapper extends BaseMapper<TDisplay> {
    /**
     * 根据药房名删除
     * @param phaId phaId
     * @return long
     */
    long deleteByPhaId(String phaId);

    /**
     * 逻辑删除
     * @param phaId
     * @return
     */
    long deleteByPhaId1(String phaId);

    List<TDisplay> getMatType(String phaId);

    /**
     * 获取物理要放下的剂型
     * @param tPharmacy
     * @return
     */
    List<TPharmacy> getAllPharmacyDisplayList();

    TDisplay getTDisplayByPhaIdAndMatType(String phaId,String matType);

    List<TDisplay> getPharmacyItemList();

    /**
     * 逻辑删除
     * @param dic_id
     * @return
     */
    long deleteByPrimaryKeyL(String dic_id);
}