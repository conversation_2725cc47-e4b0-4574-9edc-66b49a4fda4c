package com.jiuzhekan.cbkj.mapper.sysmapper;

import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface LogentityMapper extends BaseMapper<Logentity> {

    /**
     * 返回分页数据 List<Map>
     * @param t
     * @return
     */
    List<Map<String,Object>> getPageDatas(Logentity t);

    /**
     * 改变状态
     * @param params
     * @return
     */
    long changeStatus(Map<String, Object> params);

    /**
     * 获取id
     * @param name
     * @return
     */
    String getSysIdByName(String name);

    /**
     * 获取名字
     * @param id
     * @return
     */
    String getSysNameById(String id);

}