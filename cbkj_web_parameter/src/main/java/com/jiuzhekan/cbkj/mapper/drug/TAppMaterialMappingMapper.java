package com.jiuzhekan.cbkj.mapper.drug;

import com.jiuzhekan.cbkj.beans.drug.StandTMAutoMappingVO;
import com.jiuzhekan.cbkj.beans.drug.TAppMaterial;
import com.jiuzhekan.cbkj.beans.drug.TAppMaterialMapping;
import com.jiuzhekan.cbkj.beans.drug.TMaterialKnowVO;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TAppMaterialMappingMapper extends BaseMapper<TAppMaterialMapping>{
    /**
     * 分页查询本地药房和知识库映射参照
     * @param tMaterialKnowVO
     * @return List
     */
    List<TMaterialKnowVO> getDataPage(TMaterialKnowVO tMaterialKnowVO);

    /**
     * 自动映射
     * @param standTMAutoMappingVO
     * @return int
     */
    int insertautoMapping(StandTMAutoMappingVO standTMAutoMappingVO);

    /**
     * 计算未映射数量
     * @param standTMAutoMappingVO
     * @return
     */
    int selectautoMappingCount(StandTMAutoMappingVO standTMAutoMappingVO);

    /**
     * 计算映射表数量
     * @param tMaterialKnowVO
     * @return
     */
    long selectCountData(TMaterialKnowVO tMaterialKnowVO);



    List<TAppMaterial> getObjectByDrugId(StandTMAutoMappingVO standTmAutoMappingVO);
}