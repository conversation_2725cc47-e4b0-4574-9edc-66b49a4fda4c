package com.jiuzhekan.cbkj.mapper.sysapp;

import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface SysAppMapper extends BaseMapper<SysApp> {
    List<SysApp> getApplist(SysApp sysApp);

    String getMaxAppId();

    List<SysApp> getPageAssocation(SysApp sysApp);

    Integer deleteByPrimaryKey2(SysApp sysApp);

    List<SysApp> getAllAppList();
}