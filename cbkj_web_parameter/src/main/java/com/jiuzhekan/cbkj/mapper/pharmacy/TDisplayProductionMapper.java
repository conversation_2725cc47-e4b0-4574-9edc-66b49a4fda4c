package com.jiuzhekan.cbkj.mapper.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

@Component
public interface TDisplayProductionMapper extends BaseMapper<TDisplayProduction>{
    /**
     * 通过displayId和outpatientOrHospitalization获取一条
     * @param displayProduction
     * @return
     */
    TDisplayProduction getByDisplayId(TDisplayProduction displayProduction);
    int deleteByCondition(TDisplayProduction displayProduction);
}