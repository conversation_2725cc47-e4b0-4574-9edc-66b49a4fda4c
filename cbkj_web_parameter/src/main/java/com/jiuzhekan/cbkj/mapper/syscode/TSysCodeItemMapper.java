//package com.jiuzhekan.cbkj.mapper.syscode;
//
//import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCode;
//import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCodeItem;
//import com.jiuzhekan.cbkj.beans.business.sysCode.VO.TSysCodeItemSelVO;
//import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//
//@Component
//public interface TSysCodeItemMapper extends BaseMapper<TSysCodeItem> {
//
//    List<TSysCodeItem> getTSysCodeItemListByTSysCodeItem();
//
//    List<TSysCodeItem> getTSysCodeItemByName(TSysCodeItem obj);
//
//    long delByCodeId(TSysCode tSysCode);
//
//    List<TSysCodeItemSelVO> getItemSelList(TSysCodeItem tSysCodeItem);
//
//    int updateSiblingsNotDefault(TSysCodeItem tSysCodeItem);
//
//    List<TSysCodeItem> getSiblings(Short codeId);
//
//    /**
//     * 根据大类ID获取系统代码明细，返回itemId,itemName
//     *
//     * @param item
//     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
//     * <AUTHOR>
//     * @date 2020/3/2
//     */
//    List<Map<String, Object>> getSysCodeItemList(TSysCodeItem item);
//}