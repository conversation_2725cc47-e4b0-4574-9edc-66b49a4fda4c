package com.jiuzhekan.cbkj.mapper.business;

import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItem;
import com.jiuzhekan.cbkj.beans.business.chargeitem.ChargeItemIdNameDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TChargeItemMapper extends BaseMapper<TChargeItem>{

    /**
     * 获取收费项目列表（支持按名称模糊查询）
     *
     * @param chargeItemName 收费项目名称
     * @return List<TChargeItem>
     */
    List<TChargeItem> getChargeItemList(@Param("chargeItemName") String chargeItemName);

    /**
     * 获取所有收费项目的ID和名称
     *
     * @return List<ChargeItemIdNameDTO>
     */
    List<ChargeItemIdNameDTO> getChargeItemIdAndName();

    /**
     * 检查收费项目代码是否存在
     *
     * @param chargeItemCode 收费项目代码
     * @return int
     */
    int checkChargeItemCodeExists(@Param("chargeItemCode") String chargeItemCode);

    /**
     * 检查收费项目代码是否存在（排除自己）
     *
     * @param chargeItemCode 收费项目代码
     * @param chargeItemId 收费项目ID
     * @return int
     */
    int checkChargeItemCodeExistsExcludeSelf(@Param("chargeItemCode") String chargeItemCode, @Param("chargeItemId") String chargeItemId);

    /**
     * 逻辑删除收费项目
     *
     * @param chargeItemId 收费项目ID
     * @return int
     */
    int deleteChargeItemLogically(@Param("chargeItemId")String  chargeItemId);

}