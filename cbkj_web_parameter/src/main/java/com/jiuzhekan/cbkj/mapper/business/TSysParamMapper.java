package com.jiuzhekan.cbkj.mapper.business;

import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TSysParamMapper extends BaseMapper<TSysParam> {

    /**
     * 根据主键返回单条数据 Map
     *
     * @param tSysParam
     * @return
     */
    Map<String, Object> getMapById(TSysParam tSysParam);

    /**
     * 根据主键返回单个对象
     *
     * @param tSysParam
     * @return
     */
    TSysParam getObjectById(TSysParam tSysParam);

    /**
     * 根据appId,insCode和参数代码查询对象
     *
     * @param tSysParam
     * @return
     */
    TSysParam getObjectByAppIdInsCodeAndCode(TSysParam tSysParam);

    /**
     * 获取所有参数代码
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.TSysParam>
     * <AUTHOR>
     * @date 2020/12/8
     */
    List<TSysParam> getAllParamCode();

    /**
     * 查询某个字段的某条数据数量
     */
    int getObjExists(String parCode);

    /**
     * 根据parCode查询对象
     *
     * @param tSysParam
     * @return
     */
    List<TSysParam> getListByCode(TSysParam tSysParam);

    /**
     * 条件删除
     * @param tSysParam
     * @return
     */
    int deleteByCondition(TSysParam tSysParam);

    List<Map<String, Object>> getAllParamClassifyList();

    /**
     * 查询参数编号是否重复
     * @param tSysParam
     * @return
     */
    int getParamNumberRepeat(TSysParam tSysParam);
}