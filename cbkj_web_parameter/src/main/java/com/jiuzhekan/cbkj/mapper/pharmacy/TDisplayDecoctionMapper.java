package com.jiuzhekan.cbkj.mapper.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

@Component
public interface TDisplayDecoctionMapper extends BaseMapper<TDisplayDecoction>{

    TDisplayDecoction  getTDisplayDecoctionByDisplayId(TDisplayDecoction tDisplayDecoction);
    int  deleteByCondition(TDisplayDecoction tDisplayDecoction);
}