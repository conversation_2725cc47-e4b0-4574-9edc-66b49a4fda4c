package com.jiuzhekan.cbkj.mapper.business;

import com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemDetailListByChargeItemId;
import com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemList;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TChargeItemDetailMapper extends BaseMapper<TChargeItemDetail>{



    /**1
     * 检查收费编码是否存在
     *
     * @param itemDetailCode 收费编码
     * @return int
     */
    int checkDetailCodeExists(@Param("itemDetailCode") String itemDetailCode);

    /**
     * 检查收费编码是否存在（排除自己）
     *
     * @param itemDetailCode 收费编码
     * @param chargeItemDetailId 收费项目详情ID
     * @return int
     */
    int checkDetailCodeExistsExcludeSelf(@Param("itemDetailCode") String itemDetailCode, @Param("chargeItemDetailId") String chargeItemDetailId);

    /**
     * 根据查询条件获取收费项目详情列表
     *
     * @param getChargeItemList 查询条件
     * @return List<TChargeItemDetail>
     */
    List<TChargeItemDetail> getDetailsByGetChargeItemList(GetChargeItemList getChargeItemList);
    Integer countDetailsByGetChargeItemList(GetChargeItemList getChargeItemList);

    /**
     * 根据收费目录id或者关键字获取目录下的所有收费项目列表
     * @param chargeItemId
     * @return
     */
    List<TChargeItemDetail> getChargeItemDetailListByChargeItemId( GetChargeItemDetailListByChargeItemId chargeItemId);
}