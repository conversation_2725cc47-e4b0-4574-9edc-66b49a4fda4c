package com.jiuzhekan.cbkj.mapper.drug;

import com.jiuzhekan.cbkj.beans.drug.StandTMAutoMappingVO;
import com.jiuzhekan.cbkj.beans.drug.TMaterialStandVO;
import com.jiuzhekan.cbkj.beans.drug.TStandardMaterialMapping;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TStandardMaterialMappingMapper extends BaseMapper<TStandardMaterialMapping>{
    /**
     * 分页查询本地药房和标准编码映射参照
     * @param tMaterialStandVO
     * @return
     */
    List<TMaterialStandVO> getPageListByObj(TMaterialStandVO tMaterialStandVO);

    /**
     * 获取数量
     * @param tMaterialStandVO
     * @return
     */
    long getCountByObj(TMaterialStandVO tMaterialStandVO);

    /**
     * 自动映射
     * @param standTMAutoMappingVO
     * @return
     */
    int insertautoMapping(StandTMAutoMappingVO standTMAutoMappingVO);

    /**
     * 自动映射数量
     * @param standTMAutoMappingVO
     * @return
     */
    long insertautoMappingCount(StandTMAutoMappingVO standTMAutoMappingVO);


}