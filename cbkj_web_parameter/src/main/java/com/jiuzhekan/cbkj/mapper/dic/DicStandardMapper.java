package com.jiuzhekan.cbkj.mapper.dic;

import com.jiuzhekan.cbkj.beans.dic.TDicStandard;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
/**
 * <AUTHOR>
 */
public interface DicStandardMapper extends BaseMapper<TDicStandard> {
    /**
     * 获取父级名称
     * @return
     */
    List<TDicStandard> getFname();

    /**
     * 分页查询标准字典
     * @param tDicStandard
     * @return
     */
    List<TDicStandard> getTDicStandard(TDicStandard tDicStandard);

    /**
     * 根据本地字典ID获取标准字典父级编码
     * @param dicId
     * @return
     */
    TDicStandard getFCode(String dicId);

    /**
     * 查询最大排序号
     * @param stanId
     * @return
     */
    String  getMaxSort(String stanId);

    /**
     * 根据标准编码查询
     * @param stanCode
     * @return
     */
    TDicStandard findOneTDicStandard(String stanCode,String stanType);
}