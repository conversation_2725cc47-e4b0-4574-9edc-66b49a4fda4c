package com.jiuzhekan.cbkj.mapper.sysParam;

import com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TSysParamInitDescMapper extends BaseMapper<TSysParamInitDesc>{
    /**
     * 获取单个
     * @param tSysParamInitDesc
     * @return
     */
    public TSysParamInitDesc getInitByCondition(TSysParamInitDesc tSysParamInitDesc);
    public long getCountByO(TSysParamInitDesc tSysParamInitDesc);
    List<TSysParamInitDesc> selectTSysParamInitDesc(String parId);

}