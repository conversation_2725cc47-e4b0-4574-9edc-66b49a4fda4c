package com.jiuzhekan.cbkj.mapper.drug;

import com.jiuzhekan.cbkj.beans.drug.TMaterialKnowMapping;
import com.jiuzhekan.cbkj.beans.drug.TMaterialPrice;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TMaterialPriceMapper extends BaseMapper<TMaterialPrice>{


    TMaterialPrice getMaterialPrice(TMaterialKnowMapping tMaterialKnowMapping);

    void updateList(List<TMaterialPrice> list);
}