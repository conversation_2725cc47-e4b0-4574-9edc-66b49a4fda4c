package com.jiuzhekan.cbkj.mapper.sysmapper;

import com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;
import java.util.Map;

@Component
public interface SysDoctorMultipointMapper extends BaseMapper<SysDoctorMultipoint>{

    List<SysDoctorMultipoint> getPracticeList(SysDoctorMultipoint practice);
    List<SysDoctorMultipoint> checkUserEmployeeId(SysDoctorMultipoint practice);
    /**
     * 根据用户删除执业机构
     */
    void deleteByAdminId(String userId);

    void deleteByAdminIds(Map<String, Object> split);
}