package com.jiuzhekan.cbkj.mapper.sysParam;

import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamDTO;
import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.SysParamSearch;
import com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TSysParamNewMapper extends BaseMapper<TSysParamNew> {
    /**
     * 根据菜单ID和医疗机构信息查询参数集合
     *
     * @param tSysParamNew
     * @return
     */
    List<TSysParamNew> getDiagnosisParameterList(DiagnosisParamDTO tSysParamNew);

    /**
     * 保存参数
     *
     * @param tSysParamNew
     * @return
     */
    int keepParam(TSysParamNew tSysParamNew);

    /**
     * 根据参数Id查询参数
     *
     * @param parId
     * @return
     */
    TSysParamNew selectParam(String parId);

    /**
     * 根据医疗机构信息查询参数
     *
     * @param tSysParamNew
     * @return
     */
    TSysParamNew selectParamByCondition(TSysParamNew tSysParamNew);

    /**
     * 根据关键字搜索参数。
     *
     * @param sysParamSearch
     * @return
     */
    List<TSysParamNew> searchParam(SysParamSearch sysParamSearch);

    /**
     * 根据参数代码获取配置过的参数
     *
     * @param parCode
     * @return
     */
    List<TSysParamNew> getParamsByCode(String parCode);

    List<TSysParamNew> getDiagnosisParameterListAll(DiagnosisParamDTO diagnosisParamDTO);

    int deleteParamByCode(String parCode);

    TSysParamNew getSystemParamByCode(String parCode);
}
