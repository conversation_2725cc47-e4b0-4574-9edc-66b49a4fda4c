package com.jiuzhekan.cbkj.mapper.sysmapper;

import com.jiuzhekan.cbkj.beans.sysBeans.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface AdminInfoMapper {

    /**
     * 根据主键id删除
     *
     * @param id id
     * @return int
     */
    int deleteByPrimaryKey(String id);

    /**
     * 插入
     *
     * @param record 实体
     * @return int
     */
    int insert(AdminInfo record);

    /**
     * 插入
     *
     * @param record 参数
     * @return int
     */
    @Deprecated
    int insertSelective(AdminInfo record);

    /**
     * 通过id获取用户角色信息
     *
     * @param id id
     * @return AdminInfo
     */
    AdminInfo selectAdminRoleById(String id);

    /**
     * 获取列表根据id
     *
     * @param id id
     * @return AdminInfo
     */
    AdminInfo selectByPrimaryKey(String id);

    /**
     * 更新
     *
     * @param record 参数
     * @return int
     */
    @Deprecated
    int updateByPrimaryKeySelective(AdminInfo record);

    /**
     * 更新
     *
     * @param record 参数
     * @return int
     */
    int updateByPrimaryKey(AdminInfo record);

    /**
     * 加载用户信息
     *
     * @param username 用户名
     * @return AdminInfo
     */
    AdminInfo loadUserByUsername(String username);

    /**
     * 分页
     *
     * @param admin admin
     * @return List
     */
    List<AppsInfo> getPageDatas(AdminInfo admin);

    List<Grayscale> getGrayscaleStatus(AdminInfo admin);

    /**
     * 获取角色
     *
     * @param adminRule 参数
     * @return List
     */
    List<Map<String, Object>> getRoles(AdminRule adminRule);

    /**
     * 获取角色
     *
     * @param adminRule 参数
     * @return AdminRule
     */
    AdminRule getRoleByObj(AdminRule adminRule);

    /**
     * 插入
     *
     * @param adminInfoRule 参数
     * @return int
     */
    int insertAdminRule(AdminInfoRule adminInfoRule);

    /**
     * 更新密码
     *
     * @param params 参数
     * @return long
     */
    long updatePwd(Map<String, Object> params);

    /**
     * 更新状态
     *
     * @param params 参数
     * @return Long
     */
    long updateStatus(Map<String, Object> params);

    /**
     * @param split 参数
     * @return long
     */
    long deleteAdminRole(String[] split);

    /**
     * @param split 参数
     * @return long
     */
    long deleteBylis(Map<String, Object> split);

    /**
     * @param id 参数
     * @return long
     */
    List<Map> getAdmins(String id);

    /**
     * @param adminRuleList 参数
     * @return long
     */
    long insertList(List<AdminInfoRule> adminRuleList);

    /**
     * @param id
     * @return long
     */
    long deleteAdminInfoRuleByAdminId(String id);

    /**
     * @param params 参数
     * @return int
     */
    int getObjExists(Map<String, Object> params);

    /**
     * @param param 参数
     * @return List
     */
    List<Map<String, Object>> validateParam(Map<String, Object> param);

    /**
     * @param adminInfo 参数
     * @return List
     */
    List<Map<String, Object>> getLikeAdmins(AdminInfo adminInfo);

    /**
     * @param adminInfo 参数
     * @return AdminInfo
     */
    AdminInfo selectByPrimaryOriginID(AdminInfo adminInfo);

    /**
     * @param id
     * @return AdminInfo
     */
    AdminInfo getPicPathById(String id);

    /**
     * @param id 参数
     * @return void
     */
    void UpdateAdminInfo(String insCode, String id);

    /**
     * @param id 参数
     * @return void
     */
    void UpdateAdminInfoDep(String depId, String id);

    /**
     * @param depId 参数
     * @return void
     */
    void UpdateDeptName(String depName, String depId);

    /**
     * @param list 参数
     * @return int
     */
    int insertAdminInfoList(List<AdminInfo> list);

    /**
     * @param adminInfo 参数
     * @return List
     */
    List<AdminInfo> getListByAppInsDept(AdminInfo adminInfo);

    /**
     * @param appId 参数
     * @return List
     */
    List<AdminInfo> getAdminInfoByAppId(String appId);

    AdminInfo getUserNameById(String createUserId);

    AdminInfo getGrayscaleUserById(String createUserId);
}