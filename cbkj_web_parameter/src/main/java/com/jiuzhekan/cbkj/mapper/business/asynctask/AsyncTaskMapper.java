package com.jiuzhekan.cbkj.mapper.business.asynctask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTask;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskIsC;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface AsyncTaskMapper extends BaseMapper<AsyncTask> {
    /**
     * 获取任务列表
     * @param asyncTask 参数
     * @return List
     */
    List<AsyncTask> selectByCondition(AsyncTaskIsC asyncTask);

    /**
     * 获取列表分页
     * @param asyncTask 参数
     * @return List
     */
    List<AsyncTask> getPageListByObj2(AsyncTask asyncTask);

    /**
     * 更新状态
     * @param params 参数
     * @return int
     */
    int updateStatusByIds(Map<String, Object> params);

}