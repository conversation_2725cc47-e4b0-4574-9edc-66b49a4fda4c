package com.jiuzhekan.cbkj.mapper.syscode;

import com.jiuzhekan.cbkj.beans.business.sysCode.THisCodeItem;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface THisCodeItemMapper extends BaseMapper<THisCodeItem> {

    List<THisCodeItem> getTHisCodeItemListByTHisCodeItem(THisCodeItem hisCodeItem);

    List<THisCodeItem> getHisCodeListByCode(THisCodeItem hisCodeItem);

    int delAllByInsCode(String[] array);

    List<Map<String, Object>> getCodeList(AdminInfo currentHr);

    int updateSiblingsNotDefault(THisCodeItem hisCodeItem);

    /**
     * 根据大类ID获取HIS代码明细，返回itemId,itemName
     *
     * @param item
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.sysCode.THisCodeItem>
     * <AUTHOR>
     * @date 2020/3/2
     */
    List<Map<String, Object>> getHisCodeItemList(THisCodeItem item);
}