package com.jiuzhekan.cbkj.mapper.sysmapper;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface AdminMenuMapper {
    /**
     * 删除
     *
     * @param mid
     * @return int
     * <AUTHOR>
     * @date 2022/4/26 13:54
     **/
    int deleteByPrimaryKey(Integer mid);

    /**
     * 插入
     *
     * @param record
     * @return int
     * <AUTHOR>
     * @date 2022/4/26 13:55
     **/
    int insert(AdminMenu record);

    /**
     * 查询单个
     *
     * @param mid
     * @return com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu
     * <AUTHOR>
     * @date 2022/4/26 13:55
     **/
    AdminMenu selectByPrimaryKey(String mid);

    /**
     * 更新
     *
     * @param record
     * @return int
     * <AUTHOR>
     * @date 2022/4/26 13:55
     **/
    int updateByPrimaryKey(AdminMenu record);

    /**
     * 获取所有菜单
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 13:56
     **/
    List<AdminMenu> getAllMenu();

    /**
     * 获取菜单 根据父菜单id
     *
     * @param [params]
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 13:56
     **/
    List<AdminMenu> getMenuByPID(Map<String, Object> params);

    /**
     * 获取菜单
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/4/26 13:59
     **/
    List<Map<String, Object>> selectAllMenu();

    /**
     * 通过角色id获取菜单
     *
     * @param id
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/4/26 13:59
     **/
    List<Map<String, Object>> getMenuByRid(String id);

    /**
     * 删除角色菜单映射通过角色id
     *
     * @param rid
     * @return long
     * <AUTHOR>
     * @date 2022/4/26 13:59
     **/
    long deleteRmByRid(String rid);

    /**
     * 插入角色菜单
     *
     * @param resultList
     * @return
     */
    long insertListM(List<Map<String, Object>> resultList);

    /**
     * 获取菜单信息
     *
     * @param adminMenu
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> getPageDatas(AdminMenu adminMenu);

    /**
     * 获取菜单
     *
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> selectAllMenuByM();

    /**
     * 更新状态
     *
     * @param params
     * @return long
     * <AUTHOR>
     * @date 2022/4/26 14:01
     **/
    long updateEnabled(Map<String, Object> params);

    /**
     * 删除角色映射
     *
     * @param split
     * @return
     */
    long deleteRMbyMid(String[] split);

    /**
     * 删除
     *
     * @param split
     * @return
     */
    long deleteBylis(String[] split);

    /**
     * 获取按钮菜单通过path
     *
     * @param params
     * @return
     */
    List<Map<String, Object>> getBtnMenuLisByPath(Map<String, Object> params);

    /**
     * 通过菜单id获取菜单对象
     *
     * @param id
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 14:03
     **/
    List<AdminMenu> getMenuObjByMID(String id);

    /**
     * 获取菜单通过角色id
     *
     * @param id
     * @return java.util.List<com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu>
     * <AUTHOR>
     * @date 2022/4/26 14:04
     **/
    List<AdminMenu> getMenuObjByRID(String id);

    /**
     * 获取所有菜单信息
     *
     * @return
     */
    List<AdminMenu> getAllMenuList();

    /**
     * 更新菜单sort
     *
     * @param list
     * @return
     */
    int updateSortNumberByList(List<AdminMenu> list);

    /**
     * 获取不同平台菜单
     *
     * @param modelCode
     * @return
     */
    List<AdminMenu> getAllMenuByModelCode(String modelCode);

    AdminMenu getMenuByParentId(String parentMenuId);
}