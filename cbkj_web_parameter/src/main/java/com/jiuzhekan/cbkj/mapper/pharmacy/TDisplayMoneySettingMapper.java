package com.jiuzhekan.cbkj.mapper.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TDisplayMoneySettingMapper extends BaseMapper<TDisplayMoneySetting> {

    /**
     * 根据父级的dicCode 获取费用信息
     *
     * @param tDisplayMoneySetting
     * @return
     */
    List<TDisplayMoneySetting> getByDicCode(TDisplayMoneySetting tDisplayMoneySetting);

    /**
     * 根据setId删除费用配置信息
     *
     * @param tDisplayMoneySetting
     * @return
     */
    int deleteBySetId(String setId);


    /**
     * 查询默认情况的费用配置
     *
     * @param tDisplayMoneySetting
     * @return
     */
    List<TDisplayMoneySetting> getDefult(TDisplayMoneySetting tDisplayMoneySetting);

    Integer getMoneySettingByDicId(String dicId);

}