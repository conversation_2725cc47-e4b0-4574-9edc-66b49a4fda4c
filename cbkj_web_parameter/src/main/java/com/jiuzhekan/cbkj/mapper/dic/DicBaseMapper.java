package com.jiuzhekan.cbkj.mapper.dic;

import com.jiuzhekan.cbkj.beans.dic.DicNode;
import com.jiuzhekan.cbkj.beans.dic.DicNodeDto;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
/**
 * <AUTHOR>
 */
public interface DicBaseMapper extends BaseMapper<TDicBase> {

    /**
     * 获取没有映射的
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getPageListNotInMapping(TDicBase tDicBase);

    /**
     * 获取已经映射的对象
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getPageListInMapping(TDicBase tDicBase);

    /**
     * 获取父级名称
     *
     * @return
     */
    List<TDicBase> getFname();

    /**
     * 根据字典名称查询
     *
     * @param dicName
     * @return
     */
    List<TDicBase> getDicByName(String dicName);

    /**
     * 根据字典编码查询
     *
     * @param dicCode
     * @return
     */
    List<TDicBase> getCareer(String dicCode);

    /**
     * 查询大类名称
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getParentName(TDicBase tDicBase);

    /**
     * 查询系统明细代码
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getSystemCodeItem(TDicBase tDicBase);

    /**
     * 查询HIS明细
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getHisItem(TDicBase tDicBase);

    /**
     * 查询药房明细
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getPharmacyItem(TDicBase tDicBase);


    /**
     * 查询药房大类是否配置
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getDisplayItem(TDicBase tDicBase);

    /**
     * 明细代码查询系统字典
     *
     * @param tDicBase
     * @return
     */
    List<TDicBase> getDisplayByDicCodeAndParentId(TDicBase tDicBase);

    TDicBase getItemByParentId(String dicId);

    List<TDicBase> getFeeDictionaryParent(String parentId);

    DicNode getDetailByCode(String dicCode);

    List<DicNodeDto> getChildren(DicNode displayParam);
}