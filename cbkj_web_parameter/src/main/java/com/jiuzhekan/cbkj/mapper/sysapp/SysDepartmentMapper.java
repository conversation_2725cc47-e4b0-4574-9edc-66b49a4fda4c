package com.jiuzhekan.cbkj.mapper.sysapp;

import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface SysDepartmentMapper extends BaseMapper<SysDepartment> {

    Integer getDepCountByName(SysDepartment sysDepartment);

    Integer getDepCountByParent(SysDepartment sysDepartment);

    List<Map<String, Object>> queryByAppIdInsCode(SysDepartment sysDepartment);

    List<SysDepartment> getPageListByObjTree(SysDepartment sysDepartment);

    List<SysDepartment> getPageListByObj2(SysDepartment sysDepartment);

    List<SysDepartment> findPageListByObj(SysDepartment sysDepartment);

    Integer getDepCountByDepOriginId(SysDepartment sysDepartment);

    SysDepartment  getObjectById2(String deptId);

    List<SysDepartment> getDepartmentByAppId(String appId);


    SysDepartment  getObjectByDeptOriginId(SysDepartment sysDepartment);


    List<SysDepartment> getAllDeptNotInMapping(TDicBase tDicBase);

    List<SysDepartment> getAllDeptInMapping(TDicBase tDicBase);

    String getDeptNameByAppInCodeDeptID(SysDepartment sysDepartment);
    String getDeptNameByAppInCodeDeptOriginId(SysDepartment sysDepartment);
}