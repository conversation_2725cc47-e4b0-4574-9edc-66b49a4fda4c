package com.jiuzhekan.cbkj.mapper.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayCurrency;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.ArrayList;
import java.util.List;

@Component
public interface TDisplayCurrencyMapper extends BaseMapper<TDisplayCurrency> {

    int deleteBylistByDisplayId(List<String> displayId);

    TDisplayCurrency getTDisplayCurrency(String displayId);

    int deleteByDisplayId(String displayId);


}