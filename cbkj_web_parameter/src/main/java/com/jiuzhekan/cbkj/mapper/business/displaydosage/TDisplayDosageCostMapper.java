package com.jiuzhekan.cbkj.mapper.business.displaydosage;

import com.jiuzhekan.cbkj.beans.business.displaydosage.GetDisplayDosageFormListByDicId;
import com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageCost;
import com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageForm;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TDisplayDosageCostMapper extends BaseMapper<TDisplayDosageCost>{


    List<TDisplayDosageCost> getDisplayDosageFormListByBean(GetDisplayDosageFormListByDicId dicId);

    @Delete("delete from t_display_dosage_cost where dic_id=#{dicId} and dic_type=#{dicType}")
    void deleteByDicIdAndDicType(@Param("dicId") String dicId, @Param("dicType") Integer dicType);

    List<TDisplayDosageForm> getPharmacyDosageInfo(@Param("displayId") String displayId);
}