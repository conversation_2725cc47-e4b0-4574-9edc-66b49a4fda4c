package com.jiuzhekan.cbkj.mapper.drug;

import com.jiuzhekan.cbkj.beans.drug.StandTMAutoMappingVO;
import com.jiuzhekan.cbkj.beans.drug.TCenterHisMapping;
import com.jiuzhekan.cbkj.beans.drug.TMaterialHISVO;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TCenterHisMappingMapper extends BaseMapper<TCenterHisMapping>{
    /**
     * 药房的药品和HIS的药品映射表
     * @param materialHISVO
     * @return
     */
    List<TMaterialHISVO> getPageDatas2(TMaterialHISVO materialHISVO);
    long selectCountData(TMaterialHISVO materialHISVO);
    int insertautoMapping(StandTMAutoMappingVO standTMAutoMappingVO);
}