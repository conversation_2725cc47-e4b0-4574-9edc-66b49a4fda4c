package com.jiuzhekan.cbkj.mapper.business.asynctask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItem;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItemQuery;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface AsyncTaskItemMapper extends BaseMapper<AsyncTaskItem>{
    /**
     * 获取一次有效任务和定时任务
     * @param asyncTaskItemQuery 参数
     * @return List<AsyncTaskItem>
     */
    List<AsyncTaskItem> getPageListByObj2(AsyncTaskItemQuery asyncTaskItemQuery);

}