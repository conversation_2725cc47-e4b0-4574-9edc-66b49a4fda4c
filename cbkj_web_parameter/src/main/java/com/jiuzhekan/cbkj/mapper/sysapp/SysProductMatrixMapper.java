package com.jiuzhekan.cbkj.mapper.sysapp;

import com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface SysProductMatrixMapper extends BaseMapper<SysProductMatrix> {
    
    List<SysProductMatrix> getPageListByObj(SysProductMatrix sysProductMatrix);
    
    SysProductMatrix getObjectById(String id);

    List<SysProductMatrix> listByUserId(String userId);

    int getMaxOrderNum(String category);
}
