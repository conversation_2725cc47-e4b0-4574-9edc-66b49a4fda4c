package com.jiuzhekan.cbkj.mapper.business.asynctask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskValue;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface AsyncTaskValueMapper extends BaseMapper<AsyncTaskValue>{
    /**
     * 获取同步任务列表通过主键id
     * @param asyncId 同步id
     * @return List
     */
    List<AsyncTaskValue> getListByAsyncId(String asyncId);

}