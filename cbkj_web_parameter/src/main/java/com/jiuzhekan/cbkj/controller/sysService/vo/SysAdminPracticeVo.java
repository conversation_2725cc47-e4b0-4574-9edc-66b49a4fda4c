package com.jiuzhekan.cbkj.controller.sysService.vo;

import com.alibaba.fastjson.JSONArray;
import com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SysAdminPracticeVo
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysAdminPracticeVo {

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "执业机构")
    private List<SysDoctorMultipoint> practiceList;

    @ApiModelProperty(value = "医共体-医疗机构-科室树")
    private JSONArray tree;

    public SysAdminPracticeVo(String userId, List<SysDoctorMultipoint> practiceList) {
        this.userId = userId;
        this.practiceList = practiceList;
    }
}
