package com.jiuzhekan.cbkj.controller.common;

import com.jiuzhekan.cbkj.beans.bs.BsArea;
import com.jiuzhekan.cbkj.beans.bs.BsCity;
import com.jiuzhekan.cbkj.beans.bs.BsProvince;
import com.jiuzhekan.cbkj.beans.bs.BsStreet;
import com.jiuzhekan.cbkj.service.common.AddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "地址接口", tags = "地址接口")
@RequestMapping("address")
public class AddressController {

    @Autowired
    private AddressService addressService;

    @RequestMapping(value = "province", method = RequestMethod.GET)
    @ApiOperation(value = "省", response = BsProvince.class)
    @ResponseBody
    @Cacheable(value = "pre-ai-address:province", keyGenerator = "cacheKeyGenerator")
    public Object getProvinceList() {
        BsProvince bsProvince = new BsProvince();
        return addressService.getProvinceList(bsProvince);
    }

    @RequestMapping(value = "city", method = RequestMethod.GET)
    @ApiOperation(value = "市", response = BsCity.class)
    @ResponseBody
    @Cacheable(value = "pre-ai-address:city", keyGenerator = "cacheKeyGenerator")
    public Object getCityList(String provinceCode) {
        BsCity bsCity = new BsCity();
        bsCity.setProvinceCode(provinceCode);
        return addressService.getCityList(bsCity);
    }

    @RequestMapping(value = "area", method = RequestMethod.GET)
    @ApiOperation(value = "区", response = BsArea.class)
    @ResponseBody
    @Cacheable(value = "pre-ai-address:area", keyGenerator = "cacheKeyGenerator")
    public Object getAreaList(String cityCode) {
        BsArea bsArea = new BsArea();
        bsArea.setCityCode(cityCode);
        return addressService.getAreaList(bsArea);
    }

    @RequestMapping(value = "street", method = RequestMethod.GET)
    @ApiOperation(value = "街道", response = BsStreet.class)
    @ResponseBody
    @Cacheable(value = "pre-ai-address:street", keyGenerator = "cacheKeyGenerator")
    public Object getStreetList(String areaCode) {
        BsStreet bsStreet = new BsStreet();
        bsStreet.setAreaCode(areaCode);
        return addressService.getStreetList(bsStreet);
    }
}
