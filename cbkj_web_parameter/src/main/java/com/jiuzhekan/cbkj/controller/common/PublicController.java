package com.jiuzhekan.cbkj.controller.common;


import com.jiuzhekan.cbkj.beans.business.banner.SysBanner;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.controller.sysController.vo.ChangePwdBean;
import com.jiuzhekan.cbkj.service.business.banner.SysBannerService;
import com.jiuzhekan.cbkj.service.common.PublicService;
import com.jiuzhekan.cbkj.service.sysService.AdminMenuService;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.net.URLDecoder;
import java.util.List;
import java.util.Map;

@Api(value = "公用接口", tags = "公用接口")
@Controller
@RequestMapping("public")
public class PublicController {

    @Autowired
    private AdminMenuService adminMenuService;
    @Autowired
    private AdminService adminService;
    @Autowired
    private PublicService publicService;
    @Autowired
    private SysBannerService sysBannerService;
//    @Autowired
//    private TSysParamService tSysParamService;

    @RequestMapping(value = "changePwd", method = RequestMethod.POST)
    @ApiOperation(value = "自定义修改密码", notes = "自定义修改密码")
    @LogAnnotation(value = "自定义修改密码", isWrite = true)
    @StatisticsFunction(source = "设置", value = "修改密码次数")
    @ResponseBody
    public Object tyChangePwd(@RequestBody ChangePwdBean changePwdBean) {
        return adminService.updatePwd(URLDecoder.decode(changePwdBean.getOldPwd()), URLDecoder.decode(changePwdBean.getNewPwd()));
    }

    @ApiOperation(value = "获取用户所有菜单", notes = "获取用户所有菜单", response = AdminMenu.class)
    @RequestMapping(value = "getAllMenu", method = RequestMethod.GET)
    @ResponseBody
    public Object getMenuTop() {
        return adminMenuService.getMenuByUID(AdminUtils.getCurrentHr().getUserId(),Constant.BASIC_STRING_ONE);
    }

    @ApiOperation(value = "获取菜单下的按钮", notes = "获取菜单下的按钮(暂时弃用)", response = AdminMenu.class)
    @RequestMapping(value = "getMenuBtn", method = RequestMethod.GET)
    @ResponseBody
    @Cacheable(value = "pre-ai-menu:btn:btn", keyGenerator = "cacheKeyGenerator")
    public Object getMenuBtn(String href) {
        return ResEntity.entity(true, Constant.SUCCESS_DX, adminMenuService.getBtnMenuLisByPath(href));
    }

    @ApiOperation(value = "获取科室树三级", notes = "获取科室树三级", response = SysDepartment.class)
    @RequestMapping(value = "getDeptTree", method = RequestMethod.GET)
    @ResponseBody
    public Object getDeptTree(String appId, String insCode) {
        return ResEntity.entity(true, Constant.SUCCESS_DX, publicService.getDeptTree(appId, insCode));
    }

    @RequestMapping(value = "getBanner", method = RequestMethod.GET)
    @ApiOperation(value = "获取登录页轮播图", notes = "获取登录页轮播图", response = SysBanner.class)
    @LogAnnotation("分页查询系统登录页轮播图")
    @ResponseBody
    public Object getBannerList(){
        return sysBannerService.getBannerList();
    }
//    @ApiOperation(value = "获取短信验证码", notes = "获取短信验证码")
//    @RequestMapping(value = "getSmsCode", method = RequestMethod.GET)
//    @ResponseBody
//    public Object getSmsCode(String mobile) {
//        return adminService.getSmsCode(mobile);
//    }


//    @RequestMapping(value = "getParamByCode", method = RequestMethod.GET)
//    @ApiOperation(value = "根据参数代码获取当前用户的参数详情", notes = "根据参数代码获取当前用户的参数详情")
//    @LogAnnotation(value = "根据参数代码获取当前用户的参数详情")
//    @ResponseBody
//    public Object getParamByCode(String paramCode) {
//        if (StringUtils.isBlank(paramCode)) {
//            return new ResEntity(false, "参数代码不能为空！！", null);
//        }
//        return ResEntity.entity(true, Constant.SUCCESS_DX, tSysParamService.getSysParam(paramCode));
//    }

//    @RequestMapping(value = "getAllParams", method = RequestMethod.GET)
//    @ApiOperation(value = "获取当前用户的参数详情", notes = "获取当前用户的参数详情")
//    @LogAnnotation(value = "获取当前用户的参数详情")
//    @ResponseBody
//    public Object getAllParams() {
//        return tSysParamService.getAllParams(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), AdminUtils.getCurrentHr().getId());
//    }

}