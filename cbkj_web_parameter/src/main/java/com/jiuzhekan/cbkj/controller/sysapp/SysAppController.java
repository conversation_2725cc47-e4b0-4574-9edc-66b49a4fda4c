package com.jiuzhekan.cbkj.controller.sysapp;

import com.jiuzhekan.cbkj.beans.sysapp.SysApp;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysMange.AssociationDTO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.dic.GetDeptTree;
import com.jiuzhekan.cbkj.service.pharmacy.PharmacyService;
import com.jiuzhekan.cbkj.service.sysapp.SysAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Api(value = "医联体接口", tags = "医联体接口")
@Controller
@RequestMapping(value = "sys")
/**
 * <AUTHOR>
 */
public class SysAppController {


    private SysAppService sysAppService;

    @Autowired
    SysAppController(SysAppService sysAppService) {
        this.sysAppService = sysAppService;
    }

    @ApiOperation(value = "分页查询医联体信息", notes = "分页查询医联体信息", response = SysApp.class)
    @RequestMapping(value = "sysApp/getPages", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation("分页查询医联体信息")
    public Object getApps(String appName, Page page) {
        SysApp sysApp = new SysApp();
        sysApp.setAppName(appName);
        Object obj = sysAppService.getPageDatas(sysApp, page);
        return obj;
    }


    @ApiOperation(value = "新增医联体信息", notes = "新增医联体信息")
    @RequestMapping(value = "sysApp/insert", method = RequestMethod.POST)
    @LogAnnotation(value = "新增医联体信息", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody AssociationDTO associationDTO) {
        ResEntity result = sysAppService.insert(associationDTO);
        return result;
    }

    @ApiOperation(value = "修改医联体信息", notes = "修改医联体信息")
    @RequestMapping(value = "sysApp/update", method = RequestMethod.POST)
    @LogAnnotation(value = "修改医联体信息", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody AssociationDTO associationDTO) {
        ResEntity result = sysAppService.update(associationDTO);
        return result;
    }

    @ApiOperation(value = "删除医联体信息", notes = "删除医联体信息")
    @RequestMapping(value = "sysApp/deleteLis", method = RequestMethod.POST)
    @LogAnnotation(value = "删除医联体信息", isWrite = true)
    @ResponseBody
    public Object deleteLis(@RequestBody String id) throws Exception {
        ResEntity result = sysAppService.deleteLis(id);
        return result;
    }


    @RequestMapping(value = "sysApp/getApplist", method = RequestMethod.GET)
    @LogAnnotation(value = "医联体下拉列表获取")
    @ApiOperation(value = "医联体下拉列表获取", notes = "医联体下拉列表获取")
    @ResponseBody
    public Object getApplist() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysAppService.getApplist());
    }

  /*  @ApiOperation(value = "加载医联体信息详情", notes = "加载医联体信息详情", response = SysApp.class)
    @RequestMapping(value = "sysApp/findObj", method = RequestMethod.GET)
    @LogAnnotation("加载医联体信息详情")
    @ResponseBody
    public Object getObj( String appId) {
        ResEntity result = sysAppService.findObj(appId);
        return result;
    }*/
    /*
    @ApiOperation(value = "禁用启用医联体信息0删除 1有效 2禁用", notes = "禁用启用医联体信息")
    @RequestMapping(value = "sysApp/changeStatus", method = RequestMethod.GET)
    @LogAnnotation(value = "禁用启用医联体信息", isWrite = true)
    @ResponseBody
    public Object changeStatus(String appId, String isDisable) {
        SysApp sysApp = new SysApp();
        sysApp.setAppId(appId);
        sysApp.setStatus(isDisable);
        ResEntity result = sysAppService.changeUpdate(sysApp);
        return result;
    }*/
}