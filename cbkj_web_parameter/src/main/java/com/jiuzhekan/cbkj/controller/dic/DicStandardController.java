package com.jiuzhekan.cbkj.controller.dic;

import com.jiuzhekan.cbkj.beans.dic.TDicStandardVO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.dic.TDicStandard;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.dic.DicMappingService;
import com.jiuzhekan.cbkj.service.dic.DicStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@Api(value = "标准字典表接口", tags = "标准字典表接口")
@RequestMapping("tDicStandard")
/**
 * <AUTHOR>
 */
public class DicStandardController {


    private DicStandardService dicStandardService;

    @Autowired
    DicStandardController(DicStandardService dicStandardService) {
        this.dicStandardService = dicStandardService;
    }
    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询标准字典表", notes = "分页查询标准字典表", response = TDicStandard.class)
    @LogAnnotation("分页查询标准字典表")
    @ResponseBody
    public Object getApps(TDicStandard tDicStandard, Page page){
       
        return dicStandardService.getPageDatas(tDicStandard,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载标准字典表详情", notes = "加载标准字典表详情", response = TDicStandard.class)
    @LogAnnotation("加载标准字典表详情")
    @ResponseBody
    public Object getObj(String id){
        return dicStandardService.findObj(id);
    }
    @RequestMapping(value = "getFname", method = RequestMethod.GET)
    @ApiOperation(value = "加载标准字典父级字典名称", notes = "加载标准字典父级字典名称", response = TDicStandard.class)
    @LogAnnotation("加载标准字典父级字典名称")
    @ResponseBody
    public Object getFname(){
        return dicStandardService.getFname();
    }
    @RequestMapping(value = "getFcode", method = RequestMethod.POST)
    @ApiOperation(value = "标准字典父级字典代码", notes = "标准字典父级字典代码", response = TDicStandard.class)
    @LogAnnotation("标准字典父级字典代码")
    @ResponseBody
    public Object getFcode(@RequestBody String dicId){
        return dicStandardService.getFcode(dicId);
    }
    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增标准字典表", notes = "新增标准字典表")
    @LogAnnotation(value = "新增标准字典表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TDicStandardVO tDicStandardVO) {
        return dicStandardService.insert(tDicStandardVO);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改标准字典表", notes = "修改标准字典表详情")
    @LogAnnotation(value = "修改标准字典表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TDicStandard tDicStandard) {
        return dicStandardService.update(tDicStandard);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除标准字典表", notes = "删除标准字典表")
    @LogAnnotation(value = "删除标准字典表", isWrite = true)
    @ResponseBody
    public Object deleteLis(@RequestBody String id) throws Exception {
        return dicStandardService.deleteLis(id);
    }

}