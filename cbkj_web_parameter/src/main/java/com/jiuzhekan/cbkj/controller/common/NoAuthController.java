package com.jiuzhekan.cbkj.controller.common;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
//import com.jiuzhekan.cbkj.common.utils.BarcodeUtil;
import com.jiuzhekan.cbkj.common.utils.JasyptUtils;
//import com.jiuzhekan.cbkj.common.utils.ZXingCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * NoAuthController
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2020/10/26 17:17
 */
@Api(value = "不需认证的接口", tags = "不需认证的接口")
@RequestMapping("noAuth")
@Controller
public class NoAuthController {

    @Value("${address.mobile.decoct}pre/common/toInfo?preNo=")
    private String decoctUrl;

//    @RequestMapping(value = "getBarcode", produces = MediaType.IMAGE_JPEG_VALUE, method = RequestMethod.GET)
//    @ApiOperation(value = "获取条形码的接口", notes = "获取条形码的接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "preNo", value = "preNo", dataType = "String", paramType = "query", required = true)
//    })
//    @LogAnnotation(value = "获取条形码的接口")
//    @ResponseBody
//    public byte[] getBarcode(String preNo) {
//        return BarcodeUtil.generate(preNo);
//    }

    @Value("${root.encryptor.password:'::!qb#9467@'}")
    private String password;

    @RequestMapping(value = "encrypt", method = RequestMethod.GET)
    @ApiOperation(value = "加密字符串", notes = "加密字符串")
    @ApiImplicitParam(name = "val", value = "val", dataType = "String", paramType = "query", required = true)
    @LogAnnotation(value = "加密字符串")
    @ResponseBody
    public String encrypt(String val) {
        return JasyptUtils.encryptPwd(password, val);
    }



//    @RequestMapping(value = "getQRCode", produces = MediaType.IMAGE_JPEG_VALUE, method = RequestMethod.GET)
//    @ApiOperation(value = "获取二维码的接口", notes = "获取二维码的接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "preNo", value = "preNo", dataType = "String", paramType = "query", required = true)
//    })
//    @LogAnnotation(value = "获取二维码的接口")
//    @ResponseBody
//    public byte[] getQRCode(@RequestParam(value = "preNo" ,required = true) String preNo) {
//        return ZXingCode.getLogoQRCodeForZip(decoctUrl + preNo,null,null);
//    }
}
