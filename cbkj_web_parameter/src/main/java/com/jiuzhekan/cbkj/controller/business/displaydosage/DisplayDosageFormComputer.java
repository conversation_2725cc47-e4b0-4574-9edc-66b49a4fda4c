package com.jiuzhekan.cbkj.controller.business.displaydosage;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/14 17:20
 * @Version 1.0
 */
public enum DisplayDosageFormComputer {
    /**
     * 四舍五入计算方式：处方总重量四舍五入取整，数量=(处方总重量取整后g/500)；总价=数量)*单价；如560g取500g,800g取1000g；
     * <p>
     * 向前取整计算方式：处方总重量向前取整，数量=(处方总重量取整后g/500)；总价=数量)*单价；如501g取500g,1999g取1500g；
     * <p>
     * 向后取整计算方式：处方总重量向后取整，数量=(处方总重量取整后g/500)；总价=数量)*单价；如501g取1000g,1999g取2000g；
     */
    SI_SHE_WU_RU(1, "四舍五入计算方式","处方总重量四舍五入取整，数量=(处方总重量取整后g/500)；总价=数量)*单价；如560g取500g,800g取1000g"),
    /**
     * 向前取整计算方式：处方总重量向前取整，数量=(处方总重量取整后g/500)；总价=数量)*单价；如501g取500g,1999g取2000g；
     */
    XIANG_QIAN_QU_ZHENG(2, "向前取整计算方式","处方总重量向前取整，数量=(处方总重量取整后g/500)；总价=数量)*单价；如560g取600g,800g取800g"),
    /**
     * 向后取整计算方式：处方总重量向后取整，数量=(处方总重量取整)
     */
    XIANG_HOU_QU_ZHENG(3, "处方总重量向后取整","处方总重量向后取整，数量=(处方总重量取整)");
    private Integer code;
    private String name;
    private String content;

    DisplayDosageFormComputer(Integer code, String name,String content) {
        this.code = code;
        this.name = name;
        this.content = content;

    }

    /**
     * 返回名称
     * @return
     */
    public Object getName() {
        return name;
    }
    /**
     * 获取内容
     * @return
     */
    public Object getContent() {
        return content;
    }
    /**
     * 获取code
     * @return
     */
    public Object getCode() {
        return code;
    }
}
