package com.jiuzhekan.cbkj.controller.business.banner;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.business.banner.SysBanner;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.banner.SysBannerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Controller
@Api(value = "系统登录页轮播图接口", tags = "系统登录页轮播图接口")
@RequestMapping("sysBanner")
public class SysBannerController {

    @Autowired
    private SysBannerService sysBannerService;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询系统登录页轮播图", notes = "分页查询系统登录页轮播图", response = SysBanner.class)
    @LogAnnotation("分页查询系统登录页轮播图")
    @ResponseBody
    public Object getApps(SysBanner sysBanner, Page page){
        return sysBannerService.getPageDatas(sysBanner,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载系统登录页轮播图详情", notes = "加载系统登录页轮播图详情", response = SysBanner.class)
    @LogAnnotation("加载系统登录页轮播图详情")
    @ResponseBody
    public Object getObj(String id){
        return sysBannerService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增系统登录页轮播图", notes = "新增系统登录页轮播图")
    @LogAnnotation(value = "新增系统登录页轮播图", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody SysBanner sysBanner) {
        return sysBannerService.insert(sysBanner);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改系统登录页轮播图", notes = "修改系统登录页轮播图详情")
    @LogAnnotation(value = "修改系统登录页轮播图", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody SysBanner sysBanner) {
        return sysBannerService.update(sysBanner);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除系统登录页轮播图", notes = "删除系统登录页轮播图")
    @LogAnnotation(value = "删除系统登录页轮播图", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return sysBannerService.deleteLis(ids);
    }

}