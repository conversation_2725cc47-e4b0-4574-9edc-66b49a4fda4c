package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.business.SysSettingInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.service.business.SysSettingInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/4 09:24
 * @Version 1.0
 */
@Api(value = "名称维护", tags = "名称维护", position = 4)
@RestController
@RequestMapping("sysSettingInfo")
public class SysSettingInfoController {

    private final SysSettingInfoService sysSettingInfoService;

    public SysSettingInfoController(SysSettingInfoService sysSettingInfoService) {
        this.sysSettingInfoService = sysSettingInfoService;
    }

    /**
     * 获取名称维护信息
     */
    @GetMapping("getLogoTitleInfo")
    @ApiOperation(value = "获取名称维护信息", notes = "获取名称维护信息", response =  SysSettingInfo.class)
    public Object getLogoTitleInfo() {
        return ResEntity.success(sysSettingInfoService.getLogoTitleInfo());
    }

    @PostMapping("updateLogoTitleInfo")
    @ApiOperation(value = "更新名称维护信息", notes = "更新名称维护信息", response =  SysSettingInfo.class)
    public Object updateLogoTitleInfo(@RequestPart String platformSysName,
                                      @RequestPart String preSysName,
                                      @RequestPart(required = false) MultipartFile sysLogo) {
        SysSettingInfo sysSettingInfo = new SysSettingInfo();
        sysSettingInfo.setPlatformSysName(platformSysName);
        sysSettingInfo.setPreSysName(preSysName);
        return ResEntity.success(sysSettingInfoService.updateLogoTitleInfo(sysSettingInfo,sysLogo));
    }

    @GetMapping("getPlatFormLogoTitleInfo")
    @ApiOperation(value = "获取配置系统的logo和title（开方权限接口）", notes = "获取配置系统的logo和title（开方权限接口）", response =  SysSettingInfo.class)
    public Object getPlatFormLogoTitleInfo() {
        return ResEntity.success(sysSettingInfoService.getPlatFormLogoTitleInfo());
    }


}
