//package com.jiuzhekan.cbkj.controller.common.vo;
//
//import com.jiuzhekan.cbkj.beans.drug.SpecialPreMat;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.List;
//
///**
// * TransSpecialMatVo
// * <p>
// * 杭州聪宝科技有限公司
// *
// * <AUTHOR>
// * @date 2020/10/26
// */
//
//@Data
//@NoArgsConstructor
//public class TransSpecialMatVo {
//    private String centerYplx;
//    private String centerStoreId;
//    private List<SpecialPreMat> fromMats;
//
//    public TransSpecialMatVo(List<SpecialPreMat> fromMats) {
//        this.fromMats = fromMats;
//    }
//}
