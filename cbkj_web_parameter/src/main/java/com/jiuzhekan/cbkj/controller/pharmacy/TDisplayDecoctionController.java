package com.jiuzhekan.cbkj.controller.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayDecoctionP;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.pharmacy.TDisplayDecoctionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "药房服务配置接口", tags = "药房服务配置接口")
@RequestMapping("tDisplayDecoction")
public class TDisplayDecoctionController {


    private TDisplayDecoctionService tDisplayDecoctionService;

    @Autowired
    public TDisplayDecoctionController(TDisplayDecoctionService tDisplayDecoctionService){
        this.tDisplayDecoctionService=tDisplayDecoctionService;
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "代煎-加载药房配置代煎信息详情", notes = "代煎-加载药房配置代煎信息详情", response = TDisplayDecoction.class)
    @LogAnnotation("代煎-加载药房配置代煎信息详情")
    @ResponseBody
    public Object getObj(String displayId){
        return tDisplayDecoctionService.findObj(displayId);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "代煎-新增药房配置代煎信息表", notes = "代煎-新增药房配置代煎信息表")
    @LogAnnotation(value = "代煎-新增药房配置代煎信息表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TDisplayDecoctionP tDisplayDecoctionP) {
        return tDisplayDecoctionService.insertOrUpdate(tDisplayDecoctionP);
    }

    @RequestMapping(value = "insertBatch", method = RequestMethod.POST)
    @ApiOperation(value = "代煎-批量更新药房配置代煎", notes = "代煎-批量更新药房配置代煎")
    @LogAnnotation(value = "代煎-批量更新药房配置代煎", isWrite = true)
    @ResponseBody
    public Object insertBatch(@RequestBody TDisplayDecoctionP tDisplayDecoctionP) {
        ResEntity resEntity = null;
        if (!StringUtils.isBlank(tDisplayDecoctionP.getPharmacyDisplayList())){
            String[] split = tDisplayDecoctionP.getPharmacyDisplayList().split(",");
            for (int i = 0; i < split.length; i++) {
                tDisplayDecoctionP.getHospitalization().setDisplayId(split[i]);
                tDisplayDecoctionP.getOutpatient().setDisplayId(split[i]);
                 resEntity = tDisplayDecoctionService.insertOrUpdate(tDisplayDecoctionP);
                if (!resEntity.getStatus()){
                    break;
                }
            }
        }
        if (null != resEntity && !resEntity.getStatus()){
            return resEntity;
        }
        return ResEntity.success(null);
    }


}