//package com.jiuzhekan.cbkj.controller.common;
//
//import com.jiuzhekan.cbkj.beans.dic.TDicBase;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
//import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
//import com.jiuzhekan.cbkj.common.utils.SHA1;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.RequestEntity;
//import org.springframework.stereotype.Controller;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// */
//@Controller
//@Api(value = "平台接口调用", tags = "平台接口调用")
//@RequestMapping("platform")
//public class PlatformApiController {
//    @Autowired
//    private PlatformRestTemplate platformRestTemplate;
//
//    @RequestMapping(value = "getPages", method = RequestMethod.GET)
//    @ApiOperation(value = "查询数据字典表", notes = "查询数据字典表", response = TDicBase.class)
//    @LogAnnotation("查询数据字典表")
//    @ResponseBody
//    public ResEntity getDicList(TDicBase tDicBase){
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
//        String bodyValue = "{\"dicCode\":\""+tDicBase.getDicCode()+"\"}";
//        map.add("dicData",bodyValue);
//        HttpEntity<MultiValueMap<String, Object>> request1 = new HttpEntity<>(map, headers);
//        ResEntity post = platformRestTemplate.post("platform/api/dic/list", request1);
//        return post;
//    }
//}
