package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.sysService.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@RequestMapping("public/redis/")
@Api(value = "redis接口", tags = "redis接口")
@Controller
public class RedisController {
    private final RedisService redisService;
    @Autowired
    RedisController(RedisService redisService){
        this.redisService = redisService;
    }
    @RequestMapping(value = "clear", method = RequestMethod.GET)
    @ApiOperation(value = "清除redis缓存", notes = "清除redis缓存")
    @LogAnnotation(value = "清除redis缓存")
    @ResponseBody
    public ResEntity clearRedisCache(String value, String key) {
        return redisService.clearRedisCache(value, key);
    }
}
