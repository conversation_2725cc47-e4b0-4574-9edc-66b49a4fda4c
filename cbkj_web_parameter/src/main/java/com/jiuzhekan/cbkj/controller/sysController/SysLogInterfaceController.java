package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.sysBeans.SysLogInterface;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysService.SysLogInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

/**
 * <AUTHOR>
 */
@Controller
@Api(value = "接口日志接口", tags = "接口日志接口")
@RequestMapping("sysLogInterface")
public class SysLogInterfaceController {
    private final SysLogInterfaceService sysLogInterfaceService;

    @Autowired
    SysLogInterfaceController(SysLogInterfaceService sysLogInterfaceService){
        this.sysLogInterfaceService = sysLogInterfaceService;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询接口日志", notes = "分页查询接口日志", response = SysLogInterface.class)
    @LogAnnotation("分页查询接口日志")
    @ResponseBody
    public Object getApps(SysLogInterface sysLogInterface, Page page){
        return sysLogInterfaceService.getPageDatas(sysLogInterface,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载接口日志详情", notes = "加载接口日志详情", response = SysLogInterface.class)
    @LogAnnotation("加载接口日志详情")
    @ResponseBody
    public Object getObj(String id){
        return sysLogInterfaceService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增接口日志", notes = "新增接口日志")
    @LogAnnotation(value = "新增接口日志", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody SysLogInterface sysLogInterface) {
        return sysLogInterfaceService.insert(sysLogInterface);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改接口日志", notes = "修改接口日志详情")
    @LogAnnotation(value = "修改接口日志", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody SysLogInterface sysLogInterface) {
        return sysLogInterfaceService.update(sysLogInterface);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除接口日志", notes = "删除接口日志")
    @LogAnnotation(value = "删除接口日志", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return sysLogInterfaceService.deleteLis(ids);
    }

}