package com.jiuzhekan.cbkj.controller.sysController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.service.sysService.RedisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024.4.16
 */
@Component
public class LoginFailLock {
    /**
     * 登录失败次数上限，默认5次
     */
    @Value("${login.fails.time:5}")
    private int loginFailsTime;
    /**
     * 登录失败超过次数上限后，锁定时间，单位分钟，默认5分钟
     */
    @Value("${login.lock.minute:5}")
    private int loginLockMinute;
    /**
     * 登录密码定期更换周期，单位天，默认90天
     */
    @Value("${login.pwd.update:90}")
    private int loginPwdUpdate;

    private final RedisService redisService;

    public LoginFailLock(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 校验锁定时间
     *
     * @param userName userName
     */
    public boolean checkLock(String userName) {
        Object obj = redisService.get(userName);
        if (obj == null) {
            return true;
        }
        JSONObject json = JSON.parseObject(JSON.toJSONString(obj));
        Integer num = json.getInteger("num");
        Date date = json.getDate("lastDate");
        long timeDifference = ((System.currentTimeMillis() - date.getTime()) / 60 / 1000);
        return num < loginFailsTime || timeDifference >= loginLockMinute;
    }

    /**
     * 增加登录失败次数
     *
     * @param userName userName
     */
    public void addFailNum(String userName) {
        Object obj = redisService.get(userName);
        JSONObject json;
        int num = 0;
        if (obj == null) {
            json = new JSONObject();
        } else {
            json = JSON.parseObject(JSON.toJSONString(obj));
            num = json.getInteger("num");
            //Date date = json.getDate("lastDate");
            //long timeDifference = ((System.currentTimeMillis() - date.getTime()) / 60 / 1000);
            //if (timeDifference >= loginLockMinute) {
            //    num = 0;
            //}
        }
        json.put("num", num + 1);
        json.put("lastDate", new Date());
        redisService.set(String.valueOf(userName), json, loginLockMinute, TimeUnit.MINUTES);
    }

    /**
     * 判断是否需要修改密码
     *
     * @param admin admin
     */
    public boolean needUpdatePwd(AdminInfo admin) {
        Date lastUpdatePwd = admin.getLastUpdatePwd();
        if (lastUpdatePwd == null) {
            return true;
        }
        long dayDifference = ((System.currentTimeMillis() - lastUpdatePwd.getTime()) / 24 / 60 / 60 / 1000);
        if (dayDifference > loginPwdUpdate) {
            return true;
        }
        return false;
    }

    public int getLoginLockMinute() {
        return loginLockMinute;
    }
}
