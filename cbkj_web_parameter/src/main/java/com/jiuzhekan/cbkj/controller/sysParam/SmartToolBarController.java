//package com.jiuzhekan.cbkj.controller.sysParam;
//
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamDTO;
//import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
//import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
//import com.jiuzhekan.cbkj.service.parameter.smartPrescrib.DiagnosisService;
//import com.jiuzhekan.cbkj.service.parameter.smartPrescrib.ToolbarService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@Api(value = "参数配置", tags = "参数配置")
//public class SmartToolBarController {
//    @Autowired
//    private DiagnosisService diagnosisService;
//    private ToolbarService toolbarService;
//
//    @Autowired
//    SmartToolBarController(ToolbarService toolbarService) {
//        this.toolbarService = toolbarService;
//    }
//
//    @RequestMapping(value = "smart/prescribing/getToolBar", method = RequestMethod.POST)
//    @LogAnnotation(value = "参数配置-开方工具栏获取", isWrite = true)
//    @ApiOperation(value = "参数配置-开方工具栏获取", notes = "参数配置-开方工具栏获取")
//    public Object getToolBar(@RequestBody DiagnosisParamDTO sysParam) {
//        String menuId = sysParam.getMenuId();
//        if (StringUtils.isBlank(menuId)){
//            return ResEntity.entity(false, "缺少menuId", sysParam);
//        }
//
//        return toolbarService.getParameterList(sysParam);
//    }
//    @RequestMapping(value = "smart/prescribing/saveToolBar", method = RequestMethod.POST)
//    @LogAnnotation(value = "参数配置-开方工具栏保存", isWrite = true)
//    @ApiOperation(value = "参数配置-开方工具栏保存", notes = "参数配置-开方工具栏保存")
//    public Object saveToolBar(@RequestBody DiagnosisParamNewVO diagnosisParamNewVO){
//        ResEntity toolBar = toolbarService.saveParameterList(diagnosisParamNewVO);
//        return  toolBar;
//    }
//    /*@RequestMapping(value = "smart/prescribing/getprescriptionPrinting", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-获取处方打印", isWrite = true)
//    @ApiOperation(value = "智能开方-获取处方打印", notes = "智能开方-获取处方打印")
//    public Object getprescriptionPrinting(@RequestBody DiagnosisParamDTO sysParam) {
//        ResEntity toolBar = toolbarService.getParameterList(sysParam);
//        return toolBar;
//    }
//    *//*@RequestMapping(value = "smart/prescribing/saveprescriptionPrinting", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-保存处方打印", isWrite = true)
//    @ApiOperation(value = "智能开方-保存处方打印", notes = "智能开方-保存处方打印")
//    public Object saveprescriptionPrinting(@RequestBody DiagnosisParamNewVO diagnosisParamNewVO) {
//        ResEntity toolBar = toolbarService.saveParameterList(diagnosisParamNewVO);
//        return toolBar;
//    }*//*
//
//    @RequestMapping(value = "smart/prescribing/getDrugs", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-获取药品参数", isWrite = true)
//    @ApiOperation(value = "智能开方-获取药品参数", notes = "智能开方-获取药品参数")
//    public Object getDrugs(@RequestBody DiagnosisParamDTO sysParam) {
//        ResEntity toolBar = toolbarService.getParameterList(sysParam);
//        return toolBar;
//    }
//
//    @RequestMapping(value = "smart/prescribing/getKnow", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-获取知识库参数", isWrite = true)
//    @ApiOperation(value = "智能开方-获取知识库参数", notes = "智能开方-获取知识库参数")
//    public Object getKnow(@RequestBody DiagnosisParamDTO sysParam) {
//        ResEntity toolBar = toolbarService.getParameterList(sysParam);
//        return toolBar;
//    }
//
//    @RequestMapping(value = "smart/prescribing/getOtherInterface", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-获取第三方对接参数", isWrite = true)
//    @ApiOperation(value = "智能开方-获取第三方对接参数", notes = "智能开方-获取第三方对接参数")
//    public Object getOtherInterface(@RequestBody DiagnosisParamDTO sysParam) {
//        ResEntity toolBar = toolbarService.getParameterList(sysParam);
//        return toolBar;
//    }
//
//    @RequestMapping(value = "smart/prescribing/getRegulatoryPlatform", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-获取监管平台参数", isWrite = true)
//    @ApiOperation(value = "智能开方-获取监管平台参数", notes = "智能开方-获取监管平台参数")
//    public Object getRegulatoryPlatform(@RequestBody DiagnosisParamDTO sysParam) {
//        ResEntity toolBar = toolbarService.getParameterList(sysParam);
//        return toolBar;
//    }
//
//    @RequestMapping(value = "smart/prescribing/getMedicalRecord", method = RequestMethod.POST)
//    @LogAnnotation(value = "智能开方-获取电子电子病历", isWrite = true)
//    @ApiOperation(value = "智能开方-获取电子电子病历", notes = "智能开方-获取电子电子病历")
//    public Object getMedicalRecord(@RequestBody DiagnosisParamDTO sysParam) {
//        ResEntity toolBar = toolbarService.getParameterList(sysParam);
//        return toolBar;
//    }*/
//}
