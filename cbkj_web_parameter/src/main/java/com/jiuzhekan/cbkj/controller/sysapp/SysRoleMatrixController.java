package com.jiuzhekan.cbkj.controller.sysapp;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysBeans.ResultEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysapp.SysProductMatrixService;
import com.jiuzhekan.cbkj.service.sysapp.SysRoleMatrixService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "角色产品矩阵接口", tags = "角色产品矩阵接口")
@RestController
@RequestMapping("sys/sysRoleMatrix")
public class SysRoleMatrixController {

    @Autowired
    private SysRoleMatrixService sysRoleMatrixService;

    @Autowired
    private SysProductMatrixService sysProductMatrixService;


    @Data
    public static class SaveRoleMatrix{
        @ApiModelProperty(value = "角色ID")
        private String roleId;

        @ApiModelProperty(value = "产品矩阵ID列表")
        private List<String> productMatrixIdList;
    }

    @ApiOperation(value = "保存角色产品矩阵")
    @LogAnnotation(value = "保存角色产品矩阵")
    @PostMapping("/saveBeforeDel")
    public ResultEntity<Boolean> saveBeforeDel(@RequestBody SaveRoleMatrix param){
        boolean b = sysRoleMatrixService.saveBeforeDel(param.getRoleId(), param.getProductMatrixIdList());
        return ResultEntity.entity(b, Constant.SUCCESS_DX, b);
    }

    @ApiOperation(value = "根据登录用户获取产品矩阵树")
    @LogAnnotation(value = "根据登录用户获取产品矩阵树")
    @GetMapping("/getTreeByLoginUser")
    public ResultEntity<List<SysProductMatrix>> getTreeByLoginUser() {
        String userId = AdminUtils.getCurrentHr().getUserId();
        List<SysProductMatrix> treeList = sysRoleMatrixService.getTreeByUserId(userId);
        return ResultEntity.entity(true, Constant.SUCCESS_DX, treeList);
    }

    @Data
    public static class GetTreeByRoleResult{
        @ApiModelProperty(value = "角色拥有得产品矩阵ID列表")
        private List<String> productMatrixIdList;

        @ApiModelProperty(value = "产品矩阵树")
        private List<SysProductMatrix> treeList;
    }

    @ApiOperation(value = "根据角色获取产品矩阵树", notes = "一级为分类，二级为分类下的产品列表")
    @LogAnnotation(value = "根据角色获取产品矩阵树")
    @GetMapping("/getTreeByRoleId")
    public ResultEntity<GetTreeByRoleResult> getTreeByRoleId(String roleId) {
        List<SysProductMatrix> treeList = sysProductMatrixService.getTree();
        List<String> matrixIds = sysRoleMatrixService.listMatrixIdsByRoleId(roleId);

        GetTreeByRoleResult result = new GetTreeByRoleResult();
        result.setProductMatrixIdList(matrixIds);
        result.setTreeList(treeList);

        return ResultEntity.entity(true, Constant.SUCCESS_DX, result);
    }
}
