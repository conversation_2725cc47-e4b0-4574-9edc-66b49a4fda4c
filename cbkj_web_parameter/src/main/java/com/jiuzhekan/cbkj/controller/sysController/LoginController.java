package com.jiuzhekan.cbkj.controller.sysController;

import cn.hutool.core.util.RandomUtil;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.LoginBean;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.config.security.TokenBo;
import com.jiuzhekan.cbkj.common.config.security.TokenUtil;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.MD5Util;
import com.jiuzhekan.cbkj.common.utils.encry.RSAEncryption;
import com.jiuzhekan.cbkj.controller.sysController.utils.RandImageUtil;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import com.jiuzhekan.cbkj.service.sysService.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * LoginController
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/12
 */
@Api(value = "登录接口", tags = "登录接口", position = 3)
@Controller
@Slf4j
public class LoginController {

    private final AdminService adminService;
    private final TokenUtil tokenUtil;
    private final RedisService redisService;
    private final RSAEncryption rsaEncryption;
    private final LoginFailLock loginFailLock;
    @Value("${rsa.privateKey}")
    private String privateKey;
    private static final String BASE_CHECK_CODES = "qwertyuiplkjhgfdsazxcvbnmQWERTYUPLKJHGFDSAZXCVBNM1234567890";


    @Autowired
    public LoginController(AdminService adminService,
                           TokenUtil tokenUtil,
                           RedisService redisService,
                           RSAEncryption rsaEncryption,
                           LoginFailLock loginFailLock
    ) {
        this.adminService = adminService;
        this.tokenUtil = tokenUtil;
        this.redisService = redisService;
        this.rsaEncryption = rsaEncryption;
        this.loginFailLock = loginFailLock;
    }


    @RequestMapping(value = "login", method = RequestMethod.POST)
    @ApiOperation(value = "登录", notes = "登录")
    @LogAnnotation(value = "登录", isWrite = true)
    @ResponseBody
    public Object login(@RequestBody LoginBean loginBean) {
        String login = loginBean.getLogin();
        String captcha = loginBean.getCaptcha();
        String pwd = loginBean.getPwd();
        pwd = URLDecoder.decode(pwd);
        String name = loginBean.getName();
        String checkKey = loginBean.getCheckKey();
        if (StringUtils.isBlank(login) || !login.equals(Constant.ADMIN)) {
            if (StringUtils.isBlank(captcha)) {
                return ResEntity.error("验证码无效！");
            }
            String lowerCaseCaptcha = captcha.toLowerCase();
            String realKey = MD5Util.encode(lowerCaseCaptcha + checkKey);
            Object checkCode = redisService.get(realKey);
            if (StringUtils.isBlank((String) checkCode) || !checkCode.toString().equals(lowerCaseCaptcha)) {
                return ResEntity.error("验证码错误！");
            }
        }


        AdminInfo admin = adminService.loadUserByUsername(name);

        if (!loginFailLock.checkLock(name)) {
            loginFailLock.addFailNum(name);
            return ResEntity.error("该账号已被锁定" + loginFailLock.getLoginLockMinute() + "分钟！");
        }
        if (null == admin || pwd == null) {
            loginFailLock.addFailNum(name);
            return ResEntity.error("用户名或密码错误！");
        }

        try {
            pwd = rsaEncryption.dencryptByPrivateKey(pwd, privateKey);
        } catch (Exception e) {
            log.error("RSA解密失败！");
            loginFailLock.addFailNum(name);
            return ResEntity.error("用户名或密码错误！");
        }
        if (!pwd.equals(admin.getPassword())) {
            loginFailLock.addFailNum(name);
            return ResEntity.error("用户名或密码错误！");
        }

        if (!admin.isEnabled()) {
            return ResEntity.error("该用户被禁用！");
        }

        admin.setNeedUpdatePwd(loginFailLock.needUpdatePwd(admin));

//        List<SysDoctorMultipoint> practices = sysDoctorMultipointService.getPracticeList(admin.getUserId(),admin.getAppId(),admin.getInsCode());
//        if (practices == null || practices.size() == 0) {
//            return ResEntity.error("用户没有授权执业机构，请联系管理员！");
//        } else if (practices.size() > 1) {
//            return ResEntity.error("请选择执业机构！");
//        }
        TokenBo tokenBo = null;
        //final SysDoctorMultipoint practice = practices.get(0);
        if (tokenUtil.getLoginOne()) {
            String tokenKey = tokenUtil.createTokenKey(admin.getUserId());
            String authorization = tokenUtil.generateToken(tokenKey);
            if (!"ok".equals(login)) {
                final TokenBo bo = tokenUtil.getSecurityRegister(tokenKey);
                if (bo != null && admin.getUserId().equals(bo.getAdminId())) {
                    return ResEntity.error(555, "你的账号已登录，是否覆盖？");
                }
            } else {
                redisService.clearRedisCache("pre-parameter-token::" + tokenKey, null);
            }
            tokenBo = new TokenBo(tokenKey, authorization, admin.getUserId(), admin, null);
            tokenUtil.updateTokenBo(tokenBo);
        } else {
            tokenBo = tokenUtil.createTokenBo(admin, null);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("status", true);
        result.put("message", Constant.SUCCESS_DX);
        result.put("data", tokenBo.getAuthorization());
        result.put("needUpdatePwd", admin.isNeedUpdatePwd());
        return result;
    }

    @RequestMapping(value = "exit", method = RequestMethod.GET)
    @ApiOperation(value = "登出", notes = "登出")
    @LogAnnotation(value = "登出", isWrite = true)
    @ResponseBody
    public ResEntity exit() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            Object obj = authentication.getPrincipal();
            if (obj instanceof TokenBo) {
                redisService.clearRedisCache(null, "pre-parameter-token::" + ((TokenBo) obj).getTokenKey());
            }
        }
        return ResEntity.success(null);
    }

    /**
     * 后台生成图形验证码 ：有效
     */
    @GetMapping(value = "/randomImage/{key}")
    @ApiOperation(value = "获取验证码", notes = "参数路径key在登录中会再次需要这个值")
    @LogAnnotation(value = "获取验证码", isWrite = true)
    @ResponseBody
    public ResEntity randomImage(HttpServletResponse response, @PathVariable String key) {
        try {
            String code = RandomUtil.randomString(BASE_CHECK_CODES, 4);
            String lowerCaseCode = code.toLowerCase();
            String realKey = MD5Util.encode(lowerCaseCode + key);
            redisService.set(realKey, lowerCaseCode, 1, TimeUnit.DAYS);
            String base64 = RandImageUtil.generate(code);
            return ResEntity.success(base64);
        } catch (Exception e) {
            return ResEntity.error(e.getMessage());
        }
    }

}
