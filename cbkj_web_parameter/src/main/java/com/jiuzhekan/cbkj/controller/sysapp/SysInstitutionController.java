package com.jiuzhekan.cbkj.controller.sysapp;

import com.jiuzhekan.cbkj.beans.sysapp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysMange.InstitutionDTO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.FileTypeUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.common.ImportInsService;
import com.jiuzhekan.cbkj.service.sysapp.SysAppService;
import com.jiuzhekan.cbkj.service.sysapp.SysInstitutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Api(value = "医疗机构接口", tags = "医疗机构接口")
@Controller
@RequestMapping("sys")
/**
 * <AUTHOR>
 */
public class SysInstitutionController {


    private SysInstitutionService sysInstitutionService;

    private ImportInsService importInsService;

    private SysAppService sysAppService;

    @Autowired
    SysInstitutionController(SysInstitutionService sysInstitutionService, ImportInsService importInsService, SysAppService sysAppService) {
        this.sysInstitutionService = sysInstitutionService;
        this.importInsService = importInsService;
        this.sysAppService = sysAppService;
    }


    @RequestMapping(value = "sysInstitution/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询医疗机构信息", notes = "分页查询医疗机构信息", response = SysInstitution.class)
    @LogAnnotation(value = "分页查询医疗机构信息")
    @ResponseBody
    public Object getApps(InstitutionDTO institutionDTO, Page page) {
        Object obj = sysInstitutionService.getPageDatas(institutionDTO, page);
        return obj;
    }


    @RequestMapping(value = "sysInstitution/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增医疗机构信息", notes = "新增医疗机构信息")
    @LogAnnotation(value = "新增医疗机构信息", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody InstitutionDTO institutionDTO) {
        ResEntity result = sysInstitutionService.insert(institutionDTO);
        return result;
    }

    @RequestMapping(value = "sysInstitution/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改医疗机构信息", notes = "修改医疗机构信息")
    @LogAnnotation(value = "修改医疗机构信息", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody InstitutionDTO institutionDTO) {
        ResEntity result = sysInstitutionService.update(institutionDTO);
        return result;
    }

    @RequestMapping(value = "sysInstitution/deleteLis", method = RequestMethod.POST)
    @ApiOperation(value = "删除医疗机构信息", notes = "删除医疗机构信息")
    @LogAnnotation(value = "删除医疗机构信息", isWrite = true)
    @ResponseBody
    public Object deleteLis(@RequestBody String id) throws Exception {
        ResEntity result = sysInstitutionService.deleteLis(id);
        return result;
    }


    @RequestMapping(value = "sysInstitution/getInsLList", method = RequestMethod.GET)
    @ApiOperation(value = "获取上级医疗机构", notes = "获取上级医疗机构", response = SysInstitution.class)
    @LogAnnotation(value = "获取上级医疗机构")
    @ResponseBody
    public Object getInsList(SysInstitution sysInstitution) {
        List<SysInstitution> sysInstitutionList = sysInstitutionService.getSysInstitutionList(sysInstitution);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionList);
    }

    @RequestMapping(value = "sysInstitution/getInsLListTree", method = RequestMethod.GET)
    @ApiOperation(value = "获取医疗机构树", notes = "获取医疗机构树", response = SysInstitution.class)
    @LogAnnotation(value = "获取医疗机构树")
    @ResponseBody
    public Object getInsLListTree() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionService.getSysInstitutionListTree(sysAppService.getApplist()));
    }

    @RequestMapping(value = "sysInstitution/getInsTree", method = RequestMethod.GET)
    @ApiOperation(value = "医疗机构下拉列表获取", notes = "医疗机构下拉列表获取", response = SysInstitution.class)
    @LogAnnotation(value = "医疗机构下拉列表获取")
    @ResponseBody
    public Object getInsTree(String appId) {
        List<SysInstitution> sysInstitutionList = sysInstitutionService.getInsTree(appId);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionList);
    }

    /**
     * EXCEL导入医疗机构数据
     *
     * @param file file
     */
    @ApiOperation(value = "EXCEL导入医疗机构数据", notes = "EXCEL导入医疗机构数据")
    @RequestMapping(value = "excel/Institution", method = RequestMethod.POST)
    @ResponseBody
    public Object importIns(MultipartFile file) {

        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }
        if (!FileTypeUtil.isExcel(file)) {
            return new ResEntity(false, "仅支持Excel", null);
        }
        return importInsService.importIns(file);
    }
}