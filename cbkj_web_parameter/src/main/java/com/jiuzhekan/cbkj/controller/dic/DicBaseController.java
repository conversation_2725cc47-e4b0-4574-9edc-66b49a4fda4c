package com.jiuzhekan.cbkj.controller.dic;


import com.jiuzhekan.cbkj.beans.dic.DicData;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.dic.TDicBase;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.dic.GetDeptTree;
import com.jiuzhekan.cbkj.service.dic.DicBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@Api(value = "数据字典表接口", tags = "数据字典表接口")
@RequestMapping("tDicBase")
/**
 * <AUTHOR>
 */
public class DicBaseController {


    private DicBaseService dicBaseService;


    private GetDeptTree getDeptTree;

    @Autowired
    DicBaseController(DicBaseService dicBaseService, GetDeptTree getDeptTree) {
        this.dicBaseService = dicBaseService;
        this.getDeptTree = getDeptTree;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询数据字典表", notes = "分页查询数据字典表", response = TDicBase.class)
    @LogAnnotation("分页查询数据字典表")
    @ResponseBody
    public Object getApps(TDicBase tDicBase, Page page) {
        Object pageDatas = dicBaseService.getPageDatas(tDicBase, page);
        return pageDatas;
    }

    @RequestMapping(value = "getFname", method = RequestMethod.GET)
    @ApiOperation(value = "父级字典名称", notes = "父级字典名称", response = TDicBase.class)
    @LogAnnotation("父级字典名称")
    @ResponseBody
    public Object getFname() {
        return dicBaseService.getFname();
    }

    @ApiOperation(value = "获取科室树", notes = "获取科室树", response = SysDepartment.class)
    @RequestMapping(value = "getDeptTree", method = RequestMethod.GET)
    @ResponseBody
    public Object getDeptTree() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, getDeptTree.getDeptTree());
    }

    @RequestMapping(value = "getCareer", method = RequestMethod.POST)
    @ApiOperation(value = "获取职业信息", notes = "获取职业信息", response = TDicBase.class)
    @LogAnnotation("获取职业信息")
    @ResponseBody
    public Object getCareer(@RequestBody String dicCode) {
        return dicBaseService.getCareer(dicCode);
    }


    @RequestMapping(value = "getParentName", method = RequestMethod.GET)
    @ApiOperation(value = "查询大类名称", notes = "查询大类名称", response = TDicBase.class)
    @LogAnnotation("查询大类名称")
    @ResponseBody
    public Object getParentName(TDicBase tDicBase, Page page) {
        return dicBaseService.getParentName(tDicBase, page);
    }


    @RequestMapping(value = "getSystemItem", method = RequestMethod.GET)
    @ApiOperation(value = "查询明细接口", notes = "查询明细")
    @LogAnnotation(value = "查询明细", isWrite = true)
    @ResponseBody
    public Object getSystemItem(TDicBase tDicBase, Page page) {
        return dicBaseService.getSystemItem(tDicBase, page);
    }
    @RequestMapping(value = "system/item/lists", method = RequestMethod.GET)
    @ApiOperation(value = "查询系统字典明细列表", notes = "查询系统字典明细列表")
    @LogAnnotation(value = "查询系统字典明细列表", isWrite = true)
    @ResponseBody
    public Object getSystemItemList(TDicBase tDicBase) {
        return dicBaseService.getSystemItemList(tDicBase);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增数据字典接口", notes = "新增数据字典接口")
    @LogAnnotation(value = "新增数据字典接口", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TDicBase tDicBase) {
        return dicBaseService.insert(tDicBase);
    }


    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改数据字典接口", notes = "修改系统或者HIS数据字典接口")
    @LogAnnotation(value = "修改数据字典接口", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TDicBase tDicBase) {
        return dicBaseService.update(tDicBase);
    }

    @RequestMapping(value = "delete", method = RequestMethod.GET)
    @ApiOperation(value = "删除字典", notes = "删除字典")
    @LogAnnotation(value = "删除字典", isWrite = true)
    @ResponseBody
    public Object delete(String dicId) {
        return dicBaseService.delete(dicId);
    }

    @RequestMapping(value = "disable", method = RequestMethod.GET)
    @ApiOperation(value = "大类禁用接口", notes = "大类禁用接口")
    @LogAnnotation(value = "大类禁用接口", isWrite = true)
    @ResponseBody
    public Object disable(String dicId, String status) {
        return dicBaseService.disable(dicId, status);
    }

    @RequestMapping(value = "getFeeDictionaryParent", method = RequestMethod.GET)
    @ApiOperation(value = "查询收费目录大类", notes = "查询收费目录大类")
    @LogAnnotation(value = "查询收费目录大类", isWrite = true)
    @ResponseBody
    public Object getFeeDictionaryParent() {
        return dicBaseService.getFeeDictionaryParent();
    }


    @ApiOperation(value = "查询数据字典列表", response = DicData.class)
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    public ResEntity getList(@RequestBody DicData dicData) {
        return dicBaseService.getList(dicData);
    }
}