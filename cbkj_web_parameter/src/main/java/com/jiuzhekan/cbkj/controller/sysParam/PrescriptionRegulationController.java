package com.jiuzhekan.cbkj.controller.sysParam;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysParam.PersionalityParams;
import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulation;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.sysParam.PrescriptionRegulationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2024/9/11 10:18
 */
@RestController
@Api(value = "审方规则配置", tags = "审方规则配置")
public class PrescriptionRegulationController {

    private final PrescriptionRegulationService prescriptionRegulationService;

    @Autowired
    PrescriptionRegulationController(PrescriptionRegulationService prescriptionRegulationService){
        this.prescriptionRegulationService=prescriptionRegulationService;
    }


    @RequestMapping(value = "prescriptionRegulation/getCondition", method = RequestMethod.GET)
    @LogAnnotation(value = "条件搜索", isWrite = true)
    @ApiOperation(value = "条件搜索", notes = "条件搜索")
    public Object getCondition(String name){
        return prescriptionRegulationService.getCondition(name);
    }

    @RequestMapping(value = "prescriptionRegulation/getEvent", method = RequestMethod.GET)
    @LogAnnotation(value = "事件搜索", isWrite = true)
    @ApiOperation(value = "事件搜索", notes = "事件搜索")
    public Object getEvent(){
        return prescriptionRegulationService.getEvent();
    }

    @RequestMapping(value = "prescriptionRegulation/insertOrUpdate", method = RequestMethod.POST)
    @LogAnnotation(value = "审方规则配置新增修改", isWrite = true)
    @ApiOperation(value = "审方规则配置新增修改", notes = "审方规则配置新增修改")
    public Object prescriptionRegulationInsertOrUpdate(@RequestBody List<PrescriptionRegulation> prescriptionRegulation){
        return prescriptionRegulationService.prescriptionRegulationInsertOrUpdate(prescriptionRegulation);
    }

    @RequestMapping(value = "prescriptionRegulation/getPrescriptionRegulation", method = RequestMethod.GET)
    @LogAnnotation(value = "审方规则配置条件", isWrite = true)
    @ApiOperation(value = "审方规则配置条件", notes = "审方规则配置条件")
    public ResEntity getPrescriptionRegulation(){
        return prescriptionRegulationService.getPrescriptionRegulation();
    }




}
