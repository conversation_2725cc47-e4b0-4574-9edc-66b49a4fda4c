package com.jiuzhekan.cbkj.controller.business.chargeitem;

import com.jiuzhekan.cbkj.beans.business.chargeitem.InsertChargeItemMapping;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItem;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.TChargeItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/5 14:05
 * @Version 1.0
 */
@Api(value = "收费项目管理", tags = "收费项目管理")
@RestController
@RequestMapping("chargeItem")
public class ChargeItemController {

    private final TChargeItemService chargeItemService;

    public ChargeItemController(TChargeItemService chargeItemService) {
        this.chargeItemService = chargeItemService;
    }

    /**
     * 获取所有的收费目录的charge_item_id和charge_item_name
     */
    @GetMapping("getChargeItemIdAndName")
    @ApiOperation(value = "获取所有的收费目录的charge_item_id和charge_item_name", notes = "获取所有的收费目录的charge_item_id和charge" +
            "_item_name")
    @LogAnnotation("获取所有的收费目录的charge_item_id和charge_item_name")
    public ResEntity getChargeItemIdAndName() {
        return chargeItemService.getChargeItemIdAndName();
    }

    /**
     * 获取收费项目目录列表
     */
    @GetMapping("getChargeItemList")
    @ApiOperation(value = "获取收费项目目录列表", notes = "获取收费项目目录列表", response = TChargeItem.class)
    @LogAnnotation("获取收费项目目录列表")
    public Object getChargeItemList(String chargeItemName, Page page) {
        return chargeItemService.getChargeItemList(chargeItemName,page);
    }

    /**
     * 新增收费目录
     */
    @PostMapping("insertChargeItem")
    @ApiOperation(value = "新增收费目录", notes = "新增收费目录")
    @LogAnnotation(value = "新增收费目录", isWrite = true)
    public ResEntity insertChargeItem(@RequestBody TChargeItem tChargeItem) {
        return chargeItemService.insertChargeItem(tChargeItem);
    }
    /**
     * 获取某个收费目录详情
     */
    @GetMapping("getChargeItem")
    @ApiOperation(value = "获取某个收费目录详情", notes = "获取某个收费目录详情", response = TChargeItem.class)
    @LogAnnotation("获取某个收费目录详情")
    public ResEntity getChargeItem(String chargeItemId) {
        return chargeItemService.findObj(chargeItemId);
    }

    /**
     * 修改收费目录
     */
    @PostMapping("updateChargeItem")
    @ApiOperation(value = "修改收费目录", notes = "修改收费目录")
    @LogAnnotation(value = "修改收费目录", isWrite = true)
    public ResEntity updateChargeItem(@RequestBody TChargeItem tChargeItem) {
        return chargeItemService.updateChargeItem(tChargeItem);
    }
    /**
     * 删除收费目录
     */
    @GetMapping("deleteChargeItem")
    @ApiOperation(value = "删除收费目录(仅对状态修改)", notes = "删除收费目录(仅对状态修改)")
    @LogAnnotation(value = "删除收费目录(仅对状态修改)", isWrite = true)
    public ResEntity deleteChargeItem(String chargeItemId) {
        return chargeItemService.deleteChargeItem(chargeItemId);
    }

    /**
     * 收费项目机构应用权限配置
     */
    @PostMapping("insertChargeItemMapping")
    @ApiOperation(value = "收费项目机构应用权限配置", notes = "收费项目机构应用权限配置列表:只存选中的，如果是机构中所有科室都选中的那就传机构就行不要传科室，如果机构下的个别科室没选中那就传所有选中的科室。参考tPharmacy/savePharmacyConfig接口入参")
    @LogAnnotation(value = "收费项目机构应用权限配置", isWrite = true)
    public ResEntity insertChargeItemMapping(@RequestBody InsertChargeItemMapping insertChargeItemMapping) {
        return chargeItemService.insertChargeItemMapping(insertChargeItemMapping);
    }
    /**
     * 获取收费项目机构应用权限配置列表
     */
    @GetMapping("getChargeItemMappingList")
    @ApiOperation(value = "获取收费项目机构应用权限配置列表", notes = "获取收费项目机构应用权限配置列表",response = InsertChargeItemMapping.class)
    @LogAnnotation("获取收费项目机构应用权限配置列表")
    public ResEntity getChargeItemMappingList(String chargeItemId) {
        return chargeItemService.getChargeItemMappingList(chargeItemId);
    }




}
