package com.jiuzhekan.cbkj.controller.business.displaydosage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/6 11:28
 * @Version 1.0
 */
public enum DisplayDosageFormList {
    /**
     *
     *    按剂收取：按照处方剂数，计算公式：数量=剂数；总价=剂数*单价；
     *
     *    按斤收取：按照处方总重量收取，计算公式：数量=(处方总重量g/500)；总价=(处方总重量g/500)*单价；
     *
     *    按药味数收取：按照药味数收取，计算公式：数量=药味数；总价=药味数*单价；
     *
     *    按克收费：按照处方总重量收取，计算公式：数量=处方总重量；总价=(处方总重量g)*单价；
     *
     *    按次收取：处方张数计价，计算公式:数量=1；总价=1*单价
     *
     *    按百克收取：按照处方总重量收取，计算公式：数量=(处方总重量g/100)；总价=(处方总重量g/100)*单价
     */
    BY_JI(1, "按剂收取","计算公式：数量=剂数；总价=剂数*单价"),
    BY_JIN(2, "按斤收取","按照处方总重量收取，计算公式：数量=(处方总重量g/500)；总价=(处方总重量g/500)*单价"),
    BY_WEI(3, "按药味数收取","按照药味数收取，计算公式：数量=药味数；总价=药味数*单价"),
    BY_GE(4, "按克收费","按照处方总重量收取，计算公式：数量=处方总重量；总价=(处方总重量g)*单价"),
    BY_CE(5, "按次收取","处方张数计价，计算公式:数量=1；总价=1*单价"),
    BY_BAI_KE(6, "按百克收取","按照处方总重量收取，计算公式：数量=(处方总重量g/500)；总价=(处方总重量g/500)*单价");

    private Integer code;
    private String name;
    private String content;
    DisplayDosageFormList(Integer code, String name,String content) {
        this.code = code;
        this.name = name;
        this.content = content;
    }
    /**
     * 获取所有的code和name返回List
     */
    public static List<Map<String, Object>> getAllCodeAndName() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DisplayDosageFormList item : DisplayDosageFormList.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", item.code);
            map.put("name", item.name);
            map.put("content", item.content);
            if (item.code == 2 || item.code == 4) {
                ArrayList<HashMap<Object, Object>> hashMaps = new ArrayList<>();
                HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("code", DisplayDosageFormComputer.SI_SHE_WU_RU.getCode());
                objectObjectHashMap.put("name", DisplayDosageFormComputer.SI_SHE_WU_RU.getName());
                objectObjectHashMap.put("content", DisplayDosageFormComputer.SI_SHE_WU_RU.getContent());
                hashMaps.add(objectObjectHashMap);
                HashMap<Object, Object> objectObjectHashMap2 = new HashMap<>();
                objectObjectHashMap2.put("code", DisplayDosageFormComputer.XIANG_QIAN_QU_ZHENG.getCode());
                objectObjectHashMap2.put("name", DisplayDosageFormComputer.XIANG_QIAN_QU_ZHENG.getName());
                objectObjectHashMap2.put("content", DisplayDosageFormComputer.XIANG_QIAN_QU_ZHENG.getContent());
                hashMaps.add(objectObjectHashMap2);
                HashMap<Object, Object> objectObjectHashMap3 = new HashMap<>();
                objectObjectHashMap3.put("code", DisplayDosageFormComputer.XIANG_HOU_QU_ZHENG.getCode());
                objectObjectHashMap3.put("name", DisplayDosageFormComputer.XIANG_HOU_QU_ZHENG.getName());
                objectObjectHashMap3.put("content", DisplayDosageFormComputer.XIANG_HOU_QU_ZHENG.getContent());
                hashMaps.add(objectObjectHashMap3);

                map.put("numComputer",hashMaps);
            }
            list.add(map);
        }
        return list;
    }
}
