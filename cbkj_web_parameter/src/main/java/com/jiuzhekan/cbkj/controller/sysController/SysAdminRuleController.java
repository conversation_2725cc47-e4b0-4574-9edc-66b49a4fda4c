package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminRule;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRuleMenuVo;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRuleVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysService.AdminRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Api(value = "角色接口", tags = "角色接口")
@Controller
@RequestMapping("sys")
public class SysAdminRuleController {
    private final AdminRuleService adminRuleService;

    @Autowired
    SysAdminRuleController(AdminRuleService adminRuleService){
        this.adminRuleService = adminRuleService;
    }
    @RequestMapping(value = "rule/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取角色", notes = "分页获取角色", response = AdminRule.class)
    @LogAnnotation(value = "分页获取角色")
    @ResponseBody
    public Object getApps(String keyWord, Page page) {
        return adminRuleService.getPageDatas(keyWord, page);
    }

    @RequestMapping(value = "rule/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "获取角色详情", notes = "获取角色详情")
    @LogAnnotation(value = "获取角色详情")
    @ResponseBody
    public Object getObj(String id) {
        return adminRuleService.findObj(id);
    }

    @RequestMapping(value = "rule/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增角色", notes = "新增角色")
    @LogAnnotation(value = "新增角色", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody AdminRuleVo adminRuleVo) {
        return adminRuleService.insert(adminRuleVo);
    }

    @RequestMapping(value = "rule/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改角色", notes = "修改角色")
    @LogAnnotation(value = "修改角色", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody AdminRuleVo adminRuleVo) {
        return adminRuleService.update(adminRuleVo);
    }

    @RequestMapping(value = "rule/deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除角色", notes = "删除角色")
    @LogAnnotation(value = "删除角色", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return adminRuleService.deleteLis(ids);
    }

    @RequestMapping(value = "rule/authority/findMenu", method = RequestMethod.GET)
    @ApiOperation(value = "加载角色菜单", notes = "加载角色菜单")
    @LogAnnotation(value = "加载角色菜单")
    @ResponseBody
    public Object findMenu(String id) {
        return adminRuleService.findMenuByMID(id, "2", true);
    }

    @RequestMapping(value = "rule/authority", method = RequestMethod.GET)
    @ApiOperation(value = "权限设置", notes = "加载角色菜单")
    @LogAnnotation(value = "权限设置", isWrite = true)
    @ResponseBody
    public Object authority(String menuIds, String roleId) {
        return adminRuleService.insertauthority(menuIds, roleId);
    }

    @RequestMapping(value = "rule/getAllMenu", method = RequestMethod.GET)
    @ApiOperation(value = "获取不同系统菜单", notes = "获取不同系统菜单")
    @LogAnnotation(value = "获取不同系统菜单", isWrite = true)
    @ResponseBody
    public Object getSystemMenu(String modelCode) {
        return adminRuleService.getSystemMenu(modelCode);
    }

    @RequestMapping(value = "rule/getAdminRuleMenu", method = RequestMethod.POST)
    @ApiOperation(value = "获取角色菜单配置信息", notes = "获取角色菜单配置信息")
    @LogAnnotation(value = "获取角色菜单配置信息", isWrite = true)
    @ResponseBody
    public Object getAdminRuleMenu(String roleId) {
        return adminRuleService.getAdminRuleMenu(roleId);
    }
    @RequestMapping(value = "rule/saveAdminRuleMenu", method = RequestMethod.POST)
    @ApiOperation(value = "保存角色菜单配置信息", notes = "保存角色菜单配置信息")
    @LogAnnotation(value = "保存角色菜单配置信息", isWrite = true)
    @ResponseBody
    public Object saveAdminRuleMenu(@RequestBody AdminRuleMenuVo adminRuleMenuVo) {
        return adminRuleService.saveAdminRuleMenu(adminRuleMenuVo);
    }

}