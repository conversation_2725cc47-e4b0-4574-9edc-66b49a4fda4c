package com.jiuzhekan.cbkj.controller.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayVo;
import com.jiuzhekan.cbkj.beans.pharmacy.TPharmacyVo;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.pharmacy.TPharmacy;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.dic.GetDeptTree;


import com.jiuzhekan.cbkj.service.pharmacy.PharmacyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@Api(value = "药房表接口", tags = "药房表接口")
@RequestMapping("tPharmacy")
/**
 * <AUTHOR>
 */
public class PharmacyController {


    private PharmacyService pharmacyService;


    private GetDeptTree getDeptTree;

    @Autowired
    PharmacyController(PharmacyService pharmacyService, GetDeptTree getDeptTree) {
        this.pharmacyService = pharmacyService;
        this.getDeptTree = getDeptTree;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询药房表", notes = "分页查询药房表", response = TPharmacy.class)
    @LogAnnotation("分页查询药房表")
    @ResponseBody
    public Object getApps(TPharmacy tPharmacy, Page page) {
        return pharmacyService.getPageDatas(tPharmacy, page);
    }

    @RequestMapping(value = "getSuperiorPharmacy", method = RequestMethod.GET)
    @ApiOperation(value = "获取上级药房", notes = "获取上级药房", response = TPharmacy.class)
    @LogAnnotation("获取上级药房")
    @ResponseBody
    public Object getSuperiorPharmacy(String phaId) {
        return pharmacyService.getSuperiorPharmacy(phaId);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载药房表详情", notes = "加载药房表详情", response = TPharmacy.class)
    @LogAnnotation("加载药房表详情")
    @ResponseBody
    public Object getObj(String id) {
        return pharmacyService.findObj(id);
    }
    @RequestMapping(value = "getAllPharmacy", method = RequestMethod.GET)
    @ApiOperation(value = "获取所有药房列表", notes = "获取所有药房列表", response = TPharmacy.class)
    @LogAnnotation("获取所有药房列表")
    @ResponseBody
    public Object getAllPharmacy() {
        return pharmacyService.getAllPharmacy();
    }
    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增药房表", notes = "新增药房表")
    @LogAnnotation(value = "新增药房表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TPharmacyVo tPharmacyVo) {
        return pharmacyService.insert(tPharmacyVo);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改药房表", notes = "修改药房表详情")
    @LogAnnotation(value = "修改药房表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TPharmacyVo tPharmacyVo) {
        return pharmacyService.update(tPharmacyVo);
    }

    @RequestMapping(value = "deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除药房表", notes = "删除药房表")
    @LogAnnotation(value = "删除药房表", isWrite = true)
    @ResponseBody
    public Object deleteLis(String id) throws Exception {
        return pharmacyService.deleteLis(id);
    }

    @RequestMapping(value = "getMaxNum", method = RequestMethod.GET)
    @ApiOperation(value = "获取药房最大序号", notes = "获取药房最大序号")
    @LogAnnotation(value = "获取药房最大序号", isWrite = true)
    @ResponseBody
    public Object getMaxNum(String drugId) throws Exception {
        return pharmacyService.getMaxNum(drugId);
    }

    @ApiOperation(value = "获取应用配置科室树", notes = "获取应用配置科室树", response = SysDepartment.class)
    @RequestMapping(value = "getDeptTree", method = RequestMethod.GET)
    @ResponseBody
    public Object getDeptTree() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, getDeptTree.getDeptTree1());
    }

    @ApiOperation(value = "获取药房应用配置", notes = "保存药房应用配置")
    @RequestMapping(value = "getPharmacyConfig", method = RequestMethod.POST)
    @ResponseBody
    public Object getPharmacyConfig(@RequestBody String phaId) {
        return pharmacyService.getPharmacyConfig(phaId);
    }

    @ApiOperation(value = "保存药房应用配置", notes = "保存药房应用配置")
    @RequestMapping(value = "savePharmacyConfig", method = RequestMethod.POST)
    @ResponseBody
    public Object savePharmacyConfig(@RequestBody TDisplayVo tDisplayVo) {
        return pharmacyService.savePharmacyConfig(tDisplayVo);
    }

    @ApiOperation(value = "获取药房剂型列表信息", notes = "获取药房剂型列表信息")
    @RequestMapping(value = "getPharmacyItemList", method = RequestMethod.GET)
    @ResponseBody
    public Object getPharmacyItemList(String parentId) {
        return pharmacyService.getPharmacyItemList(parentId);
    }

    @ApiOperation(value = "根据药房ID获取药房剂型列表信息", notes = "根据药房ID获取药房剂型列表信息")
    @RequestMapping(value = "getDisplayList", method = RequestMethod.GET)
    @ResponseBody
    public Object getDisplayList( String phaId) {
        return pharmacyService.getDisplayList(phaId);
    }
    @ApiOperation(value = "获取所有物理药房的下的所有剂型", notes = "获所有取物理药房下的所有剂型")
    @RequestMapping(value = "getAllPharmacyDisplayList", method = RequestMethod.GET)
    @ResponseBody
    public Object getAllPharmacyDisplayList() {
        return pharmacyService.getAllPharmacyDisplayList();
    }
}