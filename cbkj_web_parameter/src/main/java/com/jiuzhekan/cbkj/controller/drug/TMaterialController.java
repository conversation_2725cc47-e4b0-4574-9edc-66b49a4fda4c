package com.jiuzhekan.cbkj.controller.drug;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.drug.TMaterial;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.drug.TMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "药品表接口", tags = "药品表接口")
@RequestMapping("tMaterial")
public class TMaterialController {

    @Autowired
    private TMaterialService tMaterialService;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询药品表", notes = "分页查询药品表", response = TMaterial.class)
    @LogAnnotation("分页查询药品表")
    @ResponseBody
    public Object getApps(TMaterial tMaterial, Page page){
        return tMaterialService.getPageDatas(tMaterial,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载药品表详情", notes = "加载药品表详情", response = TMaterial.class)
    @LogAnnotation("加载药品表详情")
    @ResponseBody
    public Object getObj(String id){
        return tMaterialService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增药品表", notes = "新增药品表")
    @LogAnnotation(value = "新增药品表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TMaterial tMaterial) {
        return tMaterialService.insert(tMaterial);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改药品表", notes = "修改药品表详情")
    @LogAnnotation(value = "修改药品表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TMaterial tMaterial) {
        return tMaterialService.update(tMaterial);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除药品表", notes = "删除药品表")
    @LogAnnotation(value = "删除药品表", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return tMaterialService.deleteLis(ids);
    }

}