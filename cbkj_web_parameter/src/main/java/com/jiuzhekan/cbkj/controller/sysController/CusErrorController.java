//package com.jiuzhekan.cbkj.controller.sysController;
//
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.web.bind.annotation.RequestMapping;
//
////@Controller
//public class CusErrorController {
//
//    @RequestMapping("400")
//    public String toError1(String code,Model model){
//        model.addAttribute("code",code);
//        return "error/400";
//    }
//
//    @RequestMapping("403")
//    public String toError2(){
//        return "error/403";
//    }
//
//    @RequestMapping("sys/403")
//    public String toError3(){
//        return "error/403";
//    }
//
//}