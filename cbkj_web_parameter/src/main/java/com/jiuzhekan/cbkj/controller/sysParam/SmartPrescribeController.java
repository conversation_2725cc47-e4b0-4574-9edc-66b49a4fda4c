package com.jiuzhekan.cbkj.controller.sysParam;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamDTO;
import com.jiuzhekan.cbkj.beans.sysParam.DiagnosisParamNewVO;
import com.jiuzhekan.cbkj.beans.sysParam.PersionalityParams;
import com.jiuzhekan.cbkj.beans.sysParam.SysParamSearch;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.parameter.smartprescrib.DiagnosisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "参数配置", tags = "参数配置")
public class SmartPrescribeController {
    private final DiagnosisService diagnosisService;
    @Autowired
    SmartPrescribeController(DiagnosisService diagnosisService) {
        this.diagnosisService = diagnosisService;
    }

    @RequestMapping(value = "smart/prescribing/get", method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @LogAnnotation(value = "参数配置-获取", isWrite = true)
    @ApiOperation(value = "参数配置-获取", notes = "参数配置")
    public Object getDiagnosis(@RequestBody DiagnosisParamDTO sysParam){
        String menuId = sysParam.getMenuId();
        if (StringUtils.isBlank(menuId)){
            return ResEntity.entity(false, "缺少menuId", sysParam);
        }
        return diagnosisService.getParameterList(sysParam);
    }

    @RequestMapping(value = "smart/prescribing/save", method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @LogAnnotation(value = "参数配置-保存", isWrite = true)
    @ApiOperation(value = "参数配置-保存", notes = "参数配置-保存")
    public Object saveDiagnosis(@RequestBody DiagnosisParamNewVO tSysParamVO){
        return diagnosisService.saveParameterList(tSysParamVO);
    }

    @RequestMapping(value = "smart/prescribing/search", method = RequestMethod.GET)
    @LogAnnotation(value = "参数配置-搜索", isWrite = true)
    @ApiOperation(value = "参数配置-搜索", notes = "参数配置-搜索")
    public Object searchParams(SysParamSearch sysParamSearch, Page page){
        return diagnosisService.searchParam(sysParamSearch,page);
    }

    @RequestMapping(value = "smart/prescribing/getPersonalityParameters", method = RequestMethod.GET)
    @LogAnnotation(value = "参数配置-获取个性参数配置", isWrite = true)
    @ApiOperation(value = "参数配置-获取个性参数配置", notes = "参数配置-个性参数配置")
    public Object getPersonalityParameters(String parCode){
        return diagnosisService.getPersonalityParameters(parCode);
    }

    @RequestMapping(value = "smart/prescribing/savePersonalityParameters", method = RequestMethod.POST)
    @LogAnnotation(value = "参数配置-个性参数保存", isWrite = true)
    @ApiOperation(value = "参数配置-个性参数保存", notes = "参数配置-个性参数保存")
    public Object savePersonalityParameters(@RequestBody PersionalityParams persionalityParams){
        return diagnosisService.savePersonalityParameters(persionalityParams);
    }
}
