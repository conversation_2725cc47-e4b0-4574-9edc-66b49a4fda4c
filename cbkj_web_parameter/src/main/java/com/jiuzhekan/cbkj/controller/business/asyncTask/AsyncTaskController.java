package com.jiuzhekan.cbkj.controller.business.asyncTask;

import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTask;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItem;
import com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItemQuery;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.asyncTask.AsyncTaskItemService;
import com.jiuzhekan.cbkj.service.business.asyncTask.AsyncTaskService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Controller
@Api(value = "同步任务和日志接口", tags = "同步任务和日志接口")
@RequestMapping("asyncTask")
public class AsyncTaskController {

    private final AsyncTaskService asyncTaskService;
    private final AsyncTaskItemService asyncTaskItemService;
    @Autowired
    public AsyncTaskController(AsyncTaskService asyncTaskService,
                               AsyncTaskItemService asyncTaskItemService){
        this.asyncTaskService = asyncTaskService;
        this.asyncTaskItemService = asyncTaskItemService;
    }

    @RequestMapping(value = "getAsyncPages", method = RequestMethod.GET)
    @ApiOperation(value = "同步任务-分页查询", notes = "同步任务-分页查询", response = AsyncTask.class)
    @LogAnnotation("同步任务-分页查询")
    @ResponseBody
    public Object getApps(AsyncTask asyncTask, Page page){
        return asyncTaskService.getPageDatas(asyncTask,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "同步任务-加载详情", notes = "同步任务-加载详情", response = AsyncTask.class)
    @LogAnnotation("同步任务-加载详情")
    @ResponseBody
    public Object getObj(String id){
        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        return asyncTaskService.findObj(id);
    }

    @RequestMapping(value = "insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "同步任务-新增或者更新", notes = "同步任务-新增或者更")
    @LogAnnotation(value = "同步任务-新增或者更", isWrite = true)
    @ResponseBody
    public ResEntity insert(@RequestBody AsyncTask asyncTask) {
        return asyncTaskService.insertOrUpdate(asyncTask);
    }



    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "同步任务-删除", notes = "同步任务-删除")
    @LogAnnotation(value = "同步任务-删除", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return asyncTaskService.deleteLis(ids);
    }

    @RequestMapping(value="statechange", method = RequestMethod.GET)
    @ApiOperation(value = "同步任务-更改状态", notes = "同步任务-更改状态")
    @LogAnnotation(value = "同步任务-更改状态", isWrite = true)
    @ResponseBody
    public Object statechange(String asyncId,String status) throws Exception {
        return asyncTaskService.statechange(asyncId,status);
    }

    @RequestMapping(value="addOnceTask", method = RequestMethod.GET)
    @ApiOperation(value = "同步任务-手动添加", notes = "同步任务-手动添加")
    @LogAnnotation(value = "同步任务-手动添加", isWrite = true)
    @ResponseBody
    public Object addOnceTask(String asyncTaskId) {
        return asyncTaskService.addOnceTask(asyncTaskId);
    }

    @RequestMapping(value = "getAsyncItemPages", method = RequestMethod.GET)
    @ApiOperation(value = "任务日志-分页查询", notes = "任务日志-分页查询", response = AsyncTaskItem.class)
    @LogAnnotation("任务日志-分页查询")
    @ResponseBody
    public Object getAsyncItemsList(AsyncTaskItemQuery asyncTask, Page page){
        return asyncTaskItemService.getPageData(asyncTask,page);
    }
    @RequestMapping(value = "getAsyncItemDetail", method = RequestMethod.GET)
    @ApiOperation(value = "任务日志-查询详情", notes = "任务日志-查询详情", response = AsyncTaskItem.class)
    @LogAnnotation("任务日志-查询详情")
    @ResponseBody
    public Object getAsyncItemDetail(String taskItemId){
        return asyncTaskItemService.findObj(taskItemId);
    }



}