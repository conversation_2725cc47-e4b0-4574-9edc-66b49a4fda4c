package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysService.LogentityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Api(value = "异常日志", tags = "异常日志")
@Controller
@RequestMapping("log")
public class LogentityController {

    private final LogentityService logentityService;

    @Autowired
    LogentityController(LogentityService logentityService) {
        this.logentityService = logentityService;
    }

    @RequestMapping(value = "logentity/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "异常日志分页", notes = "异常日志分页", response = Logentity.class)
    @ResponseBody
    public Object getApps(Logentity logentity, Page page) {
        Object obj = logentityService.getPageDatas(logentity, page);
        return obj;
    }

    @RequestMapping(value = "logentity/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "异常日志详情", notes = "异常日志详情", response = Logentity.class)
    @ResponseBody
    public Object getObj(String id) {
        return logentityService.findObj(id);
    }

    @RequestMapping(value = "logentity/deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "异常日志删除", notes = "异常日志删除")
    @LogAnnotation(value = "异常日志删除", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) {
        return logentityService.deleteLis(ids);
    }

    @RequestMapping(value = "logentity/changeStatus", method = RequestMethod.GET)
    @ApiOperation(value = "异常日志标记", notes = "异常日志标记")
    @LogAnnotation(value = "标记异常日志记录")
    @ResponseBody
    public Object changeStatus(String ids) {
        return logentityService.changeStatus(ids);
    }

}