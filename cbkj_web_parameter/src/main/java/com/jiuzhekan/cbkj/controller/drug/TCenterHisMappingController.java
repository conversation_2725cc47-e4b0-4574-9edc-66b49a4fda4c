package com.jiuzhekan.cbkj.controller.drug;

import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.drug.TCenterHisMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Controller
@Api(value = "药房的药品和HIS的药品映射表接口", tags = "药房的药品和HIS的药品映射表接口")
@RequestMapping("tCenterHisMapping")
public class TCenterHisMappingController {

    @Autowired
    private TCenterHisMappingService tCenterHisMappingService;


    @RequestMapping(value="materialHISMapping/mapList", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询本地药房和HIS参照", notes = "分页查询本地药房和标准代码映射参照",response = TMaterialHISVO.class)
    @LogAnnotation(value = "分页查询本地药房和标准代码映射参照", isWrite = true)
    @ResponseBody
    public Object getStandardMaterialMapping(TMaterialHISVO materialHisVO, Page page) throws Exception {
        Object datas = tCenterHisMappingService.getPageData2(materialHisVO, page);
        return datas;
    }
    @RequestMapping(value = "materialHISMapping/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增本地药房药品和HIS手动映射", notes = "新增本地药房药品和标准编码手动映射")
    @LogAnnotation(value = "新增本地药房药品和标准编码手动映射", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody THISMaterialMappingVo thisMaterialMappingVo) {
        return tCenterHisMappingService.insert(thisMaterialMappingVo);
    }

    @RequestMapping(value = "materialHISMapping/delete", method = RequestMethod.GET)
    @LogAnnotation(value = "取消本地药房药品和HIS映射关系", isWrite = true)
    @ApiOperation(value = "取消本地药房药品和标准编码映射关系", notes = "取消本地药房药品和标准编码映射关系")
    @ResponseBody
    public Object delete(String mapIds) {
        return tCenterHisMappingService.deleteLis(mapIds);
    }

    @RequestMapping(value = "materialHISMapping/autoMapping", method = RequestMethod.GET)
    @ApiOperation(value = "本地药房药品和HIS自动映射", notes = "本地药房药品和标准编码自动映射")
    @LogAnnotation(value = "本地药房药品和标准编码自动映射")
    @ResponseBody
    public Object autoMapping(StandTMAutoMappingVO standTmAutoMappingVO) {
        return tCenterHisMappingService.autoMapping(standTmAutoMappingVO);
    }

}