package com.jiuzhekan.cbkj.controller.sysapp;

import com.jiuzhekan.cbkj.beans.common.Create;
import com.jiuzhekan.cbkj.beans.sysBeans.ResultEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysapp.SysProductMatrixService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Api(value = "产品矩阵接口", tags = "产品矩阵接口")
@RestController
@RequestMapping("sys/productMatrix")
public class SysProductMatrixController {

    @Autowired
    private SysProductMatrixService sysProductMatrixService;

    @ApiOperation(value = "分页查询产品矩阵信息", notes = "分页查询产品矩阵信息", response = SysProductMatrix.class)
    @LogAnnotation(value = "分页查询产品矩阵信息")
    @GetMapping("/getPages")
    public Object getPages(SysProductMatrix sysProductMatrix, Page page) {
        return sysProductMatrixService.getPageDatas(sysProductMatrix, page);
    }

    @ApiOperation(value = "新增产品矩阵信息", notes = "新增产品矩阵信息",response = Boolean.class)
    @LogAnnotation(value = "新增产品矩阵信息", isWrite = true)
    @PostMapping("/insert")
    public ResultEntity<Boolean> insert(@RequestBody SysProductMatrix sysProductMatrix) {
        boolean insert = sysProductMatrixService.insert(sysProductMatrix);
        return insert?ResultEntity.entity(true, Constant.SUCCESS_DX, insert)
                :ResultEntity.entity(false, "保存失败，数据库异常", insert);
    }

    @ApiOperation(value = "修改产品矩阵信息", notes = "修改产品矩阵信息")
    @LogAnnotation(value = "修改产品矩阵信息", isWrite = true)
    @PostMapping("/update")
    public ResultEntity<SysProductMatrix> update(@RequestBody SysProductMatrix sysProductMatrix) {
        SysProductMatrix update = sysProductMatrixService.update(sysProductMatrix);
        return update!=null?ResultEntity.entity(true, Constant.SUCCESS_DX, update)
                :ResultEntity.entity(false, "修改失败，数据库异常", update);
    }

    @Data
    public static class DelParam{
        @NotBlank(message = "产品矩阵ID不能为空")
        private String productMatrixId;
    }

    @ApiOperation(value = "删除产品矩阵信息", notes = "删除产品矩阵信息")
    @LogAnnotation(value = "删除产品矩阵信息", isWrite = true)
    @PostMapping("/delete")
    public ResultEntity<Boolean> delete(@RequestBody DelParam param) {
        boolean delete = sysProductMatrixService.delete(param.getProductMatrixId());
        return delete?ResultEntity.entity(true, Constant.SUCCESS_DX, delete)
                :ResultEntity.entity(false, "删除失败，数据库异常", delete);
    }

    @ApiOperation(value = "根据ID获取产品矩阵详情", notes = "根据ID获取产品矩阵详情")
    @LogAnnotation(value = "根据ID获取产品矩阵详情")
    @GetMapping("/getById")
    public ResultEntity<SysProductMatrix> getById(String productMatrixId) {
        SysProductMatrix sysProductMatrix = sysProductMatrixService.getById(productMatrixId);
        return ResultEntity.entity(true, Constant.SUCCESS_DX, sysProductMatrix);
    }
    
}
