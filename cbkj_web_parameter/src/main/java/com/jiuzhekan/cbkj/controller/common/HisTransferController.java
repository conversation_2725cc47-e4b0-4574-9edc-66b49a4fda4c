//package com.jiuzhekan.cbkj.controller.common;
//
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
//import com.jiuzhekan.cbkj.controller.common.vo.TransSpecialMatVo;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
///**
// * HIS登录接口
// *
// * <AUTHOR>
// * @date 2020/9/14 09:33
// */
//@Api(value = "HIS登录接口", tags = "HIS登录接口")
//@Controller
//public class HisTransferController {
//
//    @Autowired
//    private THisRecordService tHisRecordService;
//
//    @Autowired
//    private HisSpecialPreService hisSpecialPres;
//
//    @RequestMapping(value = "transfer", method = RequestMethod.GET)
//    @ApiOperation(value = "HIS登录接口", notes = "HIS登录接口")
//    @LogAnnotation(value = "HIS登录接口", isWrite = true)
//    @ResponseBody
//    public Object transferByToken(String token, String preNo) {
//        return tHisRecordService.getHisRecordByToken(token, preNo);
//    }
//
//    @RequestMapping(value = "transfer/deletePre", method = RequestMethod.GET)
//    @ApiOperation(value = "删除处方", notes = "删除处方")
//    @LogAnnotation(value = "删除处方", isWrite = true)
//    @ResponseBody
//    public Object deletePre(String appId, String insCode, String token, String visitNo, String preNo) {
//        return tHisRecordService.deletePres(appId, insCode, token, visitNo, preNo);
//    }
//
//    @RequestMapping(value = "his/hisSpecialPres", method = RequestMethod.GET)
//    @ApiOperation(value = "获取HIS特病治疗方案", notes = "获取HIS特病治疗方案")
//    @LogAnnotation(value = "获取HIS特病治疗方案", isWrite = true)
//    @ResponseBody
//    public Object hisSpecialPres(String registerId) {
//        return hisSpecialPres.hisSpecialPres(registerId);
//    }
//
//    @RequestMapping(value = "his/transSpecialMat", method = RequestMethod.POST)
//    @ApiOperation(value = "HIS特病治疗方案药品转换", notes = "HIS特病治疗方案药品转换")
//    @LogAnnotation(value = "HIS特病治疗方案药品转换", isWrite = true)
//    @ResponseBody
//    public Object transSpecialMat(@RequestBody TransSpecialMatVo vo) {
//        return ResEntity.success(hisSpecialPres.transSpecialMat(vo));
//    }
//
//}
