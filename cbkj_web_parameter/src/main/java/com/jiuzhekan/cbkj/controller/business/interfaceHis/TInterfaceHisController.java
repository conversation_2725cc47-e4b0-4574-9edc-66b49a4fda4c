package com.jiuzhekan.cbkj.controller.business.interfaceHis;

import com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.interfaceHis.TInterfaceHisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Controller
@Api(value = "物理his表接口", tags = "物理his表接口")
@RequestMapping("tInterfaceHis")
public class TInterfaceHisController {

    @Autowired
    private TInterfaceHisService tInterfaceHisService;

    @RequestMapping(value = "getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取第三方（物理his）", notes = "获取第三方（物理his）", response = TInterfaceHis.class)
    @LogAnnotation("获取第三方（物理his）")
    @ResponseBody
    public Object getAll(TInterfaceHis tInterfaceHis){
        return tInterfaceHisService.getDatas(tInterfaceHis);
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询物理his表", notes = "分页查询物理his表", response = TInterfaceHis.class)
    @LogAnnotation("分页查询物理his表")
    @ResponseBody
    public Object getApps(TInterfaceHis tInterfaceHis, Page page){
        return tInterfaceHisService.getPageDatas(tInterfaceHis,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载物理his表详情", notes = "加载物理his表详情", response = TInterfaceHis.class)
    @LogAnnotation("加载物理his表详情")
    @ResponseBody
    public Object getObj(String id){
        return tInterfaceHisService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增物理his表", notes = "新增物理his表")
    @LogAnnotation(value = "新增物理his表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TInterfaceHis tInterfaceHis) {
        return tInterfaceHisService.insert(tInterfaceHis);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改物理his表", notes = "修改物理his表详情")
    @LogAnnotation(value = "修改物理his表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TInterfaceHis tInterfaceHis) {
        return tInterfaceHisService.update(tInterfaceHis);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除物理his表", notes = "删除物理his表")
    @LogAnnotation(value = "删除物理his表", isWrite = true)
    @ResponseBody
    public Object deleteLis(String id) throws Exception {
        return tInterfaceHisService.deleteLis(id);
    }

    @RequestMapping(value = "testConnect", method = RequestMethod.POST)
    @ApiOperation(value = "测试连接", notes = "测试连接")
    @LogAnnotation(value = "测试连接", isWrite = true)
    @ResponseBody
    public Object testConnect(@RequestBody TInterfaceHis tInterfaceHis) {
        return tInterfaceHisService.testConnect(tInterfaceHis);
    }

}