package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.exception.CustomRuntimeException;
import com.jiuzhekan.cbkj.common.exception.ExceptionEnum;
import com.jiuzhekan.cbkj.common.utils.Constant;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.dao.RecoverableDataAccessException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.SocketTimeoutException;
import java.util.List;

/**
 * 全局异常
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    @SneakyThrows
    public Object exceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception e) {

        if (e instanceof CustomRuntimeException) {
            CustomRuntimeException ce = (CustomRuntimeException) e;
            return ResEntity.error(ce.getCode(), ce.getMessage());
        }

        if (e instanceof QueryTimeoutException) {
            return ResEntity.error("redis连接超时");
        }
        if (e instanceof UsernameNotFoundException) {
            return ResEntity.error("用户名或密码错误！");
        }
        if (e instanceof RecoverableDataAccessException) {
            return ResEntity.error("数据库连接超时");
        }

        String message = Constant.ERRORNOTTROW;
        if (e instanceof SocketTimeoutException) {
            message = "网络请求超时！请稍后重试";
        }
        if (response.getStatus() == HttpStatus.SC_UNAUTHORIZED) {
            response.sendRedirect("login");
        }
        return ResEntity.entity(false, message, response.getStatus());

    }

}