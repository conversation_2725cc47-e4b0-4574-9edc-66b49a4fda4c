package com.jiuzhekan.cbkj.controller.business.chargeitem;

import com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemDetailListByChargeItemId;
import com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemList;
import com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetail;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.TChargeItemDetailService;
import com.jiuzhekan.cbkj.service.common.PublicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/5 14:13
 * @Version 1.0
 */
@Api(value = "收费项目目录详情数据管理", tags = "收费项目目录详情数据管理")
@RestController
@RequestMapping("chargeItemDetail")
public class ChargeItemDetailController {

    private final PublicService publicService;
    private final TChargeItemDetailService tChargeItemDetailService;

    public ChargeItemDetailController(PublicService publicService, TChargeItemDetailService tChargeItemDetailService) {
        this.publicService = publicService;
        this.tChargeItemDetailService = tChargeItemDetailService;
    }

    /**
     * 根据收费目录id或者关键字获取目录下的所有收费项目列表
     */
    @GetMapping("getChargeItemDetailListByChargeItemId")
    @ApiOperation(value = "根据收费目录id或者关键字获取目录下的所有收费项目列表", notes = "根据收费目录id或者关键字获取目录下的所有收费项目列表",response = TChargeItemDetail.class)
    @LogAnnotation("根据收费目录id或者关键字获取目录下的所有收费项目列表")
    public ResEntity getChargeItemDetailListByChargeItemId(GetChargeItemDetailListByChargeItemId bean) {
        return tChargeItemDetailService.getChargeItemDetailListByChargeItemId(bean);
    }

    /**
     * 获取收费项目目录详情数据列表
     */
    @PostMapping("getChargeItemDetailList")
    @ApiOperation(value = "获取收费项目详情数据列表", notes = "获取收费项目详情数据列表",response = TChargeItemDetail.class)
    @LogAnnotation(value = "获取收费项目详情数据列表", isWrite = true)
    public Object getChargeItemDetailList(@RequestBody GetChargeItemList getChargeItemList) {
        return tChargeItemDetailService.getChargeItemDetailList(getChargeItemList);
    }
    /**
     * 新增收费项目目录详情数据
     */
    @PostMapping("insertChargeItemDetail")
    @ApiOperation(value = "新增收费项目目录详情数据", notes = "新增收费项目目录详情数据")
    @LogAnnotation(value = "新增收费项目目录详情数据", isWrite = true)
    public ResEntity insertChargeItemDetail(@RequestBody TChargeItemDetail tChargeItemDetail) {
        return tChargeItemDetailService.insertChargeItemDetail(tChargeItemDetail);
    }

    /**
     * 获取收费项目目录单条详情数据详细信息
     */
    @GetMapping("getChargeItemDetail")
    @ApiOperation(value = "获取收费项目目录单条详情数据详细信息", notes = "获取收费项目目录单条详情数据详细信息",response = TChargeItemDetail.class)
    @LogAnnotation(value = "获取收费项目目录单条详情数据详细信息", isWrite = true)
    public ResEntity getChargeItemDetail(@RequestParam String chargeItemDetailId) {
        return tChargeItemDetailService.findObj(chargeItemDetailId);
    }

    /**
     * 修改收费项目目录详情数据
     */
    @PostMapping("updateChargeItemDetail")
    @ApiOperation(value = "修改收费项目目录详情数据", notes = "修改收费项目目录详情数据")
    @LogAnnotation(value = "修改收费项目目录详情数据", isWrite = true)
    public ResEntity updateChargeItemDetail(@RequestBody TChargeItemDetail tChargeItemDetail) {
        return tChargeItemDetailService.updateChargeItemDetail(tChargeItemDetail);
    }

    /**
     * 删除收费项目目录详情数据
     */
    @GetMapping("deleteChargeItemDetail")
    @ApiOperation(value = "删除收费项目目录详情数据", notes = "删除收费项目目录详情数据")
    @LogAnnotation(value = "删除收费项目目录详情数据", isWrite = true)
    public ResEntity deleteChargeItemDetail(@RequestParam String chargeItemDetailId) {
        return tChargeItemDetailService.deleteChargeItemDetail(chargeItemDetailId);
    }

    /**
     * 获取所有科室数据
     */
//    @GetMapping("getAllDeptList")
//    @ApiOperation(value = "获取所有科室数据", notes = "获取所有科室数据")
//    public ResEntity getAllDeptList() {
//        publicService.getDeptTree(null, null);
//    }

}
