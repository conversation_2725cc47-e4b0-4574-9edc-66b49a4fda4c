package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.sysBeans.SysLogInterfaceError;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysService.SysLogInterfaceErrorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

/**
 * <AUTHOR>
 */
@Controller
@Api(value = "接口错误日志接口", tags = "接口错误日志接口")
@RequestMapping("sysLogInterfaceError")
public class SysLogInterfaceErrorController {
    private final SysLogInterfaceErrorService sysLogInterfaceErrorService;
    @Autowired
    SysLogInterfaceErrorController(SysLogInterfaceErrorService sysLogInterfaceErrorService){
        this.sysLogInterfaceErrorService = sysLogInterfaceErrorService;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询接口错误日志", notes = "分页查询接口错误日志", response = SysLogInterfaceError.class)
    @LogAnnotation("分页查询接口错误日志")
    @ResponseBody
    public Object getApps(SysLogInterfaceError sysLogInterfaceError, Page page){
        return sysLogInterfaceErrorService.getPageDatas(sysLogInterfaceError,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载接口错误日志详情", notes = "加载接口错误日志详情", response = SysLogInterfaceError.class)
    @LogAnnotation("加载接口错误日志详情")
    @ResponseBody
    public Object getObj(String id){
        return sysLogInterfaceErrorService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增接口错误日志", notes = "新增接口错误日志")
    @LogAnnotation(value = "新增接口错误日志", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody SysLogInterfaceError sysLogInterfaceError) {
        return sysLogInterfaceErrorService.insert(sysLogInterfaceError);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改接口错误日志", notes = "修改接口错误日志详情")
    @LogAnnotation(value = "修改接口错误日志", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody SysLogInterfaceError sysLogInterfaceError) {
        return sysLogInterfaceErrorService.update(sysLogInterfaceError);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除接口错误日志", notes = "删除接口错误日志")
    @LogAnnotation(value = "删除接口错误日志", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return sysLogInterfaceErrorService.deleteLis(ids);
    }

}