package com.jiuzhekan.cbkj.controller.drug;

import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.drug.BMaterialService;
import com.jiuzhekan.cbkj.service.drug.TAppMaterialMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "知识库中药信息表接口", tags = "知识库中药信息表接口")
@RequestMapping("bMaterial")
public class BMaterialController {

    @Autowired
    private BMaterialService bMaterialService;
    @Autowired
    private TAppMaterialMappingService tAppMaterialMappingService;

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询知识库中药信息表", notes = "分页查询知识库中药信息表", response = BMaterial.class)
    @LogAnnotation("分页查询知识库中药信息表")
    @ResponseBody
    public Object getApps(BMaterial bMaterial,Page page){
        return bMaterialService.getPageDatas(bMaterial,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载知识库中药信息表详情", notes = "加载知识库中药信息表详情", response = BMaterial.class)
    @LogAnnotation("加载知识库中药信息表详情")
    @ResponseBody
    public Object getObj(String id){
        return bMaterialService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增知识库中药信息表", notes = "新增知识库中药信息表")
    @LogAnnotation(value = "新增知识库中药信息表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody BMaterial bMaterial) {
        return bMaterialService.insert(bMaterial);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改知识库中药信息表", notes = "修改知识库中药信息表详情")
    @LogAnnotation(value = "修改知识库中药信息表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody BMaterial bMaterial) {
        return bMaterialService.update(bMaterial);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除知识库中药信息表", notes = "删除知识库中药信息表")
    @LogAnnotation(value = "删除知识库中药信息表", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return bMaterialService.deleteLis(ids);
    }

    @RequestMapping(value="materialKnowMapping/mapList", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询本地药房和知识库药品映射参照", notes = "分页查询本地药房和知识库药品映射参照",response = TMaterialKnowVO.class)
    @LogAnnotation(value = "分页查询本地药房和知识库药品映射参照", isWrite = true)
    @ResponseBody
    public Object getStandardMaterialMapping(TMaterialKnowVO tMaterialKnowVO,Page page) throws Exception {
        return tAppMaterialMappingService.getDataPage(tMaterialKnowVO,page);
    }
    @RequestMapping(value = "materialKnowMapping/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增本地药房药品和知识库药品手动映射", notes = "新增本地药房药品和知识库药品手动映射")
    @LogAnnotation(value = "新增本地药房药品和知识库药品手动映射", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TKnowMaterialMappingVo tKnowMaterialMappingVo) {
        return tAppMaterialMappingService.insert(tKnowMaterialMappingVo);
    }

    @RequestMapping(value = "materialKnowMapping/delete", method = RequestMethod.GET)
    @LogAnnotation(value = "取消本地药房药品和知识库药品映射关系", isWrite = true)
    @ApiOperation(value = "取消本地药房药品和知识库药品映射关系", notes = "取消本地药房药品和知识库药品映射关系")
    @ResponseBody
    public Object delete(String mapIds) {
        return tAppMaterialMappingService.deleteLis(mapIds);
    }

    @RequestMapping(value = "materialKnowMapping/autoMapping", method = RequestMethod.GET)
    @ApiOperation(value = "本地药房药品和知识库药品自动映射", notes = "本地药房药品和知识库药品自动映射")
    @LogAnnotation(value = "本地药房药品和知识库药品自动映射")
    @ResponseBody
    public Object autoMapping(StandTMAutoMappingVO standTmAutoMappingVO) {
        return tAppMaterialMappingService.autoMapping(standTmAutoMappingVO);
    }
}