package com.jiuzhekan.cbkj.controller.sysapp;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysDepartment;
import com.jiuzhekan.cbkj.beans.sysMange.DepartmentVO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.FileTypeUtil;
import com.jiuzhekan.cbkj.common.utils.Page;

import com.jiuzhekan.cbkj.service.common.ImportInsService;
import com.jiuzhekan.cbkj.service.sysapp.SysDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

@Api(value = "科室接口", tags = "科室接口")
@Controller
@RequestMapping("sys")
/**
 * <AUTHOR>
 */
public class SysDepartmentController {


    private SysDepartmentService sysDepartmentService;


    private ImportInsService importInsService;

    @Autowired
    SysDepartmentController(SysDepartmentService sysDepartmentService, ImportInsService importInsService) {
        this.sysDepartmentService = sysDepartmentService;
        this.importInsService = importInsService;
    }

    @RequestMapping(value = "sysDepartment/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询科室信息", notes = "分页查询科室信息", response = SysDepartment.class)
    @LogAnnotation(value = "分页查询科室信息")
    @ResponseBody
    public Object getApps(SysDepartment sysDepartment, Page page) {
        Object obj = sysDepartmentService.getPageDatas(sysDepartment, page);
        return obj;
    }

    @RequestMapping(value = "sysDepartment/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增科室信息", notes = "新增科室信息")
    @LogAnnotation(value = "新增科室信息", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody DepartmentVO departmentVO) {

        ResEntity result = sysDepartmentService.insert(departmentVO);
        return result;
    }

    @RequestMapping(value = "sysDepartment/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改科室信息", notes = "修改科室信息")
    @LogAnnotation(value = "修改科室信息", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody SysDepartment sysDepartment) {
        ResEntity result = sysDepartmentService.update(sysDepartment);
        return result;
    }

    @RequestMapping(value = "sysDepartment/deleteLis", method = RequestMethod.POST)
    @ApiOperation(value = "删除科室信息", notes = "删除科室信息")
    @LogAnnotation(value = "删除科室信息", isWrite = true)
    @ResponseBody
    public Object deleteLis(@RequestBody String id) throws Exception {
        ResEntity result = sysDepartmentService.deleteLis(id);
        return result;
    }

    @RequestMapping(value = "sysDepartment/queryByAppIdInsCode", method = RequestMethod.GET)
    @ApiOperation(value = "根据appId和InsCode查询科室", notes = "根据appId和InsCode查询科室")
    @LogAnnotation(value = "根据appId和InsCode查询科室", isWrite = true)
    @ResponseBody
    public Object queryByAppIdInsCode(SysDepartment sysDepartment) throws Exception {
        ResEntity result = sysDepartmentService.queryByAppIdInsCode(sysDepartment);
        return result;
    }

    @ApiOperation(value = "EXCEL导入科室数据", notes = "EXCEL导入科室数据")
    @RequestMapping(value = "excel/Department", method = RequestMethod.POST)
    @ResponseBody
    public Object importIns(MultipartFile file) {

        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }
        if (!FileTypeUtil.isExcel(file)) {
            return new ResEntity(false, "仅支持Excel", null);
        }
        return importInsService.importDept(file);
    }
   /* @RequestMapping(value = "sysDepartment/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载科室信息详情", notes = "加载科室信息详情", response = SysDepartment.class)
    @LogAnnotation(value = "加载科室信息详情")
    @ResponseBody
    public Object getObj(String id) {
        ResEntity result = sysDepartmentService.findObj(id);
        return result;
    }*/
}