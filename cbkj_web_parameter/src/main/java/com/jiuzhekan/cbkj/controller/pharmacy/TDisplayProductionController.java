package com.jiuzhekan.cbkj.controller.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayExpressP;
import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayProductionP;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction;
import com.jiuzhekan.cbkj.service.pharmacy.TDisplayProductionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@Api(value = "药房服务配置接口", tags = "药房服务配置接口")
@RequestMapping("tDisplayProduction")
public class TDisplayProductionController {

    @Autowired
    private TDisplayProductionService tDisplayProductionService;

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "制膏-加载药房配置制膏信息表详情", notes = "制膏-加载药房配置制膏信息表详情", response = TDisplayProductionP.class)
    @LogAnnotation("制膏-加载药房配置制膏信息表详情")
    @ResponseBody
    public Object getObj(String displayId){
        return tDisplayProductionService.findObj(displayId);
    }

    @RequestMapping(value = "insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "制膏-保存药房配置制膏信息表", notes = "制膏-保存药房配置制膏信息表")
    @LogAnnotation(value = "制膏-保存药房配置制膏信息表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TDisplayProductionP tDisplayProductionP) {
        return tDisplayProductionService.insertOrUpdate(tDisplayProductionP);
    }

    @RequestMapping(value = "insertBatch", method = RequestMethod.POST)
    @ApiOperation(value = "制膏-批量更新药房配置代煎", notes = "制膏-批量更新药房配置代煎")
    @LogAnnotation(value = "制膏-批量更新药房配置代煎", isWrite = true)
    @ResponseBody
    public Object insertBatch(@RequestBody TDisplayProductionP tDisplayProductionP) {
        ResEntity resEntity = null;
        if (!StringUtils.isBlank(tDisplayProductionP.getPharmacyDisplayList())){
            String[] split = tDisplayProductionP.getPharmacyDisplayList().split(",");
            for (int i = 0; i < split.length; i++) {
                tDisplayProductionP.getHospitalization().setDisplayId(split[i]);
                tDisplayProductionP.getOutpatient().setDisplayId(split[i]);
                 resEntity = tDisplayProductionService.insertOrUpdate(tDisplayProductionP);
                if (!resEntity.getStatus()){
                    break;
                }
            }
        }
        if (null != resEntity && !resEntity.getStatus()){
            return resEntity;
        }
        return ResEntity.success(null);
    }
}