package com.jiuzhekan.cbkj.controller.business.stock;

import com.jiuzhekan.cbkj.beans.business.stock.StockListDo;
import com.jiuzhekan.cbkj.beans.business.stock.TStock;
import com.jiuzhekan.cbkj.beans.business.stock.TStockQuery;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.stock.TStockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Controller
@Api(value = "药品库存表接口", tags = "药品库存表接口")
@RequestMapping("tStock")
public class TStockController {
    private final TStockService tStockService;
    @Autowired
    TStockController(TStockService tStockService){
     this.tStockService = tStockService;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询库存明细", notes = "分页查询库存明细", response = StockListDo.class)
    @LogAnnotation("分页查询库存明细")
    @ResponseBody
    public Object getApps(TStockQuery tStock, Page page){
        return Page.getLayUiTablePageData(tStockService.getStockList(tStock,page));
    }


}