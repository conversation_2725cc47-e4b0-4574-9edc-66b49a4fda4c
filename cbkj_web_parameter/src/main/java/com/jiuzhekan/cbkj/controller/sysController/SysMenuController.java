package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminMenu;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysService.AdminMenuService;
import com.jiuzhekan.cbkj.service.sysService.AdminRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "菜单接口", tags = "菜单接口", position = 4)
@Controller
@RequestMapping("sys")
public class SysMenuController {


    private final AdminMenuService adminMenuService;
    private final AdminRuleService adminRuleService;

    @Autowired
    SysMenuController(AdminMenuService adminMenuService,
                      AdminRuleService adminRuleService){
        this.adminMenuService = adminMenuService;
        this.adminRuleService = adminRuleService;

    }
    @ApiOperation(value = "获取菜单分页列表", notes = "获取菜单分页列表", response = AdminMenu.class)
    @RequestMapping(value = "menu/getPages", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation(value = "获取菜单分页列表")
    public Object getApps(@ApiParam("菜单名称") String mname, @ApiParam("上级菜单id") String parentMid, Page page) {
        AdminMenu adminMenu = new AdminMenu();
        adminMenu.setParentMenuId(parentMid);
        adminMenu.setMenuName(mname);
        return adminMenuService.getPageDatas(adminMenu, page);
    }

    @ApiOperation(value = "获取菜单列表(非按钮)", notes = "获取菜单列表(非按钮)", response = AdminMenu.class)
    @RequestMapping(value = "menu/getList", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation(value = "获取菜单列表(非按钮)")
    public Object getPatList() {
        return adminMenuService.getAllMenuListM();
    }

    @RequestMapping(value = "menu/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "获取菜单详情", notes = "获取菜单详情", response = AdminMenu.class)
    @ResponseBody
    @LogAnnotation(value = "获取菜单详情")
    public Object getObj(@ApiParam("菜单ID") String id) {
        return adminMenuService.findObj(id);
    }

    @RequestMapping(value = "menu/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增菜单", notes = "新增菜单")
    @LogAnnotation(value = "新增菜单", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody AdminMenu adminMenu) {
        return adminMenuService.insert(adminMenu);
    }

    @RequestMapping(value = "menu/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改菜单", notes = "修改菜单")
    @LogAnnotation(value = "修改菜单", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody AdminMenu adminMenu) {
        return adminMenuService.update(adminMenu);
    }

    @RequestMapping(value = "menu/deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除菜单", notes = "删除菜单")
    @LogAnnotation(value = "删除菜单", isWrite = true)
    @ResponseBody
    public Object deleteLis(AdminMenu adminMenu) {
        return adminMenuService.deleteLisJL(adminMenu);
    }

    @RequestMapping(value = "menu/changeEnabled", method = RequestMethod.GET)
    @ApiOperation(value = "禁用启用菜单", notes = "禁用启用菜单")
    @LogAnnotation(value = "禁用启用菜单", isWrite = true)
    @ResponseBody
    public Object changeEnabled(AdminMenu adminMenu) {
        return adminMenuService.updateEnableds(adminMenu);
    }

    @RequestMapping(value = "menu/tree", method = RequestMethod.GET)
    @ApiOperation(value = "获取菜单树", notes = "获取菜单树", response = AdminMenu.class)
    @ResponseBody
    @LogAnnotation("获取菜单树")
    public Object findMenu(String id) {
        ResEntity result = adminRuleService.findMenuByMID(id, "1", false);
        if (result.getStatus()) {
            return result.getData();
        }
        return result;
    }

    @RequestMapping(value = "menu/tree/all", method = RequestMethod.GET)
    @ApiOperation(value = "获取所有菜单树", notes = "获取所有菜单树", response = AdminMenu.class)
    @ResponseBody
    @LogAnnotation("获取所有菜单树")
    public Object findMenuList() {
        return adminMenuService.getAllMenuList();
    }

    @RequestMapping(value = "menu/updateSort", method = RequestMethod.POST)
    @ApiOperation(value = "修改菜单树序号", notes = "修改菜单树序号")
    @ResponseBody
    @LogAnnotation("修改菜单树序号")
    public Object findMenuList(@RequestBody @ApiParam(value = "菜单列表(传mid,parentMid,sortNumber)") List<AdminMenu> menuList) {
        return adminMenuService.updateSortNumberByList(menuList);
    }
}