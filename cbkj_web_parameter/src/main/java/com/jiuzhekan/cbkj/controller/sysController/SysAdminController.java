package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfoVO;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRule;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.FileTypeUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.encry.RSAEncryption;
import com.jiuzhekan.cbkj.controller.sysController.vo.ChangePwdBean;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Api(value = "用户接口", tags = "用户接口")
@Controller
@RequestMapping("sys/admin/")
public class SysAdminController {

    private final AdminService adminService;

    @Autowired
    SysAdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询用户", notes = "分页查询用户", response = AdminInfo.class)
    @LogAnnotation(value = "分页查询用户")
    @ResponseBody
    public Object getApps(AdminInfo admin, Page page) {
        return adminService.getPageDatas(admin, page);
    }

    @RequestMapping(value = "roleList", method = RequestMethod.GET)
    @ApiOperation(value = "获取角色列表", notes = "获取角色列表", response = AdminRule.class)
    @LogAnnotation(value = "获取角色列表")
    @ResponseBody
    public Object getRoleList() {
        return adminService.getRoles();
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载用户信息", notes = "加载用户信息", response = AdminInfo.class)
    @LogAnnotation(value = "加载用户信息")
    @ResponseBody
    public Object getAdminInfo(String id) {
        return adminService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增用户", notes = "新增用户")
    @LogAnnotation(value = "新增用户", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody AdminInfo adminInfo) {
        return adminService.insert(adminInfo);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改用户", notes = "修改用户")
    @LogAnnotation(value = "修改用户", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody AdminInfoVO adminInfo) {
        return adminService.update(adminInfo);
    }

    @RequestMapping(value = "changePwd", method = RequestMethod.POST)
    @ApiOperation(value = "用户重置密码", notes = "用户重置密码")
    @LogAnnotation(value = "用户重置密码", isWrite = true)
    @ResponseBody
    public Object changePwd(@RequestBody ChangePwdBean changePwdBean) {
        return adminService.resetPwd(changePwdBean.getId(), URLDecoder.decode(changePwdBean.getNewPwd()));
    }

    @RequestMapping(value = "changeStatus", method = RequestMethod.GET)
    @ApiOperation(value = "禁用启用用户", notes = "禁用启用用户")
    @LogAnnotation(value = "禁用启用用户", isWrite = true)
    @ResponseBody
    public Object changeStatus(String id, String status) {
        return adminService.updateStatus(id, status);
    }

    @RequestMapping(value = "deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除用户", notes = "删除用户")
    @LogAnnotation(value = "删除用户", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return adminService.deleteLis(ids);
    }


    @RequestMapping(value = "validateParam", method = RequestMethod.GET)
    @ApiOperation(value = "验证同一医联体下登录名是否重复", notes = "验证同一医联体下登录名是否重复")
    @LogAnnotation(value = "验证同一医联体下登录名是否重复")
    @ResponseBody
    public Object validateParam(String id, String appId, String name) {
        return adminService.validateParam(id, appId, name);
    }

    @Autowired
    private RSAEncryption rsaEncryption;
    @Value("${rsa.publicKey2}")
    private String publicKey2;

    @RequestMapping(value = "getLoginAdminInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取登陆用户信息", notes = "加载用户信息", response = AdminInfo.class)
    @LogAnnotation(value = "获取登陆用户信息")
    @ResponseBody
    public Object getLoginAdminInfo() {
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        if (currentHr == null) {
            return new ResEntity(false, "请先登录", null);
        }
        Map<String, Object> result = new HashMap<>();
        try {
            result.put("name", rsaEncryption.encryptByPublicKey(currentHr.getUsername(), publicKey2));
            result.put("nameZh", rsaEncryption.encryptByPublicKey(currentHr.getNameZh(), publicKey2));
        } catch (Exception e) {
            e.printStackTrace();
        }
        result.put("sex", currentHr.getSex());
        result.put("needUpdatePwd", currentHr.isNeedUpdatePwd());

        return new ResEntity(true, Constant.SUCCESS_DX, result);
    }


    /**
     * EXCEL导入用户数据
     *
     * @param file file
     */
    @ApiOperation(value = "EXCEL导入用户数据", notes = "EXCEL导入用户数据")
    @RequestMapping(value = "excel/import", method = RequestMethod.POST)
    @ResponseBody
    @LogAnnotation(value = "EXCEL导入用户数据")
    public Object importIns(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }
        if (!FileTypeUtil.isExcel(file)) {
            return new ResEntity(false, "仅支持Excel", null);
        }
        return adminService.importIns(file);
    }

    @ApiOperation(value = "获取用户权限配置信息", notes = "获取用户权限配置信息", response = SysAdminInfoex.class)
    @RequestMapping(value = "infoex/get", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation(value = "获取用户权限配置信息")
    public Object getUserEx(String userId) {
        return adminService.getUserEx(userId);
    }

    @ApiOperation(value = "修改或新增用户权限配置", notes = "修改或新增用户权限配置", response = SysAdminInfoex.class)
    @RequestMapping(value = "infoex/update", method = RequestMethod.POST)
    @ResponseBody
    @LogAnnotation(value = "修改或新增用户权限配置")
    public Object setUserEx(@RequestBody SysAdminInfoex sysAdminInfoex) {
        return adminService.setUserEx(sysAdminInfoex);
    }
    @ApiOperation(value = "设置灰度发布跳转接口", notes = "设置灰度发布跳转接口", response = SysAdminInfoex.class)
    @RequestMapping(value = "setGrayscale", method = RequestMethod.POST)
    @ResponseBody
    @LogAnnotation(value = "设置灰度发布跳转接口")
    public Object setGrayscale(@RequestBody String  userIds) {
        return adminService.setGrayscale(userIds);
    }
    @ApiOperation(value = "展示灰度发布跳转接口", notes = "展示灰度发布跳转接口", response = SysAdminInfoex.class)
    @RequestMapping(value = "getGrayscale", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation(value = "展示灰度发布跳转接口")
    public Object getGrayscale(AdminInfo adminInfo,Page page) {
        return adminService.getGrayscale(adminInfo,page);
    }

    @ApiOperation(value = "删除灰度发布配置信息接口", notes = "删除灰度发布配置信息接口", response = SysAdminInfoex.class)
    @RequestMapping(value = "deleteGrayscale", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation(value = "删除灰度发布配置信息接口")
    public Object deleteGrayscale(String id) {
        return adminService.deleteGrayscale(id);
    }

}