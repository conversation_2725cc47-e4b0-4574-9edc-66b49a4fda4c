package com.jiuzhekan.cbkj.controller.testBusiness;


import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.DownloadUtil;
import com.jiuzhekan.cbkj.common.utils.OSSUtil;
import com.jiuzhekan.cbkj.service.testBusiness.UploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Api(value = "上传下载文件接口", tags = "上传下载文件接口")
@Controller
public class UploadController {

    @Value("${file.address}")
    private String location;

    @Value("${root.preview}")
    private String preview;

    @Value("${root.upload.relative}")
    private String relative;

    @Autowired
    private UploadService uploadService;

    private final ResourceLoader resourceLoader;

    @Autowired
    public UploadController(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    ///**
    // * 上传到阿里云OSS
    // *
    // * @param file file
    // */
    //@RequestMapping(value = "upload/post2", method = RequestMethod.POST)
    //@ResponseBody
    //public Object fileUpload(MultipartFile file) {
    //    Object obj = null;
    //    try {
    //        obj = OSSUtil.uploadFile(file, "cbkj");
    //
    //        return new ResEntity(true, Constant.SUCCESS_DX, obj);
    //
    //
    //    } catch (Exception e) {
    //        e.printStackTrace();
    //        return new ResEntity(false, "上传服务异常", e.getMessage());
    //    }
    //}

    /**
     * 用一个tomcat 作为图片服务器 上传
     *
     * @param request request
     * @param file    file
     */
    @ApiOperation(value = "文件上传", notes = "文件上传")
    @RequestMapping(value = "upload/post", method = RequestMethod.POST)
    @ResponseBody
    public Object handleFileUpload(HttpServletRequest request, MultipartFile file) {
        return uploadService.uploadMultipartFile(file,Constant.FILE_TYPE_IMAGE);
    }

    @ApiOperation(value = "视频上传", notes = "视频上传")
    @RequestMapping(value = "upload/postVideo", method = RequestMethod.POST)
    @ResponseBody
    public Object handleVideoUpload(HttpServletRequest request, MultipartFile file) {
        return uploadService.uploadMultipartFile(file,Constant.FILE_TYPE_VIDEO);
    }



    @RequestMapping(value = "rpcVideo/post", method = RequestMethod.POST)
    @ResponseBody
    public Object handleIMFileUpload(String data) throws IOException {

        if (StringUtils.isBlank(data)) {
            return ResEntity.entity(false, "not data", null);
        }
        Base64 base64 = new Base64();
        byte[] bs = base64.decode(data.substring("data:image/png;base64,".length()).getBytes());
        StringBuffer sb = new StringBuffer(relative).append(DateUtil.getDateFormats(DateUtil.date5, null)).append("/");
        sb.append(String.format("%s%s", UUID.randomUUID().toString(), ".jpg"));
        File dest = new File(String.format("%s%s", location, sb.toString()));
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
            FileUtils.writeByteArrayToFile(dest, bs);
            return new ResEntity(true, Constant.SUCCESS_DX, preview + sb.toString());
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
            return new ResEntity(false, "服务异常IM", null);
        }
    }

    @Value("${template.address}")
    private String templateAddress;

    public String getJarRootPath(HttpServletRequest request,String fileName) throws UnsupportedEncodingException {
        String agent = request.getHeader("USER-AGENT");
        if (null != agent && agent.contains("MSIE") || null != agent && agent.contains("Trident")) {
            // IE浏览器
            fileName = java.net.URLEncoder.encode(fileName, "UTF8");

        } else if (null != agent && agent.contains("Mozilla")) {
            // 火狐,chrome等浏览器
            fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        String path = templateAddress + fileName;
        File file = new File(path);
        if (file.exists()) {
            return path;
        }
        return "templates" + File.separator + fileName;
    }

    @ApiOperation(value = "文件下载", notes = "文件下载")
    @RequestMapping(value = "srcFile/get", method = RequestMethod.POST)
    @LogAnnotation(value = "文件下载", isWrite = true)
    @ResponseBody
    @ApiImplicitParam(name = "fileName", value = "文件全名包括后缀名", dataType = "String", paramType = "query", required = true)
    public Object getFiles(String fileName, HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        if (!fileName.contains(Constant.PONIT)) {
            return new ResEntity(false, "请输入文件名", null);
        }
        String path = getJarRootPath(request,fileName);
        int i = fileName.lastIndexOf(".");
        if (i>0){
            String substring = fileName.substring(0, i);
            DownloadUtil.downloadFile(path, substring, response, request);
        }
        return null;
    }
}