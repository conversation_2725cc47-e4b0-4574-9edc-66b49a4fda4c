package com.jiuzhekan.cbkj.controller.drug;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.drug.BStandardMat;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.drug.BStandardMatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
@Api(value = "药品标准编码", tags = "药品标准编码表接口")
@Controller
@RequestMapping("bStandardMat")
public class BStandardMatController {

    @Autowired
    private BStandardMatService bStandardMatService;

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载药品标准编码表详情", notes = "加载药品标准编码表详情", response = BStandardMat.class)
    @LogAnnotation("加载药品标准编码表详情")
    @ResponseBody
    public Object getObj(String id){
        return bStandardMatService.findObj(id);
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询药品标准编码", notes = "分页查询药品标准编码表", response = BStandardMat.class)
    @LogAnnotation("分页查询药品标准编码")
    @ResponseBody
    public Object getApps( BStandardMat bStandardMat,  Page page){
        return bStandardMatService.getPageDatas(bStandardMat,page);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增药品标准编码", notes = "新增药品标准编码表")
    @LogAnnotation(value = "新增药品标准编码", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody BStandardMat bStandardMat) {
        return bStandardMatService.insert(bStandardMat);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改药品标准编码", notes = "修改药品标准编码表详情")
    @LogAnnotation(value = "修改药品标准编码", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody BStandardMat bStandardMat) {
        return bStandardMatService.update(bStandardMat);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除药品标准编码", notes = "删除药品标准编码表")
    @LogAnnotation(value = "删除药品标准编码", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return bStandardMatService.deleteLis(ids);
    }


}