package com.jiuzhekan.cbkj.controller.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayDecoctionP;
import com.jiuzhekan.cbkj.beans.pharmacy.displaysettingVo.TDisplayExpressP;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayExpress;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.pharmacy.TDisplayExpressService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "药房服务配置接口", tags = "药房服务配置接口")
@RequestMapping("tDisplayExpress")
public class TDisplayExpressController {

    @Autowired
    private TDisplayExpressService tDisplayExpressService;


    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "配送-加载药房配置配送信息表详情", notes = "配送-加载药房配置配送信息表详情", response = TDisplayExpress.class)
    @LogAnnotation("配送-加载药房配置配送信息表详情")
    @ResponseBody
    public Object getObj(String displayId){
        return tDisplayExpressService.findObj(displayId);
    }



    @RequestMapping(value = "updateOrInsert", method = RequestMethod.POST)
    @ApiOperation(value = "配送-修改药房配置配送信息表", notes = "配送-修改药房配置配送信息表详情")
    @LogAnnotation(value = "配送-修改药房配置配送信息表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TDisplayExpressP tDisplayExpress) {
        return tDisplayExpressService.updateOrInsert(tDisplayExpress);
    }

    @RequestMapping(value = "insertBatch", method = RequestMethod.POST)
    @ApiOperation(value = "配送-批量更新药房配置", notes = "配送-批量更新药房配置")
    @LogAnnotation(value = "配送-批量更新药房配置", isWrite = true)
    @ResponseBody
    public Object insertBatch(@RequestBody TDisplayExpressP tDisplayExpress) {
        ResEntity resEntity = null;
        if (!StringUtils.isBlank(tDisplayExpress.getPharmacyDisplayList())){
            String[] split = tDisplayExpress.getPharmacyDisplayList().split(",");
            for (int i = 0; i < split.length; i++) {
                tDisplayExpress.getHospitalization().setDisplayId(split[i]);
                tDisplayExpress.getOutpatient().setDisplayId(split[i]);
                resEntity = tDisplayExpressService.updateOrInsert(tDisplayExpress);
                if (!resEntity.getStatus()){
                    break;
                }
            }
        }
        if (null != resEntity && !resEntity.getStatus()){
            return resEntity;
        }
        return ResEntity.success(null);
    }


}