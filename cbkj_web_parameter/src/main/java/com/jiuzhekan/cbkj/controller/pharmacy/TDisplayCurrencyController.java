package com.jiuzhekan.cbkj.controller.pharmacy;

import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayCurrencyList;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.pharmacy.TDisplayCurrency;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.pharmacy.TDisplayCurrencyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "药房服务配置接口", tags = "药房服务配置接口")
@RequestMapping("tDisplayCurrency")
public class TDisplayCurrencyController {

    @Autowired
    private TDisplayCurrencyService tDisplayCurrencyService;


    @RequestMapping(value = "getTDisplayCurrencySetting", method = RequestMethod.GET)
    @ApiOperation(value = "通用-通用配置查询", notes = "通用-通用配置查询", response = TDisplayCurrency.class)
    @LogAnnotation("通用-通用配置查询")
    @ResponseBody
    public Object getTDisplayCurrencySetting(String displayId){
        return tDisplayCurrencyService.getTDisplayCurrencySetting(displayId);
    }

    @RequestMapping(value = "saveTDisplayCurrencySetting", method = RequestMethod.POST)
    @ApiOperation(value = "通用-保存通用配置", notes = "通用-保存通用配置")
    @LogAnnotation(value = "通用-保存通用配置", isWrite = true)
    @ResponseBody
    public Object saveTDisplayCurrencySetting(@RequestBody TDisplayCurrency tDisplayCurrency){
        return tDisplayCurrencyService.saveTDisplayCurrencySetting(tDisplayCurrency);
    }

    @RequestMapping(value = "saveTDisplayCurrencySettingList", method = RequestMethod.POST)
    @ApiOperation(value = "通用-保存通用配置集合", notes = "通用-保存通用配置集合")
    @LogAnnotation("通用-保存通用配置集合")
    @ResponseBody
    public Object saveTDisplayCurrencySettingList(@RequestBody TDisplayCurrencyList tDisplayCurrencyList){
        return tDisplayCurrencyService.saveTDisplayCurrencySettingList(tDisplayCurrencyList);
    }

}