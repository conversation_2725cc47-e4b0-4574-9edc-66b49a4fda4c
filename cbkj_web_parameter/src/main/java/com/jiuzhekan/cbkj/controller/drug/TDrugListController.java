package com.jiuzhekan.cbkj.controller.drug;

import com.jiuzhekan.cbkj.beans.drug.*;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.drug.TDrugListService;
import com.jiuzhekan.cbkj.service.drug.TStandardMaterialMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "药品目录主表接口", tags = "药品目录主表接口")
@RequestMapping("tDrugList")
/**
 * <AUTHOR>
 */
public class TDrugListController {

    @Autowired
    private TDrugListService tDrugListService;
    @Autowired
    private TStandardMaterialMappingService tStandardMaterialMappingService;

    @RequestMapping(value = "getPageList", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询药品目录信息", notes = "分页查询药品目录信息", response = TDrugList.class)
    @LogAnnotation("分页查询药品目录信息")
    @ResponseBody
    public Object getDrugList(TDrugList tDrugList, Page page){
        return tDrugListService.getPageDatas(tDrugList,page);
    }
    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "国标目录映射获取药品目录列表", notes = "国标目录映射获取药品目录列表", response = TDrugList.class)
    @LogAnnotation("国标目录映射获取药品目录列表")
    @ResponseBody
    public Object getApps(TDrugList tDrugList){
        return tDrugListService.getPageDatas2(tDrugList);
    }

    @RequestMapping(value = "getDrugList", method = RequestMethod.GET)
    @ApiOperation(value = "药房管理获取药品目录", notes = "药房管理获取药品目录", response = TDrugList.class)
    @LogAnnotation("药房管理获取药品目录")
    @ResponseBody
    public Object getDrugList(TDrugList tDrugList){
        return tDrugListService.getPageDatas3(tDrugList);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载药品目录主表详情", notes = "加载药品目录主表详情", response = TDrugList.class)
    @LogAnnotation("加载药品目录主表详情")
    @ResponseBody
    public Object getObj(String id){
        return tDrugListService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增药品目录主表", notes = "新增药品目录主表")
    @LogAnnotation(value = "新增药品目录主表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TDrugListVo tDrugListVo) {
        return tDrugListService.insert(tDrugListVo);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改药品目录主表", notes = "修改药品目录主表详情")
    @LogAnnotation(value = "修改药品目录主表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TDrugListVo tDrugListVo) {
        return tDrugListService.update(tDrugListVo);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除药品目录主表", notes = "删除药品目录主表")
    @LogAnnotation(value = "删除药品目录主表", isWrite = true)
    @ResponseBody
    public Object deleteLis(String id) throws Exception {
        return tDrugListService.deleteLis(id);
    }

    @RequestMapping(value="materialStandMapping/mapList", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询本地药房和标准编码映射参照", notes = "分页查询本地药房和标准代码映射参照",response = TMaterialStandVO.class)
    @LogAnnotation(value = "分页查询本地药房和标准代码映射参照", isWrite = true)
    @ResponseBody
    public Object getStandardMaterialMapping(TMaterialStandVO tMaterialStandVO, Page page) throws Exception {
        Object datas = tStandardMaterialMappingService.getPageDatas(tMaterialStandVO, page);
        return datas;
    }
    @RequestMapping(value = "materialStandMapping/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增本地药房药品和标准编码手动映射", notes = "新增本地药房药品和标准编码手动映射")
    @LogAnnotation(value = "新增本地药房药品和标准编码手动映射", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TStandardMaterialMappingVo tStandardMaterialMapping) {
        return tStandardMaterialMappingService.insert(tStandardMaterialMapping);
    }

    @RequestMapping(value = "materialStandMapping/delete", method = RequestMethod.GET)
    @LogAnnotation(value = "取消本地药房药品和标准编码映射关系", isWrite = true)
    @ApiOperation(value = "取消本地药房药品和标准编码映射关系", notes = "取消本地药房药品和标准编码映射关系")
    @ResponseBody
    public Object delete(String mapIds) {
        return tStandardMaterialMappingService.deleteLis(mapIds);
    }

    @RequestMapping(value = "materialStandMapping/autoMapping", method = RequestMethod.GET)
    @ApiOperation(value = "本地药房药品和标准编码自动映射", notes = "本地药房药品和标准编码自动映射")
    @LogAnnotation(value = "本地药房药品和标准编码自动映射")
    @ResponseBody
    public Object autoMapping(StandTMAutoMappingVO standTmAutoMappingVO) {
        return tStandardMaterialMappingService.autoMapping(standTmAutoMappingVO);
    }
}