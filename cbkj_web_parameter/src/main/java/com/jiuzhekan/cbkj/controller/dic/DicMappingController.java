package com.jiuzhekan.cbkj.controller.dic;

import com.jiuzhekan.cbkj.beans.dic.TDicMappingVo;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.dic.TDicMapping;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.dic.DicBaseService;
import com.jiuzhekan.cbkj.service.dic.DicMappingService;
import com.jiuzhekan.cbkj.service.dic.GetDeptTree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@Api(value = "字典映射表接口", tags = "字典映射表接口")
@RequestMapping("tDicMapping")
/**
 * <AUTHOR>
 */
public class DicMappingController {


    private DicMappingService dicMappingService;

    @Autowired
    DicMappingController(DicMappingService dicMappingService) {
        this.dicMappingService = dicMappingService;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询字典映射表", notes = "分页查询字典映射表", response = TDicMapping.class)
    @LogAnnotation("分页查询字典映射表")
    @ResponseBody
    public Object getApps(TDicMapping tDicMapping, Page page){
        return dicMappingService.getPageDatas(tDicMapping,page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载字典映射表详情", notes = "加载字典映射表详情", response = TDicMapping.class)
    @LogAnnotation("加载字典映射表详情")
    @ResponseBody
    public Object getObj(String id){
        return dicMappingService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增字典映射表", notes = "新增字典映射表")
    @LogAnnotation(value = "新增字典映射表", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody TDicMappingVo tDicMappingVo) {
        return dicMappingService.insert(tDicMappingVo);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改字典映射表", notes = "修改字典映射表详情")
    @LogAnnotation(value = "修改字典映射表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TDicMapping tDicMapping) {
        return dicMappingService.update(tDicMapping);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.POST)
    @ApiOperation(value = "删除字典映射表", notes = "删除字典映射表")
    @LogAnnotation(value = "删除字典映射表", isWrite = true)
    @ResponseBody
    public Object deleteLis(@RequestBody String id) throws Exception {
        return dicMappingService.deleteLis(id);
    }

}