package com.jiuzhekan.cbkj.controller.sysController;

import com.jiuzhekan.cbkj.beans.sysBeans.OperationLog;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.sysService.OperationLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Api(value = "操作日志", tags = "操作日志")
@Controller
@RequestMapping("log")
public class OperationLogController {
    private final OperationLogService operationLogService;

    @Autowired
    OperationLogController(OperationLogService operationLogService){
        this.operationLogService = operationLogService;
    }
    @RequestMapping(value = "operationLog/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取操作日志", notes = "分页获取操作日志", response = OperationLog.class)
    @LogAnnotation("分页获取操作日志")
    @ResponseBody
    public Object getApps(OperationLog operationLog, Page page) {
        return operationLogService.getPageDatas(operationLog, page);
    }

    @RequestMapping(value = "operationLog/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "获取操作日志的详情", notes = "获取操作日志的详情", response = OperationLog.class)
    @LogAnnotation("获取操作日志的详情")
    @ResponseBody
    public Object getObj(String id) {
        return operationLogService.findObj(id);
    }

    @RequestMapping(value = "operationLog/deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除操作日志记录", notes = "删除操作日志记录")
    @LogAnnotation(value = "删除操作日志记录",isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return operationLogService.deleteLis(ids);
    }
}