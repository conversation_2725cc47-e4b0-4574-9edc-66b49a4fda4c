package com.jiuzhekan.cbkj.controller.drug;

import com.jiuzhekan.cbkj.beans.drug.VCenterYpmlmx;
import com.jiuzhekan.cbkj.beans.drug.VCenterYpmlmxVo;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;

import com.jiuzhekan.cbkj.common.utils.Page;

import com.jiuzhekan.cbkj.service.drug.CenterYpmlmxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Controller
@Api(value = "药品目录明细接口", tags = "药品目录明细接口")
@RequestMapping("vCenterYpmlmx")
/**
 * <AUTHOR>
 */
public class CenterYpmlmxController {


    private CenterYpmlmxService centerYpmlmxService;

    @Autowired
    CenterYpmlmxController(CenterYpmlmxService centerYpmlmxService) {
        this.centerYpmlmxService = centerYpmlmxService;
    }

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询药品目录明细", notes = "分页查询药品目录明细", response = VCenterYpmlmx.class)
    @LogAnnotation("分页查询药品目录明细")
    @ResponseBody
    public Object getApps(VCenterYpmlmx vCenterYpmlmx, Page page) {
        return centerYpmlmxService.getPageDatas(vCenterYpmlmx, page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载药品目录明细详情", notes = "加载药品目录明细详情", response = VCenterYpmlmx.class)
    @LogAnnotation("加载药品目录明细详情")
    @ResponseBody
    public Object getObj(String id) {
        return centerYpmlmxService.findObj(id);
    }

    @RequestMapping(value = "updateInsurance", method = RequestMethod.POST)
    @ApiOperation(value = "医保配置", notes = "医保配置")
    @LogAnnotation(value = "医保配置", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody VCenterYpmlmxVo vCenterYpmlmxVo) {
        return centerYpmlmxService.updateInsurance(vCenterYpmlmxVo);
    }
/*
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改药品目录明细", notes = "修改药品目录明细详情")
    @LogAnnotation(value = "修改药品目录明细", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody VCenterYpmlmx vCenterYpmlmx) {
        return vCenterYpmlmxService.update(vCenterYpmlmx);
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除药品目录明细", notes = "删除药品目录明细")
    @LogAnnotation(value = "删除药品目录明细", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return vCenterYpmlmxService.deleteLis(ids);
    }*/

}