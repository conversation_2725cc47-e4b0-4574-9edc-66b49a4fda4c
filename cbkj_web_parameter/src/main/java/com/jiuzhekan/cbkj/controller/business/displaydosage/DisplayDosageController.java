package com.jiuzhekan.cbkj.controller.business.displaydosage;

import com.jiuzhekan.cbkj.beans.business.displaydosage.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.business.displaydosage.TDisplayDosageFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/6 11:06
 * @Version 1.0
 */
@Api(value = "药房配置-剂型", tags = "药房配置-剂型")
@RestController
@RequestMapping("displayDosage")
public class DisplayDosageController {

    private final TDisplayDosageFormService tDisplayDosageFormService;

    public DisplayDosageController(TDisplayDosageFormService tDisplayDosageFormService) {
        this.tDisplayDosageFormService = tDisplayDosageFormService;
    }


    /**
     * @Description: 获取计价方式列表
     */
    @GetMapping("getDisplayDosageFormList")
    @ApiOperation(value = "绑定费用-获取计价方式列表", notes = "绑定费用-获取计价方式列表",position = 1)
    @LogAnnotation("获取计价方式列表")
    public Object getDisplayDosageFormList() {
        return tDisplayDosageFormService.getDisplayDosageFormList();
    }

    /**
     * 获取字典对应绑定的费用明细列表
     */
    @GetMapping("getDisplayDosageFormListByDicCode")
    @ApiOperation(value = "绑定费用-获取字典dicId对应绑定的费用明细列表", notes = "绑定费用-获取字典dicId对应绑定的费用明细列表",response = TDisplayDosageCost.class,position = 2)
    @LogAnnotation("获取字典dicId对应绑定的费用明细列表")
    public Object getDisplayDosageFormListByDicId(GetDisplayDosageFormListByDicId dicId) {
        return tDisplayDosageFormService.getDisplayDosageFormListByDicId(dicId);
    }

    /**
     * 保存字典对应费用明细
     */
    @PostMapping("saveDisplayDosageForm")
    @ApiOperation(value = "绑定费用-保存字典对应费用明细", notes = "绑定费用-保存字典对应费用明细",position = 3)
    @LogAnnotation("保存字典对应费用明细")
    public Object saveDisplayDosageForm(@RequestBody SaveDisplayDosageForm saveDisplayDosageForm) {
        return tDisplayDosageFormService.saveDisplayDosageForm(saveDisplayDosageForm);
    }

    /**
     * 制剂说明-获取制剂说明信息根据dicId
     */
    @GetMapping("getDosageDescribeInfo")
    @ApiOperation(value = "制剂说明-获取制剂说明信息根据dicId", notes = "制剂说明-获取制剂说明信息根据dicId",position = 4 ,response = TDisplayDosageDescribe.class)
    @LogAnnotation("制剂说明-获取制剂说明信息根据dicId")
    public Object getDosageDescribeInfo(GetDisplayDosageFormListByDicId dicId) {
        return tDisplayDosageFormService.getDosageDescribeInfo(dicId);
    }

    /**
     * 制剂说明-保存制剂说明信息
     */
    @PostMapping("saveDosageDescribeInfo")
    @ApiOperation(value = "制剂说明-保存/更新制剂说明信息", notes = "制剂说明-保存/更新制剂说明信息",position = 5)
    @LogAnnotation("制剂说明-保存制剂说明信息")
    public Object saveDosageDescribeInfo(@RequestBody TDisplayDosageDescribe tDisplayDosageDescribe) {
        return tDisplayDosageFormService.saveDosageDescribeInfo(tDisplayDosageDescribe);
    }

    /**
     * 获取药房管理-剂型信息
     */
    @GetMapping("getPharmacyDosageInfo")
    @ApiOperation(value = "药房管理剂型-获取剂型信息", notes = "药房管理剂型-获取剂型信息",position = 6,response = TDisplayDosageP.class)
    @LogAnnotation("药房管理剂型-剂型信息")
    public Object getPharmacyDosageInfo(String displayId) {
        return tDisplayDosageFormService.getPharmacyDosageInfo(displayId);
    }




    /**
     * 更新药房配置-剂型
     */
    @PostMapping("updatePharmacyDosageInfo")
    @ApiOperation(value = "药房管理剂型-保存/更新药房配置-剂型", notes = "药房管理剂型-保存/更新药房配置-剂型",position = 7)
    @LogAnnotation("更新药房配置-剂型")
    public Object updatePharmacyDosageInfo(@RequestBody TDisplayDosageP tDisplayDosageCost) {
        return tDisplayDosageFormService.updateOrInsert(tDisplayDosageCost);
    }

    /**
     * 批量更新药房配置-剂型
     */
    @PostMapping("updatePharmacyDosageInfoBatch")
    @ApiOperation(value = "药房管理剂型-批量更新药房配置-剂型", notes = "药房管理剂型-批量更新药房配置-剂型",position = 8)
    @LogAnnotation("批量更新药房配置-剂型")
    public Object updatePharmacyDosageInfoBatch(@RequestBody TDisplayDosageP tDisplayDosageCost) {
        ResEntity resEntity = null;
        if (!StringUtils.isBlank(tDisplayDosageCost.getPharmacyDisplayList())){
            String[] split = tDisplayDosageCost.getPharmacyDisplayList().split(",");
            for (int i = 0; i < split.length; i++) {
                tDisplayDosageCost.getHospitalization().setDisplayId(split[i]);
                tDisplayDosageCost.getOutpatient().setDisplayId(split[i]);
                resEntity = tDisplayDosageFormService.updateOrInsert(tDisplayDosageCost);
                if (!resEntity.getStatus()){
                    break;
                }
            }
        }
        if (null != resEntity && !resEntity.getStatus()){
            return resEntity;
        }
        return ResEntity.success(null);
    }

}
