package com.jiuzhekan.cbkj.common.http;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "http-pool")
public class HttpPool {
    /**
     * 最大连接
     */
    private Integer maxTotal;
    /**
     * 每个route默认的连接数
     */
    private Integer defaultMaxPerRoute;
    /**
     * 连接超时时间（服务器(握手成功)的时间）
     */
    private Integer connectTimeout;
    /**
     * 等待连接池超时时间（超时未拿到连接抛出异常 org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool）
     */
    private Integer connectionRequestTimeout;
    /**
     * 获取数据的超时时间 访问一个接口，多少时间内无法返回数据，就直接放弃此次调用
     */
    private Integer socketTimeout;
    /**
     *  空闲永久连接检查间隔,官方推荐使用这个来检查永久链接的可用性，而不推荐每次请求的时候才去检查（ms）
     */
    private Integer validateAfterInactivity;
}