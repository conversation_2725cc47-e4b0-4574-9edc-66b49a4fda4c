package com.jiuzhekan.cbkj.common.dm;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/9/18 09:34
 * @Version 1.0
 */


import com.github.pagehelper.Page;
import com.github.pagehelper.dialect.AbstractHelperDialect;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;

import java.util.Map;

/**
 * DmDialect - 达梦数据库分页（基于 Oracle 风格 ROWNUM）
 * 兼容 PageHelper 5.x / 6.x 常见实现（继承 AbstractHelperDialect）
 */
public class DmDialect extends AbstractHelperDialect {

    /**
     * 把原始 SQL 包装成达梦(Oracle-like) 的 ROWNUM 分页语句
     *
     * 常见输出：
     * SELECT * FROM (
     *   SELECT TMP_.*, ROWNUM ROW_ID FROM ( 原始SQL ) TMP_ WHERE ROWNUM <= end
     * ) WHERE ROW_ID > start
     */
    @Override
    public String getPageSql(String sql, Page page, CacheKey pageKey) {
        //// 达梦分页语法: SELECT * FROM (SELECT TMP.*, ROWNUM ROW_ID FROM (原SQL) TMP) WHERE ROW_ID BETWEEN x AND y
        if (page == null || page.getPageSize() <= 0) {
            return sql;
        }

        long pageNum = page.getPageNum();
        long pageSize = page.getPageSize();
        long startRow = (pageNum - 1L) * pageSize;
        long endRow = pageNum * pageSize;

        StringBuilder sb = new StringBuilder(sql.length() + 100);
        sb.append("SELECT * FROM (SELECT TMP_.*, ROWNUM ROW_ID FROM (");
        sb.append(sql);
        sb.append(") TMP_ WHERE ROWNUM <= ").append(endRow);
        sb.append(") WHERE ROW_ID > ").append(startRow);

        return sb.toString();
    }
   
    /**
     * 如果你需要在分页参数上做额外处理（比如把分页参数放入 paramMap），可以在这里实现；
     * 默认直接调用父类，通常不需要改动。
     */
    @Override
    public Object processPageParameter(MappedStatement ms, Map<String, Object> paramMap,
                                       Page page, BoundSql boundSql, CacheKey pageKey) {
        // 如果你不需要做额外参数注入，保持默认行为即可：
        return null;
    }
}


