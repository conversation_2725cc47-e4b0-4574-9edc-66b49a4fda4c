package com.jiuzhekan.cbkj.common.config.security;


import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
//import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * SecurityVo
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/12
 */
@Data
@NoArgsConstructor
public class TokenBo implements Serializable {

    /**
     * tokenKey
     */
    private String tokenKey;

    /**
     * authorization
     */
    private String authorization;

    /**
     * 医联体ID
     */
    private String adminId;
    /**
     * 医共体ID
     */
    private String appId;
    /**
     * 医疗机构ID
     */
    private String insCode;

    /**
     * 医生
     */
    private AdminInfo admin;

    /**
     * 医生执业机构
     */
//    private SysAdminPractice practice;
    private SysDoctorMultipoint practice;


    public TokenBo(String tokenKey, String authorization, String adminId,String appId, String insCode) {
        this.tokenKey = tokenKey;
        this.authorization = authorization;
        this.adminId = adminId;
        this.appId = appId;
        this.insCode = insCode;
    }

    public TokenBo(String tokenKey, String authorization, String adminId,  AdminInfo admin,SysDoctorMultipoint practice) {
        this.tokenKey = tokenKey;
        this.authorization = authorization;
        this.adminId = adminId;
        this.admin = admin;
        this.practice = practice;
    }

    public TokenBo(String tokenKey, String adminId,  AdminInfo admin,SysDoctorMultipoint practice) {
        this.tokenKey = tokenKey;
        this.adminId = adminId;
        this.admin = admin;
        this.practice = practice;
    }
}
