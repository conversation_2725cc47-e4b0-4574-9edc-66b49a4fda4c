package com.jiuzhekan.cbkj.common.utils.pdf;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * PdfAttributeVO
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2020/10/14 10:58
 */
@Setter
@Getter
@ToString
public class PdfAttributeVO {

    private String value;
    /*
     * 字体位置
     */
    private float fontPosition;
    /*
     * 字体大小
     */
    private float fontSize;
    /*
     * 字体路径，配置文件中的下标
     */
    private int fontPathIndex;

    public PdfAttributeVO() {
    }

    public PdfAttributeVO(String value, float fontPosition, float fontSize, int fontPathIndex) {
        this.value = value;
        this.fontPosition = fontPosition;
        this.fontSize = fontSize;
        this.fontPathIndex = fontPathIndex;
    }
}
