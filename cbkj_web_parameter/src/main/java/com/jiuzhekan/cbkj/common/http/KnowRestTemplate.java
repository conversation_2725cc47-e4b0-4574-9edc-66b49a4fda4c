//package com.jiuzhekan.cbkj.common.http;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jiuzhekan.cbkj.beans.business.TSysParam;
//import com.jiuzhekan.cbkj.beans.business.knowledge.TInterfaceKnowledge;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.utils.AdminUtils;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.SHA1;
//import com.jiuzhekan.cbkj.service.parameter.TSysParamService;
//import com.jiuzhekan.cbkj.service.sysService.RedisService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cache.annotation.Cacheable;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Component;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 知识库http请求组件
// *
// * <AUTHOR>
// * @date 2021/1/8
// */
//@Slf4j
//@Component
//public class KnowRestTemplate extends RestTemplate {
//
//    @Autowired
//    private RestTemplate restTemplate;
//    @Autowired
//    private RedisService redisService;
//    @Autowired
//    private TSysParamService tSysParamService;
//
//    /**
//     * 请求知识库接口
//     *
//     * @param url    zyznyxt_basic/interface/后面的路径
//     * @param params 不需要传 app_id,timestamp,sign,format
//     * @return ResEntity
//     */
//    public ResEntity postKnow(String url, Map<String, Object> params,
//    String appId,String insCode,String deptId,String gravidity,String gender,String age
//    ) {
//        if (params == null) {
//            params = new HashMap<>();
//        }
//
//        if (!StringUtils.isBlank(gravidity)) {
//            //是否怀孕
//            params.put("gravidity", gravidity);
//            if (!StringUtils.isBlank(gender)) {
//                //性别
//                params.put("gender", gender);
//            }
//            if (!StringUtils.isBlank(age)) {
//                //年龄
//                params.put("age", age);
//            }
//        }
//
//        TSysParam param = tSysParamService.getSysParam(Constant.KNOWLEDGE_SHOW_TYPE,appId,insCode,deptId);
//        if (param != null && StringUtils.isNotBlank(param.getParValues())) {
//            params.put("showType", param.getParValues());
//        }
//
//        return postKnow2(url, params);
//    }
//
//    @Cacheable(value = "pre-ai-know:post", keyGenerator = "cacheKeyGenerator")
//    public ResEntity postKnow2(String url, Map<String, Object> params) {
//
//        TInterfaceKnowledge interfaceKnowledge = redisService.getInitInterfaceKnowledge();
//
//        if (interfaceKnowledge == null) {
//            log.error("知识库接口表配置错误，没有找到app_id:100001,app_pwd:100001的配置");
//            return ResEntity.entity(false, "知识库接口表配置错误，没有找到app_id:100001,app_pwd:100001的配置", null);
//        }
//
//        if (interfaceKnowledge.getEffectiveTime() == null
//                || interfaceKnowledge.getEffectiveTime().getTime() - System.currentTimeMillis() < 60 * 1000) {
//            redisService.clearRedisCache("pre-ai-know:token", null);
//            interfaceKnowledge = redisService.getInitInterfaceKnowledge();
//        }
//
//        params.put("app_id", Constant.KNOW_APP_ID);
//        params.put("app_pwd", Constant.KNOW_APP_PWD);
//        params.put("format", "json");
//        params.put("token", interfaceKnowledge.getToken());
//        params.put("timestamp", System.currentTimeMillis() + "");
//
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//
//        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
//        params.forEach(map::add);
//        map.add("sign", SHA1.getSign(map));
//        HttpEntity<MultiValueMap<String, Object>> request1 = new HttpEntity<>(map, headers);
//
//        ResponseEntity<JSONObject> response = restTemplate.postForEntity(interfaceKnowledge.getUrl() + url, request1, JSONObject.class);
//        JSONObject result = response.getBody();
//        if (result != null) {
//            String code = result.getString("code");
//            String message = result.getString("message");
//            if ("0".equals(code)) {
//                //成功
//                Object obj = result.get("data");
//                return ResEntity.entity(true, Constant.SUCCESS_DX, obj);
//            } else {
//                //1002 token过期, 重新获取token
//                redisService.clearRedisCache("pre-ai-know:token", null);
//                interfaceKnowledge = redisService.getInitInterfaceKnowledge();
//                //重新请求接口
//                response = restTemplate.postForEntity(interfaceKnowledge.getUrl() + url, request1, JSONObject.class);
//                result = response.getBody();
//                if (result != null) {
//                    code = result.getString("code");
//                    message = result.getString("message");
//                    if ("0".equals(code)) {
//                        Object obj = result.get("data");
//                        return ResEntity.entity(true, Constant.SUCCESS_DX, obj);
//                    } else {
//                        log.error(message);
//                    }
//                }
//            }
//
//            return ResEntity.entity(false, message, null);
//        }
//        return ResEntity.entity(false, "【知识库】服务异常", null);
//    }
//
//}