package com.jiuzhekan.cbkj.common.config.dm;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.replace.Replace;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;
import java.util.*;

/**
 * Mysql 转 Dm sql拦截器
 * <AUTHOR>
 * @时间 2024/3/6 14:40
 */
@Slf4j
@Intercepts(
    {
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    }
)
public class MysqlToDmInterceptor implements Interceptor {


    public Map<String, Object> sqlMap = new HashMap<>();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        // 获取 MappedStatement 对象，它包含了 SQL 语句的信息
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        // 获取参数对象
        Object parameter = invocation.getArgs()[1];
        // 获取原始的 BoundSql 对象
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        // 获取 SQL 语句
        String sql ="";
        String origSql = boundSql.getSql().replaceAll("\\s", " ");
        Statement parse = CCJSqlParserUtil.parse(origSql);
        if(sqlMap.containsKey(origSql)){
            sql = (String)sqlMap.get(origSql);
        }else{
            if(parse instanceof Select){
                sql = getNewSql(origSql);
            }else if(parse instanceof Update){
                sql = getNewUpdateSql(origSql);
            }else if(parse instanceof Replace){
                sql = getNewReplaceSql(origSql);
            }else if(parse instanceof Insert){
                sql = getNewInsertSql(origSql);
            }else{
                sql = origSql;
            }
            sqlMap.put(origSql,sql);
        }

        // 创建新的 BoundSql 对象，使用修改后的 SQL
        BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), sql,
                boundSql.getParameterMappings(), boundSql.getParameterObject());
        // 把新的查询放到statement里
        MappedStatement newMs = newMappedStatement(mappedStatement, new BoundSqlSource(newBoundSql));
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        for (ParameterMapping mapping : parameterMappings) {
            String prop = mapping.getProperty();
            if (boundSql.hasAdditionalParameter(prop)) {
                newBoundSql.setAdditionalParameter(prop, boundSql.getAdditionalParameter(prop));
            }
        }
        // 继续执行原方法
        args[0] = newMs;
        if(parse instanceof  Select){
            if (args.length == 6) {
                args[5] = newMs.getBoundSql(parameter);
            }
        }
        return invocation.proceed();
    }


    private MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder = new
                MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length > 0) {
            builder.keyProperty(ms.getKeyProperties()[0]);
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());
        return builder.build();
    }

        /**
         * 获取处理过的sql update
         * @return
         */
    private String getNewUpdateSql(String oldSql ) throws Exception {
        String newSql = oldSql.replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_DIAN, SystemConfigConstants.SQL_FUNCTION_KEY_DOUS).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_CONCAT, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_group_concat, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_FORMAT_str01, SystemConfigConstants.SQL_FUNCTION_FORMAT_str02);
        newSql=JSqlParserExample.transUpdateOldSql(newSql);
        return newSql;
    }

    /**
     * 获取处理过的replace Sql
     * @param oldSql
     * @return
     * @throws Exception
     */
    private String getNewReplaceSql(String oldSql ) throws Exception {
        String newSql = oldSql.replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_DIAN, SystemConfigConstants.SQL_FUNCTION_KEY_DOUS).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_CONCAT, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_group_concat, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_FORMAT_str01, SystemConfigConstants.SQL_FUNCTION_FORMAT_str02);
        newSql=JSqlParserExample.transReplaceOldSql(newSql);
        return newSql;
    }

    private String getNewInsertSql(String oldSql ) throws Exception {
        String newSql = oldSql.replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_DIAN, SystemConfigConstants.SQL_FUNCTION_KEY_DOUS).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_CONCAT, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_group_concat, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_FORMAT_str01, SystemConfigConstants.SQL_FUNCTION_FORMAT_str02);
        newSql=JSqlParserExample.transInsertOldSql(newSql);
        return newSql;
    }



    /**
     * 获取处理过的sql SELECT
     * @return
     */
    private String getNewSql(String oldSql ) throws Exception {
        long startTime = System.currentTimeMillis();
        String newSql = oldSql.replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_DIAN, SystemConfigConstants.SQL_FUNCTION_KEY_DOUS).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_CONCAT, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_group_concat, SystemConfigConstants.SQL_FUNCTION_KEY_GROUP_wm_concat).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_KEY_date_sub, SystemConfigConstants.SQL_FUNCTION_DATE_SUB).
                replaceAll(SystemConfigConstants.SQL_FUNCTION_FORMAT_str01, SystemConfigConstants.SQL_FUNCTION_FORMAT_str02);
        newSql=JSqlParserExample.transOldSql(newSql);
        long endTime = System.currentTimeMillis();
        System.out.println("2==============="+(endTime-startTime));
        return newSql;
    }



    @Override
    public Object plugin(Object target) {
//        log.info("MysqlInterceptor plugin>>>>>>>{}", target);
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        String dialect = properties.getProperty("dialect");
//        log.info("mybatis intercept dialect:>>>>>>>{}", dialect);
    }

    /**
     * 定义一个内部辅助类，作用是包装 SQL
     */
    class BoundSqlSource implements SqlSource {
        private BoundSql boundSql;
        public BoundSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }
        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }

    }

}
