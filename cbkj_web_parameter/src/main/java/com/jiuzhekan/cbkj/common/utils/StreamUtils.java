package com.jiuzhekan.cbkj.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/24 10:22
 * @Version 1.0
 */
public class StreamUtils {
    /**
     * 创建保持顺序的去重收集器
     * @param keyExtractor 去重字段提取函数
     * @param <T> 元素类型
     * @param <R> 键类型
     * @return 自定义收集器
     */
    public static <T, R> Collector<T, ?, List<T>> distinctBy(Function<T, R> keyExtractor) {
        return Collectors.collectingAndThen(
                Collectors.toMap(
                        keyExtractor,
                        p -> p,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ), map -> new ArrayList<>(map.values())
        );
    }

    /**
     * 返回一个 Collector，用于将流收集为 Map，Key 由 keyMapper 提供
     * 自动处理 Key 重复（保留第一个出现的值）
     */
    /**
     * 返回一个 Collector，用于将流收集为 LinkedHashMap，保持插入顺序
     */
    public static <T, K> Collector<T, ?, Map<K, T>> toMapByKey(Function<T, K> keyMapper) {
        return Collectors.toMap(
                keyMapper,
                Function.identity(),
                (existing, replacement) -> existing,
                LinkedHashMap::new
        );
    }

    /**
     * 通用字段抽取方法，支持任意类型
     *
     * @param list        实体对象列表
     * @param fieldMapper 字段映射函数，如 TCourse::getCourseId
     * @param <T>         实体类型
     * @param <R>         字段类型（如 Long, Integer, String）
     * @return 抽取后的字段列表
     */
    public static <T, R> List<R> extractField(List<T> list, Function<T, R> fieldMapper) {
        if (list == null || fieldMapper == null) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(fieldMapper)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 通用 List 转 Map 方法（支持任意类型转换）
     *
     * @param list       源数据列表
     * @param keyMapper  键映射函数，如 SysDictionary::getCode
     * @param <T>        列表元素类型
     * @param <K>        映射键的类型
     * @return 转换后的 Map
     */
    public static <T, K> Map<K, T> toMap(List<T> list, Function<T, K> keyMapper) {
        return Optional.ofNullable(list)
                .orElse(new ArrayList<>())
                .stream()
                .filter(p->keyMapper.apply(p)!=null)
                .collect(Collectors.toMap(
                        keyMapper,
                        v -> v,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * list转成map
     * @param list 源数据列表
     * @param keyFunction 键映射函数，如 SysDictionary::getCode
     * @param <T> 列表元素类型
     * @return 转换后的 Map
     */
    public static <T> Map<String, T> toMap(List<T> list, Function<T, String>... keyFunction) {
        return Optional.ofNullable(list)
                .orElse(new ArrayList<>())
                .stream()
                .filter(p -> {
                    for (Function<T, String> fieldExtractor : keyFunction) {
                        if (StrUtil.isBlank(fieldExtractor.apply(p))) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toMap(
                        k -> {
                            List<String> values = new ArrayList<>();
                            for (Function<T, String> fieldExtractor : keyFunction) {
                                values.add(fieldExtractor.apply(k));
                            }
                            return CollUtil.join(values,"|");
                        },
                        v -> v,
                        (v1, v2) -> v1
                ));
    }

    /**
     * list转groupMap
     * @param list
     * @param fieldFunction
     * @param <T>
     * @return
     */
    public static <T> Map<String,List<T>> toGroupMap(List<T> list,Function<T, String> fieldFunction){
        return Optional.ofNullable(list)
                .orElse(new ArrayList<>())
                .stream()
                .filter(p->fieldFunction.apply(p)!=null)
                .collect(Collectors.groupingBy(fieldFunction));
    }

    /**
     * list转groupLinkedMap 保证原本顺序
     * @param list
     * @param fieldFunction
     * @param <T>
     * @return
     */
    public static <T> Map<String,List<T>> toGroupLinkedMap(List<T> list,Function<T, String> fieldFunction){
        return Optional.ofNullable(list)
                .orElse(new ArrayList<>())
                .stream()
                .filter(p->fieldFunction.apply(p)!=null)
                .collect(Collectors.groupingBy(
                        fieldFunction,
                        LinkedHashMap::new,
                        Collectors.toList()));
    }

}
