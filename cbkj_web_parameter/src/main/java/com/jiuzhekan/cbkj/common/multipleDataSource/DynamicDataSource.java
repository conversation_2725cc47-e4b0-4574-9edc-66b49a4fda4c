package com.jiuzhekan.cbkj.common.multipleDataSource;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * 动态数据源
 * AbstractRoutingDataSource(每执行一次数据库，动态获取DataSource)
 * <AUTHOR>
 */
public class DynamicDataSource extends AbstractRoutingDataSource {
    @Override
    protected Object determineCurrentLookupKey() {
        return DynamicDataSourceContextHolder.getDataSourceType();
    }
}