package com.jiuzhekan.cbkj.common.utils.excel;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;


public class FileUtils {

	/**
	 * 读取配置文件
	 */
	public static Properties properties = new Properties();

	static {
		try {
			String path = "config.properties";
			InputStream inStream = FileUtils.class.getResourceAsStream(path);
			if (inStream == null) {
				inStream = FileUtils.class.getClassLoader().getResourceAsStream("/" + path);
			}
			properties.load(inStream);
		} catch (IOException e) {
			e.printStackTrace();
		}

	}

	public static String getMessageUrl(){
		return properties.getProperty("messageUrl");
	}
	
	public static String getMessageUserAccount(){
		return properties.getProperty("messageUserAccount");
	}
	
	public static String getMessagePassword(){
		return properties.getProperty("messagePassword");
	}
	
	public static String getMessageMessage(){
		return properties.getProperty("messageMessage");
	}
	
	public static String getWechatUrl(){
		return properties.getProperty("wechatUrl");
	}
	
	public static String getRegisterCode(){
		return properties.getProperty("registerCode");
	}
	
	// 上传文件的目录
	public static String getLocationPath() {
		return properties.getProperty("uploadFilePath");
	}

	// 文件访问路径
	public static String getFileRootUrl() {
		return properties.getProperty("fileRootUrl");
	}
	// 二维码存储根路径
	public static String qrCodeFilePath() {
		return properties.getProperty("qrCodeFilePath");
	}
	
	public static String getWebRootUrl() {
		return properties.getProperty("webRootUrl");
	}
//	// 文件完整访问路径
//	public static String getFielUrl(FileInfo fileInfo) {
//		String url =""+ properties.getProperty("fileRootUrl")  + fileInfo.getFile_path() + fileInfo.getOriginal_name();
//		url =  url.replace(File.separatorChar, '/');
//		return url;
//	}
//	//二维码完整存储路径
//		public static String qrCodeFileAllPath(FileInfo fileInfo) {
//			String url =""+qrCodeFilePath() + fileInfo.getFile_path() + fileInfo.getOriginal_name();
//			url =  url.replace(File.separatorChar, '/');
//			return url;
//		}
	// 二维码内容路径
	public static String getCodeContentUrl() {
		return properties.getProperty("qrCodeRootUrl");
	}
	
	public static String getTimeFormat(Date date, String string) {
		SimpleDateFormat sdFormat;
		if (isEmptyString(string)) {
			sdFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		} else {
			sdFormat = new SimpleDateFormat(string);
		}
		try {
			return sdFormat.format(date);
		} catch (Exception e) {
			return "";
		}
	}
	
	public static boolean isEmptyString(String value) {
		if (value == null || value.length() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * �?查路径是否存在，不存在则创建路径
	 * 
	 * @param path
	 */
	public static void checkPath(String path) {
		String[] paths = null;
		if (path.contains("/")) {
			paths = path.split(File.separator);
		} else {
			paths = path.split(File.separator + File.separator);
		}
		if (paths == null || paths.length == 0) {
			return;
		}
		String pathdir = "";
		for (String string : paths) {
			pathdir = pathdir + string + File.separator;
			File file = new File(pathdir);
			if (!file.exists()) {
				file.mkdir();
			}
		}
	}

}