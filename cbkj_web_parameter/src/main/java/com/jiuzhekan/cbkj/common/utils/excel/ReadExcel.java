package com.jiuzhekan.cbkj.common.utils.excel;

import com.jiuzhekan.cbkj.beans.business.sysCode.THisCodeItem;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReadExcel {
    private final static String excel2003L = ".xls";    //2003- 版本的excel
    private final static String excel2007U = ".xlsx";   //2007+ 版本的excel
    //总行数
    private int totalRows = 0;
    //总条数
    private int totalCells = 0;
    //错误信息接收器
    private String errorMsg;

    //构造方法
    public ReadExcel() {
    }

    //获取总行数
    public int getTotalRows() {
        return totalRows;
    }

    //获取总列数
    public int getTotalCells() {
        return totalCells;
    }

    //获取错误信息-暂时未用到暂时留着
    public String getErrorInfo() {
        return errorMsg;
    }

    /**
     * 读EXCEL文件，获取客户信息集合
     *
     * @param
     * @return
     */
    public List<Customer> getExcelInfo(MultipartFile Mfile) {
        String fileName = Mfile.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        //把spring文件上传的MultipartFile转换成CommonsMultipartFile类型
        CommonsMultipartFile cf = (CommonsMultipartFile) Mfile; //获取本地存储路径
        File file = new File("E:\\Excel");
        //创建一个目录 （它的路径名由当前 File 对象指定，包括任一必须的父路径。）
        if (!file.exists()) {
            file.mkdirs();
        }
        //新建一个文件
        File file1 = new File("E:\\Excel\\" + System.currentTimeMillis() + fileType);
        //将上传的文件写入新建的文件中
        try {
            cf.getFileItem().write(file1);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //初始化客户信息的集合
        List<Customer> customerList = new ArrayList<Customer>();
        //初始化输入流
        FileInputStream is = null;
        Workbook wb = null;
        try {
            //根据新建的文件实例化输入流
            is = new FileInputStream(file1);
            //根据excel里面的内容读取客户信息
            if (excel2003L.equals(fileType)) {
                wb = new HSSFWorkbook(is);  //2003-
            } else if (excel2007U.equals(fileType)) {
                wb = new XSSFWorkbook(is);  //2007+
            } else {
                throw new Exception("解析的文件格式有误！");
            }
            //读取Excel里面客户的信息
            customerList = readExcelValue(wb);
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    is = null;
                    e.printStackTrace();
                }
            }
        }
        return customerList;
    }

    /**
     * 读取Excel里面客户的信息
     *
     * @param wb
     * @return
     */
    private List<Customer> readExcelValue(Workbook wb) {
        //得到第一个shell
        Sheet sheet = wb.getSheetAt(0);

        //得到Excel的行数
        this.totalRows = sheet.getPhysicalNumberOfRows();

        //得到Excel的列数(前提是有行数)
        if (totalRows >= 1 && sheet.getRow(0) != null) {//判断行数大于一，并且第一行必须有标题（这里有bug若文件第一行没值就完了）
            this.totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
        } else {
            return null;
        }

        List<Customer> customerList = new ArrayList<Customer>();//声明一个对象集合
        Customer customer;//声明一个对象

        //循环Excel行数,从第二行开始。标题不入库
        for (int r = 1; r < totalRows; r++) {
            Row row = sheet.getRow(r);
            if (row == null) {
                continue;
            }
            customer = new Customer();

            //循环Excel的列
            for (int c = 0; c < this.totalCells; c++) {
                Cell cell = row.getCell(c);
                if (null != cell) {
                    if (c == 0) {
                        customer.setId(CommonUtils.parseInt(getValue(cell), 0));//得到行中第一个值
                    } else if (c == 1) {
                        customer.setShcoolName(getValue(cell));
                    } else if (c == 2) {
                        customer.setMonth(getValue(cell));//得到行中第三个值
                    } else if (c == 3) {
                        customer.setPass_eva(CommonUtils.parseFloat(getValue(cell), 5));
                    } else if (c == 4) {
                        customer.setTeach_eva((long) CommonUtils.parseFloat(getValue(cell), 5));
                    } else if (c == 5) {
                        customer.setEnvironment_eva((long) CommonUtils.parseFloat(getValue(cell), 5));
                    }
                }
            }
            //添加对象到集合中
            customerList.add(customer);
        }
        return customerList;
    }

//
//    public List<MonCustomer> getExcel(MultipartFile Mfile){
//
//        //把spring文件上传的MultipartFile转换成CommonsMultipartFile类型
//        CommonsMultipartFile cf= (CommonsMultipartFile)Mfile; //获取本地存储路径
//        File file = new  File(FileUtils.getLocationPath());
//        //创建一个目录 （它的路径名由当前 File 对象指定，包括任一必须的父路径。）
//        if (!file.exists()) file.mkdirs();
//        //新建一个文件
//        File file1 = new File(FileUtils.getLocationPath() + new Date().getTime() + ".xls");
//        //将上传的文件写入新建的文件中
//        try {
//            cf.getFileItem().write(file1);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        //初始化客户信息的集合
//        List<MonCustomer> customerList=new ArrayList<MonCustomer>();
//        //初始化输入流
//        FileInputStream is = null;
//        Workbook wb = null;
//        try{
//            //根据新建的文件实例化输入流
//            is = new FileInputStream(file1);
//            //根据excel里面的内容读取客户信息
//
//            wb = new HSSFWorkbook(is);
//
//            //读取Excel里面客户的信息
//            customerList=readExcelMon(wb);
//            is.close();
//        }catch(Exception e){
//            e.printStackTrace();
//        } finally{
//            if(is !=null)
//            {
//                try{
//                    is.close();
//                }catch(IOException e){
//                    is = null;
//                    e.printStackTrace();
//                }
//            }
//        }
//        return customerList;
//    }

    /**
     * 读取Excel里面客户的信息
     * @param wb
     * @return
     */
//    private List<MonCustomer> readExcelMon(Workbook wb){
//        //得到第一个shell
//        Sheet sheet=wb.getSheetAt(0);
//
//        //得到Excel的行数
//        this.totalRows=sheet.getPhysicalNumberOfRows();
//
//        //得到Excel的列数(前提是有行数)
//        if(totalRows>=1 && sheet.getRow(0) != null){//判断行数大于一，并且第一行必须有标题（这里有bug若文件第一行没值就完了）
//            this.totalCells=sheet.getRow(0).getPhysicalNumberOfCells();
//        }else{
//            return null;
//        }
//
//        List<MonCustomer> customerList=new ArrayList<MonCustomer>();//声明一个对象集合
//        MonCustomer customer;//声明一个对象
//
//        //循环Excel行数,从第二行开始。标题不入库
//        for(int r=1;r<totalRows;r++){
//            Row row = sheet.getRow(r);
//            if (row == null) continue;
//            customer = new MonCustomer();
//
//            //循环Excel的列
//            for(int c = 0; c <this.totalCells; c++){
//                Cell cell = row.getCell(c);
//                if (null != cell){
//                    if(c==0){
//                        customer.setId(CommonUtils.parseInt(getValue(cell), 0));//得到行中第一个值
//                    }else if(c==1){
//                        customer.setMonitorName(getValue(cell));
//                    }else if(c==2){
//                        customer.setMonth(getValue(cell));//得到行中第三个值
//                    }else if(c==3){
//                        customer.setZh_eva((long)CommonUtils.parseFloat(getValue(cell), 5));
//                    }else if(c==4){
//                        customer.setServer_eva((long)CommonUtils.parseFloat(getValue(cell), 5));
//                    }else if(c==5){
//                        customer.setEnvironment_eva((long)CommonUtils.parseFloat(getValue(cell), 5));
//                    }
//                }
//            }
//            //添加对象到集合中
//            customerList.add(customer);
//        }
//        return customerList;
//    }

    /**
     * 得到Excel表中的值
     *
     * @param cell Excel中的每一个格子
     * @return Excel中每一个格子中的值
     */
    @SuppressWarnings({"static-access", "unused"})
    private String getValue(Cell cell) {
        CellType cellType = cell.getCellType();
        int code = cellType.getCode();
        if (cell.getCellType().getCode() == 4) {
            // 返回布尔类型的值
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType().getCode() == 0) {
            // 返回数值类型的值
            return String.valueOf(cell.getNumericCellValue());
        } else {
            // 返回字符串类型的值
            return String.valueOf(cell.getStringCellValue());
        }
    }


    public static String double2Str(Double d) {
        if (d == null) {
            return "";
        }
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        return (nf.format(d));
    }


    public List<THisCodeItem> getExcelInfoByHisCodeItem(MultipartFile Mfile) {
        String fileName = Mfile.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        //初始化客户信息的集合
        List<THisCodeItem> hisDepList = new ArrayList<THisCodeItem>();
        //初始化输入流
        InputStream is = null;
        Workbook wb = null;
        try {
            //根据新建的文件实例化输入流
            is = Mfile.getInputStream();
            //根据excel里面的内容读取客户信息
            if (excel2003L.equals(fileType)) {
                wb = new HSSFWorkbook(is);  //2003-
            } else if (excel2007U.equals(fileType)) {
                wb = new XSSFWorkbook(is);  //2007+
            } else {
                throw new Exception("解析的文件格式有误！");
            }
            //读取Excel里面客户的信息
            hisDepList = readExcelValueByHisCodeItem(wb);
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    is = null;
                    e.printStackTrace();
                }
            }
        }
        return hisDepList;
    }

    private List<THisCodeItem> readExcelValueByHisCodeItem(Workbook wb) {
        //得到第一个shell
        Sheet sheet = wb.getSheetAt(0);
        Short a = 1;
        Double b = 0.0;
        //得到Excel的行数
        this.totalRows = sheet.getPhysicalNumberOfRows();

        //得到Excel的列数(前提是有行数)
        if (totalRows >= 1 && sheet.getRow(0) != null) {//判断行数大于一，并且第一行必须有标题（这里有bug若文件第一行没值就完了）
            this.totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
        } else {
            return null;
        }

        List<THisCodeItem> hisDepList = new ArrayList<THisCodeItem>();//声明一个对象集合
        THisCodeItem hisDep;//声明一个对象

        //循环Excel行数,从第二行开始。标题不入库
        for (int r = 1; r < totalRows; r++) {
            Row row = sheet.getRow(r);
            if (row == null) {
                continue;
            }
            hisDep = new THisCodeItem();

            //循环Excel的列
            for (int c = 0; c < this.totalCells; c++) {
                Cell cell = row.getCell(c);
                if (null != cell) {
                    if (c == 0) {
                        // studentDO.setId(CommonUtils.parseInt(getValue(cell), 0));//得到行中第一个值
                        String s = double2Str(CommonUtils.parseDouble(getValue(cell), b));
                        hisDep.setAppId(s);
                    } else if (c == 1) {
                        String s = double2Str(CommonUtils.parseDouble(getValue(cell), b));
                        hisDep.setInsCode(s);
                    } else if (c == 2) {
                        String s = double2Str(CommonUtils.parseDouble(getValue(cell), b));
                        hisDep.setItemHisId(s);
                    } else if (c == 3) {
                        hisDep.setItemHisName(getValue(cell));
                    } else if (c == 4) {
                        String s = double2Str(CommonUtils.parseDouble(getValue(cell), b));
                        hisDep.setCodeId(s);
                    }
                }

            }
            hisDep.setCreateDate(new Date());
            hisDep.setCreateUser(AdminUtils.getCurrentHr().getUserId());
            hisDep.setCreateUserName(AdminUtils.getCurrentHr().getUsername());
            hisDep.setIsDel(Constant.BASIC_DEL_NO);
            //添加对象到集合中
            hisDepList.add(hisDep);
        }
        return hisDepList;
    }
}