package com.jiuzhekan.cbkj.common.interceptor.tianan;

import com.jiuzhekan.cbkj.common.interceptor.SecretStrategy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service(value = "defaultSecret")
public class DefaultSecretUtils implements SecretStrategy {
    @Override
    public String encrypt(String input) {
        return input;
    }

    @Override
    public String decrypt(String input) {
        return input;
    }
}
