package com.jiuzhekan.cbkj.common.dm;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.arithmetic.Addition;
import net.sf.jsqlparser.expression.operators.arithmetic.Division;
import net.sf.jsqlparser.expression.operators.arithmetic.Subtraction;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.*;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.replace.Replace;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;

import java.util.ArrayList;
import java.util.List;

/**
 * Sql 解析器  打乱重组
 */
public class JSqlParserExample {

    //转换查询语句
    public static String transOldSql(String oldSql) {
        if(!sqlIsToDm(oldSql)){
            return oldSql;
        }
        try {
            Select select = (Select) CCJSqlParserUtil.parse(oldSql);
            PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
            transPlainSelect(plainSelect);
            return select.toString();
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return oldSql;
    }

    //判断sql语句是否有必要转达梦
    public static Boolean sqlIsToDm(String oldSql){
        Boolean isTodm = true;
        if (oldSql.indexOf(SystemConfigConstants.SQL_FUNCTION_CONVERT)!=-1
                || oldSql.indexOf(SystemConfigConstants.SQL_FUNCTION_convert)!=-1
                || oldSql.indexOf(SystemConfigConstants.SQL_FUNCTION_DATE_ADD)!=-1
                || oldSql.indexOf(SystemConfigConstants.SQL_FUNCTION_DATE_SUB)!=-1
                || oldSql.indexOf(SystemConfigConstants.SQL_FUNCTION_DATEDIFF)!=-1
        ) {
            isTodm = true;
        }
        return isTodm;
    }

    //转换replace语句
    public static String transReplaceOldSql(String oldSql) {
        StringBuilder sb = new StringBuilder();
        try {
            Statement parse = CCJSqlParserUtil.parse(oldSql);
            if(parse instanceof Replace){
                Replace replace = (Replace) parse;

                List<String> replaceSqlFirstColums = getReplaceSqlFirstColums(replace);
                String replaceSqlColums = getReplaceSqlColums(replace);
                String replaceSqlColumsNokey = getReplaceSqlColumsNokey(replace, replaceSqlFirstColums);
                sb.append(SystemConfigConstants.SQL_REPLACE_MERGE_INTO);
                sb.append(replace.getTable().toString());
                sb.append(SystemConfigConstants.SQL_REPLACE_USING+getReplaceSqlItemList(replace)+
                        SystemConfigConstants.SQL_REPLACE_A_ON_A+
                        getReplaceSqlWhere(replaceSqlFirstColums)+
                        SystemConfigConstants.SQL_REPLACE_WHEN_MATCHED_THEN);
                sb.append(getReplaceSqlSetColums(replaceSqlColumsNokey));
                sb.append(SystemConfigConstants.SQL_REPLACE_MATCHED_THEN);
                sb.append(replaceSqlColums);
                sb.append(SystemConfigConstants.SQL_REPLACE_VALUES);
                sb.append(replaceSqlColums);
                sb.append(SystemConfigConstants.SQL_REPLACE_KH);
            }

        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    public static String getReplaceSqlItemList(Replace replace){
        List<Column> columns = replace.getColumns();
        ItemsList itemsList = replace.getItemsList();
        StringBuilder sb = new StringBuilder();
        if(itemsList instanceof  ExpressionList){
            ExpressionList expressionList = (ExpressionList)itemsList;
            sb.append(transReplaceExpressionListSql(expressionList,columns));
        }
        if(itemsList instanceof  MultiExpressionList){
            MultiExpressionList expressionList = (MultiExpressionList)itemsList;
            List<ExpressionList> expressionLists = expressionList.getExpressionLists();
            if(expressionLists!=null && expressionLists.size()>0){
                int i = 0;
                for(ExpressionList ex:expressionLists){
                    sb.append(transReplaceExpressionListSql(ex,columns));
                    if(i!=expressionLists.size()-1){
                        sb.append(SystemConfigConstants.SQL_REPLACE_union);
                    }
                    i++;
                }
            }
        }
       
        return sb.toString();
    }
    public static String transReplaceExpressionListSql(ExpressionList expressionList,List<Column> columns){
        StringBuilder sb = new StringBuilder();
        sb.append(SystemConfigConstants.SQL_REPLACE_SELECT);
        List<Expression> expressions = expressionList.getExpressions();
        if(expressions!=null && expressions.size()>0){
            for(int i=0;i<expressions.size();i++){
                sb.append( expressions.get(i).toString() +" as "+ columns.get(i).getColumnName());
                if(i!=expressions.size()-1){
                    sb.append(", ");
                }
            }
        }
        sb.append(SystemConfigConstants.SQL_REPLACE_FROM_DUAL);
        return sb.toString();
    }

    /**
     * 获取主键集合  默认获取带标注”`“ 的，没有取第一个
     * @param replace
     * @return
     */
    public static List<String> getReplaceSqlFirstColums(Replace replace){
        List<String> keys = new ArrayList<>();
        List<Column> columns = replace.getColumns();
        for(Column col:columns){
            if(col.getColumnName().indexOf(SystemConfigConstants.SQL_FUNCTION_KEY_DIAN)!=-1){
                keys.add(col.getColumnName());
            }
        }
        if(keys==null || keys.size()==0){
            keys.add(columns.get(0).getColumnName());
        }
        return keys;
    }

    public static String getReplaceSqlWhere(List<String> columns){
        StringBuilder sb = new StringBuilder();
        if(columns!=null && columns.size()>0){
            int i = 1;
            for(String s:columns){
                sb.append(" A."+s+" = T."+s+" ");
                if(i != columns.size()){
                    sb.append(" and ");
                }
                i++;
            }
        }
        return sb.toString();
    }

    public static String getReplaceSqlSetColums(String replaceSqlColums){
        String[] split = replaceSqlColums.split(",");
        StringBuilder sb = new StringBuilder();
        if(split!=null && split.length>0){
            int i = 1;
            for(String s:split){
                sb.append(" T."+s+" = A."+s+" ");
                if(i != split.length){
                    sb.append(",");
                }
                i++;
            }
//            for(Column col:columns){
//                if(i!=1){
//                    sb.append(" T."+col.getColumnName()+" = A."+col.getColumnName()+" ");
//                    if(i != columns.size()){
//                        sb.append(",");
//                    }
//                }
//                i++;
//            }
        }
        return sb.toString();
    }

    public static String getReplaceSqlColums(Replace replace){
        List<Column> columns = replace.getColumns();
        StringBuilder sb = new StringBuilder();
        if(columns!=null && columns.size()>0){
            int i = 1;
            for(Column col:columns){
                sb.append(col.getColumnName());
                if(i != columns.size()){
                    sb.append(",");
                }
                i++;
            }
        }
        return sb.toString();
    }

    public static String getReplaceSqlColumsNokey(Replace replace,List<String> replaceSqlFirstColums){
        List<Column> columns = replace.getColumns();
        StringBuilder sb = new StringBuilder();
        if(columns!=null && columns.size()>0){
            int i = 1;
            for(Column col:columns){
                if(!replaceSqlFirstColums.contains(col.getColumnName())){
                    sb.append(col.getColumnName());
                    if(i != columns.size()){
                        sb.append(",");
                    }
                }
                i++;
            }
        }
        return sb.toString();
    }

    public static String transUpdateOldSql(String oldSql) {
        try {
            Statement parse = CCJSqlParserUtil.parse(oldSql);
            if(parse instanceof Update){
                Update update = (Update) parse;
                Expression where = update.getWhere();
                transExpression(where);
                return update.toString();
            }

        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return oldSql;
    }

    public static String transInsertOldSql(String oldSql) {
        try {
            Statement parse = CCJSqlParserUtil.parse(oldSql);
            if(parse instanceof Insert){
                Insert insert = (Insert) parse;
                return insert.toString();
            }

        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return oldSql;
    }

    private static void transPlainSelect(PlainSelect plainSelect){
        String sql = plainSelect.toString();
        if(!sqlIsToDm(sql)){
            return;
        }
        List<SelectExpressionItem> itemList = new ArrayList<>();
        for(SelectItem selectItem :plainSelect.getSelectItems()){
            if(selectItem instanceof SelectExpressionItem){
                SelectExpressionItem implObj = (SelectExpressionItem) selectItem;
                itemList.add(implObj);
            }
        }
        if(itemList!=null && itemList.size()>0){
            for (SelectExpressionItem selectItem: itemList) {
                // 检查是否是Function类型的SelectItem
                transExpression(selectItem.getExpression());
            }
        }
        FromItem fromItem = plainSelect.getFromItem();
        if(fromItem!=null && fromItem instanceof  SubSelect){
            SubSelect subSelect = (SubSelect) fromItem;
            SelectBody selectBody = subSelect.getSelectBody();
            if(selectBody !=null){
                if(selectBody instanceof SetOperationList){
                    SetOperationList sol = (SetOperationList)selectBody;
                    List<SelectBody> selects = sol.getSelects();
                    for(SelectBody sb:selects){
                        if(sb instanceof PlainSelect){
                            transPlainSelect((PlainSelect)sb);
                        }
                    }
                }
                if(selectBody instanceof PlainSelect){
                    transPlainSelect((PlainSelect)selectBody);
                }
            }
        }
        Expression where = plainSelect.getWhere();
        transExpression(where);

    }


    private static void transExpression(Expression expression){
        if (expression instanceof Function
                || expression instanceof Subtraction
                || expression instanceof Parenthesis
                || expression instanceof GreaterThan
                || expression instanceof GreaterThanEquals
                || expression instanceof AndExpression
                || expression instanceof InExpression
                || expression instanceof SubSelect
                || expression instanceof Addition
                || expression instanceof Division
                || expression instanceof CaseExpression
                || expression instanceof MinorThanEquals
        ) {
            if(expression instanceof Function){
                Function function = (Function) expression;
                // 检查是否是CONVERT函数
                transfunction(function);
            }
            if(expression instanceof Subtraction){
                Subtraction subtraction = (Subtraction) expression;
                Expression leftExpression = subtraction.getLeftExpression();
                Expression rightExpression = subtraction.getRightExpression();
                transExpression(leftExpression);
                transExpression(rightExpression);
            }
            if(expression instanceof Parenthesis){
                transParenthesis(expression);
            }
            if(expression instanceof GreaterThanEquals){
                transGreaterThanEquals(expression);
            }
            if(expression instanceof GreaterThan){
                transGreaterThan(expression);
            }
            if(expression instanceof AndExpression){
                transAndExpression(expression);
            }
            if(expression instanceof InExpression){
                transInExpression(expression);
            }
            if(expression instanceof Addition){
                transAddition(expression);
            }
            if(expression instanceof  SubSelect){
                SubSelect subSelect = (SubSelect) expression;
                PlainSelect plainSelect = (PlainSelect) subSelect.getSelectBody();
                transPlainSelect(plainSelect);
            }
            if(expression instanceof MinorThanEquals){
                MinorThanEquals thanEquals = (MinorThanEquals) expression;
                transExpression(thanEquals.getLeftExpression());
                transExpression(thanEquals.getRightExpression());
            }
            if(expression instanceof Division){
                transDivision(expression);
            }
            if(expression instanceof CaseExpression){
                transCaseExpression(expression);
            }
        }

    }
    private static void transInExpression(Expression expression){
        InExpression inExpression = (InExpression) expression;
        transExpression(inExpression.getLeftExpression());
        ItemsList rightItemsList = inExpression.getRightItemsList();
        if(rightItemsList instanceof SubSelect ){
            SubSelect subSelect = (SubSelect) rightItemsList;
            PlainSelect plainSelect = (PlainSelect) subSelect.getSelectBody();
            transPlainSelect(plainSelect);
        }
    }
    private static void transDivision(Expression expression){
        Division addition = (Division) expression;
        transExpression(addition.getLeftExpression());
        transExpression(addition.getRightExpression());
    }
    private static void transAddition(Expression expression){
        Addition addition = (Addition) expression;
        transExpression(addition.getLeftExpression());
        transExpression(addition.getRightExpression());
    }
    private static void transAndExpression(Expression expression){
        AndExpression greaterThanEquals = (AndExpression) expression;
        transExpression(greaterThanEquals.getLeftExpression());
        transExpression(greaterThanEquals.getRightExpression());
    }
    private static void transGreaterThan(Expression expression){
        GreaterThan greaterThanEquals = (GreaterThan) expression;
        transExpression(greaterThanEquals.getLeftExpression());
        transExpression(greaterThanEquals.getRightExpression());
    }
    private static void transGreaterThanEquals(Expression expression){
        GreaterThanEquals greaterThanEquals = (GreaterThanEquals) expression;
        transExpression(greaterThanEquals.getLeftExpression());
        transExpression(greaterThanEquals.getRightExpression());
    }

    private static void transCaseExpression(Expression expression){
        if(expression instanceof  CaseExpression ){
            CaseExpression caseExpression = (CaseExpression) expression;
            List<WhenClause> whenClauses = caseExpression.getWhenClauses();
            if(whenClauses!=null && whenClauses.size()>0){
                for(WhenClause whenClause:whenClauses){
                    transExpression(whenClause.getWhenExpression());
                    transExpression(whenClause.getThenExpression());
                }
            }
        }

    }
    private static void transParenthesis(Expression expression){
        Parenthesis parenthesis = (Parenthesis) expression;
        Expression expression1 = parenthesis.getExpression();
        if(expression1 instanceof CaseExpression ){
            CaseExpression caseExpression = (CaseExpression) parenthesis.getExpression();
            List<WhenClause> whenClauses = caseExpression.getWhenClauses();
            if(whenClauses!=null && whenClauses.size()>0){
                for(WhenClause whenClause:whenClauses){
                    Expression whenExpression = whenClause.getWhenExpression();
                    if(whenExpression instanceof Parenthesis){
                        transParenthesis(whenExpression);
                    }
                }
            }
        }
        if(expression1 instanceof OrExpression){
            OrExpression ore = (OrExpression)expression1;
            transExpression(ore.getLeftExpression());
            transExpression(ore.getRightExpression());
        }
        if(expression1 instanceof Subtraction){
            Subtraction ore = (Subtraction)expression1;
            transExpression(ore.getLeftExpression());
            transExpression(ore.getRightExpression());
        }
    }
    
    private static void transfunction(Function function){
        if(function.getParameters()!=null && function.getParameters().getExpressions().size()>0){
            List<Expression> parameters = function.getParameters().getExpressions();
            for(Expression exp2:parameters){
                transExpression(exp2);
            }
        }
        String name = function.getName();
        if (name.indexOf(SystemConfigConstants.SQL_FUNCTION_CONVERT)!=-1
                || name.indexOf(SystemConfigConstants.SQL_FUNCTION_convert)!=-1
                || (function.getParameters()!=null && function.getParameters().getExpressions().toString().indexOf(SystemConfigConstants.SQL_FUNCTION_CONVERT)!=-1)) {
            // 确保CONVERT函数至少有两个参数
            if(SystemConfigConstants.SQL_FUNCTION_CONVERT.equals(name) || SystemConfigConstants.SQL_FUNCTION_convert.equals(name)  ){
                if (function.getParameters().getExpressions().size() >= 2) {
                    // 交换两个参数的位置
                    List<Expression> parameters = function.getParameters().getExpressions();
                    Expression temp = parameters.get(0);
                    parameters.set(0, parameters.get(1));
                    parameters.set(1, temp);
                }
            }

        }


        if (name.indexOf(SystemConfigConstants.SQL_FUNCTION_DATEDIFF)!=-1
                || (function.getParameters()!=null && function.getParameters().getExpressions().toString().indexOf(SystemConfigConstants.SQL_FUNCTION_DATEDIFF)!=-1)) {
            // 确保CONVERT函数至少有两个参数
            if(SystemConfigConstants.SQL_FUNCTION_DATEDIFF.equals(name)){
                if (function.getParameters().getExpressions().size() >= 2) {
                    // 交换两个参数的位置
                    List<Expression> parameters = function.getParameters().getExpressions();
                    Expression temp0 = parameters.get(0);
                    Expression temp1 = parameters.get(1);
                    try {
                        parameters.set(0, CCJSqlParserUtil.parseExpression(" DD "));
                        parameters.set(1, temp0);
                        parameters.add(temp1);
                    } catch (JSQLParserException e) {
                        e.printStackTrace();
                    }

                }
            }

        }

        if (name.indexOf(SystemConfigConstants.SQL_FUNCTION_DATE_ADD)!=-1 || (function.getParameters()!=null && function.getParameters().getExpressions().toString().indexOf(SystemConfigConstants.SQL_FUNCTION_DATE_ADD)!=-1)) {
            if(SystemConfigConstants.SQL_FUNCTION_DATE_ADD.equals(name)){
                if (function.getParameters().getExpressions().size() >= 2) {
                    // 交换两个参数的位置
                    List<Expression> parameters = function.getParameters().getExpressions();
                    Expression temp = parameters.get(0);
                    IntervalExpression intervalExpression = (IntervalExpression)parameters.get(1);
                    String intervalType = intervalExpression.getIntervalType();
                    String parameter = intervalExpression.getParameter();
                    try {
                        parameters.set(0, CCJSqlParserUtil.parseExpression(intervalType));
                        parameters.set(1,  CCJSqlParserUtil.parseExpression(parameter));
                    } catch (JSQLParserException e) {
                        e.printStackTrace();
                    }
                    parameters.add(temp);
                    function.setName(SystemConfigConstants.SQL_FUNCTION_DATEADD);
                }
            }
        }
        if (name.indexOf(SystemConfigConstants.SQL_FUNCTION_DATE_SUB)!=-1 || (function.getParameters()!=null
                && function.getParameters().getExpressions().toString().indexOf(SystemConfigConstants.SQL_FUNCTION_DATE_SUB)!=-1)) {
            // 确保CONVERT函数至少有两个参数
            if(SystemConfigConstants.SQL_FUNCTION_DATE_SUB.equals(name)){
                if (function.getParameters().getExpressions().size() >= 2) {
                    // 交换两个参数的位置
                    List<Expression> parameters = function.getParameters().getExpressions();
                    Expression temp = parameters.get(0);
                    IntervalExpression intervalExpression = (IntervalExpression)parameters.get(1);
                    String intervalType = intervalExpression.getIntervalType();
                    String parameter = intervalExpression.getParameter();
                    try {
                        parameters.set(0, CCJSqlParserUtil.parseExpression(intervalType));
                        parameters.set(1,  CCJSqlParserUtil.parseExpression(SystemConfigConstants.SQL_FUNCTION_KEY_CUT+parameter));
                    } catch (JSQLParserException e) {
                        e.printStackTrace();
                    }
                    parameters.add(temp);
                    function.setName(SystemConfigConstants.SQL_FUNCTION_DATEADD);
                }
            }
        }
    }
}