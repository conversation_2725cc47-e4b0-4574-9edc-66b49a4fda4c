package com.jiuzhekan.cbkj.common.dm;

/**
 * 系统配置常量
 */
public class SystemConfigConstants {
	public static final String DB_TYPE_MYSQL = "mysql";
	public static final String DB_TYPE_DM = "dm";


	public static final String SQL_FUNCTION_KEY_DIAN = "`";
	public static final String SQL_FUNCTION_KEY_NULL = "";
	public static final String SQL_FUNCTION_KEY_DOUS = "\"";
	public static final String SQL_FUNCTION_KEY_CUT = "-";
	public static final String SQL_FUNCTION_KEY_GROUP_CONCAT = "GROUP_CONCAT";
	public static final String SQL_FUNCTION_KEY_date_sub = "date_sub";

	public static final String SQL_FUNCTION_KEY_GROUP_wm_concat = "wm_concat";
	public static final String SQL_FUNCTION_KEY_group_concat = "group_concat";

	public static final String SQL_FUNCTION_DATE_SUB = "DATE_SUB";
	public static final String SQL_FUNCTION_DATE_ADD = "DATE_ADD";
	public static final String SQL_FUNCTION_DATEADD = "DATEADD";
	public static final String SQL_FUNCTION_DATEDIFF = "DATEDIFF";
	public static final String SQL_FUNCTION_CONVERT = "CONVERT";
	public static final String SQL_FUNCTION_convert = "convert";
	public static final String SQL_FUNCTION_SEPARATOR = "SEPARATOR ','";

	public static final String SQL_FUNCTION_FORMAT_str01 = "%Y年%m月%d日";
	public static final String SQL_FUNCTION_FORMAT_str02 = "%Y-%m-%d";

	public static final String SQL_REPLACE_MERGE_INTO = "MERGE INTO ";
	public static final String SQL_REPLACE_MATCHED_THEN = " WHEN NOT MATCHED THEN INSERT (";
	public static final String SQL_REPLACE_A_ON_A = ") as A on (";
	public static final String SQL_REPLACE_WHEN_MATCHED_THEN = ") WHEN MATCHED THEN UPDATE SET ";
	public static final String SQL_REPLACE_VALUES = " ) VALUES ( ";
	public static final String SQL_REPLACE_USING= " T USING (";
	public static final String SQL_REPLACE_TT = "= T.";
	public static final String SQL_REPLACE_union = " union ";
	public static final String SQL_REPLACE_SELECT = " SELECT ";
	public static final String SQL_REPLACE_FROM_DUAL = " FROM DUAL ";
	public static final String SQL_REPLACE_KH = ")";
}