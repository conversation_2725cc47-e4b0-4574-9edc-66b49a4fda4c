//package com.jiuzhekan.cbkj.common.dm;
//
//
//import com.example.cbkj_core.utils.encrypt.AESEncrypt;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.EncryptField;
//import com.jiuzhekan.cbkj.common.utils.StringUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.binding.MapperMethod;
//import org.apache.ibatis.cache.CacheKey;
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlCommandType;
//import org.apache.ibatis.mapping.SqlSource;
//import org.apache.ibatis.plugin.*;
//import org.apache.ibatis.reflection.DefaultReflectorFactory;
//import org.apache.ibatis.reflection.MetaObject;
//import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
//import org.apache.ibatis.reflection.wrapper.DefaultObjectWrapperFactory;
//import org.apache.ibatis.session.ResultHandler;
//import org.apache.ibatis.session.RowBounds;
//import org.apache.ibatis.session.defaults.DefaultSqlSession;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.lang.reflect.Field;
//import java.util.*;
//
//@Slf4j
//@Component
//@Intercepts(value = {
//        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
//        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
//                RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})})
//public class MybatisExecutorIntercept implements Interceptor {
//    @Value("${system.dbType:mysql}")
//    private String dbType;
//    private static final String encryptkeyPre = "convert(aes_decrypt(UNHEX(aes_decrypt(UNHEX(";
//    private static final String encryptkeyEnd = "),'" + AESEncrypt.DATA_PWD_SEC + "')),'" + AESEncrypt.DATA_PWD_FIR + "') using utf8)";
//
//    private static final String encryptkeyPreDm = "BINTOCHAR(DBMS_CRYPTO.DECRYPT(BINTOCHAR(DBMS_CRYPTO.DECRYPT(";
//    private static final String encryptkeyEndDm = ", DBMS_CRYPTO.ENCRYPT_AES128 + DBMS_CRYPTO.CHAIN_ECB + DBMS_CRYPTO.PAD_PKCS5,hex(RPAD('" + AESEncrypt.DATA_PWD_SEC + "', 16, CHR(0))))), DBMS_CRYPTO.ENCRYPT_AES128 + DBMS_CRYPTO.CHAIN_ECB + DBMS_CRYPTO.PAD_PKCS5,hex(RPAD('" + AESEncrypt.DATA_PWD_FIR + "', 16, CHR(0)))))";
//
//
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//
//        log.info("EncryptDaoInterceptor.intercept开始执行==> ");
//        System.out.println(dbType);
//        MappedStatement statement = (MappedStatement) invocation.getArgs()[0];
//        Object parameter = invocation.getArgs()[1];
//        String sqlAddress = statement.getId();
//        SqlCommandType commandType = statement.getSqlCommandType();
//
//        if (isNotCrypt(sqlAddress)) {
//            if (commandType.compareTo(SqlCommandType.SELECT) == 0) {
//                BoundSql boundSql = (BoundSql) invocation.getArgs()[5];
//                String sql = boundSql.getSql();
//                sql = decryptSql(sql);
//                resetSql2Invocation(invocation, sql);
//            } else {
//                if (isNotCrypt(parameter)) {
//                    encryptParams(parameter);
//                }
//            }
//            invocation.getArgs()[1] = parameter;
//        }
//
//        Object returnValue = invocation.proceed();
//
//        if (isNotCrypt(sqlAddress)) {
//            if (commandType.compareTo(SqlCommandType.SELECT) == 0) {
//                if (isNotCrypt(returnValue)) {
//                    decryptResult(returnValue);
//                }
//            } else {
//                decryptParams(parameter);
//            }
//        }
//
//
//        log.info("EncryptDaoInterceptor.intercept执行结束==> ");
//
//        return returnValue;
//    }
//
//    @Override
//    public Object plugin(Object target) {
//        return Plugin.wrap(target, this);
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//    }
//
//    /**
//     * 判断是否需要加解密
//     *
//     * @param obj 待加密对象
//     */
//    private boolean isNotCrypt(Object obj) {
//        if (obj == null || obj instanceof Double || obj instanceof Integer || obj instanceof Long || obj instanceof Boolean) {
//            return false;
//        }
//        if (obj instanceof ArrayList && ((List) obj).size() > 0) {
//            Object obj2 = ((List) obj).get(0);
//            if (obj2 == null || obj2 instanceof Double || obj2 instanceof Integer || obj2 instanceof Long || obj2 instanceof Boolean) {
//                return false;
//            }
//        }
//        if (obj instanceof String && ((String) obj).startsWith("com.example.cbkj_core.mapper.sys")) {
//            return false;
//        }
//        return true;
//    }
//
//    /**
//     * insert update 加密参数
//     *
//     * @param parameter parameter
//     */
//    private void encryptParams(Object parameter) {
//        if (parameter instanceof String) {
//
//        } else if (parameter instanceof DefaultSqlSession.StrictMap) {
//            DefaultSqlSession.StrictMap<Object> strictMap = (DefaultSqlSession.StrictMap<Object>) parameter;
//            for (Map.Entry<String, Object> entry : strictMap.entrySet()) {
//                if (entry.getKey().contains("collection")) {
//
//                } else if (entry.getKey().contains("list")) {
//                    for (Object o : (List) entry.getValue()) {
//                        encryptBean(o);
//                    }
//                }
//            }
//            // 多参数
//        } else if (parameter instanceof MapperMethod.ParamMap) {
//            MapperMethod.ParamMap<Object> paramMap = (MapperMethod.ParamMap<Object>) parameter;
//            // 解析每一个参数
//            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
//                // 判断不需要解析的类型 不解析map
//                if (isNotCrypt(entry.getValue()) || entry.getValue() instanceof Map || entry.getKey().contains("param")) {
//
//                } else if (entry.getValue() instanceof String) {
//
//                } else if (entry.getValue() instanceof List) {
//                    for (Object o : (List) entry.getValue()) {
//                        encryptBean(o);
//                    }
//                } else {
//                    encryptBean(entry.getValue());
//                }
//            }
//        } else { // bean
//            encryptBean(parameter);
//        }
//    }
//
//    /**
//     * 加密实体对象
//     *
//     * @param obj obj
//     */
//    private void encryptBean(Object obj) {
//        Class clazz = obj.getClass();
//        if (isNotCrypt(clazz)) {
//            Field[] fields = clazz.getDeclaredFields();
//            for (Field field : fields) {
//                try {
//                    EncryptField.valueOf(field.getName());
//
//                    field.setAccessible(true);
//                    Object value = field.get(obj);
//                    if (value != null) {
//                        String encrypt = AESEncrypt.encrypt(value.toString());
//                        field.set(obj, encrypt);
//                    }
//                } catch (Exception ignored) {
//                }
//            }
//        }
//    }
//
//    /**
//     * insert update 后 解密参数
//     *
//     * @param parameter parameter
//     */
//    private void decryptParams(Object parameter) {
//        if (parameter instanceof String) {
//
//        } else if (parameter instanceof DefaultSqlSession.StrictMap) {
//            DefaultSqlSession.StrictMap<Object> strictMap = (DefaultSqlSession.StrictMap<Object>) parameter;
//            for (Map.Entry<String, Object> entry : strictMap.entrySet()) {
//                if (entry.getKey().contains("collection")) {
//
//                } else if (entry.getKey().contains("list")) {
//                    for (Object o : (List) entry.getValue()) {
//                        decryptBean(o);
//                    }
//                }
//            }
//            // 多参数
//        } else if (parameter instanceof MapperMethod.ParamMap) {
//            MapperMethod.ParamMap<Object> paramMap = (MapperMethod.ParamMap<Object>) parameter;
//            // 解析每一个参数
//            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
//                // 判断不需要解析的类型 不解析map
//                if (isNotCrypt(entry.getValue()) || entry.getValue() instanceof Map || entry.getKey().contains("param")) {
//
//                } else if (entry.getValue() instanceof String) {
//
//                } else if (entry.getValue() instanceof List) {
//                    for (Object o : (List) entry.getValue()) {
//                        decryptBean(o);
//                    }
//                } else {
//                    decryptBean(entry.getValue());
//                }
//            }
//        } else { // bean
//            decryptBean(parameter);
//        }
//    }
//
//
//    /**
//     * 解密结果
//     *
//     * @param returnValue returnValue
//     */
//    private void decryptResult(Object returnValue) {
//
//        if (returnValue instanceof List) {
//            List resultList = (List) returnValue;
//            for (Object o : resultList) {
//                if (o instanceof HashMap) {
//                    Map<String, Object> map = (HashMap<String, Object>) o;
//                    for (Map.Entry<String, Object> m : map.entrySet()) {
//                        try {
//                            EncryptField.valueOf(m.getKey());
//
//                            Object value = m.getValue();
//                            if (value != null) {
//                                String encrypt = decryptStrings(value.toString());
//                                m.setValue(encrypt);
//                            }
//                        } catch (Exception ignored) {
//                        }
//                    }
//                } else {
//                    decryptBean(o);
//                }
//            }
//        }
//    }
//
//    /**
//     * 解密bean，解密bean中List包含的bean
//     *
//     * @param bean bean
//     */
//    private void decryptBean(Object bean) {
//        Class clazz = bean.getClass();
//        Field[] fields = clazz.getDeclaredFields();
//        for (Field field : fields) {
//            try {
//                if (field.getType() == List.class) {
//                    field.setAccessible(true);
//                    List list = (List) field.get(bean);
//                    for (Object o : list) {
//                        decryptBean(o);
//                    }
//                } else {
//                    EncryptField.valueOf(field.getName());
//
//                    field.setAccessible(true);
//                    Object value = field.get(bean);
//                    if (value != null) {
//                        String encrypt = decryptStrings(value.toString());
//                        field.set(bean, encrypt);
//                    }
//                }
//            } catch (Exception ignored) {
//            }
//        }
//    }
//
//    /**
//     * 解密字符串
//     * 英文逗号拼接的会自动拆分
//     *
//     * @param str str
//     */
//    private String decryptStrings(String str) {
//        StringBuilder encryptBuff = new StringBuilder();
//        for (String s : str.split(Constant.ENGLISH_COMMA)) {
//            if (org.apache.commons.lang.StringUtils.isNotBlank(s)) {
//                String st = AESEncrypt.decrypt(s);
//                if (org.apache.commons.lang.StringUtils.isNotBlank(st)) {
//                    encryptBuff.append(st).append(Constant.ENGLISH_COMMA);
//                }
//            }
//        }
//        String encrypt = encryptBuff.toString();
//        encrypt = !encrypt.contains(Constant.ENGLISH_COMMA) ? encrypt : encrypt.substring(0, encrypt.length() - 1);
//        return encrypt;
//    }
//
//
//    /**
//     * 转化sql里的where条件
//     */
//    private String decryptSql(String sql) {
//        sql = " " + sql;
//        sql = sql.replaceAll("\n", " ");
//        sql = sql.replaceAll("\\(", "\\( ");
//        sql = sql.replaceAll("\\) ", " \\) ");
//        sql = sql.replaceAll(" select ", " SELECT ");
//        sql = sql.replaceAll(" from ", " FROM ");
//        sql = sql.replaceAll(" join ", " JOIN ");
//        sql = sql.replaceAll(" on ", " ON ");
//        sql = sql.replaceAll(" where ", " WHERE ");
//
//        StringBuilder sb = new StringBuilder();
//
//        String[] sql2 = sql.split(" SELECT ");
//        for (String s2 : sql2) {
//            if (org.apache.commons.lang.StringUtils.isBlank(s2)) {
//                continue;
//            }
//            sb.append(" SELECT ");
//
//
//            if (s2.contains(" FROM ")) {
//                String[] sql3 = s2.split(" FROM ");
//                sb.append(sql3[0]);
//                for (int i = 1; i < sql3.length; i++) {
//                    sb.append(" FROM ");
//
//                    if (sql3[i].contains(" JOIN ")) {
//                        String[] sql4 = sql3[i].split(" JOIN ");
//                        sb.append(sql4[0]);
//
//                        for (int i1 = 1; i1 < sql4.length; i1++) {
//                            String[] sql5 = sql4[i1].split(" ON ");
//
//                            sb.append(" JOIN ");
//                            sb.append(sql5[0]);
//
//                            if (sql5.length == 2) {
//                                sb.append(" ON ");
//
//                                if (sql5[1].contains(" WHERE ")) {
//                                    String[] sql6 = sql5[1].split(" WHERE ");
//                                    sb.append(replaceField(sql6[0]));
//                                    for (int i2 = 1; i2 < sql6.length; i2++) {
//                                        sb.append(" WHERE ");
//                                        sb.append(replaceField(sql6[i2]));
//                                    }
//                                } else {
//                                    sb.append(replaceField(sql5[1]));
//                                }
//                            }
//                        }
//                    } else {
//
//                        if (sql3[i].contains(" WHERE ")) {
//                            String[] sql7 = sql3[i].split(" WHERE ");
//                            sb.append(sql7[0]);
//                            for (int i3 = 1; i3 < sql7.length; i3++) {
//                                sb.append(" WHERE ");
//                                sb.append(replaceField(sql7[i3]));
//                            }
//                        } else {
//                            sb.append(replaceField(sql3[i]));
//                        }
//                    }
//                }
//            } else {
//                sb.append(s2);
//            }
//        }
//
//        return sb.toString();
//    }
//
//    private StringBuilder replaceField(String sql) {
//        StringBuilder sb = new StringBuilder();
//        String[] wheres = sql.split(" ");
//
//        for (String where : wheres) {
//            try {
//                String encryField = where.contains(".") ? where.split("\\.")[1] : where;
//                EncryptField.valueOf(StringUtils.underlineToCamel2(encryField));
//
//                if(SystemConfigConstants.DB_TYPE_DM.equals(dbType)){
//                    sb.append(encryptkeyPreDm).append(where).append(encryptkeyEndDm).append(" ");
//                }else{
//                    sb.append(encryptkeyPre).append(where).append(encryptkeyEnd).append(" ");
//                }
//            } catch (Exception ignored) {
//                sb.append(where).append(" ");
//            }
//        }
//        return sb;
//    }
//
//    private StringBuilder replaceWhere(String sql) {
//        StringBuilder sb = new StringBuilder();
//        if (sql.contains(" WHERE ")) {
//            String[] sql3 = sql.split(" WHERE ");
//            sb.append(sql3[0]);
//            sb.append(" WHERE ");
//            sb.append(replaceField(sql3[1]));
//
//            for (int i = 0; i < sql3.length; i++) {
//                String s3 = sql3[i];
//                if (i == 0) {
//                    sb.append(s3);
//                } else {
//                    sb.append(" WHERE ");
//                    sb.append(replaceField(s3));
//                }
//            }
//        } else {
//            sb.append(sql);
//        }
//        return sb;
//    }
//
//    private String replaceSqlByKey2(String sql, String encryptkeyPre, String encryptkeyEnd) {
//
//        StringBuilder sb = new StringBuilder();
//
//        String[] sql1 = sql.split("select");
//        for (int i1 = 0; i1 < sql1.length; i1++) {
//            String s1 = sql1[i1];
//            String[] sql2 = s1.split("SELECT");
//            for (String s2 : sql2) {
//                sb.append(" SELECT ");
//
//                if (s2.contains("where")) {
//                    String[] sql3 = s2.split("where");
//                    sb.append(sql3[0]);
//                    sb.append(" WHERE ");
//
//                    String whereStr = sql3[1];
//                    whereStr = whereStr.replaceAll("\n", " ");
//                    whereStr = whereStr.replaceAll("\\(", "\\( ").replaceAll("\\)", " \\) ");
//                    String[] wheres = whereStr.split(" ");
//
//                    for (String where : wheres) {
//                        try {
//                            String encryField = where.contains(".") ? where.split("\\.")[1] : where;
//                            EncryptField.valueOf(StringUtils.underlineToCamel2(encryField));
//
//                            sb.append(encryptkeyPre).append(where).append(encryptkeyEnd).append(" ");
//                        } catch (Exception ignored) {
//                            sb.append(where).append(" ");
//                        }
//                    }
//                } else if (s2.contains("WHERE")) {
//                    String[] sql3 = s2.split("WHERE");
//                    sb.append(sql3[0]);
//                    sb.append(" WHERE ");
//
//                    String whereStr = sql3[1];
//                    whereStr = whereStr.replaceAll("\n", " ");
//                    whereStr = whereStr.replaceAll("\\(", "\\( ").replaceAll("\\)", " \\) ");
//                    String[] wheres = whereStr.split(" ");
//
//                    for (String where : wheres) {
//                        try {
//                            String encryField = where.contains(".") ? where.split("\\.")[1] : where;
//                            EncryptField.valueOf(StringUtils.underlineToCamel2(encryField));
//
//                            sb.append(encryptkeyPre).append(where).append(encryptkeyEnd).append(" ");
//                        } catch (Exception ignored) {
//                            sb.append(where).append(" ");
//                        }
//                    }
//                } else {
//                    sb.append(s2);
//                }
//            }
//        }
//
//        return sb.toString().replaceFirst("SELECT", "");
//    }
//
//
//    /**
//     * 包装sql后，重置到invocation中
//     *
//     * @param invocation invocation
//     * @param sql        sql
//     */
//    private void resetSql2Invocation(Invocation invocation, String sql) {
//        final Object[] args = invocation.getArgs();
//        MappedStatement statement = (MappedStatement) args[0];
//        BoundSql boundSql = (BoundSql) invocation.getArgs()[5];
//        MappedStatement newStatement = newMappedStatement(statement, new BoundSqlSqlSource(boundSql));
//        MetaObject msObject = MetaObject.forObject(newStatement, new DefaultObjectFactory(), new DefaultObjectWrapperFactory(), new DefaultReflectorFactory());
//        msObject.setValue("sqlSource.boundSql.sql", sql);
//        args[0] = newStatement;
//    }
//
//    private MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
//        MappedStatement.Builder builder =
//                new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
//        builder.resource(ms.getResource());
//        builder.fetchSize(ms.getFetchSize());
//        builder.statementType(ms.getStatementType());
//        builder.keyGenerator(ms.getKeyGenerator());
//        if (ms.getKeyProperties() != null && ms.getKeyProperties().length != 0) {
//            StringBuilder keyProperties = new StringBuilder();
//            for (String keyProperty : ms.getKeyProperties()) {
//                keyProperties.append(keyProperty).append(",");
//            }
//            keyProperties.delete(keyProperties.length() - 1, keyProperties.length());
//            builder.keyProperty(keyProperties.toString());
//        }
//        builder.timeout(ms.getTimeout());
//        builder.parameterMap(ms.getParameterMap());
//        builder.resultMaps(ms.getResultMaps());
//        builder.resultSetType(ms.getResultSetType());
//        builder.cache(ms.getCache());
//        builder.flushCacheRequired(ms.isFlushCacheRequired());
//        builder.useCache(ms.isUseCache());
//
//        return builder.build();
//    }
//
//    private String getOperateType(Invocation invocation) {
//        final Object[] args = invocation.getArgs();
//        MappedStatement ms = (MappedStatement) args[0];
//        SqlCommandType commondType = ms.getSqlCommandType();
//        if (commondType.compareTo(SqlCommandType.SELECT) == 0) {
//            return "select";
//        }
//        if (commondType.compareTo(SqlCommandType.INSERT) == 0) {
//            return "insert";
//        }
//        if (commondType.compareTo(SqlCommandType.UPDATE) == 0) {
//            return "update";
//        }
//        if (commondType.compareTo(SqlCommandType.DELETE) == 0) {
//            return "delete";
//        }
//        return null;
//    }
//
//    /**
//     * 定义一个内部辅助类，作用是包装sq
//     */
//    class BoundSqlSqlSource implements SqlSource {
//        private BoundSql boundSql;
//
//        public BoundSqlSqlSource(BoundSql boundSql) {
//            this.boundSql = boundSql;
//        }
//
//        @Override
//        public BoundSql getBoundSql(Object parameterObject) {
//            return boundSql;
//        }
//    }
//
//}