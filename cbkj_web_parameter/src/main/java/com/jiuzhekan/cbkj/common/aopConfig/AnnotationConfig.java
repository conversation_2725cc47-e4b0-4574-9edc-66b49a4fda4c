package com.jiuzhekan.cbkj.common.aopConfig;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.beans.sysBeans.OperationLog;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.LogAsync;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class AnnotationConfig {


    @Autowired
    private LogAsync logAsync;


    @Pointcut("execution(public * com.jiuzhekan.cbkj.controller..*.*(..))")
    public void web() {
    }

    @Before("web()")
    public void before(JoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            StringBuffer url = request.getRequestURL();
            String ip = request.getRemoteAddr();
            String methodName = request.getMethod();

            AdminInfo adminInfo = AdminUtils.getCurrentHr();
            StringBuilder sb = new StringBuilder();
            if (null != adminInfo) {
                sb.append(adminInfo.getUsername());
            }
            sb.append(",ip=").append(ip);
            sb.append(",url=").append(url);
            sb.append(",method=").append(methodName).append(",");
            sb.append(getParams(request, joinPoint));
            log.info(sb.toString());


            Method method = getMethod(joinPoint);
            if (null == method) {
                return;
            }

//            StatisticsFunction statisticsFunction = method.getAnnotation(StatisticsFunction.class);
//
//            if (null != statisticsFunction && statisticsFunction.isStatistic()) {
//
//                String functionName = request.getParameter("functionName");
//                String functionSource = request.getParameter("functionSource");
//
//                if (StringUtils.isBlank(functionName)) {
//                    functionName = statisticsFunction.value();
//                }
//                if (StringUtils.isBlank(functionSource)) {
//                    functionSource = statisticsFunction.source();
//                }
//
//                //tStatisticsFunctionService.addUpFunctionUsageTimes(functionName, functionSource);
//            }

        } catch (Exception e) {
            log.error(e.toString());
        }
    }

    @Around("web()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Method method = getMethod(joinPoint);
        String methodName = joinPoint.getSignature().getName();

        Object o = joinPoint.proceed();

        if (null != method) {

            LogAnnotation logAnnotation = method.getAnnotation(LogAnnotation.class);

            if (null != logAnnotation && logAnnotation.isWrite()) {

                OperationLog log = new OperationLog();
                log.setId(IDUtil.getID());
                log.setDesc(logAnnotation.value());
                log.setContent(logAnnotation.content());
                String params = getParams(request, joinPoint);

                log.setMethodParams(params);

                AdminInfo adminInfo = AdminUtils.getCurrentHr();
                if (null != adminInfo) {
                    log.setOperateId(adminInfo.getUserId());
                    log.setOperateName(adminInfo.getUsername());
                }
//                log.setHttpMode(request.getMethod());
                log.setIp(AdminUtils.getIpAddress(request));
                log.setMethodName(methodName);
//                log.setIsOk(o instanceof ResEntity ? ((ResEntity) o).getStatus() ? 1 : 2 : 3);

                logAsync.logCZRecord(log);
            }
        }
        return o;
    }

    @AfterReturning(returning = "reo", pointcut = "web()")
    public void doAfterReturning(JoinPoint joinPoint, Object reo) {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        StringBuffer url = request.getRequestURL();
        String ip = request.getRemoteAddr();
        String methodName = request.getMethod();

        AdminInfo adminInfo = AdminUtils.getCurrentHr();
        StringBuilder sb = new StringBuilder("params:[");
        if (null != adminInfo) {
            sb.append(adminInfo.getUsername());
        }
        sb.append(",ip=").append(ip);
        sb.append(",url=").append(url);
        sb.append(",method=").append(methodName).append(",");
        sb.append(getParams(request, joinPoint));
        sb.append("]; result:[");
        sb.append(com.alibaba.fastjson.JSONObject.toJSONString(reo));
        sb.append("]");
        log.info(sb.toString());
    }

    @AfterThrowing(pointcut = "web()", throwing = "e")
    private void doAfterThrow(JoinPoint joinPoint, Throwable e) {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        Method method = this.getMethod(joinPoint);
        if (null != method) {

            String descpt = null;

            Object logObj = method.getAnnotation(LogAnnotation.class);
            if (null != logObj) {
                LogAnnotation logAnnotation = ((LogAnnotation) logObj);
                descpt = logAnnotation.value();
            }
            if (StringUtils.isBlank(descpt)) {
                descpt = "未知";
            }
            //这里建议异步处理
            String methodName = joinPoint.getSignature().getName();
            Logentity log = new Logentity();
            log.setLid(IDUtil.getID());
            log.setUrl(request.getRequestURL().toString());
            log.setHttpMethod(request.getMethod());
            log.setIp(AdminUtils.getIpAddress(request));
            log.setClassName(joinPoint.getSignature().getDeclaringTypeName());
            log.setMethodName(methodName);
            log.setExecuType("Exception");
            log.setStatus("error");
            log.setDescr(descpt);
            String errMsg = getStackTrace(e);
            log.setErrmsg(errMsg);
            if (null != AdminUtils.getCurrentHr()) {
                log.setCreateId(AdminUtils.getCurrentHr().getUserId());
            }
            logAsync.logRecord(log);
            e.printStackTrace();
        }
    }


    /**
     * 获取参数
     *
     * @param request   request
     * @param joinPoint joinPoint
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/13
     */
    private static String getParams(HttpServletRequest request, JoinPoint joinPoint) {

        String methodType = request.getMethod();
        Map<String, String[]> paramMap = request.getParameterMap();
        StringBuilder sb = new StringBuilder(" ");

        if ("POST".equals(methodType)) {
            try {

                Object[] args = joinPoint.getArgs();
                //参数名
                String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();

                for (int i = 0; i < argNames.length; i++) {
                    sb.append(argNames[i]).append(":").append(com.alibaba.fastjson.JSONObject.toJSONString(args[i])).append(",");
                }
            } catch (Exception e) {
                sb.append("JSON解析失败：").append(e.getMessage());
            }
        } else {
            for (String key : paramMap.keySet()) {
                String[] values = paramMap.get(key);
                for (String value : values) {
                    if (StringUtils.isNotBlank(value)) {
                        sb.append(key).append(":").append(value).append(",");
                    }
                }
            }
        }

        return sb.toString();
    }

    /**
     * 获取堆栈信息
     *
     * @param throwable throwable
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/13
     */
    private String getStackTrace(Throwable throwable) {

        StringWriter sw = new StringWriter();

        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }

    /**
     * 获取方法
     *
     * @param joinPoint joinPoint
     * @return Method
     */
    private Method getMethod(JoinPoint joinPoint) {

        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        String methodName = joinPoint.getSignature().getName();
        Object[] objs = joinPoint.getArgs();

        return Arrays.stream(methods).filter(method ->
                (method.getName().equals(methodName) && method.getParameterTypes().length == objs.length))
                .findAny()
                .orElse(null);

    }
}