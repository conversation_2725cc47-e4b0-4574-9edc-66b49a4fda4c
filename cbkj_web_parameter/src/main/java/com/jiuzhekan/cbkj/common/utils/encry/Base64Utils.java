package com.jiuzhekan.cbkj.common.utils.encry;

import org.apache.commons.codec.binary.Base64;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;

/**
 * 加密编码
 * <p>
 * author lo<PERSON><PERSON><PERSON><PERSON>
 */
public class Base64Utils {
    /**
     * <p>
     * 二进制数据编码为BASE64字符串
     *
     * @param base64 base64
     * @return String
     */
    public static String encode(byte[] base64) {
        return new String(Base64.encodeBase64(base64), StandardCharsets.UTF_8);
    }

    /**
     * <p>
     * BASE64字符串解码为二进制数据
     * </p>
     *
     * @param base64 base64
     * @return String
     */
    public static byte[] decode(String base64) {
        return Base64.decodeBase64(base64.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 图片base64 转化为 MultipartFile
     *
     * @param imgStr imgStr
     * @return MultipartFile
     */
    public static MultipartFile toMutipartFile(String imgStr) {
        try {
            String[] baseStr = imgStr.split(",");
            byte[] b;
            b = decode(baseStr[1]);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            return new BASE64MultipartFile(b, baseStr[0]);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }


}
