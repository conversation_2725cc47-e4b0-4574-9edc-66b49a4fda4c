package com.jiuzhekan.cbkj.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.business.TPrescriptStatusPass;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.TPrescriptStatusPassMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/14 13:26
 * @Version 1.0
 */
@Slf4j
@Service
public class TPrescriptStatusPassHandler {


    private final RestTemplate restTemplate;
    private final TPrescriptStatusPassMapper tPrescriptStatusPassMapper;

    public TPrescriptStatusPassHandler(RestTemplate restTemplate, TPrescriptStatusPassMapper tPrescriptStatusPassMapper) {
        this.restTemplate = restTemplate;
        this.tPrescriptStatusPassMapper = tPrescriptStatusPassMapper;
    }


    @Transactional(rollbackFor = Exception.class)
    public void postForObject(HttpEntity request1,
                              TPrescriptStatusPass tPrescriptStatusPass, JSONObject jsonObject) {
        ResEntity res = null;
        try {
            if (tPrescriptStatusPass.getPassNum() > 3){
                log.error("补传失败，次数超过3次。请主动检查调用服务是否异常。passId={}，调用passUrl={}", tPrescriptStatusPass.getPassId(),tPrescriptStatusPass.getPassUrl());
               return;
            }
            if ("post".equals(tPrescriptStatusPass.getPassMethod())) {
                res = restTemplate.postForObject(tPrescriptStatusPass.getPassUrl(), request1, ResEntity.class);
            } else if ("get".equals(tPrescriptStatusPass.getPassMethod())) {
                res = restTemplate.getForObject(tPrescriptStatusPass.getPassUrl(), ResEntity.class, jsonObject);
            }
            if (null != res && res.getStatus()){
                tPrescriptStatusPass.setPassMessage(res.getMessage());
                if ( null != res.getData() && StringUtils.isBlank(res.getMessage())){
                    tPrescriptStatusPass.setPassMessage(JSON.toJSONString(res.getData()));
                }
            }
            if ( null == res || !res.getStatus()) {
                //调用失败
                //成功。
                Integer passNum = tPrescriptStatusPass.getPassNum();
                tPrescriptStatusPass.setPassNum(passNum == null ? (1) : (passNum + 1));
            } else {
                //成功。
                Integer passNum = tPrescriptStatusPass.getPassNum();

                tPrescriptStatusPass.setPassNum(passNum == null ? (1) : (passNum + 1));
                tPrescriptStatusPass.setStatus(Constant.BASIC_STRING_ONE);
                tPrescriptStatusPass.setDelDate(new Date());
                tPrescriptStatusPass.setDelType(Constant.BASIC_STRING_ONE);
                tPrescriptStatusPass.setDelUser("system");
            }
            tPrescriptStatusPassMapper.updateByPrimaryKey(tPrescriptStatusPass);
        } catch (Exception e) {
//            log.error("【补传任务】异常{}", e.getMessage());
//            log.error("【补传任务】需要补传的接口路径未通");
            Integer passNum = tPrescriptStatusPass.getPassNum();
            tPrescriptStatusPass.setPassNum(passNum == null ? (1) : (passNum + 1));
            tPrescriptStatusPass.setPassMessage(e.getMessage());
            tPrescriptStatusPassMapper.updateByPrimaryKey(tPrescriptStatusPass);
        }
    }

}
