package com.jiuzhekan.cbkj.common.utils;

import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024.04.16
 */
public class FileTypeUtil {

    public static boolean isImage(MultipartFile file) {
        String contentType = file.getContentType();
        assert contentType != null;
        boolean c1 = contentType.startsWith("image/jpg") || contentType.startsWith("image/jpeg") || contentType.startsWith("image/png");

        String fileName = file.getOriginalFilename();
        assert fileName != null;
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
        boolean c2 = "jpg".equals(extension) || "jpeg".equals(extension) || "png".equals(extension);

        return c1 && c2;
    }

    public static boolean isPdf(MultipartFile file) {
        String contentType = file.getContentType();
        boolean c1 = "application/pdf".equals(contentType);

        String fileName = file.getOriginalFilename();
        assert fileName != null;
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
        boolean c2 = "pdf".equals(extension);

        return c1 && c2;
    }

    public static boolean isDoc(MultipartFile file) {
        String contentType = file.getContentType();
        boolean c1 = "application/msword".equals(contentType)
                || "application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(contentType);

        String fileName = file.getOriginalFilename();
        assert fileName != null;
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
        boolean c2 = "doc".equals(extension) || "docx".equals(extension);

        return c1 && c2;
    }

    public static boolean isExcel(MultipartFile file) {
        String contentType = file.getContentType();
        boolean c1 = "application/vnd.ms-excel".equals(contentType) ||
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType);

        String fileName = file.getOriginalFilename();
        assert fileName != null;
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
        boolean c2 = "xls".equals(extension) || "xlsx".equals(extension);

        return c1 && c2;
    }

    public static boolean isVideo(MultipartFile file) {
        // 检查Content-Type是否为视频类型
        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }
        boolean c1 = contentType.startsWith("video/mp4") ||
                contentType.startsWith("video/quicktime") ||  // MOV
                contentType.startsWith("video/x-msvideo") ||  // AVI
                contentType.startsWith("video/x-ms-wmv") ||   // WMV
                contentType.startsWith("video/x-flv") ||       // FLV
                contentType.startsWith("video/x-matroska") || // MKV
                contentType.startsWith("video/mpeg") ||       // MPEG/MPG
                contentType.startsWith("video/webm");         // WEBM

        // 检查文件扩展名是否为视频格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.indexOf('.') == -1) {
            return false;
        }
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        boolean c2 = "mp4".equals(extension) ||
                "mov".equals(extension) ||
                "avi".equals(extension) ||
                "wmv".equals(extension) ||
                "flv".equals(extension) ||
                "mkv".equals(extension) ||
                "mpeg".equals(extension) ||
                "mpg".equals(extension) ||
                "webm".equals(extension);

        return c1 && c2;
    }
}
