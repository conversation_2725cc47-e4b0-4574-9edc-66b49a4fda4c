package com.jiuzhekan.cbkj.common.annotaionUtil;

import java.lang.annotation.*;

/**
 * 日志注解
 *
 * <AUTHOR>
 * @date 2021/4/13
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogAnnotation {

    /**
     * 默认值
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/13
     */
    String value() default "";

    /**
     * 是否写入日志数据库
     *
     * <AUTHOR>
     * @date 2021/4/13
     */
    boolean isWrite() default false;

    /**
     * 操作了什么
     *
     * <AUTHOR>
     * @date 2022/2/23
     * @return java.lang.String
     */
    String content() default "";
}