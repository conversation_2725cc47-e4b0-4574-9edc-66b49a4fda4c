package com.jiuzhekan.cbkj.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CustomException
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomRuntimeException extends RuntimeException {

    private static final long serialVersionUID = -6345278824391495117L;

    private int code;

    public CustomRuntimeException(int code, String message) {
        super(message);
        this.code = code;
    }
}
