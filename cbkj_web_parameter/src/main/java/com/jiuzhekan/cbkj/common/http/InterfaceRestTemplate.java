package com.jiuzhekan.cbkj.common.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.business.TPrescriptStatusPass;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
@Component
public class InterfaceRestTemplate extends RestTemplate {


    private final TPrescriptStatusPassHandler tPrescriptStatusPassHandler;

    public InterfaceRestTemplate(TPrescriptStatusPassHandler tPrescriptStatusPassHandler) {
        this.tPrescriptStatusPassHandler = tPrescriptStatusPassHandler;
    }

    /**
     * HIS系统状态异步补传
     *
     * @param tPrescriptStatusPassList
     * @return
     */
    @Async
    public void passStatus(List<TPrescriptStatusPass> tPrescriptStatusPassList) {
        for (TPrescriptStatusPass tPrescriptStatusPass : tPrescriptStatusPassList) {
            log.info("【补传任务】{}?statusVO={}", tPrescriptStatusPass.getPassUrl(), tPrescriptStatusPass.getPassParams());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            JSONObject jsonObject = null;
            try {
                jsonObject = JSON.parseObject(tPrescriptStatusPass.getPassParams());
            } catch (Exception e) {
                log.error("【补传任务】参数转换异常passId= {},错误信息{}", tPrescriptStatusPass.getPassId(),e.getMessage());
                return;
            }
            HttpEntity request1 = new HttpEntity(jsonObject, headers);
            tPrescriptStatusPassHandler.postForObject(request1, tPrescriptStatusPass, jsonObject);
        }
    }


    @Data
    public class StatusVO {
        //系统参数
        private String appId;
        private String insCode;
        private String timestamp;
        private List<PreStatus> preStatusList;
    }

    @Data
    public class PreStatus {
        private String preNo;
        private String status;
        private String operationName;
        private String operationTime;
        private String operationContent;
    }

    public static void main(String[] args) {
        String a = "{\"appId\":\"100003\",\"insCode\":\"3304240008\",\"preStatusList\":[{\"operationName\":\"系统\",\"operationTime\":\"2022-11-24 15:12:28\",\"preNo\":\"20221110153531008\",\"source\":\"2\",\"status\":\"100\"}],\"timestamp\":\"1669273948666\"}";
        JSONObject jsonObject = JSON.parseObject(a);
        System.out.println(jsonObject.toJSONString());
    }
}