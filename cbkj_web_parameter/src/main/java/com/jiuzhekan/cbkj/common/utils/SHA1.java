package com.jiuzhekan.cbkj.common.utils;

import org.springframework.util.MultiValueMap;

import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class SHA1 {

    public static String getSha1(String str) {

        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }


    //知识库接口中的签名
    public static String getSign(MultiValueMap<String, Object> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.getFirst(key);
            sb.append(key);
            sb.append("=");
            if (value != null) {
                sb.append(value);
            }
            // 拼接时，不包括最后一个&字符
            if (i < keys.size() - 1) {
                sb.append("&");
            }
        }
        return SHA1.getSha1(sb.toString());
    }

    //知识库接口中的签名
    public static String getSign(Map<String, Object> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            sb.append(key);
            sb.append("=");
            sb.append(value);

            if (i < keys.size() - 1) {// 拼接时，不包括最后一个&字符
                sb.append("&");
            }
        }
        return SHA1.getSha1(sb.toString());
    }
}