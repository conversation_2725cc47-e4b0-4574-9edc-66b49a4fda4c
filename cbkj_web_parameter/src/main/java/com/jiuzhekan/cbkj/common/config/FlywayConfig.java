package com.jiuzhekan.cbkj.common.config;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/3 16:17
 * @Version 1.0
 */
import org.flywaydb.core.Flyway;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Version 1.0
 */
@Configuration
public class FlywayConfig {
    @Bean
    @ConditionalOnProperty(name = "system.dbType", havingValue = "mysql")
    public Flyway flyway(DataSource dataSource) {
        Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .locations("classpath:dbUp")  // 指定迁移脚本的位置，位于resources文件夹下的dbUp文件夹
                .baselineOnMigrate(true)  // 指定迁移脚本的位置
                .baselineVersion("1.1.6")  // 基准，从这个版本为基准，后续版本自动执行脚本。
                .validateOnMigrate(false) //禁用临时校验
                .outOfOrder(true)
                .load();
        flyway.repair();
        flyway.migrate(); // 自动执行迁移
        return flyway;
    }
}
