package com.jiuzhekan.cbkj.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtil {
    /**
     * 获取 yyyy-MM-dd HH:mm:ss 格式
     */
    public static final String date1 = "yyyy-MM-dd HH:mm:ss";
    /**
     * 获取 yyyy-MM-dd 格式
     */
    public static final String date2 = "yyyy-MM-dd";
    /**
     * 获取 yyyy年MM月dd HH时mm分ss 格式
     */
    public static final String date3 = "yyyy年MM月dd HH时mm分ss";
    /**
     * 获取 yyyy年MM月dd日 格式
     */
    public static final String date4 = "yyyy年MM月dd日";
    /**
     * 获取 yyyyMMdd 格式
     */
    public static final String date5 = "yyyyMMdd";
    /**
     * 获取 yyyy年MM月dd 格式
     */
    public static final String date6 = "yyyy年MM月dd";
    /**
     * 获取 yyyyMMddHHmmss 格式
     */
    public static final String date7 = "yyyyMMddHHmmss";
    /**
     * 获取
     */
    public static final String date8 = "yyyy/MM/dd";
    /**
     * 获取 yyyy-MM
     */
    public static final String date9 = "yyyy-MM";

    /**
     * 获取 M.d 格式
     */
    public static final String date10 = "M.d";

    /**
     * 获取 yyyy.MM.dd 格式
     */
    public static final String date11 = "yyyy.MM.dd";

    /**
     * 获取 yyyy 格式
     */
    public static final String date12 = "yyyy";

    /**
     * 日期格式化为字符串 日期为null 取当前时间
     *
     * @param format
     * @param date
     * @return
     */
    public static String getDateFormats(String format, Date date) {
        SimpleDateFormat sdfTime = new SimpleDateFormat(format);
        if (date == null) {
            return sdfTime.format(new Date());
        } else {
            return sdfTime.format(date);
        }
    }

    /**
     * 字符串转日期
     *
     * @param date
     * @param format
     * @return
     */
    public static Date getDateFormatd(String date, String format) {
        SimpleDateFormat sdfTime = new SimpleDateFormat(format);
        try {
            return sdfTime.parse(date);
        } catch (Exception e) {
            System.out.println("日期转换错误！！错误原因：" + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 计算失效时间
     *
     * @param nowDate：起始时间
     * @param type         ：如果是year：计算起始时间到几年后的时间、month计算多少月之后，day计算多少天之后的时间,hour 计算多少小时后，minute 计算多少分钟后
     * @return
     */
    public static Date getPreDate(Date nowDate, String type, Integer time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(nowDate);
        int amount = 0;
        if ("minute".equals(type)) {
            amount = time + cal.get(Calendar.MINUTE);
            cal.set(Calendar.MINUTE, amount);
        } else if ("hour".equals(type)) {
            amount = time + cal.get(Calendar.HOUR);
            cal.set(Calendar.HOUR, amount);
        } else if ("day".equals(type)) {
            amount = time + cal.get(Calendar.DAY_OF_MONTH);
            cal.set(Calendar.DAY_OF_MONTH, amount);
        } else if ("month".equals(type)) {
            int mont = time + cal.get(Calendar.MONTH);
            cal.set(Calendar.MONTH, mont);
        } else {
            int year = time + cal.get(Calendar.YEAR);
            cal.set(Calendar.YEAR, year);
        }
        Date preDate = cal.getTime();
        return preDate;
    }

    /**
     * 获取 date 星期几
     *
     * @param date  日期
     * @param weeks 周天数组 默认{ "0", "1", "2", "3", "4", "5", "6" }
     * @return
     */
    public static String getWeekOfDate(Date date, String[] weeks) {
        String[] weekDaysCode = weeks;
        if (weeks == null) {
            weekDaysCode = new String[]{"0", "1", "2", "3", "4", "5", "6"};
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int intWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        return weekDaysCode[intWeek];
    }

    /**
     * 获取日期多少前
     *
     * @param sourceStamp 过去时间
     * @param targetStamp 当前时间
     * @return String 分钟 小时 昨天 前天 三天前 。。
     * @throws Exception
     */
    public static String getTimeDifference(Date sourceStamp, Date targetStamp) throws Exception {
        StringBuilder sb = new StringBuilder();
        long diff = targetStamp.getTime() - sourceStamp.getTime();
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        if (diff / (1000 * 60) < 60) {
            sb.append(diff / (1000 * 60) < 1 ? 1 : (int) diff / (1000 * 60));
            sb.append("分钟前");
        } else if ((diff / (1000 * 60)) >= 60 && (diff / (1000 * 60)) < 60 * 24) {
            sb.append((int) diff / (1000 * 60 * 60));
            sb.append("小时前");
        } else {
            diff = sdf1.parse(sdf1.format(targetStamp)).getTime() - sdf1.parse(sdf1.format(sourceStamp)).getTime();
            if ((diff / (1000 * 60)) >= 60 * 24 && (diff / (1000 * 60)) < 60 * 48) {
                sb.append("昨天");
            } else if ((diff / (1000 * 60)) >= 60 * 48 && (diff / (1000 * 60)) < 60 * 72) {
                sb.append("前天");
            } else if ((diff / (1000 * 60)) >= 60 * 72 && (diff / (1000 * 60)) < 60 * 96) {
                sb.append("三天前");
            } else {
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sb.append(sdf2.format(sourceStamp));
            }
        }
        return sb.toString();
    }

    /**
     * 得到n天之后是周几
     *
     * @param daysInt
     * @return
     */
    public static String getAfterDayWeek(int daysInt) {
        Calendar canlendar = Calendar.getInstance(); // java.util包
        canlendar.add(Calendar.DATE, daysInt); // 日期减 如果不够减会将月变动
        Date date = canlendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("E");
        String dateStr = sdf.format(date);
        return dateStr;
    }

    /**
     * <li>功能描述：时间相减得到天数
     *
     * @param beginDateStr
     * @param endDateStr
     * @return long
     * <AUTHOR>
     */
    public static long getDaySub(String beginDateStr, String endDateStr) {
        long day = 0;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date beginDate = null;
        Date endDate = null;
        try {
            beginDate = format.parse(beginDateStr);
            endDate = format.parse(endDateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        day = (endDate.getTime() - beginDate.getTime()) / (24 * 60 * 60 * 1000);
        return day;
    }

    /**
     * 获取某个月的最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat(date2);
        String lastDayOfMonth = sdf.format(cal.getTime());

        return lastDayOfMonth;
    }

    public static Date getDateAddOfDay(Date date, int i) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_WEEK, i);
        Date a = c.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return a;
    }

    public static Date getDateAddOfYear(Date date, int i) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.YEAR, i);
        Date a = c.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return a;
    }

}