package com.jiuzhekan.cbkj.common.interceptor.tianan;

import com.example.cbkj_core.utils.encrypt.AESEncrypt;
import com.jiuzhekan.cbkj.common.interceptor.SecretStrategy;
import com.jiuzhekan.cbkj.common.utils.Constant;
import org.springframework.stereotype.Service;

@Service(value = "AESSecret")
public class AESSecretUtils implements SecretStrategy {
    @Override
    public String encrypt(String input) {
        String encrypt = AESEncrypt.encrypt(input.toString());
        return encrypt;
    }

    @Override
    public String decrypt(String input) {
        StringBuilder encryptBuff = new StringBuilder();
        for (String s : input.split(Constant.ENGLISH_COMMA)) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(s)) {
                String st = AESEncrypt.decrypt(s);
                if (org.apache.commons.lang.StringUtils.isNotBlank(st)) {
                    encryptBuff.append(st).append(Constant.ENGLISH_COMMA);
                }
            }
        }
        String encrypt = encryptBuff.toString();
        encrypt = !encrypt.contains(Constant.ENGLISH_COMMA) ? encrypt : encrypt.substring(0, encrypt.length() - 1);
        return encrypt;
    }

    public static void main(String[] args) {
        /**
         *
         * UPDATE t_patients AS a SET
         * PATIENT_CERTIFICATE = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`PATIENT_CERTIFICATE`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'))
         *
         *
         */
        String a = AESEncrypt.encrypt("青山湖街道研口村304号");
        System.out.println(a);
        System.out.println(AESEncrypt.decrypt(a));
    }
}