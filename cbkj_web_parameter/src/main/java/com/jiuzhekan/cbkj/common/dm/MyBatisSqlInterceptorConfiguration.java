package com.jiuzhekan.cbkj.common.dm;

import com.github.pagehelper.PageInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * 最小侵入：不要自己创建 SqlSessionFactory，使用 ConfigurationCustomizer 注册拦截器
 */
@Configuration
@Slf4j
public class MyBatisSqlInterceptorConfiguration{

    @Value("${system.dbType:mysql}")
    private String dbType;

//    @Bean
    public PageInterceptor pageInterceptor() {
        PageInterceptor interceptor = new PageInterceptor();
        Properties props = new Properties();
        props.setProperty("helperDialect", "dm");
        // dialectAlias 指向你自己实现的 DmDialect 完整类名
        props.setProperty("dialectAlias", "dm=com.jiuzhekan.cbkj.common.dm.DmDialect");
        props.setProperty("reasonable", "true");
        props.setProperty("supportMethodsArguments", "true");
        props.setProperty("params", "count=countSql");
        interceptor.setProperties(props);
        return interceptor;
    }

    /**
     * 不要自定义 SqlSessionFactory
     * MyBatis Starter 提供的 ConfigurationCustomizer（或 org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer）
     * 把 PageHelper 和你自己的 MysqlToDmInterceptor 注册到已经由 Starter 创建的 SqlSessionFactory/Configuration 上
     * @return
     */
    @ConditionalOnProperty(name = "system.dbType", havingValue = "dm")
    @Bean
    public ConfigurationCustomizer mybatisCustomizer() {
        return configuration -> {
            // 先移除已有的同类拦截器（避免重复）
            configuration.getInterceptors().removeIf(i ->
                    i instanceof PageInterceptor || i instanceof MysqlToDmInterceptor
            );
            //  MysqlToDmInterceptor（负责 SQL 兼容/替换）
//            configuration.addInterceptor(new MysqlToDmInterceptor());
            // 加 PageHelper 拦截器
            configuration.addInterceptor(pageInterceptor());

            // 打印当前注册的拦截器顺序，方便调试
            configuration.getInterceptors().forEach(i -> {
                log.debug("[MyBatis Interceptor] loaded -> {}", i.getClass().getName());
            });
        };
    }
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        log.info("dbType======================="+dbType);
//        if(SystemConfigConstants.DB_TYPE_DM.equals(dbType)){
//            SqlSessionFactory sqlSessionFactory = applicationContext.getBean(SqlSessionFactory.class);
//            sqlSessionFactory.getConfiguration().addInterceptor(new MysqlToDmInterceptor());
//        }
//    }
    @ConditionalOnProperty(name = "system.dbType", havingValue = "dm")
    @Bean
    public MysqlToDmInterceptor mysqlToDmInterceptor() {
        return new MysqlToDmInterceptor();
    }
}
