package com.jiuzhekan.cbkj.common.utils.excel;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 */
public class CommonUtils {


    /**
     * 判断String是否为空
     */
    public static boolean isEmptyString(String value) {
        return value == null || value.length() == 0;
    }

    /**
     * 只判断多个String是否为空(无论有没全角或半角的空格) 若非空则返回true,否则返回false
     *
     * @param str
     * @return boolean
     */
    public static boolean isEmptyAllString(String... str) {
        if (null == str) {
            return true;
        }
        for (String s : str) {
            if (isEmptyString(s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * String -> int
     *
     * @param
     * @return int
     */
    public static int parseInt(String string, int def) {
        if (isEmptyString(string)) {
            return def;
        }
        int num = def;
        try {
            num = Integer.parseInt(string);
        } catch (Exception e) {
            num = def;
        }
        return num;
    }

    /**
     * String -> short
     *
     * @param
     * @return int
     */
    public static short parseShort(String string, short def) {
        if (isEmptyString(string)) {
            return def;
        }
        short num = def;
        try {
            num = Short.parseShort(string);
        } catch (Exception e) {
            num = def;
        }
        return num;
    }

    /**
     * String -> long
     *
     * @param
     * @return long
     */
    public static long parseLong(String string, long def) {
        if (isEmptyString(string)) {
            return def;
        }
        long num;
        try {
            num = Long.parseLong(string);
        } catch (Exception e) {
            e.printStackTrace();
            num = def;
        }
        return num;
    }

    /**
     * String -> double
     *
     * @param
     * @return long
     */
    public static double parseDouble(String string, double def) {
        if (isEmptyString(string)) {
            return def;
        }
        double num;
        try {
            num = Double.parseDouble(string);
        } catch (Exception e) {
            num = def;
        }
        return num;
    }

    /**
     * String -> float
     *
     * @param
     * @return long
     */
    public static float parseFloat(String string, float def) {
        if (isEmptyString(string)) {
            return def;
        }
        float num;
        try {
            num = Float.parseFloat(string);
        } catch (Exception e) {
            e.printStackTrace();
            num = def;
        }
        return num;
    }

    /**
     * 过滤字符串中的可能存在XSS攻击的非法字符
     *
     * @param value
     * @return
     */
    public static String cleanXSS(String value) {
        value = value.replaceAll("<", "& lt;").replaceAll(">", "& gt;");
        value = value.replaceAll("\\(", "& #40;").replaceAll("\\)", "& #41;");
        value = value.replaceAll("'", "& #39;");
        value = value.replaceAll("eval\\((.*)\\)", "");
        value = value.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");
        value = value.replaceAll("script", "");
        value = value.replaceAll(" ", "");
        return value;
    }


}