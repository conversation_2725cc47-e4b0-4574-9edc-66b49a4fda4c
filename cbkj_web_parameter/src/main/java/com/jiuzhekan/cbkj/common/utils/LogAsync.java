package com.jiuzhekan.cbkj.common.utils;

import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.beans.sysBeans.OperationLog;
import com.jiuzhekan.cbkj.service.sysService.LogentityService;
import com.jiuzhekan.cbkj.service.sysService.OperationLogService;
import com.jiuzhekan.cbkj.service.sysService.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
public class LogAsync {

    @Autowired
    private LogentityService logentityService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private OperationLogService operationLogService;

    @Async
    public void logRecord(Logentity logentity) {
        logentityService.insert(logentity);
    }

    @Async
    public void logCZRecord(OperationLog operationLog) {
        operationLogService.insert(operationLog);
    }

}