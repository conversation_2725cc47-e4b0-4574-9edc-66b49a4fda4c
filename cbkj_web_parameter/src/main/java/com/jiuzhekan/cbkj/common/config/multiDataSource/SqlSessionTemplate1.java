//package com.jiuzhekan.cbkj.common.config.multiDataSource;
//
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.transaction.PlatformTransactionManager;
//
//import javax.sql.DataSource;
//
////@Configuration
////@MapperScan(basePackages = "com.example.cbkj_core.mapper",sqlSessionTemplateRef = "primarySqlSessionTemplate")
//public class SqlSessionTemplate1 {
//
//    @Bean(name="primarySqlSessionFactory")
//    @Primary
//    public SqlSessionFactory primarySqlSessionFactory(@Qualifier("primaryDataSource")DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setTypeAliasesPackage("com.example.cbkj_core.beans");
//        bean.setVfs(SpringBootVFS.class);
//        bean.setConfigLocation(new PathMatchingResourcePatternResolver().getResource("classpath:/mybatis-config.xml"));
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mappers/*/*.xml"));
//        return bean.getObject();
//    }
//
//    //声明式事物管理
//    @Primary
//    @Bean
//    public PlatformTransactionManager platformTransactionManager(@Qualifier("primaryDataSource") DataSource dataSource){
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name="primarySqlSessionTemplate")
//    @Primary
//    public SqlSessionTemplate primarySqlSessionTemplate(@Qualifier("primarySqlSessionFactory") SqlSessionFactory sqlSessionFactory){
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}