package com.jiuzhekan.cbkj.common.utils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EncryptField.java
 * @Description 加密字段枚举
 * @createTime 2019年07月30日 13:20:00
 */
public enum EncryptField {

    /**
     * 字段名（类名）
     * 加密字段命名规范：实体类bean字段命名必须遵循驼峰命名法，数据库字段必须是大写字母和下划线命名；否则无法识别；
     * insert，update 必须对以实体类bean作为参数，不要使用Map或String
     * select 支持实体类bean、Map，包括查询字段、查询条件都会自动解密处理；
     * select 查询条件中的加密字段前后要有空格与符号隔离开；
     */
    name,
    parentName,
    py,
    wb,
    book,
    bookName,
    dept,
    deptName,
    useage,
    useageName,
    unit,
    unitName,
    groupName,
    groupname,
    itemName,
    disName,
    disNameTop,
    disNameRevise,
    disPy,
    disWb,
    symName,
    symNameRevise,
    symPy,
    symWb,
    matName,
    matPy,
    matWb,
    matIncName,
    matSynName,
    matSynPy,
    matSynWb,
    matPrepareName,
    effectName,
    therapy,
    flavourName,
    meridianName,
    preName,
    smoPreName,
    acuName,
    acuPy,
    acuWb,
    merName,
    merPy,
    merWb,
    dietName,
    drugName,
    westName,
    westPy,
    westWb,
    therapyName,
    therapyPy,
    therapyWb,

    /**
     * 20220928上海联仁
     * 产品增加加密字段
     */
    acuPosition,
    acuposotion,
    acuDiscection,
    acudiscection,
    acuMainTreat,
    acumaintreat,
    acuOperation,
    acuoperation,
    acuNeedling,
    acuneedling,
    situation,
    tongue,
    pulse,
    pulseName,
    pulseType,
    pulseFeature,
    signality,
    pulseNormal,
    bitStatusPoem,
    similarPoem,
    primaryDisPoem,
    discuss,
    text,
    tongueName,
    primaryClassification,
    secondaryClassification,
    tongueFeatures,
    mechanismAnalyze,
    gzhzjj,
    cqfyss,
    slqjj,
    ylqjj,
    prqjj,
    etjj,
    lrjj,
    mbhzjj,
    gmtzjj,
    dxyjj,
    ydyjj,
    cxhzjj,
    takeMedicine,
    decotion,
    attendingExpert,
    content,
    westernDisease,
    chineseSymptom,
    symptom,
    analysis,
    dosName,
    theName,
    description,
    ttRule,
    ttPoint,
    ttPrescription,
    lzjj,
    vegetables,
    sports,
    emotionalAdjustment,
    dietRecuperation,
    dailyLifeAdjustment,
    acupointHealthCare,
    medicatedDiet,
    flowerTea,
    acuMethod,
    mainAcupoint,
    subAcupoint,
    mechanismPre,
    operation,
    materials,
    method,



    /**
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/8/14 14:08
     */
    disname,
    prename,
    deptname,
    bookname,
    useagename,
    flavourname,
    meridianname,
    categorys,
    categorys2,
    matsynonyms,
    matusage,
    matmeridian,
    matflavour,
    acuname,
    mername,
    drugname,
    westname,
    westpy,
    westwb,
    matname,
    unitname,
    dosname,
    synname,
}