package com.jiuzhekan.cbkj.common.utils;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRule;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.common.config.security.TokenBo;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by sang on 2017/12/30.
 */
@UtilityClass
public class AdminUtils {

    /**
     * 获取当前登录管理员
     *
     * @return
     */
    public AdminInfo getCurrentHr() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            Object obj = authentication.getPrincipal();
            if (obj instanceof TokenBo) {
                return ((TokenBo) obj).getAdmin();
            }

            if (obj instanceof AdminInfo) {
                return (AdminInfo) obj;
            }
        }
        return null;
    }





    /**
     * 判断当前登录管理员是否神级管理员
     *
     */
    public boolean getSuper() {
        AdminInfo adminInfo = getCurrentHr();
        if (null != adminInfo) {
            AdminRule adminRule = adminInfo.getRoles().stream().filter(rule -> rule.getRoleId().equals(Constant.ROLEGRADE)).findAny().orElse(null);
            return null != adminRule;
        }
        return false;
    }


    /**
     * 获取真实IP
     *
     * @param request
     * @return
     */
    public String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public String getCurrentAppId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication && authentication.getPrincipal() instanceof TokenBo) {
            TokenBo tokenBo = (TokenBo) authentication.getPrincipal();
//            if (tokenBo.getRegister() != null) {
//                return tokenBo.getRegister().getAppId();
//            }
             if (tokenBo.getPractice() != null) {
                return tokenBo.getPractice().getAppId();
            }
            else {
                return tokenBo.getAppId();
            }
        }
        return null;
    }
}