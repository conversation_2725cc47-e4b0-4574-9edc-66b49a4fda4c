package com.jiuzhekan.cbkj.common.utils.encry;


import com.jiuzhekan.cbkj.common.utils.MD5Util;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.net.URLDecoder;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * Description 非对称加密
 * Auther loujianbo
 * Create 2017/11/2 下午2:31
 * Company 杭州买吧信息科技有限公司
 * URL www.mai8mall.com
 **/
@Component
public class RSAEncryption {


    /**
     * 加密算法RSA
     */
    String KEY_RSA_ALGORITHM = "RSA";

    /**
     * 签名算法
     */
    String SIGNATURE_ALGORITHM = "SHA1withRSA";

    /**
     * RSA最大解密密文大小
     */
    int MAX_DECRYPT_BLOCK = 128;
    /**
     * RSA最大加密
     */
    int MAX_ENCRYPT_BLOCK = 117;

    /**
     * 编码方式
     */
    String CHARSET = "UTF-8";

    public String encryptByPublicKey(String data, String publicKey) throws Exception {
        byte[] dataStrBytes = data.getBytes(CHARSET);
        Key publicK = getPublicKey(publicKey);
        Cipher cipher = Cipher.getInstance(KEY_RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicK);
        byte[] encryptedData = this.getValue(dataStrBytes, cipher, MAX_ENCRYPT_BLOCK);
        return Base64Utils.encode(encryptedData);

    }


    public String encryptByPrivateKey(String data, String privateKey) throws Exception {
        byte[] dataStrBytes = data.getBytes(CHARSET);
        Key privateK = getPrivateKey(privateKey);
        Cipher cipher = Cipher.getInstance(KEY_RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, privateK);
        byte[] encryptedData = this.getValue(dataStrBytes, cipher, MAX_ENCRYPT_BLOCK);
        return Base64Utils.encode(encryptedData);

    }


    public String dencryptByPrivateKey(String data, String privateKey) throws Exception {
        byte[] dataStrBytes = Base64Utils.decode(data);
        Key privateK = this.getPrivateKey(privateKey);
        Cipher cipher = Cipher.getInstance(KEY_RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateK);
        byte[] encryptedData = this.getValue(dataStrBytes, cipher, MAX_DECRYPT_BLOCK);
        return new String(encryptedData, CHARSET);
    }


    public String sign(String data, String privateKey) throws Exception {
        PrivateKey privateK = getPrivateKey(privateKey);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateK);
        signature.update(data.getBytes(CHARSET));
        return Base64Utils.encode(signature.sign());
    }


    public boolean checkSign(String data, String publicKey, String sign) throws Exception {
        PublicKey publicK = getPublicKey(publicKey);
        byte[] dataStrBytes = data.getBytes(CHARSET);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicK);
        signature.update(dataStrBytes);
        return signature.verify(Base64Utils.decode(sign));
    }

    /**
     * 获得 公钥Key
     *
     * @param publicKey publicKey
     * @return Key
     * @throws Exception 异常
     */

    private PublicKey getPublicKey(String publicKey) throws Exception {

        byte[] keyBytes = Base64Utils.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_RSA_ALGORITHM);
        return keyFactory.generatePublic(x509KeySpec);

    }

    /**
     * 获得密钥Key
     *
     * @param privateKey privateKey
     * @return Key
     * @throws Exception 异常
     */
    private PrivateKey getPrivateKey(String privateKey) throws Exception {

        byte[] keyBytes = Base64Utils.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_RSA_ALGORITHM);
        return keyFactory.generatePrivate(pkcs8KeySpec);


    }

    /**
     * 分段加解密方法
     *
     * @param dataStrBytes 加密数据byte[]
     * @param cipher       cipher
     * @return byte[]
     * @throws Exception 异常
     */
    private byte[] getValue(byte[] dataStrBytes, Cipher cipher, int maxSize) throws Exception {
        int inputLen = dataStrBytes.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > maxSize) {
                cache = cipher.doFinal(dataStrBytes, offSet, maxSize);
            } else {
                cache = cipher.doFinal(dataStrBytes, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * maxSize;
        }
        byte[] data = out.toByteArray();
        out.close();
        return data;

    }

    /**
     * 初始化密钥
     *
     * @return Map
     * @throws Exception 异常
     */
    private Map<String, String> initKey() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator
                .getInstance(KEY_RSA_ALGORITHM);
        keyPairGen.initialize(1024);

        KeyPair keyPair = keyPairGen.generateKeyPair();

        // 公钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();


        // 私钥
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

        Map<String, String> keyMap = new HashMap<>(2);

        keyMap.put("publicKey", Base64Utils.encode(publicKey.getEncoded()));
        keyMap.put("privateKey", Base64Utils.encode(privateKey.getEncoded()));
        return keyMap;
    }


    public static void main(String[] args) throws Exception {
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDaKmCXLWqBIVOVSa/dTY08bgKhWcvi3j6vPksjrV7X3oFZ6dbF3XhaTXuV65x9qqmlQqlNepkqv03miY72QkjWMgbaEt0v5MFnQ+B2AWPIGunvSbaDIBwer/6V9f+ra7imLdgt9jwlkjDYvRjvw7mDHFkXkMvKWl1FFihV0EN8JwIDAQAB";
        String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANoqYJctaoEhU5VJr91NjTxuAqFZy+LePq8+SyOtXtfegVnp1sXdeFpNe5XrnH2qqaVCqU16mSq/TeaJjvZCSNYyBtoS3S/kwWdD4HYBY8ga6e9JtoMgHB6v/pX1/6truKYt2C32PCWSMNi9GO/DuYMcWReQy8paXUUWKFXQQ3wnAgMBAAECgYBnRa4pgg8rh0oYniQIv0A/Pdgy5t3zy76il/tbrSL7TtGubVoEmfzxykHZSwnuFs5tc2vPSFye9qX6nl01R1VQZ7JM2FVpldRw0Rrqv3paQjyHdKOcIYBJG2mxI7fpg/du8kwlkNCHRl39aJHQXHbY+lLEwE4HBJ7niMvdGoIpGQJBAPb+47h3++8vFFx99GlsrEVS5NAYNr8VyuGRYfxJHD/qNFcOZmosi8NnziFFnyQ/0C0rou5EDJu+jlBJdl1KNHsCQQDiHm2J9zSLDAr70ZyQ4l+vaPbw+26CFByBG4dsh7xeNa95mSbj72JuCZCyuQZaacThf3iWQuzy2N9TUFib+9VFAkEAlamnFJzndGwDm3PayJLH5A2xhgJWEf1DfODaDcPDMVtZsbKRDh7F5Xad6X1FS/K60tQRGuzy8uBJXY7WAPs4xwJAZ1zNadHM/PrGUpJg5YH1h3ON3l6xB1k2JnZ1E1GA8/fKfOVbd7pH3lEVCf22P8I1s3bXoqh5NBGbFLSXrMYTmQJBAJBxs9VEz2+qFfJmWSYiJBugoFD/cBWfL1VHwNeuZtJxFXXpkB3G1CLaII1SaGOWXHFMOycbemfTQzxSEiRFjOE=";



        RSAEncryption rsa = new RSAEncryption();
        String enStr = rsa.encryptByPublicKey(MD5Util.md5("21232f297a57a5a743894a0e4a801fc3"), publicKey);
        System.out.println("enStr = [" + enStr + "]");
        System.out.println("deStr = [" + rsa.dencryptByPrivateKey(enStr, privateKey) + "]");

        String deStr = rsa.dencryptByPrivateKey("TmibUWJjkHGpPrHGC4QzgifR4m7X8o/7jzEKP7mkpF1sgAZ8scIjJtO6pG2KxhiEw3Kx2Qxe8Dq2W7TaoWGmVdIXEjBTLoiLf8b3EDxUHqFpQW8Tj5Q5EBnB8OpZhB69YxavjnJg9jDAsGOokCjtvOP7vjHQ1tZ60CgD0RCvgb4=", privateKey);
        String deStr2 = rsa.dencryptByPrivateKey("0nwpbTqO94iAtJSbGDJDxj0oTO/LB4IDPx2JIDK5DbRXTVXEWn1bebNNQampQlXRLL+hNXJQL/wxmIGBS1jsKj/Au82FO1GLG0E/VFFTQEepBR3A7Km3rUaNmYN8b2eSgj0HBvwjoQGZPFF9TXJ+ti+l/GvkdlXQz3DK9zZgRv0=", privateKey);
        String decode = URLDecoder.decode("W%2FgAsfh%2Fd5mTlvdOUKjcAoFkxCl6x6VUtscjbfqKR8LxArZcBntTUoNNYbizJ3alXJYkauE7BdHuzGvBoENqSZOA%2F4ALHLim%2BvozVeUherhyx7FRLcphdu9qOuyr523zjAk%2BmsaaqLN58Q%2Fb0wE68liOBdAbRmYn9v1BD6F5%2BwI%3D");
        String deStr22 = rsa.dencryptByPrivateKey(decode, privateKey);
        String decode1 = URLDecoder.decode("V3Nl7KvWyCY3vA2JDAuKUSQKnzRrmfj%2Frtn6iDEoUmfZio%2B56voC4gTsvTvP%2Bhp9JEk8ppJH2qrL6QBZt2UdgoiEw17e0zaC2JYCvS%2BZqJlDMw433bMz%2FnQN9GEwCbhz3vlRZAZTzcQMjNQrtgFjc5iVZp662Zih45wZyLjuHwQ%3D");
        String deStr222 = rsa.dencryptByPrivateKey(decode1, privateKey);

        System.out.println(deStr2);
        System.out.println(deStr22);
        System.out.println(deStr222);
        System.out.println("deStr = [" + MD5Util.encode("admin").equals(deStr) + "]");
        System.out.println(MD5Util.encode("123456"));
    }
}
