package com.jiuzhekan.cbkj.common.config.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import io.jsonwebtoken.ExpiredJwtException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AuthorizationTokenFilter.java
 * @createTime 2019年12月13日 14:27:00
 */
@Slf4j
public class AuthorizationTokenFilter extends OncePerRequestFilter {

    private ObjectMapper objectMapper;
    private TokenUtil tokenUtil;
    private String tokenHeader;
    private Long expirationExt;

    public AuthorizationTokenFilter(TokenUtil tokenUtil, ObjectMapper objectMapper, String tokenHeader, Long expirationExt) {
        this.tokenUtil = tokenUtil;
        this.objectMapper = objectMapper;
        this.tokenHeader = tokenHeader;
        this.expirationExt = expirationExt;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {

        String authToken = request.getHeader(tokenHeader);
        String tokenKey = null;
        String resetToken = null;
        TokenBo bo = null;

        //跳过登录接口
        if (!request.getRequestURI().equals(request.getContextPath() + "/login")) {
            if (!request.getRequestURI().equals(request.getContextPath() + "/randomImage")){
                if (StringUtils.isBlank(authToken)) {
                    response.getWriter().write(objectMapper.writeValueAsString(
                            ResEntity.entity(false, "请登录！！", "301")));
                    return;
                }

                try {
                    tokenKey = tokenUtil.getKeyFromToken(authToken);
                    bo = getTokenBo(response, tokenKey, authToken);
                    if (bo == null) {
                        return;
                    }
                } catch (ExpiredJwtException e) {

                    response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);

                    Map<String, Object> map = tokenUtil.getKeyAndExp(authToken);
                    tokenKey = (String) map.get("key");
                    Long expiration = (Long) map.get("expirationTime");
                    bo = getTokenBo(response, tokenKey, authToken);
                    if (bo == null) {
                        return;
                    }
                    if (new Date().before(new Date(expiration * 1000 + expirationExt))) {

                        resetToken = tokenUtil.generateToken(tokenKey);

                        response.setHeader("Access-Control-Expose-Headers", "reset_token");
                        response.setHeader("reset_token", resetToken);

                    } else {
                        response.getWriter().write(objectMapper.writeValueAsString(
                                new ResEntity(false, 301, "登录已过期，请重新登录！！", "301")));
                        return;
                    }
                }

                tokenUtil.setSecurityContext(bo);
            }
        }
        chain.doFilter(request, response);

    }

    private TokenBo getTokenBo(HttpServletResponse response, String tokenKey, String authorization) throws IOException {
        TokenBo bo;
        bo = tokenUtil.getSecurityRegister(tokenKey);
        if (bo == null || bo.getAdmin() == null) {
            response.getWriter().write(objectMapper.writeValueAsString(
                    new ResEntity(false, 301, "登录已过期，请重新登录！！", "301")));
            return null;
        }
        if (tokenUtil.getLoginOne() && !authorization.equals(bo.getAuthorization())) {
            response.getWriter().write(objectMapper.writeValueAsString(
                    new ResEntity(false, 540, "此账户已在其他终端登陆，请重新登录！！", "540")));
            return null;
        }
        return bo;
    }

}
