package com.jiuzhekan.cbkj.common.utils;

import java.math.BigDecimal;

/**
 * 描述
 * 数学运算工具类
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
public class MathUtils {


    /**
     * 除法
     * @param num1 除数
     * @param num2 被除数
     * @param scale 精度
     * @return
     */
    public static double div(double num1, double num2, int scale){
        if(scale<0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        if (0 == num1 || 0 == num2){
            return  0;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(num1));
        BigDecimal b2 = new BigDecimal(Double.toString(num2));
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_DOWN).doubleValue();
    }

    /**
     * 乘法运算
     * @param num1
     * @param num2
     * @return
     */
    public static double multiply(double num1, double num2){
        BigDecimal b1 = new BigDecimal(Double.toString(num1));
        BigDecimal b2 = new BigDecimal(Double.toString(num2));
        return b1.multiply(b2).doubleValue();
    }


    /**
     * 减法运算
     * @param num1
     * @param num2
     * @return
     */
     public static double subtract(double num1, double num2){
        BigDecimal b1 = new BigDecimal(Double.toString(num1));
        BigDecimal b2 = new BigDecimal(Double.toString(num2));
        return b1.subtract(b2).doubleValue();
     }
}
