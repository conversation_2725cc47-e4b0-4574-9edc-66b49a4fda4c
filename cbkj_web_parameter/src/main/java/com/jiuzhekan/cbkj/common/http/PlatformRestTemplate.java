package com.jiuzhekan.cbkj.common.http;


import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import java.util.Map;
import java.util.Objects;

/**
 * http请求组件
 */
@Slf4j
@Component
public class PlatformRestTemplate extends RestTemplate {
    @Value("${address.platform.api.url:platform/api/}")
    private String platformUrl;

    @Autowired
    private RestTemplate restTemplate;


    public ResEntity post(String path, Map<String, Object> params) {

        String ip = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest().getRequestURI();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("providerId", "1");
        map.add("providerSecret", MD5Util.encode("1".concat(ip)));
        map.add("modualCode", "1");
        params.forEach(map::add);
        HttpEntity<MultiValueMap<String, Object>> request1 = new HttpEntity<>(map, headers);

        log.info("platform api request --- URL:{}, params:{}", platformUrl + path, map);
        ResEntity result = restTemplate.postForEntity(platformUrl + path, request1, ResEntity.class).getBody();
        log.info("platform api response --- URL:{}, params:{}, result:{}", platformUrl + path, map, result);
        return result;
    }

    public ResEntity post(String path, HttpEntity<MultiValueMap<String, Object>> request) {

        String ip = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest().getRequestURI();
        MultiValueMap<String, Object> map = request.getBody();
        map.add("providerId", "1");
        map.add("providerSecret", MD5Util.encode("1".concat(ip)));
        map.add("modualCode", "1");

        log.info("platform api request --- URL:{}, params:{}", platformUrl + path, request);
        ResEntity result = restTemplate.postForEntity(platformUrl + path, request, ResEntity.class).getBody();
        log.info("platform api response --- URL:{}, params:{}, result:{}", platformUrl + path, request, result);
        return result;
    }

}