package com.jiuzhekan.cbkj.common.config.dm;


import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

/**
 * Mybatis配置类
 * <AUTHOR>
 * @时间 2024/3/6 14:40
 */
@Configuration
public class MyBatisSqlInterceptorConfiguration implements ApplicationContextAware {

    @Value("${system.dbType}")
    private String dbType;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if(SystemConfigConstants.DB_TYPE_DM.equals(dbType)){
            SqlSessionFactory sqlSessionFactory = applicationContext.getBean(SqlSessionFactory.class);
            sqlSessionFactory.getConfiguration().addInterceptor(new MysqlToDmInterceptor());
        }
    }

}

