//package com.jiuzhekan.cbkj.common.utils;
//
//import java.awt.image.BufferedImage;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.FileNotFoundException;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.io.OutputStream;
//
////import com.itextpdf.text.FontFactory;
//import org.krysalis.barcode4j.impl.code39.Code39Bean;
//import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
//import org.krysalis.barcode4j.tools.UnitConv;
//import org.springframework.util.StringUtils;
//
//import javax.imageio.ImageIO;
//import javax.imageio.spi.ImageOutputStreamSpi;
//import javax.imageio.stream.FileImageOutputStream;
//import javax.imageio.stream.ImageOutputStream;
//import javax.imageio.stream.ImageOutputStreamImpl;
//import javax.servlet.ServletOutputStream;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * 条形码工具类
// *
// * <AUTHOR>
// * @createDate 2015年9月17日
// *
// */
//public class BarcodeUtil {
//
//    /**
//     * 生成文件
//     *
//     * @param msg
//     * @param path
//     * @return
//     */
//    public static File generateFile(String msg, String path) {
//        File file = new File(path);
//        try {
//            generate(msg, new FileOutputStream(file));
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        }
//        return file;
//    }
//
//    /**
//     * 生成字节
//     *
//     * @param msg
//     * @return
//     */
//    public static byte[] generate(String msg) {
//        ByteArrayOutputStream ous = new ByteArrayOutputStream();
//        generate(msg, ous);
//        return ous.toByteArray();
//    }
//
//    public static void generate2(String msg, HttpServletResponse response) {
//        ServletOutputStream ous = null;
//        try {
//            ous = response.getOutputStream();
////            ous.write();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        generate(msg, ous);
//    }
//
//    /**
//     * 生成到流
//     *
//     * @param msg
//     * @param ous
//     */
//    public static void generate(String msg, OutputStream ous) {
//        if (StringUtils.isEmpty(msg) || ous == null) {
//            return;
//        }
//
//        Code39Bean bean = new Code39Bean();
//
//        // 精细度
//        final int dpi = 150;
//        // module宽度
//        final double moduleWidth = UnitConv.in2mm(1.0f / dpi);
//
//        // 配置对象
//        bean.setModuleWidth(moduleWidth);
//        bean.setWideFactor(3);
//        bean.doQuietZone(false);
//
//        String format = "image/png";
//        try {
//
////            FontProgram fontProgram = FontProgramFactory.createFont("/usr/share/fonts/ttf-dejavu/simsun.ttf");
//
//
//            // 输出到流
//            BitmapCanvasProvider canvas = new BitmapCanvasProvider(ous, format, dpi,
//                    BufferedImage.TYPE_BYTE_BINARY, false, 0);
//
//            // 生成条形码
//            bean.generateBarcode(canvas, msg);
//
//            // 结束绘制
//            canvas.finish();
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    public static void main(String[] args) {
//        String msg = "123456789";
////        String path = "barcode.png";
//        String path = "D:/"+IDUtil.getID()+".png";
//        byte[] sdfs = generate("sdf");
//        generateFile(msg, path);
//        System.out.println("条形码生成=="+generateFile(msg, path));
//    }
//}