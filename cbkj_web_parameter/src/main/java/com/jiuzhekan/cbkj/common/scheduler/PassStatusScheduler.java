package com.jiuzhekan.cbkj.common.scheduler;


import com.jiuzhekan.cbkj.service.business.TPrescriptStatusPassService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
@EnableScheduling
@Transactional(rollbackFor = Exception.class)
public class PassStatusScheduler {

    private final TPrescriptStatusPassService tPrescriptStatusPassService;
    @Autowired
    PassStatusScheduler(TPrescriptStatusPassService tPrescriptStatusPassService){
        this.tPrescriptStatusPassService = tPrescriptStatusPassService;
    }
    @Scheduled(cron = "0 0/5 * * * ?")
    public void statisticFunction() {
        tPrescriptStatusPassService.passStatusScheduler();
    }
}
;