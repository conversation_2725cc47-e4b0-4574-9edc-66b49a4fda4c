package com.jiuzhekan.cbkj.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiuzhekan.cbkj.common.config.security.AuthorizationTokenFilter;
import com.jiuzhekan.cbkj.common.config.security.TokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 *
 * <AUTHOR>
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${root.preview}")
    private String preview;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TokenUtil tokenUtil;

    @Value("${jwt.token}")
    private String tokenHeader;

    @Value("${jwt.expirationExt}")
    private Long expirationExt;


    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/login","/public/getBanner","/randomImage/**","/doc.html", "/swagger-ui.html", "/swagger-ui.html/**", "/webjars/**",
                "/swagger-resources/**", "/barcode/**", "/v2/api-docs",
                "/transfer/**", "/error/**", "/noAuth/**", "/" + preview + "**", "/know/analysis/resultInsert",
                "/tBusinessManual/getManualFile", "/tUserAnalysisResult/download",
                "/sysAdminPractice/validPractices","/sysSettingInfo/getPlatFormLogoTitleInfo");
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {

//        http.headers().frameOptions().disable();

        http.cors().and().csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and().authorizeRequests().anyRequest().authenticated()
                .and().addFilterAfter(getAuthorizationTokenFilter(), UsernamePasswordAuthenticationFilter.class);
    }

    private AuthorizationTokenFilter getAuthorizationTokenFilter() {
        return new AuthorizationTokenFilter(tokenUtil, objectMapper, tokenHeader, expirationExt);
    }
}