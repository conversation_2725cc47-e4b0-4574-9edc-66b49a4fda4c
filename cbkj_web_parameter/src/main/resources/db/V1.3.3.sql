CREATE TABLE `sys_product_matrix` (
  `product_matrix_id` varchar(64) NOT NULL COMMENT '产品矩阵ID',
  `category` varchar(32) NOT NULL COMMENT '系统分类',
  `order_num` int(11) DEFAULT 0 COMMENT '排序号',
  `status` varchar(2) DEFAULT '1' COMMENT '状态,0:未启用，1:启用',
  `name` varchar(100) NOT NULL COMMENT '系统名称',
  `type` varchar(32) NOT NULL COMMENT '系统类型',
  `tag` varchar(200) DEFAULT NULL COMMENT '系统标签',
  `link` varchar(1000) DEFAULT NULL COMMENT '访问链接',
  `video_url` varchar(1000) DEFAULT NULL COMMENT '视频地址',
  `logo_url` varchar(1000) DEFAULT NULL COMMENT 'logo地址',
  `is_del` varchar(2) DEFAULT '0' COMMENT '是否删除,0:未删除，1:删除',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_username` varchar(100) DEFAULT NULL COMMENT '创建人姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_username` varchar(100) DEFAULT NULL COMMENT '修改人姓名',
  `del_date` datetime DEFAULT NULL COMMENT '删除时间',
  `del_user` varchar(64) DEFAULT NULL COMMENT '删除人',
  `del_username` varchar(100) DEFAULT NULL COMMENT '删除人姓名',
  PRIMARY KEY (`product_matrix_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品矩阵表';

CREATE TABLE `sys_role_matrix` (
                                   `role_matrix_id` varchar(64) NOT NULL COMMENT '主键',
                                   `role_id` varchar(64) NOT NULL COMMENT '角色ID',
                                   `matrix_id` varchar(64) NOT NULL COMMENT '矩阵ID',
                                   PRIMARY KEY (`role_matrix_id`),
                                   KEY `idx_role_id` (`role_id`),
                                   KEY `idx_matrix_id` (`matrix_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色矩阵关联表';

INSERT INTO cbkj_web_parameter.sys_admin_menu (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) VALUES ('2902', '产品矩阵管理', '/system/product-matrix', NULL, '0', '1', '2022-03-14 17:44:30', NULL, 1, NULL, NULL, 1, 16, 2, 0, 1, '1');

REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('064c2507824b11f0aa7a00163f006620', 'product-matrix-category', '产品矩阵-系统分类', '0', '产品矩阵，系统分类', '0', NULL, 1, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bc9c824b11f0aa7a00163f006620', 'product-matrix-category-znzl', '智能诊疗', '064c2507824b11f0aa7a00163f006620', '智能诊疗', '0', NULL, 1, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bd1b824b11f0aa7a00163f006620', 'product-matrix-category-znys', '智能药事', '064c2507824b11f0aa7a00163f006620', '智能药事', '0', NULL, 2, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bd37824b11f0aa7a00163f006620', 'product-matrix-category-zncc', '智能传承', '064c2507824b11f0aa7a00163f006620', '智能传承', '0', NULL, 3, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bd50824b11f0aa7a00163f006620', 'product-matrix-category-znjg', '智能监管', '064c2507824b11f0aa7a00163f006620', '智能监管', '0', NULL, 4, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bd85824b11f0aa7a00163f006620', 'product-matrix-category-znyj', '智能硬件', '064c2507824b11f0aa7a00163f006620', '智能硬件', '0', NULL, 5, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bd6d824b11f0aa7a00163f006620', 'product-matrix-category-hzfw', '患者服务', '064c2507824b11f0aa7a00163f006620', '患者服务', '0', NULL, 6, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0650bd9a824b11f0aa7a00163f006620', 'product-matrix-category-kjdz', '科技底座', '064c2507824b11f0aa7a00163f006620', '科技底座', '0', NULL, 7, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');


REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('06534427824b11f0aa7a00163f006620', 'product-matrix-type', '产品矩阵-系统类型', '0', '产品矩阵，类型', '0', NULL, 1, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0657bfab824b11f0aa7a00163f006620', 'product-matrix-type-web', 'Web端', '06534427824b11f0aa7a00163f006620', 'Web端', '0', NULL, 2, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0657c00d824b11f0aa7a00163f006620', 'product-matrix-type-sj', '手机端', '06534427824b11f0aa7a00163f006620', '手机端', '0', NULL, 1, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0657c02a824b11f0aa7a00163f006620', 'product-matrix-type-pbd-hp', '平板端-横屏', '06534427824b11f0aa7a00163f006620', '平板端-横屏', '0', NULL, 3, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');
REPLACE INTO cbkj_web_parameter.t_dic_base (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('0657c046824b11f0aa7a00163f006620', 'product-matrix-type-pbd-sp', '平板端-竖屏', '06534427824b11f0aa7a00163f006620', '平板端-竖屏', '0', NULL, 4, '000000', '000000', '000000', '000000', '000000', '2025-08-26 15:05:18', 'yujn', 'yujn', '0');


