package com.jiuzhekan.cbkj.beans${businessPath};

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class ${entityName} implements Serializable{

<#list maps?keys as key>
    @ApiModelProperty(value = "${comments[key_index]}")
    private ${javaType[key]} ${maps[key]};

</#list>

}
