<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <meta name="renderer" content="webkit"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title th:replace="../templates/common :: commonHeand('${desc}首页')"></title>
    <link rel="stylesheet" type="text/css" media="all" th:href="@{/css/listIndex.css}"/>
    <script type="text/javascript" charset="UTF-8" th:src="@{/sys/${entityName?uncap_first}/index.js}"></script>

</head>
<body>
<div class="btns">

    <div class="lefts">
        <div th:each="btn,bStar : ${r"${btnLis}"}" th:if="${r"${btn.btnWeight}"} != 2" th:class="${r"${btn.btnClass}"}" th:data-type="${r"${btn.btnType}"}"  >
            <i  th:class="|iconfont_ ${r"${btn.iconCls}|"}" ></i>
            <span th:text="${r"${btn.mname}"}"></span>
        </div>
    </div>

    <div type="text/html"  id="barDemo">
        <a th:class="|layui-btn-sm ${r"${btn.btnClass}|"}|" th:each="btn,bStar : ${r"${btnLis}"}" th:if="${r"${btn.btnWeight}"} != 1" th:lay-event="${r"${btn.btnType}"}" >
            <span th:text="${r"${btn.mname}"}"></span>
        </a>
    </div>

    <div class="seachTable">

        <div class="layui-inline" style="width: 201px;">
            <input placeholder="名称" class="layui-input" name="name" id="name" autocomplete="off">
        </div>
        <div class="layui-btn" data-type="reload"><i class="layui-icon">&#xe615;</i>搜索</div>

    </div>

</div>

<div class="tableLy">
    <table class="layui-hide" id="qb" lay-filter="qbTable"></table>
</div>

</body>
</html>