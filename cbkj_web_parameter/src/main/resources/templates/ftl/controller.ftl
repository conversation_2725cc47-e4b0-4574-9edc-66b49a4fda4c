package com.jiuzhekan.cbkj.controller${businessPath};

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans${businessPath}.${entityName};
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service${businessPath}.${entityName}Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

@Controller
@Api(value = "${desc}接口", tags = "${desc}接口")
@RequestMapping("${entityName?uncap_first}")
public class ${entityName}Controller {

    @Autowired
    private ${entityName}Service ${entityName?uncap_first}Service;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询${desc}", notes = "分页查询${desc}", response = ${entityName}.class)
    @LogAnnotation("分页查询${desc}")
    @ResponseBody
    public Object getApps(${entityName} ${entityName?uncap_first}, Page page){
        return ${entityName?uncap_first}Service.getPageDatas(${entityName?uncap_first},page);
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载${desc}详情", notes = "加载${desc}详情", response = ${entityName}.class)
    @LogAnnotation("加载${desc}详情")
    @ResponseBody
    public Object getObj(String id){
        return ${entityName?uncap_first}Service.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增${desc}", notes = "新增${desc}")
    @LogAnnotation(value = "新增${desc}", isWrite = true)
    @ResponseBody
    public Object insert(@RequestBody ${entityName} ${entityName?uncap_first}) {
        return ${entityName?uncap_first}Service.insert(${entityName?uncap_first});
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改${desc}", notes = "修改${desc}详情")
    @LogAnnotation(value = "修改${desc}", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody ${entityName} ${entityName?uncap_first}) {
        return ${entityName?uncap_first}Service.update(${entityName?uncap_first});
    }

    @RequestMapping(value="deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除${desc}", notes = "删除${desc}")
    @LogAnnotation(value = "删除${desc}", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return ${entityName?uncap_first}Service.deleteLis(ids);
    }

}