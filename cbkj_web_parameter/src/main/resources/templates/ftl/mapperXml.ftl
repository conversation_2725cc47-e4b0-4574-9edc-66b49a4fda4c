<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper${businessPath}.${entityName}Mapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans${businessPath}.${entityName}">
        <id column="${primary}" jdbcType="${colTypeM[primary]}"  property="${maps[primary]}" />
        <#list maps?keys as key>
            <#if key != primary>
        <result column="${key}" jdbcType="${colTypeM[key]}" property="${maps[key]}" />
            </#if>
        </#list>
    </resultMap>


    <sql id="Base_Column_List">
    ${colNames}
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="${entityName}">
        delete from ${tableName} where ${primary} = ${r"#{"} ${maps[primary]} ${r"}"}
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from ${tableName} where ${primary} in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        ${r"#{item}"}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="${entityName}">
        insert into ${tableName} (<include refid="Base_Column_List" />) values
        (<#list maps?keys as key>${r"#{"}${maps[key]}${r"}"}<#if key_has_next>,</#if></#list>)
    </insert>

    <insert id="insertList" parameterType="List">
        insert into ${tableName} (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (<#list maps?keys as key>${r"#{item."}${maps[key]}${r"}"}<#if key_has_next>,</#if></#list>)
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="${entityName}">
        update ${tableName}
        <set>
        <#list maps?keys as key>
            <#if key != primary>
             <if test="${maps[key]} != null">
                ${key} = ${r"#{"} ${maps[key]} ${r"}"},
             </if>
            </#if>
        </#list>
        </set>
        where ${primary} = ${r"#{"} ${maps[primary]} ${r"}"}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from ${tableName} where ${primary} = ${r"#{id}"}
    </select>

    <select id="getPageListByObj" parameterType="${entityName}" resultMap="BaseResultMap">
        SELECT ${colNames}
        from ${tableName}
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(${r"#{name}"}),'%')
            </if>
        </where>
    </select>

</mapper>