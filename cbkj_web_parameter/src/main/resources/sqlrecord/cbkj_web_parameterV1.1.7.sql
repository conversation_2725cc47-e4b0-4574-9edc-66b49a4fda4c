ALTER TABLE t_display_production ADD  isProduction  VARCHAR(1) NOT NULL DEFAULT  '1' COMMENT '默认制膏(0默认，1否)' AFTER SHOW_PRODUCTION;

UPDATE t_sys_param SET PAR_NAME='中医疾病、证型限制使用扩充名称' WHERE PAR_CODE='DIAGNOSIS_RANGE_VERIFY';
UPDATE t_sys_param SET PAR_NAME='中医疾病GB-2021限制使用类名' WHERE PAR_CODE='DIS_FILTER_CATEGORY';
UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param SET cbkj_web_parameter.t_sys_param_init_desc.param_init_name='开启'
WHERE cbkj_web_parameter.t_sys_param.`PAR_ID`=cbkj_web_parameter.t_sys_param_init_desc.`param_id`
  AND cbkj_web_parameter.t_sys_param.`PAR_CODE`='DIS_FILTER_CATEGORY'
  AND cbkj_web_parameter.t_sys_param_init_desc.param_init_name='不可搜索';
UPDATE cbkj_web_parameter.t_sys_param_init_desc,cbkj_web_parameter.t_sys_param SET cbkj_web_parameter.t_sys_param_init_desc.param_init_name='关闭'
WHERE cbkj_web_parameter.t_sys_param.`PAR_ID`=cbkj_web_parameter.t_sys_param_init_desc.`param_id`
  AND cbkj_web_parameter.t_sys_param.`PAR_CODE`='DIS_FILTER_CATEGORY'
  AND cbkj_web_parameter.t_sys_param_init_desc.param_init_name='可以搜索';
#中医证型GB-2021限制使用类名
INSERT INTO t_sys_param
(PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
 STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','TCM_SYNDROME_TYPE','中医证型GB-2021限制使用类名','2',NOW(),
       'admin','admin','0','2','0','1','10','1011','中医证型GB-2021限制使用类名','B108');
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'0','不限制',0
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'1','一级',1
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'2','二级',2
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'3','三级',3
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'4','四级',4
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'5','五级',5
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'6','六级',6
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_SYNDROME_TYPE';
#中医治法GB-2021限制使用类名
INSERT INTO t_sys_param
(PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
 STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','TCM_TREATMENT','中医治法GB-2021限制使用类名','2',NOW(),
       'admin','admin','0','2','0','1','11','1011','中医治法GB-2021限制使用类名','B109');
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'0','不限制',0
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_TREATMENT';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'1','一级',1
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_TREATMENT';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'2','二级',2
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE='TCM_TREATMENT';
UPDATE `cbkj_web_parameter`.`t_sys_param` SET `PAR_NAME` = '处方状态变更调用接口项目' WHERE par_code = 'PRE_INTERFACE';