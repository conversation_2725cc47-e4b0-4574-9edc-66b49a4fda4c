INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'SCOP_MEDICATION_EVALAUTION', '安全合理用药处方测评报告展示范围', '', NOW(),
    'admin', 'admin', '0', '3', '0', '', '81', '1301', '1.慎禁忌用药、2.孕妇慎禁忌、3.十八反、4.十九畏、5.不宜通用、6.药物毒性、7.药物毒性超剂量、8.剂量超标、9.剂量偏低、10.超规定用药、11.病症用药禁忌、12.饮食禁忌、13.多规格', 'C016'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'SCOP_MEDICATION_EVALAUTION'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '慎禁忌用药', 1 FROM (
                                               SELECT tsp.PAR_ID
                                               FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                               WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                           ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='慎禁忌用药'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '2', '孕妇慎禁忌', 2 FROM (
                                               SELECT tsp.PAR_ID
                                               FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                               WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                           ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='孕妇慎禁忌'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '3', '十八反', 3 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='十八反'
    );



INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '十九畏', 4 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='十九畏'
    );



INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '5', '不宜通用', 5 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='5' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='不宜通用'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '6', '药物毒性', 6 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='6' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='药物毒性'
    );



INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '7', '药物毒性超剂量', 7 FROM (
                                                   SELECT tsp.PAR_ID
                                                   FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                   WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                               ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='7' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='药物毒性超剂量'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '8', '剂量超标', 8 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='8' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='剂量超标'
    );



INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '9', '剂量偏低', 9 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='9' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='剂量偏低'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '10', '超规定用药', 10 FROM (
                                                 SELECT tsp.PAR_ID
                                                 FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                 WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                             ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='10' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='超规定用药'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '11', '病症用药禁忌', 11 FROM (
                                                   SELECT tsp.PAR_ID
                                                   FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                   WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                               ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='11' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='病症用药禁忌'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '12', '饮食禁忌', 12 FROM (
                                               SELECT tsp.PAR_ID
                                               FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                               WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                           ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='12' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='饮食禁忌'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '13', '多规格', 13 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='SCOP_MEDICATION_EVALAUTION'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='13' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='多规格'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '21', '毒性药物超剂量', 21 FROM (
                                                     SELECT tsp.PAR_ID
                                                     FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                     WHERE tsp.PAR_CODE='SAFETY_EVALUATION_SIGN'
                                                 ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='21' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='毒性药物超剂量');



ALTER TABLE `cbkj_web_parameter`.`t_material_price`
    ADD COLUMN `toxicity_overdose_multiple` VARCHAR(10) NULL COMMENT '毒性超剂量双签倍数' AFTER `mat_factory_name`,
    ADD COLUMN `external_use_only` VARCHAR(1) NULL COMMENT '仅限外用(0关1开)' AFTER `toxicity_overdose_multiple`;

	ALTER TABLE `cbkj_web_parameter`.`t_material_price`
    ADD COLUMN `maxdose`DOUBLE NULL COMMENT '最大剂量' AFTER `external_use_only`,
    ADD COLUMN `mindose` DOUBLE NULL COMMENT '最小剂量' AFTER `maxdose`;




DELIMITER $$

ALTER ALGORITHM=UNDEFINED DEFINER=`root`@`%` SQL SECURITY DEFINER VIEW `v_center_ypmlmx` AS (
    SELECT
        `matprice`.`drug_id`                    AS `drug_id`,
        `matdeatil`.`mat_standard`              AS `drug_code`,
        `matprice`.`mat_price_id`               AS `mat_price_id`,
        `matdeatil`.`mat_name`                  AS `mat_name`,
        `matprice`.`mat_spe_id`                 AS `mat_spe_id`,
        `matspecification`.`mat_spe`            AS `mat_spe`,
        `matorigin`.`mat_origin_id`             AS `mat_origin_id`,
        `matorigin`.`mat_origin_name`           AS `mat_origin_name`,
        `matprice`.`retail_price`               AS `retail_price`,
        `matprice`.`purchase_price`             AS `purchase_price`,
        `matprice`.`small_purchase_price`       AS `small_purchase_price`,
        `matprice`.`small_retail_price`         AS `small_retail_price`,
        `matspecification`.`mat_dose`           AS `mat_dose`,
        `matspecification`.`mat_dose_unit`      AS `mat_dose_unit`,
        `matspecification`.`mat_pack_mount`     AS `mat_pack_mount`,
        `matspecification`.`mat_pack_unit`      AS `mat_pack_unit`,
        `matspecification`.`mat_type`           AS `mat_type`,
        `matspecification`.`mat_once_dose`      AS `mat_once_dose`,
        `matspecification`.`mat_once_dose_unit` AS `mat_once_dose_unit`,
        `matdeatil`.`mat_class`                 AS `mat_class`,
        `matspecification`.`conversion_factor`  AS `conversion_factor`,
        `matdeatil`.`status`                    AS `status`,
        `matdeatil`.`mat_pinyin`                AS `py`,
        `matdeatil`.`mat_wubi`                  AS `wb`,
        `matdeatil`.`is_medical`                AS `is_medical`,
        `matdeatil`.`is_del`                    AS `is_del`,
        `matdeatil`.`create_date`               AS `create_date`,
        `matdeatil`.`update_date`               AS `update_date`,
        `matprice`.`daily_max_dose_in`          AS `daily_max_dose_in`,
        `matprice`.`daily_max_dose_ext`         AS `daily_max_dose_ext`,
        `matprice`.`daily_max_num_prep`         AS `daily_max_num_prep`,
        `matdeatil`.`mat_usage`                 AS `mat_usage`,
        `matprice`.`not_pay_alone`              AS `not_pay_alone`,
        `matprice`.`not_pay_in_fund`            AS `not_pay_in_fund`,
        `matprice`.`frequency_id`               AS `frequency_id`,
        `matprice`.`frequency`                  AS `frequency`,
        `matprice`.`frequency_rate`             AS `frequency_rate`,
        `matdeatil`.`content`                   AS `content`,
        `matdeatil`.`usage_desc`                AS `usage_desc`,
        `matdeatil`.`effect`                    AS `effect`,
        `matprice`.`approval_number`            AS `approval_number`,
        `matdeatil`.`mat_id`                    AS `mat_id`,
        `matprice`.`toxicity_overdose_multiple` AS `toxicity_overdose_multiple`,
        `matprice`.`external_use_only`          AS `external_use_only`,
        `matdeatil`.`medicontrol_text`          AS `medicontrol_text`,
		 `matprice`.`maxdose`          AS `maxdose`,
		  `matprice`.`mindose`          AS `mindose`
    FROM (((`t_material_price` `matprice`
        JOIN `t_material` `matdeatil`
            ON (((`matprice`.`mat_id` = `matdeatil`.`mat_id`)
                AND (`matprice`.`drug_id` = `matdeatil`.`drug_id`))))
        JOIN `t_material_specification` `matspecification`
           ON (((`matspecification`.`mat_id` = `matprice`.`mat_id`)
               AND (`matspecification`.`mat_spe_id` = `matprice`.`mat_spe_id`)
               AND (`matspecification`.`status` = '0')
               AND (`matspecification`.`drug_id` = `matprice`.`drug_id`))))
        JOIN `t_material_origin` `matorigin`
          ON ((`matorigin`.`mat_origin_id` = `matprice`.`mat_origin_id`)))
    WHERE ((`matdeatil`.`status` = '0')
        OR (`matdeatil`.`status` = '2')))$$

DELIMITER ;