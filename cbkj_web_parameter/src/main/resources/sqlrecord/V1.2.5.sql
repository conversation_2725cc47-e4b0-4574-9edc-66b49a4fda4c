
ALTER TABLE `cbkj_web_parameter`.`t_material_price`
    ADD COLUMN `toxicity`VARCHAR(32) NULL COMMENT '毒性' AFTER `mindose`,
    ADD COLUMN `mother_taboos` VARCHAR(12) NULL COMMENT '孕妇慎禁忌' AFTER `toxicity`,
     ADD COLUMN `external_use_orally` VARCHAR(1) NULL COMMENT '仅限内服(0关1开)' AFTER `mother_taboos`,
    ADD COLUMN `external_marusan` VARCHAR(1) NULL COMMENT '仅限丸散(0关1开)' AFTER `external_use_orally`,
      ADD COLUMN `external_marusan_name` VARCHAR(32) NULL COMMENT '仅限丸散关键字' AFTER `external_marusan`;


INSERT INTO `cbkj_web_parameter`.`sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`)
VALUES('21','用药监测','/business/rs',NULL,'0','2',NULL,NULL,'1',NULL,NULL,'1','4','2','0','1','1');

CREATE TABLE `cbkj_web_parameter`.`t_prescription_regulation_param` (
                                                   `id` int(12) NOT NULL AUTO_INCREMENT,
                                                   `code` varchar(12) DEFAULT NULL,
                                                   `name` varchar(64) DEFAULT NULL,
                                                   `type` varchar(32) DEFAULT NULL,
                                                   `parent_id` varchar(12) DEFAULT NULL,
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COMMENT='审方规则条件表';



insert  into `cbkj_web_parameter`.`t_prescription_regulation_param`(`id`,`code`,`name`,`type`,`parent_id`) values (1,'101','响应事件','1','0'),(2,'102','条件','1','0'),(3,'1','妊娠相关','blue','2'),(4,'2','肝肾功能不全','blue','2'),(5,'3','小儿用药','blue','2'),(6,'4','只限内服','green','2'),(7,'5','只限内服丸散','green','2'),(8,'6','毒性药物需先煎','green','2'),(9,'7','单贴处方','green','2'),(10,'8','孕妇慎用超剂量','red','2'),(11,'9','孕妇禁用','red','2'),(12,'10','孕妇忌用','red','2'),(13,'11','孕妇慎用','red','2'),(14,'12','只限外用','green','2'),(15,'13','毒性药物超剂量','red','2'),(16,'14','医保限制','red','2'),(17,'15','禁用药','red','2'),(18,'16','忌用药','red','2'),(19,'17','慎用药','red','2'),(20,'18','十八反','red','2'),(21,'19','十九畏','red','2'),(22,'20','不宜同用','red','2'),(23,'21','大毒','red','2'),(24,'22','有毒','red','2'),(25,'23','小毒','red','2'),(26,'24','大毒+毒性超量','red','2'),(27,'25','有毒+毒性超量','red','2'),(28,'26','小毒+毒性超量','red','2'),(29,'27','病症禁忌','red','2'),(30,'28','饮食禁忌','red','2'),(31,'29','超规定用药','red','2'),(32,'30','剂量超标','red','2'),(33,'31','剂量偏低','red','2'),(34,'32','多规格同时开','red','2'),(35,'1','直接拦截',NULL,'1'),(36,'2','双签+药师审核',NULL,'1'),(37,'3','药师审核',NULL,'1'),(38,'4','双签',NULL,'1'),(39,'5','医生确认',NULL,'1');


CREATE TABLE `cbkj_web_parameter`.`t_prescription_regulation` (
                                             `id` int(12) NOT NULL AUTO_INCREMENT,
                                             `level` int(12) DEFAULT NULL COMMENT '事件级别',
                                             `event` varchar(128) DEFAULT NULL COMMENT '响应事件(1.直接拦截,2.双签+药师审核,3.药师审核,4.双签,5,医生确认)',
                                             `CREATE_DATE` datetime NOT NULL COMMENT '创建时间',
                                             `CREATE_USER` varchar(32) DEFAULT NULL COMMENT '创建人',
                                             `create_user_name` varchar(32) DEFAULT NULL COMMENT '创建人姓名',
                                             `UPDATE_DATE` datetime DEFAULT NULL COMMENT '修改时间',
                                             `UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '修改人',
                                             `update_user_name` varchar(32) DEFAULT NULL COMMENT '修改人姓名',
                                             `DEL_DATE` datetime DEFAULT NULL COMMENT '删除时间',
                                             `DEL_USER` varchar(32) DEFAULT NULL COMMENT '删除人',
                                             `DEL_USERNAME` varchar(32) DEFAULT NULL COMMENT '删除人姓名',
                                             `IS_DEL` varchar(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0否 1是）',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=168 DEFAULT CHARSET=utf8mb4 COMMENT='审方规则表';

CREATE TABLE `cbkj_web_parameter`.`t_prescription_regulation_item` (
                                                  `id` int(12) NOT NULL AUTO_INCREMENT,
                                                  `level` int(12) DEFAULT NULL COMMENT '事件id',
                                                  `category` int(2) DEFAULT NULL COMMENT '处方类型(1内服2外用)',
                                                  `options` varchar(128) DEFAULT NULL COMMENT '条件',
                                                  `IS_DEL` varchar(1) DEFAULT NULL COMMENT '是否删除（0否 1是）',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=383 DEFAULT CHARSET=utf8mb4 COMMENT='审方规则明细表';
