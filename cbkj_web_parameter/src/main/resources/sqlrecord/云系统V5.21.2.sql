INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'ZKXC_DIAGNOSTIC', '中科芯创四诊仪', '', NOW(),
    'admin', 'admin', '0', '3', '0', '', '3', '1701', '中科芯创四诊仪', 'K003'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'ZKXC_DIAGNOSTIC'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '处方一件事病历', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                   ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='处方一件事病历'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '2', '中医电子病历', 2 FROM (
                                                   SELECT tsp.PAR_ID
                                                   FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                   WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                               ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='中医电子病历'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '3', '智能辨证', 3 FROM (
                                                 SELECT tsp.PAR_ID
                                                 FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                 WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                             ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='智能辨证'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '国医大师辨证', 4 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='国医大师辨证'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '5', '传承', 5 FROM (
                                                 SELECT tsp.PAR_ID
                                                 FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                                 WHERE tsp.PAR_CODE='ZKXC_DIAGNOSTIC'
                                             ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='5' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='传承'
    );



