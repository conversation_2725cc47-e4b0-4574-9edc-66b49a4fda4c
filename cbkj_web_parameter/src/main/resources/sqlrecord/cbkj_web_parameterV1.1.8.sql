INSERT INTO t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
VALUES (REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'HISTORY_PRESCRIPTION_PAY', '智能云是否开启”模拟收退费”', '0', NOW(),
        'admin', 'admin', '0', '1', '0', '1', '101', '105', '智能云是否开启”模拟收退费”', 'G002');
INSERT INTO `t_sys_param_init_desc`(param_id, param_init_code, param_init_name, sort)
SELECT tsp.PAR_ID, '1', '开', 1
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE = 'HISTORY_PRESCRIPTION_PAY';
INSERT INTO `t_sys_param_init_desc`(param_id, param_init_code, param_init_name, sort)
SELECT tsp.PAR_ID, '0', '关', 2
FROM `t_sys_param` AS tsp
WHERE tsp.PAR_CODE = 'HISTORY_PRESCRIPTION_PAY';
UPDATE
    cbkj_web_parameter.`t_sys_param`
SET
    `PAR_NAME` = '医保限制第一次保存时提醒内容'
WHERE `PAR_CODE` = 'INSURANCE_LIMIT_TIP';


DELETE FROM  cbkj_web_parameter.`t_sys_param_init_desc` WHERE param_id IN(SELECT PAR_ID FROM cbkj_web_parameter.t_sys_param WHERE PAR_CODE='INSURANCE_LIMIT_OBJECT');
DELETE FROM cbkj_web_parameter.t_sys_param WHERE PAR_CODE='INSURANCE_LIMIT_OBJECT';

INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'INSURANCE_LIMIT_OBJECT', '医保提醒患者范围控制', '1,3', NOW(),
    'admin', 'admin', '0', '3', '0', '1,3', '96', '1401', '1.门诊医保、2.门诊自费、3.住院医保、4.住院自费、5.门诊特病', 'F009'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'INSURANCE_LIMIT_OBJECT'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '门诊医保', 1 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊医保'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '2', '门诊自费', 2 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊医保'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '3', '住院医保', 3 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='住院医保'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '住院自费', 4 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='住院自费'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '5', '门诊特病', 5 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='INSURANCE_LIMIT_OBJECT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='5' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='门诊特病'
    );


INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'HISTORY_PRESCRIPTION_PAY', '是否智能云开启模拟收退费', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '38', '1009', '是否智能云开启,模拟收退费1开0关', 'G002'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'ANALOG_RECEIVING_REFUND'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 1 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='HISTORY_PRESCRIPTION_PAY'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 2 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='HISTORY_PRESCRIPTION_PAY'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '处方退费（演示）', 4 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='PRE_INTERFACE'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='处方退费（演示）'
    );


INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'INSURANCE_LIMIT_REMIND', '医保限制开药时提醒', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '101', '1401', '医保限制开药时提醒，1开0关', 'F014'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'INSURANCE_LIMIT_REMIND'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 1 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='INSURANCE_LIMIT_REMIND'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 2 FROM (
                                      SELECT tsp.PAR_ID
                                      FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                      WHERE tsp.PAR_CODE='INSURANCE_LIMIT_REMIND'
                                  ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
    );



#增加药品明细表-医保说明-医保强制提醒
ALTER TABLE cbkj_web_parameter.`t_material_price`
    ADD COLUMN `xzsm` VARCHAR (128) NULL COMMENT '医保说明';

insert into `cbkj_web_parameter`.`sys_admin_menu` (
    `menu_id`,
    `menu_name`,
    `menu_path`,
    `menu_class`,
    `status`,
    `parent_menu_id`,
    `create_date`,
    `cteate_user`,
    `menu_type`,
    `btn_class`,
    `btn_type`,
    `btn_weight`,
    `sort`,
    `menu_level`,
    `open_type`,
    `modual_code`
)
values
    (
        '0002314',
        '监管看板',
        '/statistical/supervise-board',
        NULL,
        '0',
        '000214',
        '2021-07-21 11:22:20',
        '70810c874405453b99c6c2cf72296fe5',
        '2',
        NULL,
        NULL,
        NULL,
        '-1',
        '2',
        '1',
        '2'
    );
UPDATE `cbkj_web_parameter`.`t_sys_param` SET `par_number` = 'B601' WHERE `PAR_CODE` = 'PRINT_PRESCRIPTION_SHOW';
UPDATE `cbkj_web_parameter`.`t_sys_param` SET `par_number` = 'B602' WHERE `PAR_CODE` = 'PRINT_PRESCRIPTION_SHOW_QRCODE';
DROP TABLE IF EXISTS `t_prescript_status_pass`;
CREATE TABLE `t_prescript_status_pass` (
                                           `pass_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '补传主键',
                                           `pre_id` varchar(32) DEFAULT NULL COMMENT '处方id',
                                           `pre_no` varchar(32) DEFAULT NULL COMMENT '处方号码',
                                           `pre_status` varchar(6) DEFAULT NULL COMMENT '处方状态',
                                           `pass_url` varchar(500) DEFAULT NULL COMMENT '补传路径',
                                           `pass_params` text COMMENT '参数',
                                           `pass_method` varchar(10) DEFAULT NULL COMMENT 'get post',
                                           `pass_message` text,
                                           `server_type` int(1) DEFAULT NULL COMMENT '1 药房  2 开方',
                                           `pass_num` int(11) DEFAULT NULL COMMENT '补传次数',
                                           `create_date` datetime DEFAULT NULL COMMENT '新增时间',
                                           `create_user` varchar(32) DEFAULT NULL COMMENT '新增人',
                                           `del_user` varchar(32) DEFAULT NULL COMMENT '删除人',
                                           `del_type` varchar(1) DEFAULT NULL COMMENT '删除类型 1、定时任务删除    2、人工删除',
                                           `del_date` datetime DEFAULT NULL COMMENT '删除时间',
                                           `status` varchar(1) NOT NULL DEFAULT '0' COMMENT '0 正常 1 删除',
                                           PRIMARY KEY (`pass_id`)
) ENGINE=InnoDB AUTO_INCREMENT=144 DEFAULT CHARSET=utf8mb4;



INSERT INTO `cbkj_web_parameter`.`t_dic_base`
(dic_id,`dic_code`, `dic_name`, `parent_id`, `sort`, `pha_id`, `create_date`, `create_user`, `create_user_name`,other_json) VALUES
    (REPLACE(UUID(),"-",""),'08', '港澳台居民居住证',  '34' , '8', '000000', '2022-12-02 09:28:56', 'admin', 'admin',
     '{"hideProject":"", "extendDisplay":"", "unitPrice": "", "priceNum":"", "rate":""}');

UPDATE t_dic_standard SET `stan_code` = '03.'
WHERE `stan_name` = '内科' AND parent_id =
                               ( SELECT stan_id FROM ( SELECT stan_id FROM t_dic_standard WHERE stan_code='CVX_DEPARTMENTCODE') AS a )AND stan_type=1;

INSERT INTO `cbkj_web_parameter`.`t_dic_standard`(`stan_id`,
                                                       `stan_code`, `stan_name`, `parent_id`, `stan_desc`,
                                                       `stan_type`, `sort`, `create_date`, `create_user`, `create_user_name`, `status`)
SELECT
    REPLACE(UUID(),'-',''), '5xd', '每天5次', (SELECT  stan_id FROM `t_dic_standard` WHERE stan_code = 'CVX_FREQUENCY'),
    NULL, '1', '1', '2022-04-08 17:21:02', 'admin', NULL, '0';

INSERT INTO `cbkj_web_parameter`.`t_dic_standard`
(`stan_id`, `stan_code`, `stan_name`, `parent_id`, `stan_desc`,
 `stan_type`, `sort`, `create_date`, `create_user`, `create_user_name`, `status`)
SELECT
    REPLACE(UUID(),"-",""), 'q4d', '每4天1次', (SELECT  stan_id FROM `t_dic_standard` WHERE stan_code = 'CVX_FREQUENCY') AS p_id, NULL, '1', '1', '2022-04-08 17:21:02', 'admin', NULL, '0';

INSERT INTO `cbkj_web_parameter`.`t_dic_standard`
(`stan_id`, `stan_code`, `stan_name`, `parent_id`, `stan_desc`,
 `stan_type`, `sort`, `create_date`, `create_user`, `create_user_name`, `status`)
SELECT
    REPLACE(UUID(),"-",""), 'q6d', '每6天1次', (SELECT  stan_id FROM `t_dic_standard` WHERE stan_code = 'CVX_FREQUENCY') AS p_id, NULL, '1', '1', '2022-04-08 17:21:02', 'admin', NULL, '0';

INSERT INTO `cbkj_web_parameter`.`t_dic_standard`
(`stan_id`, `stan_code`, `stan_name`, `parent_id`, `stan_desc`,
 `stan_type`, `sort`, `create_date`, `create_user`, `create_user_name`, `status`)
SELECT
    REPLACE(UUID(),"-",""), 'q3w', '每3周1次', (SELECT  stan_id FROM `t_dic_standard` WHERE stan_code = 'CVX_FREQUENCY') AS p_id, NULL, '1', '1', '2022-04-08 17:21:02', 'admin', NULL, '0';

INSERT INTO `cbkj_web_parameter`.`t_dic_standard`
(`stan_id`, `stan_code`, `stan_name`, `parent_id`, `stan_desc`,
 `stan_type`, `sort`, `create_date`, `create_user`, `create_user_name`, `status`)
SELECT
    REPLACE(UUID(),"-",""), 'q4w', '每4周1次', (SELECT  stan_id FROM `t_dic_standard` WHERE stan_code = 'CVX_FREQUENCY') AS p_id, NULL, '1', '1', '2022-04-08 17:21:02', 'admin', NULL, '0';


INSERT INTO `cbkj_web_parameter`.`t_dic_standard`
(`stan_id`, `stan_code`, `stan_name`, `parent_id`, `stan_desc`,
 `stan_type`, `sort`, `create_date`, `create_user`, `create_user_name`, `status`)
SELECT
    REPLACE(UUID(),"-",""), 'once', '单次', (SELECT  stan_id FROM `t_dic_standard` WHERE stan_code = 'CVX_FREQUENCY') AS p_id, NULL, '1', '1', '2022-04-08 17:21:02', 'admin', NULL, '0';



INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6020edf171fa11edb0a7c018504fa798','06150740600100118','炒核桃仁','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6020f0d371fa11edb0a7c018504fa798','06154120500206725','姜厚朴（煮）','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6020f13b71fa11edb0a7c018504fa798','06154520500105002','桂尔通','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6020f16371fa11edb0a7c018504fa798','06156330200200116','炒扁豆花','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6020f22e71fa11edb0a7c018504fa798','06156340200304004','槐角片','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6020f30c71fa11edb0a7c018504fa798','06174430300200002','胎菊','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021352971fa11edb0a7c018504fa798','06191610600103006','生天南星片','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021358271fa11edb0a7c018504fa798','06192910700800119','炒薤白','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021368371fa11edb0a7c018504fa798','06193540600300007','砂米','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602136a671fa11edb0a7c018504fa798','06400410100102005','雷丸片','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602136c971fa11edb0a7c018504fa798','06152050100104003','寻骨风','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602136ed71fa11edb0a7c018504fa798','06158520700200008','枸骨叶','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021371071fa11edb0a7c018504fa798','06153710500302414','黄连炭','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021373271fa11edb0a7c018504fa798','06174110100303355','蜜桔梗','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021375271fa11edb0a7c018504fa798','06156310100603415','黄芪炭','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021377571fa11edb0a7c018504fa798','06135610500103117','炒骨碎补','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021379671fa11edb0a7c018504fa798','06152950500104118','炒马齿苋','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602137bb71fa11edb0a7c018504fa798','06154520200103117','炒桂枝','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602137dd71fa11edb0a7c018504fa798','06156340600300330','盐刀豆','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021380071fa11edb0a7c018504fa798','06156140200200111','炒乌梅','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021382271fa11edb0a7c018504fa798','06159340600100111','炒荔枝核','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021384371fa11edb0a7c018504fa798','06220620200107008','鹿角粉','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021386571fa11edb0a7c018504fa798','06172340200200335','盐枸杞子','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021388971fa11edb0a7c018504fa798','06154950100104110','炒荠菜花','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602138aa71fa11edb0a7c018504fa798','06172340200200410','枸杞子炭','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602138cb71fa11edb0a7c018504fa798','06154440500100411','肉豆蔻炭','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602138ed71fa11edb0a7c018504fa798','06153710400303412','附子炭','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021390f71fa11edb0a7c018504fa798','06499920100100009','竹篁','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021393071fa11edb0a7c018504fa798','06135650100104008','抱石莲','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021395171fa11edb0a7c018504fa798','06154120600204003','红茴香','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021397571fa11edb0a7c018504fa798','06172310300303007','白茄根','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021399771fa11edb0a7c018504fa798','06156310400103004','土圞儿','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602139dd71fa11edb0a7c018504fa798','06156350500404007','省头草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602139fd71fa11edb0a7c018504fa798','06172250501704008','土藿香','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213a2171fa11edb0a7c018504fa798','06156450100104007','酢浆草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213a4471fa11edb0a7c018504fa798','06174020400105002','蒲种壳','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213a6871fa11edb0a7c018504fa798','06172240200400004','白苏子','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213a8871fa11edb0a7c018504fa798','06191440200200009','棕榈子','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213aaa71fa11edb0a7c018504fa798','06158540200100004','枸骨子','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213acb71fa11edb0a7c018504fa798','06220130200104001','坎炁','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213aec71fa11edb0a7c018504fa798','06174450102004004','杏香兔耳风','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213b0e71fa11edb0a7c018504fa798','06174410500403007','於术','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213b2f71fa11edb0a7c018504fa798','06174410500503219','麸炒於术','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213b5371fa11edb0a7c018504fa798','06156310100903003','苦甘草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213b7471fa11edb0a7c018504fa798','06173510300303000','栀子根','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213b9571fa11edb0a7c018504fa798','06154110100303009','红木香','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213bb771fa11edb0a7c018504fa798','06191610600503707','姜半夏(浙)','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213bda71fa11edb0a7c018504fa798','06153710400903698','制草乌（浙）','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213c0671fa11edb0a7c018504fa798','06192910400404112','炒浙麦冬','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213c2971fa11edb0a7c018504fa798','06156390600100867','黑豆黄卷','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213c4a71fa11edb0a7c018504fa798','06156350100704002','鸡眼草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213c6d71fa11edb0a7c018504fa798','06174450102104001','天名精','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213c8e71fa11edb0a7c018504fa798','06160020700206000','浙木芙蓉叶','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213caf71fa11edb0a7c018504fa798','06172120700706007','浙紫珠叶','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213cd071fa11edb0a7c018504fa798','06150320100204006','浙海风藤','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213cf271fa11edb0a7c018504fa798','06225110200204005','乌梢蛇（浙）','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213d1671fa11edb0a7c018504fa798','06225110200204319','酒乌梢蛇（浙）','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213d3a71fa11edb0a7c018504fa798','06174040900100000','南瓜蒂','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213d5d71fa11edb0a7c018504fa798','06399910100299002','咸秋石','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213d7e71fa11edb0a7c018504fa798','06164340200500009','芫荽子','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213d9f71fa11edb0a7c018504fa798','06799910100107990','黛蛤粉','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213dc071fa11edb0a7c018504fa798','06192910300303003','白河车','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213de471fa11edb0a7c018504fa798','06191210300104008','薏苡根','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213e0571fa11edb0a7c018504fa798','06135610500204005','青石蚕','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213e2771fa11edb0a7c018504fa798','06333310100107002','硇砂','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213e4871fa11edb0a7c018504fa798','06192930200100007','贝母花','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213e6971fa11edb0a7c018504fa798','06164220600304004','八角金盘','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213e8c71fa11edb0a7c018504fa798','06153910300103000','八角莲','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213eb171fa11edb0a7c018504fa798','06193950100504001','斑叶兰','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213ed371fa11edb0a7c018504fa798','06153710100403009','大火草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213ef471fa11edb0a7c018504fa798','06174450102204008','狗舌草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213f1471fa11edb0a7c018504fa798','06172321200104007','枸杞叶','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213f3571fa11edb0a7c018504fa798','06173550500204004','过山龙','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213f5671fa11edb0a7c018504fa798','06400220100300000','槐耳','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213f7771fa11edb0a7c018504fa798','06155810400105004','檵木','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213f9971fa11edb0a7c018504fa798','06153750100104001','金剪刀','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213fba71fa11edb0a7c018504fa798','06326610100400005','料姜石','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213fdd71fa11edb0a7c018504fa798','06172510100102002','凌霄根','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60213ffd71fa11edb0a7c018504fa798','06172250100804000','猫须草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021401e71fa11edb0a7c018504fa798','06159710100204006','爬山虎','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021404071fa11edb0a7c018504fa798','06174450102304005','肾炎草','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021406071fa11edb0a7c018504fa798','06156121200106009','石楠藤','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021408171fa11edb0a7c018504fa798','06157720700203004','柿子叶','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602140a271fa11edb0a7c018504fa798','06193910600305003','手掌参','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602140c371fa11edb0a7c018504fa798','06158621200104015','卫矛','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602140f071fa11edb0a7c018504fa798','06163120500104005','喜树皮','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021411171fa11edb0a7c018504fa798','06221220100207007','象牙屑','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021413371fa11edb0a7c018504fa798','06191440100100003','枣槟榔','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021415471fa11edb0a7c018504fa798','06150920599904007','榨树皮','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021417371fa11edb0a7c018504fa798','062109101001002101','蜜麸僵蚕选货','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021419871fa11edb0a7c018504fa798','062109101001002102','蜜麸僵蚕统货','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602141ba71fa11edb0a7c018504fa798','06154010400303009','白药脂','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602141da71fa11edb0a7c018504fa798','06210910100100210','炒僵蚕','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602141fa71fa11edb0a7c018504fa798','06157120600106212','麸炒椿皮','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021421d71fa11edb0a7c018504fa798','06157040400406216','麸炒青皮','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021424071fa11edb0a7c018504fa798','06156130299900009','白残花','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021426271fa11edb0a7c018504fa798','06192990299907000','龙血竭','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021428271fa11edb0a7c018504fa798','06157340200103001','川楝子片','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602142a371fa11edb0a7c018504fa798','06132750100304002','凤尾草（凤尾蕨科）','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602142c671fa11edb0a7c018504fa798','06154950100104004','荠菜','1','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602142e871fa11edb0a7c018504fa798','06191240200209008','淮小麦配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021430b71fa11edb0a7c018504fa798','06152310300109414','大黄炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021432d71fa11edb0a7c018504fa798','06153710100109000','白头翁配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021434f71fa11edb0a7c018504fa798','06153710400309711','淡附片配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021437271fa11edb0a7c018504fa798','06154130300109003','辛夷配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021439671fa11edb0a7c018504fa798','06156140200109124','焦山楂配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602143b871fa11edb0a7c018504fa798','06156340200209330','盐补骨脂配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602143da71fa11edb0a7c018504fa798','06156340600509115','炒决明子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602143fc71fa11edb0a7c018504fa798','06157040200309373','制吴茱萸配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021441e71fa11edb0a7c018504fa798','06157040600109337','盐橘核配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021444071fa11edb0a7c018504fa798','06160410100209006','藤梨根配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021446071fa11edb0a7c018504fa798','06160890800109002','冰片配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021448471fa11edb0a7c018504fa798','06164310100309316','酒当归配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602144a771fa11edb0a7c018504fa798','06164340200109332','盐小茴香配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602144c771fa11edb0a7c018504fa798','06172140200109115','炒蔓荆子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602144e871fa11edb0a7c018504fa798','06172150500109005','马鞭草配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021450b71fa11edb0a7c018504fa798','06172210100109314','酒黄芩配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021452b71fa11edb0a7c018504fa798','06172210100109413','黄芩炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021454d71fa11edb0a7c018504fa798','06172350500109009','龙葵配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021456f71fa11edb0a7c018504fa798','06172821100109312','酒苁蓉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021459271fa11edb0a7c018504fa798','06173150500109004','穿心莲配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602145b271fa11edb0a7c018504fa798','06174410100509000','漏芦配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602145d471fa11edb0a7c018504fa798','06174420700109944','醋艾炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602145f871fa11edb0a7c018504fa798','06191290800209120','焦麦芽配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021461b71fa11edb0a7c018504fa798','06191610600209937','竹沥半夏配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021463b71fa11edb0a7c018504fa798','06192910500609615','酒黄精配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021465d71fa11edb0a7c018504fa798','06193910600209004','白及配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021467e71fa11edb0a7c018504fa798','06199990800109872','青黛粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602146a071fa11edb0a7c018504fa798','06199990800209879','建曲配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602146c071fa11edb0a7c018504fa798','06199990800309128','焦六神曲配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602146e271fa11edb0a7c018504fa798','06206120300209515','煅石决明配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021470371fa11edb0a7c018504fa798','06210640900109112','炒蜂房配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602147b171fa11edb0a7c018504fa798','06220140100112006','紫河车粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602147d671fa11edb0a7c018504fa798','06221040100109008','五灵脂配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602147f971fa11edb0a7c018504fa798','06221320100109001','刺猬皮配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021485071fa11edb0a7c018504fa798','06223210100109003','干蟾配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021487371fa11edb0a7c018504fa798','06330110100109524','煅紫石英配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021489471fa11edb0a7c018504fa798','06173450100109009','车前草配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602148b571fa11edb0a7c018504fa798','06174040600409007','冬瓜子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602148d671fa11edb0a7c018504fa798','06172350100209000','白毛藤配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602148f771fa11edb0a7c018504fa798','06193210500309003','粉萆薢配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021491771fa11edb0a7c018504fa798','06221040100109114','炒五灵脂配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021493871fa11edb0a7c018504fa798','06190130500109416','蒲黄炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021495871fa11edb0a7c018504fa798','06193510500209229','炮姜配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021497a71fa11edb0a7c018504fa798','06173540200109112','炒栀子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021499a71fa11edb0a7c018504fa798','06221040100109329','醋五灵脂配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602149bb71fa11edb0a7c018504fa798','06225620300109969','鳖甲胶配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602149dc71fa11edb0a7c018504fa798','06199990800309111','炒六神曲配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602149fc71fa11edb0a7c018504fa798','06211310100209118','炒虻虫配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214a1b71fa11edb0a7c018504fa798','06171740600109114','炒牵牛子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214a3a71fa11edb0a7c018504fa798','06157710100109327','醋京大戟配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214a5e71fa11edb0a7c018504fa798','06162330300109327','醋芫花配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214a7f71fa11edb0a7c018504fa798','06156340200412006','大皂角粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214aa071fa11edb0a7c018504fa798','06155920500109410','杜仲炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214ac071fa11edb0a7c018504fa798','06131750500209007','海金沙藤配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214ae071fa11edb0a7c018504fa798','06326610100309001','寒水石配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214b0271fa11edb0a7c018504fa798','06153220800109007','荷梗配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214b2271fa11edb0a7c018504fa798','06174410500209128','焦白术配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214b4171fa11edb0a7c018504fa798','06152310300109315','酒大黄配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214f4071fa11edb0a7c018504fa798','06152040200109350','蜜马兜铃配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214f6e71fa11edb0a7c018504fa798','06157510100109354','蜜远志配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214f9271fa11edb0a7c018504fa798','06134510500109007','绵马贯众配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214fb471fa11edb0a7c018504fa798','06174040600509004','南瓜子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214fd671fa11edb0a7c018504fa798','06191610600209739','清半夏配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('60214ff671fa11edb0a7c018504fa798','06204110100109001','水蛭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021501771fa11edb0a7c018504fa798','06158520700109004','四季青配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021503771fa11edb0a7c018504fa798','06156140600409008','甜杏仁配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021505771fa11edb0a7c018504fa798','06225110200109003','乌梢蛇配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021507771fa11edb0a7c018504fa798','06172250501609006','香茶菜配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021509871fa11edb0a7c018504fa798','06191490200112000','血竭粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602150b871fa11edb0a7c018504fa798','06154710600109004','延胡索配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602150da71fa11edb0a7c018504fa798','06173510100109635','盐巴戟天配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602150fc71fa11edb0a7c018504fa798','06152310100309007','羊蹄配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021511c71fa11edb0a7c018504fa798','06157040100209001','枳壳配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021513d71fa11edb0a7c018504fa798','06156340200112005','猪牙皂粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021515f71fa11edb0a7c018504fa798','06174040600409113','炒冬瓜子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021517e71fa11edb0a7c018504fa798','06151240200309113','炒火麻仁配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021519f71fa11edb0a7c018504fa798','06156140600109113','炒苦杏仁配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602151be71fa11edb0a7c018504fa798','06193510400109322','醋郁金配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602151e171fa11edb0a7c018504fa798','06174450500109417','大蓟炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021520171fa11edb0a7c018504fa798','06164310100309415','当归炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021522171fa11edb0a7c018504fa798','06153240500109213','麸炒芡实配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021524271fa11edb0a7c018504fa798','06154440500109803','麸煨肉豆蔻配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021526471fa11edb0a7c018504fa798','06152510100109311','酒川牛膝配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021528371fa11edb0a7c018504fa798','06174430100109352','蜜旋覆花配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602152a471fa11edb0a7c018504fa798','06220420300109229','炮山甲配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602152c471fa11edb0a7c018504fa798','06157020500209337','盐黄柏配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602152e471fa11edb0a7c018504fa798','06171240200109335','盐女贞子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021530571fa11edb0a7c018504fa798','06210440100109338','盐桑螵蛸配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021532571fa11edb0a7c018504fa798','06153920700209363','炙淫羊藿配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602153ac71fa11edb0a7c018504fa798','06162320600109004','祖师麻配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602153d371fa11edb0a7c018504fa798','06191290900112009','天竺黄粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602153f371fa11edb0a7c018504fa798','06163340200309115','炒诃子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021541371fa11edb0a7c018504fa798','06153710500309116','炒黄连配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021543271fa11edb0a7c018504fa798','06154140200209703','蒸五味子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021545471fa11edb0a7c018504fa798','06192910500209600','制玉竹配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021547671fa11edb0a7c018504fa798','06173550100609001','黄毛耳草配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021549671fa11edb0a7c018504fa798','06156310100809114','炒葛根配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602154d371fa11edb0a7c018504fa798','06174450100609003','佛耳草配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602154f671fa11edb0a7c018504fa798','06338110100409008','琥珀配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021551671fa11edb0a7c018504fa798','06172410400109940','熟地黄炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021553871fa11edb0a7c018504fa798','06174410100309211','麸木香配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021555771fa11edb0a7c018504fa798','06154120600109001','紫金皮配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021557971fa11edb0a7c018504fa798','06151230400309006','薜荔果配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021559971fa11edb0a7c018504fa798','06505210100109004','老君须配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602155ba71fa11edb0a7c018504fa798','06156340700409001','生扁豆衣配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602155db71fa11edb0a7c018504fa798','06191240600209004','秫米配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602155fd71fa11edb0a7c018504fa798','06164310100509112','炒防风配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021561e71fa11edb0a7c018504fa798','06157020500209115','炒黄柏配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021563e71fa11edb0a7c018504fa798','06156310100609110','炒黄芪配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021565f71fa11edb0a7c018504fa798','06173150500409005','小青草配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021568171fa11edb0a7c018504fa798','06154520200109355','蜜桂枝配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602156a171fa11edb0a7c018504fa798','06172240200209355','蜜紫苏子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602156c271fa11edb0a7c018504fa798','06153710500109419','升麻炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602156e371fa11edb0a7c018504fa798','06154910100209000','地骷髅配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021570471fa11edb0a7c018504fa798','06153720600109110','炒丹皮配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021572471fa11edb0a7c018504fa798','06190130500109119','炒蒲黄配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021576071fa11edb0a7c018504fa798','06154940600309115','炒葶苈子配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021578471fa11edb0a7c018504fa798','06199990801309004','六一粉配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602157a671fa11edb0a7c018504fa798','06338110100209516','煅龙齿配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602157c571fa11edb0a7c018504fa798','06192910500309119','炒知母配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602157e671fa11edb0a7c018504fa798','06155840200109117','炒路路通配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021580871fa11edb0a7c018504fa798','06157020500209313','酒黄柏配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021582a71fa11edb0a7c018504fa798','06220420300109328','醋山甲配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021584a71fa11edb0a7c018504fa798','06156310100809213','麸炒葛根配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021586a71fa11edb0a7c018504fa798','06134310500309410','贯众炭配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('6021588b71fa11edb0a7c018504fa798','06153710400309001','附片配方颗粒','2','2');
INSERT INTO `b_standard_mat` (`s_id`, `s_mat_code`, `s_mat_name`, `s_mat_class`, `s_mat_type`) VALUES('602158ac71fa11edb0a7c018504fa798','06153710400309223','炮附子配方颗粒','2','2');


UPDATE `b_standard_mat` SET `s_mat_code` = '06157040400406353'
WHERE  `s_mat_code` = '06157040400406216';
UPDATE `b_standard_mat` SET `s_mat_code` = '06157120600106359'
WHERE  `s_mat_code` = '06157120600106212';

UPDATE `b_standard_mat` SET `s_mat_code` = '06210910100100357'
WHERE  `s_mat_code` = '06210910100100210';

UPDATE `b_standard_mat` SET `s_mat_code` = '06156310100602357'
WHERE  `s_mat_code` = '06156310100603354';

UPDATE `b_standard_mat` SET `s_mat_code` = '06174421000104004'
WHERE  `s_mat_code` = '06174421000604009';




INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'KIOSK_CLOSE', '通过插件关闭标签', 6 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM `t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='PRESCRIPTION_CHECK_MODE'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='KIOSK_CLOSE' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='通过插件关闭标签'
    );
INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'KIOSK_PRINT_CLOSE', '打印处方后并通过插件关闭标签', 7 FROM (
                                                               SELECT tsp.PAR_ID
                                                               FROM `t_sys_param` AS tsp
                                                               WHERE tsp.PAR_CODE='PRESCRIPTION_CHECK_MODE'
                                                           ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='KIOSK_PRINT_CLOSE' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='打印处方后并通过插件关闭标签'
    );

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'SKIP_/audit/doctor', '跳转至历史处方', 8 FROM (
                                                                                 SELECT tsp.PAR_ID
                                                                                 FROM `t_sys_param` AS tsp
                                                                                 WHERE tsp.PAR_CODE='PRESCRIPTION_CHECK_MODE'
                                                                             ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='SKIP_/audit/doctor' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name=' 跳转至历史处方'
    );

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'SKIP_/audit/doctor_CLEAR', '跳转至历史处方并清空处方数据', 8 FROM (
                                                                    SELECT tsp.PAR_ID
                                                                    FROM `t_sys_param` AS tsp
                                                                    WHERE tsp.PAR_CODE='PRESCRIPTION_CHECK_MODE'
                                                                ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='SKIP_/audit/doctor_CLEAR' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name=' 跳转至历史处方并清空处方数据'
    );

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, 'WinForm_CLOSE', '调用WinForm关闭', 9 FROM (
                                                                                        SELECT tsp.PAR_ID
                                                                                        FROM `t_sys_param` AS tsp
                                                                                        WHERE tsp.PAR_CODE='PRESCRIPTION_CHECK_MODE'
                                                                                    ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='WinForm_CLOSE' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name=' 调用WinForm关闭'
    );


