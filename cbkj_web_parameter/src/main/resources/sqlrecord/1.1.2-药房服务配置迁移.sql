
#配送-门诊
INSERT INTO  cbkj_web_parameter.t_display_express (
    set_id,
    display_id,#机构药房配置ID
    outpatient_or_hospitalization,#门诊，住院（1门诊，2住院）
    SHOW_EXPRESS,#配送是否开启（0是，1否）
    IS_EXPRESS,#是否默认配送（0是，1否）
    is_use_express,#是否启用配送地址（0是，1否）
    express_set,#配送费用配置（0默认，1配置)
    `status`,#状态
    create_date,
    create_user
)
SELECT
    REPLACE(UUID(), '-', ''),
    DISPLAY_ID,
    '1',
    (CASE
        SHOW_EXPRESS_MZ
         WHEN '0' THEN '1'
         WHEN '1' THEN '0'
         ELSE '1'
        END
        ) SHOW_EXPRESS_MZ,
    (CASE
        IS_EXPRESS_MZ
         WHEN '0' THEN '1'
         WHEN '1' THEN '0'
         ELSE '1'
        END
        ) IS_EXPRESS_MZ,
    (SELECT (CASE a.`PAR_VALUES` WHEN '1' THEN '1' ELSE '0' END) AS is_use_express FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_CONSIGNEE_DISABLED') is_use_express,
    '0',
    '0',
    NOW(),
    'admin'
FROM cbkj_web_api.`t_display`;
#配送-住院
INSERT INTO  cbkj_web_parameter.t_display_express (
    set_id,
    display_id,#机构药房配置ID
    outpatient_or_hospitalization,#门诊，住院（1门诊，2住院）
    SHOW_EXPRESS,#配送是否开启（0是，1否）
    IS_EXPRESS,#是否默认配送（0是，1否）
    is_use_express,#是否启用配送地址（0是，1否）
    express_set,#配送费用配置（0默认，1配置)
    `status`,#状态
    create_date,
    create_user
)
SELECT
    REPLACE(UUID(), '-', ''),
    DISPLAY_ID,
    '2',#住院
        (CASE
            SHOW_EXPRESS_ZY
             WHEN '0' THEN '1'
             WHEN '1' THEN '0'
             ELSE '1'
            END
        ) SHOW_EXPRESS_ZY,
    (CASE
        IS_EXPRESS_ZY
         WHEN '0' THEN '1'
         WHEN '1' THEN '0'
         ELSE '1'
        END
        ) IS_EXPRESS_ZY,
    (SELECT (CASE a.`PAR_VALUES` WHEN '1' THEN '1' ELSE '0' END) AS is_use_express FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_CONSIGNEE_DISABLED') is_use_express,
    '0',
    '0',
    NOW(),
    'admin'
FROM cbkj_web_api.`t_display`;


#配送-门诊-费用配置
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '1',
    '3',
    '1',
    '00000000',
    '00000000',
    '不限制',
    '0',
    b.`FEE_EXPRESS_MZ`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_express` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='1' AND a.display_id=b.`DISPLAY_ID`;


#配送-住院-费用配置
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '2',
    '3',
    '1',
    '00000000',
    '00000000',
    '不限制',
    '0',
    b.`FEE_EXPRESS_ZY`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_express` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='2' AND a.display_id=b.`DISPLAY_ID`;





#------------------------制膏------------------------------------------------------------------------------------------------------


#费用配置-门诊-制膏
INSERT INTO  cbkj_web_parameter.t_display_production(
    set_id,
    display_id,
    outpatient_or_hospitalization,#门诊，住院(1门诊，2住院)
    SHOW_PRODUCTION,#制膏配置是否开启(0是，1否)
    Is_show_production,#医保患者是否显示膏方(0是，1否)
    Is_show_production_type,#是否显示膏方类型(0是，1否)
    production_num,#膏方控制开方贴数
    usually_production_set,#通用制膏费用配置（0默认，1配置）
    formula_production_set,#配方制膏费用配置（0默认，1配置）
    `status`,#状态
    create_date,
        create_user
)
SELECT
    REPLACE(UUID(), '-', ''),
    DISPLAY_ID,
    '1',
    (CASE
        SHOW_PRODUCTION_MZ
         WHEN '0' THEN '1'
         WHEN '1' THEN '0'
         ELSE '1'
        END
        ) SHOW_PRODUCTION_MZ,#门诊是否显示膏方（0否1是）
    (SELECT (CASE a.`PAR_VALUES` WHEN '0' THEN '1' ELSE '0' END) AS PAR_VALUES FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_YB_SHOW_UNGUENT') AS Is_show_production, ##医保患者是否显示膏方(0是，1否)
    (SELECT (CASE a.`PAR_VALUES` WHEN '0' THEN '1' ELSE '0' END) AS PAR_VALUES FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_INTERNAL_PRODUCTION_TYPE_SHOW') AS Is_show_production_type, #是否显示膏方类型(0是，1否)
    (SELECT a.`PAR_VALUES`  FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_INTERNAL_PRODUCTION_CONTROL_NUM') AS production_num,#膏方控制开方贴数
    '0',
        '0',
    '0',
    NOW(),
    'admin'
FROM cbkj_web_api.`t_display`;

#费用配置-住院-制膏
INSERT INTO  cbkj_web_parameter.t_display_production(
    set_id,
    display_id,
    outpatient_or_hospitalization,#门诊，住院(1门诊，2住院)
    SHOW_PRODUCTION,#制膏配置是否开启(0是，1否)
    Is_show_production,#医保患者是否显示膏方(0是，1否)
    Is_show_production_type,#是否显示膏方类型(0是，1否)
    production_num,#膏方控制开方贴数
    usually_production_set,#通用制膏费用配置（0默认，1配置）
    formula_production_set,#配方制膏费用配置（0默认，1配置）
    `status`,#状态
    create_date,
        create_user
)
SELECT
    REPLACE(UUID(), '-', ''),
    DISPLAY_ID,
    '2',
    (CASE
        SHOW_PRODUCTION_MZ
         WHEN '0' THEN '1'
         WHEN '1' THEN '0'
         ELSE '1'
        END
        ) SHOW_PRODUCTION_MZ,#门诊是否显示膏方（0否1是）
    (SELECT (CASE a.`PAR_VALUES` WHEN '0' THEN '1' ELSE '0' END) AS PAR_VALUES FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_YB_SHOW_UNGUENT') AS Is_show_production, ##医保患者是否显示膏方(0是，1否)
    (SELECT (CASE a.`PAR_VALUES` WHEN '0' THEN '1' ELSE '0' END) AS PAR_VALUES FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_INTERNAL_PRODUCTION_TYPE_SHOW') AS Is_show_production_type, #是否显示膏方类型(0是，1否)
    (SELECT a.`PAR_VALUES`  FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_INTERNAL_PRODUCTION_CONTROL_NUM') AS production_num,#膏方控制开方贴数
    '0',
        '0',
    '0',
    NOW(),
    'admin'
FROM cbkj_web_api.`t_display`;

#膏方-费用配置-门诊-通用
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '1',
    '2',
    '1',#通用
    '00000000',
        '00000000',
    '通用制膏',
    '0',
    b.`FEE_PRODUCTION_MZ`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_production` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='1' AND a.display_id=b.`DISPLAY_ID`;

#膏方-费用配置-门诊-配方
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '1',
    '2',
    '2',#配方
    '00000000',
        '00000000',
    '配方制膏',
    '0',
    b.`FEE_PRODUCTION_MZ_FORMULA`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_production` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='1' AND a.display_id=b.`DISPLAY_ID`;



#膏方-费用配置-住院-通用
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '2',#住院
    '2',
        '1',#通用
    '00000000',
        '00000000',
    '通用制膏',
    '0',
    b.`FEE_PRODUCTION_ZY`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_production` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='2' AND a.display_id=b.`DISPLAY_ID`;


#膏方-费用配置-住院-配方
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '2',#住院
    '2',
        '2',#配方
    '00000000',
        '00000000',
    '配方制膏',
    '0',
    b.`FEE_PRODUCTION_ZY_FORMULA`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_production` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='2' AND a.display_id=b.`DISPLAY_ID`;



#--------------------------------------------------代煎---------------------------------------------------------------------------------------



#代煎-门诊
INSERT INTO  cbkj_web_parameter.t_display_decoction(
    set_id,
    display_id,
    outpatient_or_hospitalization,#门诊，住院(1门诊，2住院)
    SHOW_DECOCTION,#代煎配置是否开启(0是，1否)
    IS_DECOCTION,#是否默认代煎（0是，1否）
    Is_decoction_must,#代煎是否禁用（0是，1否）
    Is_decoction_num,#是否显示代煎贴数（0是，1否）
    LEAST_DECOCTION,#可代煎最低贴数(默认0)
    usually_decoction_set,#通用代煎费用配置（0默认，1配置）
    Formula_decoction_set,#配方代煎费用配置（0默认，1配置）
    `status`,#状态
    create_date,
    create_user
)
SELECT REPLACE(UUID(), '-', ''),
       DISPLAY_ID,
       '1',
       (CASE
           SHOW_DECOCTION_MZ
            WHEN '0' THEN '1'
            WHEN '1' THEN '0'
            ELSE '1'
           END
           ) SHOW_DECOCTION_MZ,#代煎配置是否开启(0是，1否)
    (CASE
           IS_DECOCTION_MZ
            WHEN '0' THEN '1'
            WHEN '1' THEN '0'
            ELSE '1'
           END
           ) IS_DECOCTION_MZ,#是否默认代煎（0是，1否
       '0',#代煎是否禁用
    (SELECT (CASE a.`PAR_VALUES` WHEN '0' THEN '1' ELSE '0' END) AS PAR_VALUES FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_DECOCT_SHOW') AS Is_decoction_num,
       LEAST_DECOCTION,#可代煎的最低贴数
    '0','0','0',NOW(),'admin'
FROM cbkj_web_api.`t_display`;


#代煎-住院
INSERT INTO  cbkj_web_parameter.t_display_decoction(
    set_id,
    display_id,
    outpatient_or_hospitalization,#门诊，住院(1门诊，2住院)
    SHOW_DECOCTION,#代煎配置是否开启(0是，1否)
    IS_DECOCTION,#是否默认代煎（0是，1否）
    Is_decoction_must,#代煎是否禁用（0是，1否）
    Is_decoction_num,#是否显示代煎贴数（0是，1否）
    LEAST_DECOCTION,#可代煎最低贴数(默认0)
    usually_decoction_set,#通用代煎费用配置（0默认，1配置）
    Formula_decoction_set,#配方代煎费用配置（0默认，1配置）
    `status`,#状态
    create_date,
    create_user
)
SELECT REPLACE(UUID(), '-', ''),
       DISPLAY_ID,
       '2',
       (CASE
           SHOW_DECOCTION_MZ
            WHEN '0' THEN '1'
            WHEN '1' THEN '0'
            ELSE '1'
           END
           ) SHOW_DECOCTION_MZ,#代煎配置是否开启(0是，1否)
    (CASE
           IS_DECOCTION_MZ
            WHEN '0' THEN '1'
            WHEN '1' THEN '0'
            ELSE '1'
           END
           ) IS_DECOCTION_MZ,#是否默认代煎（0是，1否
       '0',#代煎是否禁用
    (SELECT (CASE a.`PAR_VALUES` WHEN '0' THEN '1' ELSE '0' END) AS PAR_VALUES FROM cbkj_web_parameter.t_sys_param AS a WHERE a.`APP_ID`='000000' AND a.`INS_CODE`='000000' AND a.`DEPT_ID`='000000' AND a.`PAR_CODE`='PRESCRIPTION_DECOCT_SHOW') AS Is_decoction_num,
       LEAST_DECOCTION,#可代煎的最低贴数
    '0','0','0',NOW(),'admin'
FROM cbkj_web_api.`t_display`;


#代煎-费用配置-门诊-通用
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '1',
    '1',
    '1',#通用
    '00000000',
        '00000000',
    '通用煎药',
    '0',
    b.`FEE_DECOCTION_MZ`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_decoction` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='1' AND a.display_id=b.`DISPLAY_ID`;



#代煎-费用配置-门诊-配送
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '1',
    '1',
    '2',#配方
    '00000000',
        '00000000',
    '配方煎药',
    '0',
    b.`FEE_DECOCTION_MZ_FORMULA`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_decoction` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='1' AND a.display_id=b.`DISPLAY_ID`;




#代煎-费用配置-住院-通用
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '2',
    '1',
    '1',#通用
    '00000000',
        '00000000',
    '通用煎药',
    '0',
    b.`FEE_DECOCTION_ZY`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_decoction` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='2' AND a.display_id=b.`DISPLAY_ID`;



#代煎-费用配置-住院-配方
INSERT INTO  cbkj_web_parameter.t_display_money_setting(
    id,
    set_id,
    outpatient_or_hospitalization,#门诊住院（1门诊，2住院）
    decoction_or_production_express,#代煎，制膏，配送（1代煎，2制膏，3配送）
    currency_or_formula,#通用，配方（1通用，2配方）
    dic_id,#字典ID,
    dic_code,#字典代码,
    dic_name,
    is_show_setting,#收费项目配置是否显示(0显示,1不显示)
    price,#价格
    create_date,
    create_user,
    `status`,#状态
    sort#排序
)
SELECT
    REPLACE(UUID(), '-', ''),
    a.`set_id`,
    '2',
    '1',
    '2',#配方
    '00000000',
        '00000000',
    '配方煎药',
    '0',
    b.`FEE_DECOCTION_ZY_FORMULA`,
    NOW(),
    'admin',
    '0',
    b.`SORT_NUM`
FROM cbkj_web_parameter.`t_display_decoction` AS a JOIN cbkj_web_api.`t_display`  AS b ON a.outpatient_or_hospitalization='2' AND a.display_id=b.`DISPLAY_ID`;
