CREATE TABLE `t_display_decoction` (
                                       `set_id` varchar(32) NOT NULL COMMENT '主键',
                                       `display_id` varchar(32) NOT NULL COMMENT '机构药房配置ID',
                                       `outpatient_or_hospitalization` varchar(1) NOT NULL COMMENT '门诊,住院（1门诊，2住院）',
                                       `SHOW_DECOCTION` varchar(1) NOT NULL COMMENT '代煎配置是否开启（0是，1否）',
                                       `IS_DECOCTION` varchar(1) NOT NULL COMMENT '是否默认代煎（0是，1否）',
                                       `Is_decoction_must` varchar(1) NOT NULL COMMENT '代煎是否禁用（0是，1否）',
                                       `Is_decoction_num` varchar(1) NOT NULL COMMENT '是否显示代煎贴数（0是，1否）',
                                       `LEAST_DECOCTION` int(3) DEFAULT NULL COMMENT '可代煎最低贴数(默认0)',
                                       `usually_decoction_set` varchar(1) NOT NULL COMMENT '通用代煎费用配置（0默认，1配置）',
                                       `Formula_decoction_set` varchar(1) NOT NULL COMMENT '配方代煎费用配置（0默认，1配置）',
                                       `status` varchar(1) NOT NULL COMMENT '是否开启状态',
                                       `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                       `create_user` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                                       PRIMARY KEY (`set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='药房配置代煎信息表';

CREATE TABLE `t_display_express` (
                                     `set_id` varchar(32) NOT NULL COMMENT '主键',
                                     `display_id` varchar(32) NOT NULL COMMENT '机构药房配置ID',
                                     `outpatient_or_hospitalization` varchar(1) NOT NULL COMMENT '门诊，住院（1门诊，2住院）',
                                     `SHOW_EXPRESS` varchar(1) NOT NULL COMMENT '配送是否开启（0是，1否）',
                                     `IS_EXPRESS` varchar(1) NOT NULL COMMENT '是否默认配送（0是，1否）',
                                     `is_use_express` varchar(1) NOT NULL COMMENT '是否启用配送地址（0是，1否）',
                                     `express_set` varchar(1) NOT NULL COMMENT '配送费用配置（0默认，1配置）',
                                     `status` varchar(1) DEFAULT NULL COMMENT '状态',
                                     `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                     `create_user` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                                     PRIMARY KEY (`set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='药房配置配送信息表';

CREATE TABLE `t_display_money_setting` (
                                           `id` varchar(32) NOT NULL,
                                           `set_id` varchar(32) NOT NULL COMMENT '配置ID',
                                           `outpatient_or_hospitalization` varchar(1) NOT NULL COMMENT '门诊住院（1门诊，2住院）',
                                           `decoction_or_production_express` varchar(1) NOT NULL COMMENT '代煎，制膏，配送（1代煎，2制膏，3配送）',
                                           `currency_or_formula` varchar(1) NOT NULL COMMENT '通用，配方（1通用，2配方）',
                                           `dic_id` varchar(32) NOT NULL COMMENT '字典ID',
                                           `dic_code` varchar(32) DEFAULT NULL COMMENT '字典代码',
                                           `dic_name` varchar(64) DEFAULT NULL,
                                           `is_show_setting` varchar(1) NOT NULL COMMENT '收费项目配置是否显示(0显示,1不显示)',
                                           `charge_code` varchar(32) DEFAULT NULL COMMENT 'HIS收费代码',
                                           `charge_name` varchar(32) DEFAULT NULL COMMENT '收费项目名称',
                                           `price` decimal(6,2) DEFAULT NULL COMMENT '价格',
                                           `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                           `create_user` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                                           `status` varchar(1) DEFAULT NULL COMMENT '状态',
                                           `sort` int(3) DEFAULT NULL COMMENT '排序',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='药房费用配置信息表';

CREATE TABLE `t_display_production` (
                                        `set_id` varchar(32) NOT NULL COMMENT '主键',
                                        `display_id` varchar(32) NOT NULL COMMENT '机构药房配置ID',
                                        `outpatient_or_hospitalization` varchar(1) NOT NULL COMMENT '门诊，住院(1门诊，2住院)',
                                        `SHOW_PRODUCTION` varchar(1) NOT NULL COMMENT '制膏配置是否开启(0是，1否)',
                                        `Is_show_production` varchar(1) NOT NULL COMMENT '医保患者是否显示膏方(0是，1否)',
                                        `Is_show_production_type` varchar(1) NOT NULL COMMENT '是否显示膏方类型(0是，1否)',
                                        `production_num` int(3) DEFAULT NULL COMMENT '膏方控制开方贴数',
                                        `usually_production_set` varchar(1) NOT NULL COMMENT '通用制膏费用配置（0默认，1配置）',
                                        `formula_production_set` varchar(1) NOT NULL COMMENT '配方制膏费用配置（0默认，1配置）',
                                        `status` varchar(1) NOT NULL COMMENT '状态',
                                        `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                        `create_user` varchar(32) DEFAULT NULL COMMENT '创建人ID',
                                        PRIMARY KEY (`set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='药房配置制膏信息表';

#调整参数排序
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=1
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='序号';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=2
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='图标';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=3
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='药品名称';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=4
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='规格';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=5
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='剂量';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=6
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='单位';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=7
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='用法';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=8
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='备注';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=9
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='产地';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=10
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='单价';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=11
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='计价总额';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=12
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='库存';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=13
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='外用中药方显示项目' AND  t_sys_param_init_desc.param_init_name='医保';


UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=1
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='序号';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=2
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='图标';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=3
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='药品名称';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=4
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='规格';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=5
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='剂量';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=6
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='单位';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=7
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='用法';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=8
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='备注';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=9
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='产地';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=10
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='单价';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=11
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='计价总额';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=12
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='库存';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.`sort`=13
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='内服中药方显示项目' AND  t_sys_param_init_desc.param_init_name='医保';

UPDATE `t_sys_param_init_desc`,t_sys_param  SET t_sys_param_init_desc.sort=1
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='序号';
UPDATE `t_sys_param_init_desc`,t_sys_param  SET t_sys_param_init_desc.sort=2
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='药品';
UPDATE `t_sys_param_init_desc`, t_sys_param SET t_sys_param_init_desc.sort=3
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='规格';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=4
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='产地';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=5
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'  AND t_sys_param_init_desc.param_init_name='自费';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=6
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'  AND t_sys_param_init_desc.param_init_name='每次用量';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=7
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'  AND t_sys_param_init_desc.param_init_name='单位';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=8
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='用法';
UPDATE `t_sys_param_init_desc` ,t_sys_param  SET t_sys_param_init_desc.sort=9
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='频次';
UPDATE `t_sys_param_init_desc` ,t_sys_param  SET t_sys_param_init_desc.sort=10
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'  AND t_sys_param_init_desc.param_init_name='天数';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=11
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='数量';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=12
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'  AND t_sys_param_init_desc.param_init_name='包装单位';
UPDATE `t_sys_param_init_desc` ,t_sys_param  SET t_sys_param_init_desc.sort=13
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'  AND t_sys_param_init_desc.param_init_name='单价';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=14
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='总金额';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=15
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目' AND t_sys_param_init_desc.param_init_name='库存';
UPDATE `t_sys_param_init_desc`,t_sys_param   SET t_sys_param_init_desc.sort=16
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`PAR_NAME`='中药制剂显示项目'AND t_sys_param_init_desc.param_init_name='备注';


#字典大类
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`)
VALUES('bfbf0a64f36911ecad8d00163f006620','jyfx','煎药方式','0','煎药方式','0',NULL,'1','000000','000000','000000',NULL,'2022-06-15 10:49:11','1','1','0');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`)
VALUES('bfc236d2f36911ecad8d00163f006620','bzfs','包装方式','0','包装方式','0',NULL,'1','000000','000000','000000',NULL,'2022-06-15 10:50:10','1','1','0');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`)
VALUES('bfc596a5f36911ecad8d00163f006620','psfx','配送方式','0','配送方式','0',NULL,'1','000000','000000','000000',NULL,'2022-06-15 10:50:38','1','1','0');

#字典子级
insert into `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `create_date`, `create_user`, `create_user_name`, `status`, `display_id`) values('129d3d59793044fe9f1ea09a710c54bb','2','机器煎药','bfbf0a64f36911ecad8d00163f006620',NULL,'0',NULL,'2','000000','000000','000000','000000','2022-06-24 11:14:58','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
insert into `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `create_date`, `create_user`, `create_user_name`, `status`, `display_id`) values('a97ff0185bf8424e90b035e2a21fd806','1','手工煎药','bfbf0a64f36911ecad8d00163f006620',NULL,'0',NULL,'1','000000','000000','000000','000000','2022-06-24 11:08:33','70810c874405453b99c6c2cf72296fe5','admin','0','000000');

insert into `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `create_date`, `create_user`, `create_user_name`, `status`, `display_id`) values('0abf152f90884b5c8ffd6e39dc078e12','1','陶瓷罐装','bfc236d2f36911ecad8d00163f006620',NULL,'0',NULL,'1','000000','000000','000000','000000','2022-06-24 11:16:03','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
insert into `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `create_date`, `create_user`, `create_user_name`, `status`, `display_id`) values('25fea87b6f364905929b12b3c84c6c81','2','切片膏方','bfc236d2f36911ecad8d00163f006620',NULL,'0',NULL,'2','000000','000000','000000','000000','2022-06-24 11:16:12','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
insert into `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `create_date`, `create_user`, `create_user_name`, `status`, `display_id`) values('886e5aee553e495196a3d646b01f06ca','3','小包装','bfc236d2f36911ecad8d00163f006620',NULL,'0',NULL,'3','000000','000000','000000','000000','2022-06-24 11:16:20','70810c874405453b99c6c2cf72296fe5','admin','0','000000');



#修改字典数据类型
UPDATE t_dic_base SET sort=REPLACE(sort,' ','');
ALTER TABLE t_dic_base MODIFY sort INT(3) COMMENT '排序';
#禁用部分参数
UPDATE t_sys_param SET STATUS='1' WHERE par_name='膏方控制开方贴数';
UPDATE t_sys_param SET STATUS='1' WHERE par_name='医保患者是否显示膏方';
UPDATE t_sys_param SET STATUS='1' WHERE par_name='开方界面是否显示膏方类型（糖）';
UPDATE t_sys_param SET STATUS='1' WHERE par_name='开方界面是否显示代煎贴数';
UPDATE t_sys_param SET STATUS='1' WHERE par_name='智能开方-配送地址禁用（无需填写）';
