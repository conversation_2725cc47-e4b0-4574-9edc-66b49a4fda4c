#修改参数位置
UPDATE t_sys_param SET menu_id='1009',par_number='B242',SORT=37
WHERE par_number='B108';

ALTER TABLE `t_interface_his`
    CHANGE `his_url_content` `his_url_content` varchar(1)  COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口内容：1.药品目录2.药品库存3.人员字典4.科室字典' after `his_name` ,
    CHANGE `his_url_type` `his_url_type` varchar(1)  COLLATE utf8mb4_general_ci NOT NULL COMMENT '1 MYSQL 2 ORACLE 3 SqlServer 4 http 5 webservice' after `his_url_content` ,
    CHANGE `his_url` `his_url` varchar(200)  COLLATE utf8mb4_general_ci  NULL COMMENT '数据库连接地址/his接口地址/webService URL' after `his_url_type` ,
    ADD COLUMN `jdbc_ip` varchar(16)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库连接IP' after `his_url` ,
    ADD COLUMN `jdbc_port` varchar(10)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库端口' after `jdbc_ip` ,
    ADD COLUMN `jdbc_database_name` varchar(32)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库名' after `jdbc_port` ,
    ADD COLUMN `jdbc_username` varchar(128)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库用户名' after `jdbc_database_name` ,
    ADD COLUMN `jdbc_password` varchar(256)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库密码' after `jdbc_username` ,
    ADD COLUMN `jdbc_table` varchar(128)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库表名/视图名' after `jdbc_password` ,
    ADD COLUMN `jdbc_sqlwhere` varchar(256)  COLLATE utf8mb4_general_ci NULL COMMENT '数据库查询条件' after `jdbc_table` ,
    CHANGE `request_method_type` `request_method_type` varchar(10)  COLLATE utf8mb4_general_ci NULL COMMENT 'HTTP请求方式：GET、POST' after `jdbc_sqlwhere` ,
    ADD COLUMN `request_params` varchar(1024)  COLLATE utf8mb4_general_ci NULL COMMENT 'HTTP请求固定参数（JSON）' after `request_method_type` ,
    ADD COLUMN `webservice_params` varchar(1024)  COLLATE utf8mb4_general_ci NULL COMMENT 'webService入参（\">\",\"<\" 需转换成\"&gt;\",\"&lt;\"）' after `request_params` ,
    ADD COLUMN `webservice_namespace` varchar(256)  COLLATE utf8mb4_general_ci NULL COMMENT 'webService命名空间' after `webservice_params` ,
    ADD COLUMN `webservice_method` varchar(256)  COLLATE utf8mb4_general_ci NULL COMMENT 'webService方法名' after `webservice_namespace` ,
    ADD COLUMN `webservice_resultnode` varchar(256)  COLLATE utf8mb4_general_ci NULL COMMENT 'webService出参的节点' after `webservice_method` ,
    ADD COLUMN `webservice_contenttype` varchar(256)  COLLATE utf8mb4_general_ci NULL COMMENT 'webService参数类型' after `webservice_resultnode` ,
    CHANGE `status` `status` varchar(1)  COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态（0、有效  1、删除） 默认0' after `webservice_contenttype` ,
    CHANGE `create_date` `create_date` datetime   NULL COMMENT '创建时间' after `status` ,
    CHANGE `create_user_name` `create_user_name` varchar(50)  COLLATE utf8mb4_general_ci NULL COMMENT '创建人' after `create_user` ;


CREATE TABLE `t_interface_async_user` (
                                          `his_id` varchar(32) NOT NULL COMMENT 'HISID',
                                          `user_name` varchar(32) NOT NULL COMMENT '登录名',
                                          `name_zh` varchar(32) NOT NULL COMMENT '姓名',
                                          `sex` varchar(32) NOT NULL COMMENT '性别',
                                          `phone` varchar(32) NOT NULL COMMENT '手机号',
                                          `certificate` varchar(32) NOT NULL COMMENT '身份证号',
                                          `app_id` varchar(32) NOT NULL COMMENT 'APPID',
                                          `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
                                          `dept_id_his` varchar(32) NOT NULL COMMENT 'HIS科室ID',
                                          `dept_name_his` varchar(32) NOT NULL COMMENT 'HIS科室名称',
                                          `employee_id` varchar(32) NOT NULL COMMENT '工号',
                                          `origin_doctor_id` varchar(32) NOT NULL COMMENT 'HIS医生ID',
                                          `is_qualifier` varchar(32) NOT NULL COMMENT '中医资质',
                                          `status` varchar(32) NOT NULL COMMENT '状态',
                                          PRIMARY KEY (`his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步人员字段表';

CREATE TABLE `t_interface_async_dept` (
                                          `his_id` varchar(32) NOT NULL COMMENT 'HISID',
                                          `app_id` varchar(32) NOT NULL COMMENT 'APPID',
                                          `ins_code` varchar(32) NOT NULL COMMENT '医疗机构代码',
                                          `dept_origin_id` varchar(32) NOT NULL COMMENT 'HIS科室ID',
                                          `dept_name` varchar(32) NOT NULL COMMENT 'HIS科室名称',
                                          `status` varchar(32) NOT NULL COMMENT '状态',
                                          PRIMARY KEY (`his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步科室字段表';

CREATE TABLE `t_interface_async_material` (
                                              `his_id` varchar(32) NOT NULL COMMENT 'HISID',
                                              `app_id` varchar(32) NOT NULL COMMENT 'APPID',
                                              `mat_price_id` varchar(32) NOT NULL COMMENT '药品价格ID',
                                              `mat_name` varchar(32) NOT NULL COMMENT '药品名称',
                                              `mat_type` varchar(32) NOT NULL COMMENT '中药类型（1散装饮片  2散装颗粒  3膏方  4小包装饮片 5小包装颗粒 6配方 7制剂 8 中成药）',
                                              `mat_spe` varchar(32) NOT NULL COMMENT '规格',
                                              `mat_origin_name` varchar(32) NOT NULL COMMENT '产地',
                                              `mat_pack_mount` varchar(32) NOT NULL COMMENT '包装量',
                                              `mat_pack_unit` varchar(32) NOT NULL COMMENT '包装单位',
                                              `small_retail_price` varchar(32) NOT NULL COMMENT '小规格零售价',
                                              `pha_id` varchar(32) NOT NULL COMMENT '药房ID',
                                              `status` varchar(32) NOT NULL COMMENT '状态（0有效 1删除 2禁用） 默认1',
                                              `pha_name` varchar(32) NOT NULL COMMENT '药房名称',
                                              `mat_id` varchar(32) DEFAULT NULL COMMENT '药品ID',
                                              `mat_origin_py` varchar(32) DEFAULT NULL COMMENT '拼音',
                                              `mat_origin_wb` varchar(32) DEFAULT NULL COMMENT '五笔',
                                              `mat_spe_id` varchar(32) DEFAULT NULL COMMENT '规格ID',
                                              `mat_origin_id` varchar(32) DEFAULT NULL COMMENT '产地ID',
                                              `mat_dose` varchar(32) DEFAULT NULL COMMENT '剂量',
                                              `mat_dose_unit` varchar(32) DEFAULT NULL COMMENT '剂量单位',
                                              `mat_once_dose` varchar(32) DEFAULT NULL COMMENT '一次剂量',
                                              `mat_once_dose_unit` varchar(32) DEFAULT NULL COMMENT '一次剂量单位',
                                              `conversion_factor` varchar(32) DEFAULT NULL COMMENT '包装转换系数',
                                              `purification_factor` varchar(32) DEFAULT NULL COMMENT '颗粒提纯系数',
                                              `is_medical` varchar(32) DEFAULT NULL COMMENT '是否医保',
                                              `usage_desc` varchar(32) DEFAULT NULL COMMENT '用法用量',
                                              `daily_max_dose_in` varchar(32) DEFAULT NULL COMMENT '日最大剂量（内服）',
                                              `daily_max_dose_ext` varchar(32) DEFAULT NULL COMMENT '日最大剂量（外用）',
                                              `daily_max_num_prep` varchar(32) DEFAULT NULL COMMENT '日最大开药量（制剂）',
                                              `not_pay_alone` varchar(32) DEFAULT NULL COMMENT '单独使用时不予支付',
                                              `not_pay_in_fund` varchar(32) DEFAULT NULL COMMENT '不纳入基金支付范围',
                                              `frequency_id` varchar(32) DEFAULT NULL COMMENT '频次ID',
                                              `frequency` varchar(32) DEFAULT NULL COMMENT '频次',
                                              `frequency_rate` varchar(32) DEFAULT NULL COMMENT '频次系数(次数/天)',
                                              `mat_standard` varchar(32) DEFAULT NULL COMMENT '标准编码',
                                              `mat_medical_code` varchar(32) DEFAULT NULL COMMENT '医保编码',
                                              `toxin_type` varchar(32) DEFAULT NULL COMMENT '毒理分类（1、正常  2、有毒   3、微毒）',
                                              `stock_num` varchar(32) DEFAULT NULL COMMENT '库存数量',
                                              PRIMARY KEY (`his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步药品字段表';


CREATE TABLE `t_interface_async_stock` (
                                           `his_id` varchar(32) NOT NULL COMMENT 'HISID',
                                           `app_id` varchar(32) NOT NULL COMMENT 'APPID',
                                           `mat_price_id` varchar(32) NOT NULL COMMENT '药品价格ID',
                                           `pha_id` varchar(32) NOT NULL COMMENT '药房ID',
                                           `pha_name` varchar(32) NOT NULL COMMENT '药房名称',
                                           `stock_num` varchar(32) NOT NULL COMMENT '库存数量',
                                           PRIMARY KEY (`his_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步库存字段表';

CREATE TABLE `t_interface_column_trans`
(
    `his_id`          varchar(32)   DEFAULT NULL COMMENT 'HISID',
    `his_url_content` varchar(1)    DEFAULT NULL COMMENT '接口内容：1.药品目录2.药品库存3.人员字典4.科室字典',
    `cb_column`       varchar(32)   DEFAULT NULL COMMENT '聪宝系统字段',
    `his_column`      varchar(32)   DEFAULT NULL COMMENT 'HIS系统字段',
    `trans_type`      varchar(3)    DEFAULT NULL COMMENT '转换规则类型（1枚举 2模糊匹配）',
    `trans_rule`      varchar(1024) DEFAULT NULL COMMENT '转换规则JSON',
    KEY `IDX_HIS_ID` (`his_id`),
    KEY `IDX_CB_COLUMN` (`cb_column`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='字段值转换规则';
CREATE TABLE `sys_admin_grayscale` (
                                       `user_id` varchar(32) NOT NULL,
                                       `grayscale_status` varchar(3) DEFAULT NULL COMMENT '是否已试用(大于1:已试用,0:未试用)',
                                       `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                       `create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                                       `create_user_name` varchar(32) DEFAULT NULL COMMENT '创建人姓名',
                                       PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='灰度发布试用表';


INSERT INTO `sys_admin_menu` (
    `menu_id`,
    `menu_name`,
    `menu_path`,
    `parent_menu_id`,
    `create_date`,
    `menu_type`,
    `sort`,
    `menu_level`,
    `modual_code`
)
VALUES
    (
        '3001',
        '新版试用',
        '/system/user/channel',
        '1',
        '2022-05-24 11:04:37',
        '1',
        '16',
        '2',
        '1'
    );
INSERT INTO `sys_admin_menu` (
    `menu_id`,
    `menu_name`,
    `menu_path`,
    `parent_menu_id`,
    `create_date`,
    `menu_type`,
    `sort`,
    `menu_level`,
    `modual_code`
)
VALUES
    (
        '402',
        '第三方管理',
        NULL,
        '4',
        NOW(),
        '1',
        '1',
        '2',
        '1'
    );


INSERT INTO `sys_admin_menu` (
    `menu_id`,
    `menu_name`,
    `menu_path`,
    `parent_menu_id`,
    `create_date`,
    `menu_type`,
    `sort`,
    `menu_level`,
    `modual_code`
)
VALUES
    (
        '4021',
        '接口配置',
        '/task-manage/interface/list',
        '402',
        NOW(),
        '1',
        '16',
        '3',
        '1'
    );

UPDATE sys_admin_menu SET sort=2 WHERE menu_id='401';
INSERT INTO `sys_admin_rule_menu` VALUES('b4a17b6b635c4de48f95178676905aa5','3001');
INSERT INTO `sys_admin_rule_menu` VALUES('b4a17b6b635c4de48f95178676905aa5','402');
INSERT INTO `sys_admin_rule_menu` VALUES('b4a17b6b635c4de48f95178676905aa5','4021');

#增加备注字段,是否通用字段
ALTER TABLE t_interface_his ADD  interface_desc VARCHAR(128) DEFAULT NULL COMMENT '备注' AFTER `status`;

ALTER TABLE t_interface_his ADD  is_general VARCHAR(1) DEFAULT NULL COMMENT '是否通用(1:通用,0:不通用)' AFTER `status`;
ALTER TABLE t_interface_his ADD  jdbc_driver VARCHAR(64) DEFAULT NULL COMMENT '数据库驱动' AFTER `his_url_type`;
ALTER TABLE t_interface_his CHANGE  his_url_content dic_code VARCHAR(32) COMMENT '字典代码(1.药品目录2.药品库存3.人员字典4.科室字典)' ;
#系统字典新增业务类型
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`)
VALUES('2c133f183ef011edad8d00163f006620','business','业务类型','0','业务类型','0',NULL,'1','000000','000000','000000',NULL,NOW(),'1','1','0');

INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'1','药品目录','2c133f183ef011edad8d00163f006620',NULL,'0',NULL,1,'000000','000000','000000','2022-06-24 11:14:58','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'2','药品库存','2c133f183ef011edad8d00163f006620',NULL,'0',NULL,2,'000000','000000','000000','2022-06-24 11:08:33','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'3','人员字典','2c133f183ef011edad8d00163f006620',NULL,'0',NULL,3,'000000','000000','000000','2022-06-24 11:14:58','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'4','科室字典','2c133f183ef011edad8d00163f006620',NULL,'0',NULL,4,'000000','000000','000000','2022-06-24 11:08:33','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'5','状态','2c133f183ef011edad8d00163f006620',NULL,'0',NULL,5,'000000','000000','000000','2022-06-24 11:08:33','70810c874405453b99c6c2cf72296fe5','admin','0','000000');