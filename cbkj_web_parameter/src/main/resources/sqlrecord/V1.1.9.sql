ALTER TABLE `cbkj_web_parameter`.`sys_admin_info`
    CHANGE `name_zh` `name_zh` VARCHAR(500) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '姓名',
    CHANGE `email` `email` VARCHAR(250) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '邮箱',
    CHANGE `phone` `phone` VARCHAR(250) CHARSET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手机号',
    CHANGE `address` `address` TEXT CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '住址',
    CHANGE `certificate` `certificate` VARCHAR(250) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '身份证号';



UPDATE sys_admin_info AS a SET
    name_zh = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`name_zh`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE sys_admin_info AS a SET
    phone = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`phone`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE sys_admin_info AS a SET
    address = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`address`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE sys_admin_info AS a SET
    certificate = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`certificate`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));

UPDATE sys_admin_info AS a SET
    email = HEX(AES_ENCRYPT(   HEX(AES_ENCRYPT(a.`email`, 'S@T#K$J'))    , 'TWGDH@BTZHY$'));


ALTER TABLE `cbkj_web_parameter`.`sys_department`
    ADD COLUMN `frmzbz` INT(1) NULL COMMENT '0是1不是' AFTER `dept_address`;
