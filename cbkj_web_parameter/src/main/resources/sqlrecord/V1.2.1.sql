INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'HISTORY_PRESCRIPTION_ANNUL_INPATIENT', '历史处方是否显示住院撤销医嘱按钮', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '0', '103', '105', '1显示 0不显示（默认）', 'G003'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'HISTORY_PRESCRIPTION_ANNUL_INPATIENT'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '显示', 0 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='HISTORY_PRESCRIPTION_ANNUL_INPATIENT'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='显示'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '不显示', 1 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='HISTORY_PRESCRIPTION_ANNUL_INPATIENT'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='不显示'
    );


INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME', '显示中药方剂名', '0,0', NOW(),
    'admin', 'admin', '0', '21,21', '0', '门诊显示,住院显示', '103', '1009', '门诊显示 门诊必填.住院显示 住院必填0，不显示.1，显示2，编辑3，编辑必填', 'B247'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '不显示', 0 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='不显示'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '显示', 1 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='显示'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '2', '编辑', 2 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='编辑'
    );
INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '3', '编辑必填', 3 FROM (
                                         SELECT tsp.PAR_ID
                                         FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                         WHERE tsp.PAR_CODE='SHOW_CHINESE_MEDICINE_PRESCRIPTION_NAME'
                                     ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='编辑必填'
    );






INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '4', '单帖金额=∑(药品金额（保留2位）* 剂量)；总金额=单帖金额 * 剂数', 4 FROM (
                                             SELECT tsp.PAR_ID
                                             FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                             WHERE tsp.PAR_CODE='PRES_COMPUTE_MODE'
                                         ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='4' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='单帖金额=∑(药品金额（保留2位）* 剂量)；总金额=单帖金额 * 剂数'
    );



ALTER TABLE `cbkj_web_parameter`.`sys_doctor_multipoint`
    ADD COLUMN `ORIGIN_DOCTOR_ID` VARCHAR(32) NULL COMMENT '第三方用户id' ;




INSERT INTO cbkj_web_parameter.t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'MODIFY_PRESCRIPTION_LIMIT_TYPE', '修改处方链接是否限制处方类型（默认是）', '1', NOW(),
    'admin', 'admin', '0', '1', '0', '1', '104', '1009', '修改处方链接是否限制处方类型 1限制 0不限制', 'B248'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM cbkj_web_parameter.t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'MODIFY_PRESCRIPTION_LIMIT_TYPE'
    );


INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '不限制', 0 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='MODIFY_PRESCRIPTION_LIMIT_TYPE'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='不限制'
    );

INSERT INTO cbkj_web_parameter.`t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '限制', 1 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM cbkj_web_parameter.`t_sys_param` AS tsp
                                           WHERE tsp.PAR_CODE='MODIFY_PRESCRIPTION_LIMIT_TYPE'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM cbkj_web_parameter.`t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='限制'
    );


ALTER TABLE `cbkj_web_parameter`.`sys_doctor_multipoint`
    ADD COLUMN `ORIGIN_DOCTOR_ID` VARCHAR(32) NULL ;



ALTER TABLE `cbkj_web_parameter`.`sys_admin_info`
    ADD COLUMN `doctor_sign_img_path` TEXT NULL COMMENT '医生签名图片路径' ;

