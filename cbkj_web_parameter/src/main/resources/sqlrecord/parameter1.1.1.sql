######2022/5/19参数修改菜单sql--------------------------
#辩证
UPDATE t_sys_param SET menu_id='1901'
WHERE par_number IN ('A001','A002');
#诊断
UPDATE t_sys_param SET menu_id='1011'
WHERE par_number IN ('B101','B102','B103','B104','B105','B106');
#处方配置
UPDATE t_sys_param SET menu_id='1009'
WHERE par_number IN ('B208','B209','B210','B211','B212','B213','B214','B215');
#药品
UPDATE t_sys_param SET menu_id='1061'
WHERE par_number IN ('B301','B302','B303','B304');
#转方
UPDATE t_sys_param SET menu_id='1071'
WHERE par_number IN ('B401');
#处方打印
UPDATE t_sys_param SET menu_id='1041'
WHERE par_number IN ('G002','G003');
#名家医案
UPDATE t_sys_param SET menu_id='1024'
WHERE par_number IN ('B501','B503','B504','B505','B506','B507','B508','B509');
#合理用药
UPDATE t_sys_param SET menu_id='1301'
WHERE menu_id='13';
#电子病历
UPDATE t_sys_param SET menu_id='1601'
WHERE menu_id='16';
#监管平台
UPDATE t_sys_param SET menu_id='1501'
WHERE menu_id='15';
#医保控制
UPDATE t_sys_param SET menu_id='1401'
WHERE menu_id='14';
#挂号
UPDATE t_sys_param SET menu_id='1101'
WHERE menu_id='11';
#药品目录
UPDATE t_sys_param SET menu_id='201'
WHERE menu_id='20';
#中医知识库
UPDATE t_sys_param SET menu_id='1201'
WHERE menu_id='12';
#第三方对接
UPDATE t_sys_param SET menu_id='1701'
WHERE menu_id='17';
#住院医嘱
UPDATE t_sys_param SET menu_id='1021'
WHERE PAR_CODE='INPATIENT_ADVICE_DISPLAY';
######2022/5/19更改部分参数所属菜单-------------------------
UPDATE t_sys_param SET menu_id='1009'
WHERE par_number='B228';
UPDATE t_sys_param SET menu_id='1004'
WHERE par_number='B215';

######2022/5/19新增菜单信息--------------------------------
INSERT  INTO
    `sys_admin_menu`
(`menu_id`,`menu_name`,`menu_path`,`menu_class`,`status`,`parent_menu_id`,`create_date`,`cteate_user`,`menu_type`,`btn_class`,`btn_type`,`btn_weight`,`sort`,`menu_level`,`open_type`,`modual_code`)
VALUES
    ('1901','智能辨证','',NULL,'0','19',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1011','诊断','',NULL,'0','101',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1009','处方配置','',NULL,'0','103',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1061','药品','',NULL,'0','106',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1071','转方','',NULL,'0','107',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1041','处方打印','',NULL,'0','104',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1024','名家医案','',NULL,'0','102',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1301','合理用药','',NULL,'0','13',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1601','电子病历','',NULL,'0','16',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1501','监管平台','',NULL,'0','15',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1401','医保控制','',NULL,'0','14',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1101','挂号','',NULL,'0','11',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('201','药品目录','',NULL,'0','20',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1201','中医知识库','',NULL,'0','12',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1'),
    ('1701','第三方对接','',NULL,'0','17',NULL,NULL,2,NULL,NULL,NULL,1,4,1,'1');

######2022/5/20 修改历史处方菜单数据
UPDATE sys_admin_menu SET menu_type=2
WHERE menu_id='105';
UPDATE sys_admin_menu SET menu_level=4
WHERE menu_id='105';
UPDATE sys_admin_menu SET menu_path='/business/param/18'
WHERE menu_id='18';

#参数表新增字段
ALTER TABLE t_sys_param ADD  modual_code VARCHAR(10) DEFAULT NULL COMMENT '平台代码' AFTER `menu_id`;

#t_dispaly表药房名称和t_pharmacy药房名称数据长度保持一致
ALTER  TABLE t_display MODIFY COLUMN pha_name VARCHAR(64) COMMENT '药房名称'

#字典表修改药房字段
ALTER TABLE t_dic_base add display_id  VARCHAR(32) DEFAULT '000000' COMMENT '机构药房ID' AFTER `pha_id`;

#新增菜单
insert into `cbkj_web_parameter`.`sys_admin_menu` (
  `menu_id`,
  `menu_name`,
  `menu_path`,
  `parent_menu_id`,
  `create_date`,
  `menu_type`,
  `sort`,
  `menu_level`,
  `modual_code`
)
values
  (
    '0002231',
    '系统学习统计',
    '/statistical/study-target',
    '000214',
    '2022-05-24 11:04:37',
    '2',
    '9',
    '2',
    '2'
  );
insert into `cbkj_web_parameter`.`sys_admin_menu` (
    `menu_id`,
    `menu_name`,
    `menu_path`,
    `parent_menu_id`,
    `create_date`,
    `menu_type`,
    `sort`,
    `menu_level`,
    `modual_code`
)
values
    (
        '0002311',
        '系统使用统计',
        '/statistical/system-use',
        '000214',
        '2022-05-24 11:04:37',
        '2',
        '10',
        '2',
        '2'
    );
#新增系统字典维护菜单
INSERT INTO `sys_admin_menu` ( `menu_id`, `menu_name`, `menu_path`, `parent_menu_id`, `create_date`, `menu_type`, `sort`, `menu_level`, `modual_code` )
VALUES ( '303', '系统字典管理', NULL, '3', '2022-05-24 11:04:37', '1', '3', '2', '1' );
INSERT INTO `sys_admin_menu` ( `menu_id`, `menu_name`, `menu_path`, `parent_menu_id`, `create_date`, `menu_type`, `sort`, `menu_level`, `modual_code` )
VALUES ( '30301', '系统字典维护', '/dictionaries/system/list', '303', '2022-05-24 11:04:37', '1', '3', '3', '1' );


#禁用部分菜单
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='医共体管理' AND menu_id='000223' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='医疗机构管理' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='科室管理' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='用户管理' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='角色管理' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='参数管理' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='公用代码管理' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='药品目录' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='药品目录-机构' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='药品明细' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='医共体药品映射' AND modual_code='2';
UPDATE sys_admin_menu SET STATUS='2' WHERE menu_name='知识库药品映射' AND modual_code='2';