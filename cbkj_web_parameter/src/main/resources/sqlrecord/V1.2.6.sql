ALTER TABLE `cbkj_web_parameter`.`t_material_price`
    ADD COLUMN `special_usages` VARCHAR(32) NULL COMMENT '药品特殊用法' AFTER `external_marusan_name`,
      ADD COLUMN `usages_ustrict` VARCHAR(32) NULL COMMENT '特殊用法限制(0关1开)' AFTER `special_usages`;


DELIMITER $$

ALTER ALGORITHM=UNDEFINED DEFINER=`root`@`%` SQL SECURITY DEFINER VIEW `v_center_ypmlmx` AS (
SELECT
  `matprice`.`drug_id`                    AS `drug_id`,
  `matdeatil`.`mat_standard`              AS `drug_code`,
  `matprice`.`mat_price_id`               AS `mat_price_id`,
  `matdeatil`.`mat_name`                  AS `mat_name`,
  `matprice`.`mat_spe_id`                 AS `mat_spe_id`,
  `matspecification`.`mat_spe`            AS `mat_spe`,
  `matorigin`.`mat_origin_id`             AS `mat_origin_id`,
  `matorigin`.`mat_origin_name`           AS `mat_origin_name`,
  `matprice`.`retail_price`               AS `retail_price`,
  `matprice`.`purchase_price`             AS `purchase_price`,
  `matprice`.`small_purchase_price`       AS `small_purchase_price`,
  `matprice`.`small_retail_price`         AS `small_retail_price`,
  `matspecification`.`mat_dose`           AS `mat_dose`,
  `matspecification`.`mat_dose_unit`      AS `mat_dose_unit`,
  `matspecification`.`mat_pack_mount`     AS `mat_pack_mount`,
  `matspecification`.`mat_pack_unit`      AS `mat_pack_unit`,
  `matspecification`.`mat_type`           AS `mat_type`,
  `matspecification`.`mat_once_dose`      AS `mat_once_dose`,
  `matspecification`.`mat_once_dose_unit` AS `mat_once_dose_unit`,
  `matdeatil`.`mat_class`                 AS `mat_class`,
  `matspecification`.`conversion_factor`  AS `conversion_factor`,
  `matdeatil`.`status`                    AS `status`,
  `matdeatil`.`mat_pinyin`                AS `py`,
  `matdeatil`.`mat_wubi`                  AS `wb`,
  `matdeatil`.`is_medical`                AS `is_medical`,
  `matdeatil`.`is_del`                    AS `is_del`,
  `matdeatil`.`create_date`               AS `create_date`,
  `matdeatil`.`update_date`               AS `update_date`,
  `matprice`.`daily_max_dose_in`          AS `daily_max_dose_in`,
  `matprice`.`daily_max_dose_ext`         AS `daily_max_dose_ext`,
  `matprice`.`daily_max_num_prep`         AS `daily_max_num_prep`,
  `matdeatil`.`mat_usage`                 AS `mat_usage`,
  `matprice`.`not_pay_alone`              AS `not_pay_alone`,
  `matprice`.`not_pay_in_fund`            AS `not_pay_in_fund`,
  `matprice`.`frequency_id`               AS `frequency_id`,
  `matprice`.`frequency`                  AS `frequency`,
  `matprice`.`frequency_rate`             AS `frequency_rate`,
  `matdeatil`.`content`                   AS `content`,
  `matdeatil`.`usage_desc`                AS `usage_desc`,
  `matdeatil`.`effect`                    AS `effect`,
  `matprice`.`approval_number`            AS `approval_number`,
  `matdeatil`.`mat_id`                    AS `mat_id`,
  `matprice`.`toxicity_overdose_multiple` AS `toxicity_overdose_multiple`,
  `matprice`.`external_use_only`          AS `external_use_only`,
  `matdeatil`.`medicontrol_text`          AS `medicontrol_text`,
  `matprice`.`maxdose`                    AS `maxdose`,
  `matprice`.`mindose`                    AS `mindose`,
  `matprice`.`toxicity`                   AS `toxicity`,
  `matprice`.`mother_taboos`              AS `mother_taboos`,
  `matprice`.`external_use_orally`        AS `external_use_orally`,
  `matprice`.`external_marusan`           AS `external_marusan`,
  `matprice`.`external_marusan_name`      AS `external_marusan_name`,
  `matprice`.`special_usages`      AS `special_usages`,
  `matprice`.`usages_ustrict`      AS `usages_ustrict`
FROM (((`t_material_price` `matprice`
     JOIN `t_material` `matdeatil`
       ON (((`matprice`.`mat_id` = `matdeatil`.`mat_id`)
            AND (`matprice`.`drug_id` = `matdeatil`.`drug_id`))))
    JOIN `t_material_specification` `matspecification`
      ON (((`matspecification`.`mat_id` = `matprice`.`mat_id`)
           AND (`matspecification`.`mat_spe_id` = `matprice`.`mat_spe_id`)
           AND (`matspecification`.`status` = '0')
           AND (`matspecification`.`drug_id` = `matprice`.`drug_id`))))
   JOIN `t_material_origin` `matorigin`
     ON ((`matorigin`.`mat_origin_id` = `matprice`.`mat_origin_id`)))
WHERE ((`matdeatil`.`status` = '0')
        OR (`matdeatil`.`status` = '2')))$$

DELIMITER ;

UPDATE  `cbkj_web_parameter`.`t_sys_param_init_desc` SET param_init_name ='不开启特殊用法' WHERE param_id ='606410ab415e44069727a02670e3af2e' AND param_init_code='0'



ALTER TABLE `cbkj_web_parameter`.`t_interface_async_material`
    ADD COLUMN `special_usages` VARCHAR(32) NULL COMMENT '药品特殊用法' AFTER `mat_least_unit`;

UPDATE  `cbkj_web_parameter`.`t_prescription_regulation_param` SET NAME ='药品特殊用法监测' WHERE id ='8';



    DELIMITER $$
ALTER ALGORITHM=UNDEFINED DEFINER=`root`@`%` SQL SECURITY DEFINER VIEW `view_mat_directory` AS
SELECT `price`.`mat_price_id` AS `mat_price_id`,`price`.`mat_id` AS `mat_id`,`price`.`mat_spe_id` AS `mat_spe_id`,`price`.`small_purchase_price` AS `small_purchase_price`,`price`.`small_retail_price` AS `small_retail_price`,`price`.`approval_number` AS `approval_number`,`price`.`daily_max_dose_in` AS `daily_max_dose_in`,`price`.`daily_max_dose_ext` AS `daily_max_dose_ext`,`price`.`daily_max_num_prep` AS `daily_max_num_prep`,`price`.`not_pay_alone` AS `not_pay_alone`,`price`.`not_pay_in_fund` AS `not_pay_in_fund`,`price`.`frequency` AS `frequency`,`price`.`frequency_id` AS `frequency_id`,`price`.`frequency_rate` AS `frequency_rate`,`price`.`markup_rate` AS `markup_rate`,`price`.`status` AS `status`,`price`.`purchase_price` AS `purchase_price`,`price`.`retail_price` AS `retail_price`,`price`.`mat_origin_name` AS `mat_origin_name`,`price`.`mat_origin_id` AS `mat_origin_id`,`price`.`update_date` AS `update_date`,`price`.`purification_factor` AS `purification_factor`,`price`.`mat_factory_name` AS `mat_factory_name`,`price`.`mat_factory_id` AS `mat_factory_id`,`price`.`toxicity_overdose_multiple` AS `toxicity_overdose_multiple`,`price`.`external_use_only` AS `external_use_only`,`price`.`maxdose` AS `max_dose`,`price`.`mindose` AS `min_dose`,`price`.`toxicity` AS `toxicity`,`price`.`mother_taboos` AS `mother_taboos`,`price`.`external_use_orally` AS `external_use_orally`,`price`.`external_marusan` AS `external_marusan`,`price`.`external_marusan_name` AS `external_marusan_name`,`spe`.`mat_pack_unit` AS `mat_pack_unit`,`spe`.`account_type` AS `account_type`,`spe`.`mat_name_spe_wb` AS `mat_wb`,`spe`.`mat_name_spe_py` AS `mat_py`,`spe`.`mat_name_spe` AS `mat_name`,`spe`.`small_spe_unit` AS `mat_unit`,`spe`.`mat_spe` AS `mat_spe_big`,`spe`.`mat_pack_mount` AS `mat_pack_mount`,`spe`.`mat_small_spe` AS `mat_spe`,`spe`.`mat_type` AS `mat_type`,CAST(`spe`.`mat_dose` AS DECIMAL(10,0)) AS `conversion_factor`,`spe`.`mat_dose` AS `mat_dose`,`spe`.`mat_dose_unit` AS `mat_dose_unit`,`spe`.`mat_once_dose` AS `mat_once_dose`,`spe`.`mat_once_dose_unit` AS `mat_once_dose_unit`,`mat`.`drug_id` AS `drug_id`,`mat`.`mat_class` AS `mat_class`,`mat`.`mat_usage_code` AS `mat_usage_code`,`mat`.`is_medical` AS `is_medical`,`mat`.`mat_usage` AS `mat_usage`,`mat`.`usage_desc` AS `usage_desc`,`mat`.`toxin_type` AS `toxin_type`,`mat`.`worth_type` AS `worth_type`,`mat`.`content` AS `content`,`mat`.`effect` AS `effect`,`mat`.`mat_medical_code` AS `mat_medical_code`,`mat`.`dosage_form` AS `dosage_form`,`price`.`special_usages` AS `special_usages`,`price`.`usages_ustrict` AS `usages_ustrict` FROM ((((SELECT `price`.`mat_price_id` AS `mat_price_id`,`price`.`mat_id` AS `mat_id`,`price`.`mat_spe_id` AS `mat_spe_id`,`price`.`small_purchase_price` AS `small_purchase_price`,`price`.`small_retail_price` AS `small_retail_price`,`price`.`approval_number` AS `approval_number`,`price`.`daily_max_dose_in` AS `daily_max_dose_in`,`price`.`daily_max_dose_ext` AS `daily_max_dose_ext`,`price`.`daily_max_num_prep` AS `daily_max_num_prep`,`price`.`not_pay_alone` AS `not_pay_alone`,`price`.`not_pay_in_fund` AS `not_pay_in_fund`,`price`.`frequency` AS `frequency`,`price`.`frequency_id` AS `frequency_id`,`price`.`frequency_rate` AS `frequency_rate`,`price`.`markup_rate` AS `markup_rate`,`price`.`status` AS `status`,`price`.`purchase_price` AS `purchase_price`,`price`.`retail_price` AS `retail_price`,`price`.`mat_origin_name` AS `mat_origin_name`,`price`.`mat_origin_id` AS `mat_origin_id`,`price`.`drug_id` AS `drug_id`,`price`.`purification_factor` AS `purification_factor`,`price`.`create_date` AS `update_date`,`price`.`mat_factory_name` AS `mat_factory_name`,`price`.`mat_factory_id` AS `mat_factory_id`,`price`.`toxicity_overdose_multiple` AS `toxicity_overdose_multiple`,`price`.`external_use_only` AS `external_use_only`,`price`.`maxdose` AS `maxdose`,`price`.`mindose` AS `mindose`,`price`.`toxicity` AS `toxicity`,`price`.`mother_taboos` AS `mother_taboos`,`price`.`external_use_orally` AS `external_use_orally`,`price`.`external_marusan` AS `external_marusan`,`price`.`external_marusan_name` AS `external_marusan_name`,`price`.`special_usages` AS `special_usages`,`price`.`usages_ustrict` AS `usages_ustrict` FROM `cbkj_web_parameter`.`t_material_price` `price`)) `price` JOIN `cbkj_web_parameter`.`t_material_specification` `spe` ON(((`spe`.`mat_spe_id` = `price`.`mat_spe_id`) AND (`spe`.`drug_id` = `price`.`drug_id`)))) JOIN `cbkj_web_parameter`.`t_material` `mat` ON(((`price`.`mat_id` = `mat`.`mat_id`) AND (`mat`.`drug_id` = `price`.`drug_id`))))$$
    DELIMITER ;