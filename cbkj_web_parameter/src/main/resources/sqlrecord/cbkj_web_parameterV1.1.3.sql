CREATE TABLE `t_display_currency` (
                                      `display_currency_id` varchar(32) NOT NULL,
                                      `display_id` varchar(32) NOT NULL COMMENT '机构药房ID',
                                      `withhold_switch` varchar(1) NOT NULL COMMENT '是否预扣计算 0否1是',
                                      `pre_stock_switch` varchar(1) NOT NULL COMMENT '处方保存接口校验库存开关 1是0否',
                                      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                      `create_user` varchar(32) DEFAULT NULL COMMENT '创建人',
                                      PRIMARY KEY (`display_currency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用配置';
#修改西医诊断参数
UPDATE t_sys_param SET param_desc='[1:禁用][2:必填][3:校验]'
WHERE par_number='B102';
UPDATE t_sys_param SET param_type='3'
WHERE par_number='B102';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.param_init_name='禁用'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B102'
  AND t_sys_param_init_desc.param_init_name='是';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.param_init_name='必填',param_init_code='2'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B102'
  AND t_sys_param_init_desc.param_init_name='否';

#修改参数选项插入
# INSERT INTO `t_sys_param_init_desc`(
#     param_id,param_init_code,param_init_name,sort)
# SELECT tsp.PAR_ID,'3','校验',3
# FROM `t_sys_param` AS tsp
#          INNER JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
# WHERE
#         tsp.`par_number`='B102' AND tspi.param_init_name='必填';
#不存在则执行
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)

SELECT  t.PAR_ID,'3','校验',3 FROM (
                                     SELECT tsp.PAR_ID
                                     FROM `t_sys_param` AS tsp
                                              INNER JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
                                     WHERE
                                             tsp.`par_number`='B102' AND tspi.param_init_name='必填'

                                 ) AS t  WHERE NOT EXISTS (
        SELECT 1
        FROM
                  t_sys_param_init_desc AS tsp
        WHERE
        tsp.param_init_code='3' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='校验'
                                     );


UPDATE t_sys_param SET PAR_CODE='PRESCRIPTION_ICD',PAR_NAME='西医诊断配置' WHERE
        PAR_CODE='PRESCRIPTION_ICD_DISABLED';
UPDATE t_sys_param SET PAR_VALUES=''
WHERE par_number='B102' AND PAR_VALUES='0';

#西医疾病版本参数-判断是否已经存在。
# INSERT INTO t_sys_param
# (PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
#  STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
# VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','WEST_DIS_VISION','西医疾病版本','1',NOW(),'admin','admin','0','2','0','2','9','1011','1:ICD10，2:国家临床诊断2.0','B107');
INSERT INTO t_sys_param
(PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
 STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
SELECT
    REPLACE(UUID(),'-',''),'000000','000000','000000','WEST_DIS_VISION','西医疾病版本','1',NOW(),'admin','admin','0','2','0','2','9','1011','1:ICD10，2:国家临床诊断2.0','B107'
FROM DUAL WHERE  NOT EXISTS (
        SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'WEST_DIS_VISION'
    );


# INSERT INTO `t_sys_param_init_desc`(
#     param_id,param_init_code,param_init_name,sort)
# SELECT tsp.PAR_ID,'1','ICD10',1
# FROM `t_sys_param` AS tsp
# WHERE tsp.`par_number`='B107';

INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID,'1','ICD10',1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM `t_sys_param` AS tsp
                                       WHERE tsp.`par_number`='B107'
                                   ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='ICD10'
    )
;



# INSERT INTO `t_sys_param_init_desc`(
#     param_id,param_init_code,param_init_name,sort)
# SELECT tsp.PAR_ID,'2','国家临床诊断2.0',2
# FROM `t_sys_param` AS tsp
# WHERE tsp.`par_number`='B107';

INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID,'2','国家临床诊断2.0',2 FROM (
                                           SELECT tsp.PAR_ID
                                           FROM `t_sys_param` AS tsp
                                           WHERE tsp.`par_number`='B107'
                                       ) AS t WHERE NOT EXISTS (
        SELECT 1
        FROM `t_sys_param_init_desc` AS tsp
        WHERE tsp.param_init_code='2' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='国家临床诊断2.0'
    );


#提纯系数
INSERT INTO t_sys_param
(PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
 STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','PRESCRIPTION_TCXL','是否开启饮片提纯转散装颗粒计算','0',NOW(),'admin','admin','0','1','0','1','10','1011','内服、外用开启饮片提纯转散装颗粒计算：1是/0否；(是：散装颗粒类型显示“饮片剂量”，原“剂量”可更改；否：关闭不显示)','B108');
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'1','是',1
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B108';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'0','否',2
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B108';
#患者类型
INSERT INTO t_sys_param
(PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
 STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','PATIENTS_TYPES_SHOW','患者类型显示','0',NOW(),'admin','admin','0','1','0','1','100','1401','1.开：智能云开方显示 0关：智能云开方不显示','F013');
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'1','是',1
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='F013';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'0','否',2
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='F013';
#外用中药方显示项目
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort
)SELECT par_id,'51','第二剂量','14' FROM `t_sys_param` WHERE par_code='PRESCRIPTION_EXTERNAL_COLUMN';
#内服中药方显示项目
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort
)SELECT par_id,'51','第二剂量','14' FROM `t_sys_param` WHERE par_code='PRESCRIPTION_INTERNAL_COLUMN';

#系统字典新增患者类型大类
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`)
VALUES('ce4ed7b01e1011edad8d00163f006620','patients_types','患者类型','0','患者类型','0',NULL,'1','000000','000000','000000',NULL,NOW(),'1','1','0');

INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'0','自费','ce4ed7b01e1011edad8d00163f006620',NULL,'0',NULL,1,'000000','000000','000000','2022-06-24 11:14:58','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'1','普通医保','ce4ed7b01e1011edad8d00163f006620',NULL,'0',NULL,2,'000000','000000','000000','2022-06-24 11:08:33','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'2','医保离休','ce4ed7b01e1011edad8d00163f006620',NULL,'0',NULL,3,'000000','000000','000000','2022-06-24 11:14:58','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'3','职工特慢','ce4ed7b01e1011edad8d00163f006620',NULL,'0',NULL,4,'000000','000000','000000','2022-06-24 11:08:33','70810c874405453b99c6c2cf72296fe5','admin','0','000000');
INSERT INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`,`create_date`, `create_user`, `create_user_name`, `status`, `display_id`)
VALUES(REPLACE(UUID(),'-',''),'J','居民特慢','ce4ed7b01e1011edad8d00163f006620',NULL,'0',NULL,5,'000000','000000','000000','2022-06-24 11:14:58','70810c874405453b99c6c2cf72296fe5','admin','0','000000');

#修改菜单名字
UPDATE `sys_admin_menu` SET `menu_name` = '处方分析' WHERE `menu_id` = '0002221';

#适宜技术项目类型

#处方剂量受规格限制
UPDATE t_sys_param SET param_type='3' WHERE par_number='B303';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.param_init_name='散装饮片'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B303'
  AND t_sys_param_init_desc.param_init_name='限制';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.param_init_name='散装颗粒',t_sys_param_init_desc.`param_init_code`='2'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B303'
  AND t_sys_param_init_desc.param_init_name='不限制';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tspi.param_id,'4','小包装饮片',3
FROM `t_sys_param` AS tsp
         JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
WHERE
        tsp.`par_number`='B303' AND tspi.param_init_name='散装饮片' ;
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tspi.param_id,'5','小包装颗粒',4
FROM `t_sys_param` AS tsp
         JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
WHERE
        tsp.`par_number`='B303' AND tspi.param_init_name='散装饮片' ;
UPDATE  t_sys_param SET  param_desc='1:散装饮片,2:散装颗粒,4:小包装饮片,5:小包装颗粒',PAR_VALUES='4,5'
WHERE par_number='B303';


#代煎处方剂量受规格限制
UPDATE t_sys_param SET param_type='3' WHERE par_number='B304';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.param_init_name='散装饮片'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B304'
  AND t_sys_param_init_desc.param_init_name='限制';
UPDATE t_sys_param_init_desc,t_sys_param SET t_sys_param_init_desc.param_init_name='散装颗粒',t_sys_param_init_desc.`param_init_code`='2'
WHERE t_sys_param.`PAR_ID`=t_sys_param_init_desc.`param_id` AND t_sys_param.`par_number`='B304'
  AND t_sys_param_init_desc.param_init_name='不限制';

INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tspi.param_id,'4','小包装饮片',3
FROM `t_sys_param` AS tsp
         JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
WHERE
        tsp.`par_number`='B304' AND tspi.param_init_name='散装饮片' ;

INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tspi.param_id,'5','小包装颗粒',4
FROM `t_sys_param` AS tsp
         JOIN t_sys_param_init_desc AS tspi ON tsp.`PAR_ID`=tspi.`param_id`
WHERE
        tsp.`par_number`='B304' AND tspi.param_init_name='散装饮片' ;

UPDATE  t_sys_param SET  param_desc='1:散装饮片,2:散装颗粒,4:小包装饮片,5:小包装颗粒',PAR_VALUES='4,5'
WHERE par_number='B304';


#处方剂量小数点后位数精确限制
INSERT INTO t_sys_param
(PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,
 STATUS,param_type,is_global,param_init_value,sort,menu_id,param_desc,par_number)
VALUES(REPLACE(UUID(),'-',''),'000000','000000','000000','PRESCRIPTION_LIMIT_POINT','处方剂量小数点后位数精确限制','2,2,2,2',NOW(),'admin','admin','0','3','0','','56','1061','处方剂量小数点后位数精确限制','B305');
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'0','0',0
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B305';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'1','1',1
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B305';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'2','2',2
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B305';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'3','3',3
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B305';
INSERT INTO `t_sys_param_init_desc`(
    param_id,param_init_code,param_init_name,sort)
SELECT tsp.PAR_ID,'4','4',4
FROM `t_sys_param` AS tsp
WHERE tsp.`par_number`='B305';
ALTER TABLE t_sys_param MODIFY COLUMN param_type VARCHAR(16) COMMENT '参数类型：1 判断：是|否 开通|不开通 2 单选（多选一）：铺开 21不铺开3 多选（多选多）：铺开4 排序（多文本）：可拖拽排序5 文本输入：6 时间段';
UPDATE t_sys_param SET param_type='21,21,21,21'
WHERE par_number='B305';
#删除无用的参数
UPDATE t_sys_param SET STATUS='1'
WHERE par_number='B242';
UPDATE t_sys_param SET STATUS='1'
WHERE par_number='B243';

INSERT INTO `t_sys_param_init_desc` (
    `param_id`,
    `param_init_code`,
    `param_init_name`,
    `sort`
)
VALUES
    ('11111122222223333334444455','1','智能辨证','1'),
    ('11111122222223333334444455','2','智能推导','2'),
    ('11111122222223333334444455','3','名家验案','3'),
    ('11111122222223333334444455','4','中药查询','4'),
    ('11111122222223333334444455','5','方剂查询','5'),
    ('11111122222223333334444455','6','经络穴位查询','6'),
    ('11111122222223333334444455','7','经方查询','7'),
    ('11111122222223333334444455','8','疾病查询','8'),
    ('11111122222223333334444455','9','中成药','9'),
    ('11111122222223333334444455','10','临床诊疗指南','10'),
    ('11111122222223333334444455','11','舌诊','11'),
    ('11111122222223333334444455','12','脉诊','12'),
    ('11111122222223333334444455','13','古书籍','13')
;
INSERT INTO `t_sys_param` (
    `PAR_ID`,
    `PAR_CODE`,
    `PAR_NAME`,
    `PAR_VALUES`,
    `CREATE_DATE`,
    `CREATE_USER`,
    `create_user_name`,
    `param_type`,
    `is_global`,
    `sort`,
    `menu_id`,
    `param_desc`,
    `par_number`
)
VALUES
    (
        '11111122222223333334444455',
        'SYS_STUDY_COLUMN',
        '系统学习统计项目',
        '1,2,3,4,5,6,7,8,9,10,11,12,13',
        '2022-05-26 16:21:02',
        'system',
        'system',
        '3',
        '0',
        '88',
        '1501',
        '智能辨证 智能推导 名家验案 中药查询 方剂查询 经络穴位查询  经方查询 疾病查询 中成药  临床诊疗指南 舌诊 脉诊  古书籍',
        'E003'
    );


