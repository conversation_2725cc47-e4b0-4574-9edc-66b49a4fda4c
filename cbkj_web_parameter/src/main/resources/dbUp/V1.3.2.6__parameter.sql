START TRANSACTION;
CREATE TABLE if not exists cbkj_web_parameter.`t_interface_async_charge` (
                                                               `his_id` VARCHAR(32) NOT NULL,
                                                               `XIANGMUCODE` VARCHAR(32) NOT NULL COMMENT '收费项目代码',
                                                               `XIANGMUNAME` VARCHAR(32) NOT NULL COMMENT '收费项目名称',
                                                               `JILIANGDW` VARCHAR(32) NOT NULL COMMENT '计量单位（如次、剂，付、穴、g等）',
                                                               `JIAGE` VARCHAR(32) NOT NULL COMMENT '价格',
                                                               `PARENTXMCODE` VARCHAR(32) NOT NULL COMMENT '收费项目归属收费大项编码',
                                                               `PARENTXMNAME` VARCHAR(32) NOT NULL COMMENT '收费项目归属收费大项名称',
                                                               `EXECUTINGDEPTID` VARCHAR(32) DEFAULT NULL COMMENT '执行科室id (适宜技术)',
                                                               `EXECUTINGDEPTNAME` VARCHAR(32) DEFAULT NULL COMMENT '执行科室名称(适宜技术)',
                                                               `SFXMNOTES` VARCHAR(32) DEFAULT NULL COMMENT '备注，计价说明',
                                                               `XIUGAISJ` VARCHAR(32) DEFAULT NULL COMMENT '修改时间',
                                                               `ZUOFEIBZ` VARCHAR(32) DEFAULT NULL COMMENT '禁用作废标志（0可用，1禁用作废）',
                                                               `SHURUMA1` VARCHAR(32) NOT NULL COMMENT '拼音码',
                                                               `SHURUMA2` VARCHAR(32) DEFAULT NULL COMMENT '五笔码',
                                                               `YIBAOYPBM` VARCHAR(32) DEFAULT NULL COMMENT '项目医保编码',
                                                               `YIBAOYPBM2` VARCHAR(32) DEFAULT NULL COMMENT '项目医保国家（区域）编码',
                                                               `YIBAOYPMC` VARCHAR(32) DEFAULT NULL COMMENT '项目医保名称',
                                                               PRIMARY KEY (`his_id`)
) ;

COMMIT;

