START TRANSACTION;
ALTER TABLE `cbkj_web_parameter`.`t_charge_item_detail`
    CHANGE `item_detail_binding` `item_detail_binding` VARCHAR(20) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '项目绑定代码-字典表取',
    CHANGE `item_detail_binding_name` `item_detail_binding_name` VARCHAR(50) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '项目绑定名称-字典表取';



insert into `cbkj_web_parameter`.`t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) values ('shou_fei_mu_lu', '8', '收费目录', '2c133f183ef011edad8d00163f006620', NULL, '0', NULL, '8', '000000', '000000', '000000', '000000', '000000', '2025-03-19 09:38:14', '74230cda1fb8413b9968be3500a5441e', 'xiaolin', '0');

COMMIT;

