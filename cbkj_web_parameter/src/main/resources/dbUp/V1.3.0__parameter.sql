START TRANSACTION;
CREATE TABLE if not exists `sys_setting_info` (
                                    `set_id` int(11) NOT NULL AUTO_INCREMENT,
                                    `platform_sys_name` varchar(32) DEFAULT NULL COMMENT '配置系统名称',
                                    `pre_sys_name` varchar(32) DEFAULT NULL COMMENT '辅诊系统名称',
                                    `sys_logo` longtext COMMENT 'base64 图片',
                                    `insert_time` datetime DEFAULT NULL,
                                    `insert_user_id` varchar(32) DEFAULT NULL,
                                    `insert_user_name` varchar(128) DEFAULT NULL,
                                    PRIMARY KEY (`set_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

REPLACE INTO `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) VALUES('3101','名称管理','/system/title',NULL,'0','1','2022-05-24 11:04:37',NULL,'1',NULL,NULL,'1','17','2','0','1','1');

COMMIT;

