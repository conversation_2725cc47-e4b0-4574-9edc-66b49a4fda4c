START TRANSACTION;
ALTER TABLE `cbkj_web_parameter`.`t_display_dosage_cost`
    ADD INDEX (`dic_id`, `dic_type`);


ALTER TABLE `cbkj_web_parameter`.`t_display_dosage_form`
    ADD INDEX (`display_id`, `outpatient_or_hospitalization`);



CREATE INDEX idx_dosage_dicid_dictype_sort
    ON t_display_dosage_cost(dic_id, dic_type, sort);


CREATE INDEX idx_detail_id_itemid
    ON t_charge_item_detail(charge_item_detail_id, charge_item_id);


CREATE INDEX idx_mapping_match
    ON t_charge_item_mapping(charge_item_id, app_id, ins_code, dept_id);


ALTER TABLE `cbkj_web_parameter`.`t_display_currency`
    ADD COLUMN `urgent_sign` INT(1) DEFAULT 0 NULL COMMENT '加急标志 0 关 1开';

COMMIT;

