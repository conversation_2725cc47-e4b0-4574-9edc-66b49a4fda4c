START TRANSACTION;
CREATE TABLE `cbkj_web_parameter`.`t_charge_item`
(
    `charge_item_id`   VARCHAR(32) NOT NULL,
    `charge_item_code` VARCHAR(50) COMMENT '收费项目代码',
    `charge_item_name` VARCHAR(50) COMMENT '收费项目名称',
    `charge_item_type` VARCHAR(32) COMMENT '收费项目类型0 中心药房 1 HIS 2、通用',
    `charge_item_desc` VARCHAR(100) COMMENT '收费项目描述',
    `charge_item_py`   VARCHAR(100) COMMENT '收费项目拼音',
    `charge_item_wb`   VARCHAR(100) COMMENT '收费项目五笔',
    `status`           INT(1) COMMENT '0正常1删除',
    `create_user_name` VARCHAR(50),
    `create_user_id`   VARCHAR(32),
    `create_time`      DATETIME,
    PRIMARY KEY (`charge_item_id`)
);



CREATE TABLE `cbkj_web_parameter`.`t_charge_item_detail`
(
    `charge_item_detail_id`                 VARCHAR(32) NOT NULL,
    `item_detail_code`                      VARCHAR(50) NOT NULL COMMENT '收费编码',
    `item_detail_name`                      VARCHAR(50) NOT NULL COMMENT '项目名称',
    `charge_item_id`                        VARCHAR(32) NOT NULL,
    `item_detail_status`                    INT         NOT NULL COMMENT '0启用1停用',
    `item_detail_single_price`              DECIMAL(10, 4) COMMENT '单价',
    `item_detail_single_unit`               VARCHAR(10) COMMENT '单位',
    `item_detail_py`                        VARCHAR(50) COMMENT '拼音',
    `item_detail_wb`                        VARCHAR(50) COMMENT '五笔',
    `item_detail_acupoint_must`             INT(1)      NOT NULL COMMENT '穴位必填 0必填1非必填',
    `item_detail_type_name`                 VARCHAR(50) NOT NULL COMMENT '项目分类名称-字典表取',
    `item_detail_type`                      VARCHAR(20) NOT NULL COMMENT '项目分类代码-字典表取',
    `item_detail_binding`                   VARCHAR(20) NOT NULL COMMENT '项目绑定代码-字典表取',
    `item_detail_binding_name`              VARCHAR(50) NOT NULL COMMENT '项目绑定名称-字典表取',
    `item_detail_pre_check`                 INT(1)      NOT NULL DEFAULT 1 COMMENT '前置审核1关闭0开启',
    `item_detail_execute_status`            INT(1)      NOT NULL DEFAULT 1 COMMENT '执行科室-启用执行：1关闭 0执行',
    `item_detail_inner_text`                TEXT COMMENT '项目内涵',
    `item_detail_notes`                     TEXT COMMENT '备注',
    `create_user_id`                        VARCHAR(32),
    `create_user_name`                      VARCHAR(50),
    `create_time`                           DATETIME,
    `update_user_id`                        VARCHAR(32),
    `update_user_name`                      VARCHAR(50),
    `update_time`                           DATETIME,
    `medical_insurance_code`                VARCHAR(32) COMMENT '项目医保编码',
    `medical_insurance_country_region_code` VARCHAR(32) COMMENT '项目医保国家（区域）编码',
    PRIMARY KEY (`charge_item_detail_id`)
);



CREATE TABLE `t_charge_item_detail_dept`
(
    `id`                    int(10)     NOT NULL AUTO_INCREMENT,
    `charge_item_detail_id` varchar(32) NOT NULL,
    `dept_id`               varchar(32) DEFAULT NULL,
    `dept_name`             varchar(60) DEFAULT NULL,
    `app_id`                varchar(32) DEFAULT NULL,
    `app_name`              varchar(50) DEFAULT NULL,
    `ins_code`              varchar(32) DEFAULT NULL,
    `ins_name`              varchar(50) DEFAULT NULL,
    PRIMARY KEY (`id`)
);


CREATE TABLE `t_charge_item_mapping`
(
    `charge_item_id` varchar(32) NOT NULL COMMENT '收费项目id',
    `app_id`         varchar(32) NOT NULL COMMENT '医共体ID',
    `ins_code`       varchar(32) NOT NULL COMMENT '医疗机构代码',
    `dept_id`        varchar(32) NOT NULL COMMENT 'HIS科室ID',
    `create_date`    datetime    NOT NULL COMMENT '创建时间',
    `create_user`    varchar(32) NOT NULL COMMENT '创建人',
    KEY `charge_item_id` (`charge_item_id`)
);



COMMIT;

