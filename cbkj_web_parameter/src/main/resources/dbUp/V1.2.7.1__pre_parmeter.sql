REPLACE INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('89af44331abda15b3530edd2ad1fdf12', 'dis_GB2021', '中医疾病GB2021', '0', NULL, '0', NULL, '35', '000000', '000000', '000000', '000000', '000000', '2025-03-04 16:12:25', 'zjh', NULL, '0');
REPLACE INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('798068e6b349134d600c62e8d3e81cd0', 'sym_GB2021', '中医证型GB2021', '0', NULL, '0', NULL, '36', '000000', '000000', '000000', '000000', '000000', '2025-03-04 16:12:25', 'zjh', NULL, '0');
REPLACE INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('b095ee6565657ad26bf94d3c8a9f6194', 'the_GB2021', '中医治法GB2021', '0', NULL, '0', NULL, '37', '000000', '000000', '000000', '000000', '000000', '2025-03-04 16:12:25', 'zjh', NULL, '0');


INSERT INTO t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'NOT_PAY_PRE_UPDATE_BUTTON_DISPLAY', '未缴费处方-修改按钮是否展示', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '', '21', '1009', '未缴费处方-修改按钮是否展示', 'B250'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'NOT_PAY_PRE_UPDATE_BUTTON_DISPLAY'
);

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 0 FROM (
                                               SELECT tsp.PAR_ID
                                               FROM `t_sys_param` AS tsp
                                               WHERE tsp.PAR_CODE='NOT_PAY_PRE_UPDATE_BUTTON_DISPLAY'
                                           ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM `t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
);

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM `t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='NOT_PAY_PRE_UPDATE_BUTTON_DISPLAY'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM `t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
);



INSERT INTO t_sys_param
(PAR_ID, APP_ID, INS_CODE, DEPT_ID, PAR_CODE, PAR_NAME, PAR_VALUES, CREATE_DATE, CREATE_USER, create_user_name,
 STATUS, param_type, is_global, param_init_value, sort, menu_id, param_desc, par_number)
SELECT
    REPLACE(UUID(), '-', ''), '000000', '000000', '000000', 'WHETHER_THE_CHARGE_ITEM_IS_ENABLED', '适宜技术收费项目是否开启', '0', NOW(),
    'admin', 'admin', '0', '1', '0', '', '16', '1007', '0关（默认） 1开)', 'B249'
FROM DUAL WHERE  NOT EXISTS (
    SELECT APP_ID,INS_CODE,DEPT_ID,PAR_CODE FROM t_sys_param WHERE APP_ID = '000000' AND INS_CODE='000000' AND DEPT_ID='000000' AND PAR_CODE = 'WHETHER_THE_CHARGE_ITEM_IS_ENABLED'
);

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '0', '关', 0 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM `t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='WHETHER_THE_CHARGE_ITEM_IS_ENABLED'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM `t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='0' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='关'
);

INSERT INTO `t_sys_param_init_desc`( param_id,param_init_code,param_init_name,sort)
SELECT t.PAR_ID, '1', '开', 1 FROM (
                                       SELECT tsp.PAR_ID
                                       FROM `t_sys_param` AS tsp
                                       WHERE tsp.PAR_CODE='WHETHER_THE_CHARGE_ITEM_IS_ENABLED'
                                   ) AS t WHERE NOT EXISTS (
    SELECT 1
    FROM `t_sys_param_init_desc` AS tsp
    WHERE tsp.param_init_code='1' AND tsp.param_id=t.PAR_ID AND tsp.param_init_name='开'
);



UPDATE t_sys_param SET PAR_NAME = '处方自动作废时间，配置说明：门诊,住院(2000,1000)，若仅有一个值则通用，0值为不作废；'
WHERE par_number ='B210';

CREATE TABLE IF NOT EXISTS  `project_binding` (
                                                      `bid` INT(11) NOT NULL AUTO_INCREMENT,
                                                      `pay_code` VARCHAR(32) DEFAULT NULL COMMENT '收费编码',
                                                      `pro_name` VARCHAR(126) DEFAULT NULL COMMENT '项目名称',
                                                      `pro_connotation` TEXT COMMENT '项目内涵',
                                                      `unit_price` DECIMAL(10,4) DEFAULT NULL COMMENT '单价',
                                                      `unit_of_account` VARCHAR(11) DEFAULT NULL COMMENT '计价单位',
                                                      `sort_name` VARCHAR(32) DEFAULT NULL COMMENT '分类名称',
                                                      `sort_code` VARCHAR(11) DEFAULT NULL COMMENT '分类编码',
                                                      `pro_py` VARCHAR(32) DEFAULT NULL COMMENT '项目拼音',
                                                      `pro_wb` VARCHAR(32) DEFAULT NULL COMMENT '项目五笔',
                                                      `create_user` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
                                                      `create_date` DATE DEFAULT NULL COMMENT '创建时间',
                                                      `update_user` VARCHAR(32) DEFAULT NULL COMMENT '修改人',
                                                      `update_date` DATE DEFAULT NULL COMMENT '修改时间',
                                                      `memo` VARCHAR(255) DEFAULT NULL COMMENT '备注信息',
                                                      `status` VARCHAR(1) DEFAULT NULL COMMENT '状态【0:可用，1:禁用】',
                                                      KEY `bid` (`bid`)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='适宜技术收费项目绑定表';


CREATE TABLE IF NOT EXISTS `project_binding_mapping` (
                                           `bpm_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                           `pb_id` varchar(32) DEFAULT NULL COMMENT '项目绑定主键',
                                           `dic_id` varchar(32) DEFAULT NULL COMMENT '字典主键',
                                           `app_id` varchar(32) DEFAULT NULL COMMENT '医共体id',
                                           `ins_code` varchar(32) DEFAULT NULL COMMENT '医疗机构编码',
                                           `dept_id` varchar(32) DEFAULT NULL COMMENT '科室id',
                                           PRIMARY KEY (`bpm_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='项目绑定中间表';