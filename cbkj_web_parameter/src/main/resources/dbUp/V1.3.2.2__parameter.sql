START TRANSACTION;
REPLACE INTO cbkj_web_parameter.`t_dic_base` (
    `dic_id`,
    `dic_code`,
    `dic_name`,
    `parent_id`,
    `dic_desc`,
    `is_default`,
    `other_json`,
    `sort`,
    `app_id`,
    `ins_code`,
    `dept_id`,
    `pha_id`,
    `display_id`,
    `create_date`,
    `create_user`,
    `create_user_name`,
    `status`
)
VALUES
    (
        'inpatient_dosage_form',
        'inpatient_dosage_form',
        '住院剂型',
        '0',
        NULL,
        '0',
        NULL,
        NULL,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        '2025-08-04 15:39:50',
        'zjh',
        '导入',
        '0'
    );



REPLACE INTO cbkj_web_parameter.`t_dic_base` (
    `dic_id`,
    `dic_code`,
    `dic_name`,
    `parent_id`,
    `dic_desc`,
    `is_default`,
    `other_json`,
    `sort`,
    `app_id`,
    `ins_code`,
    `dept_id`,
    `pha_id`,
    `display_id`,
    `create_date`,
    `create_user`,
    `create_user_name`,
    `status`
)
VALUES
    (
        'outpatient_dosage_form',
        'outpatient_dosage_form',
        '门诊剂型',
        '0',
        NULL,
        '0',
        NULL,
        NULL,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        '2025-08-04 15:39:50',
        'zjh',
        '导入',
        '0'
    );

CREATE TABLE `cbkj_web_parameter`.`t_display_dosage_form`(
                                                             `set_id` VARCHAR(32) NOT NULL COMMENT '主键',
                                                             `display_id` VARCHAR(32) NOT NULL COMMENT '机构药房配置ID',
                                                             `outpatient_or_hospitalization` VARCHAR(1) COMMENT '门诊，住院（1门诊，2住院）',
                                                             `status` INT(1) COMMENT '状态0开启1关闭',
                                                             `expense_entry` INT(1) COMMENT '0录入入口开启1录入入口关闭',
                                                             `create_date` DATETIME,
                                                             `create_user` VARCHAR(32) COMMENT '创建人ID',
                                                             PRIMARY KEY (`set_id`)
);


CREATE TABLE `cbkj_web_parameter`.`t_display_dosage_describe`(
                                                                 `dosage_describe_id` VARCHAR(32) NOT NULL,
                                                                 `dic_id` VARCHAR(32) NOT NULL,
                                                                 `dic_type` INT(1) COMMENT '3.系统明细2.HIS明细1.药房明细',
                                                                 `daily_dosage_show` INT(1) DEFAULT 1 COMMENT '每日剂数（0显示1隐藏）',
                                                                 `daily_dosage_required` INT(1) DEFAULT 1 COMMENT '每日剂数（0必填1不必填）',
                                                                 `daily_preparations_show` INT(1) DEFAULT 1 COMMENT '每日制剂次数（0显示1隐藏）',
                                                                 `daily_preparations_required` INT(1) DEFAULT 1 COMMENT '每日制剂次数（0必填1不必填）',
                                                                 `daily_processing_show` INT(1) DEFAULT 1 COMMENT '每日加工量-0显示1隐藏',
                                                                 `daily_processing_required` INT(1) DEFAULT 1 COMMENT '每日加工量-0必填1不必填',
                                                                 `each_time_show` INT(1) DEFAULT 1 COMMENT '每次用量-0显示1隐藏',
                                                                 `each_time_required` INT(1) DEFAULT 1 COMMENT '每次用量-0必填1不必填',
                                                                 PRIMARY KEY (`dosage_describe_id`)
);



CREATE TABLE `cbkj_web_parameter`.`t_display_dosage_cost`(
                                                             `dosage_cost_id` VARCHAR(32) NOT NULL,
                                                             `dic_type` INT(1) NOT NULL COMMENT '3.系统明细2.HIS明细1.药房明细',
                                                             `dic_id` VARCHAR(32) NOT NULL,
                                                             `cost_catalogue_id` VARCHAR(32) NOT NULL COMMENT '收费目录id',
                                                             `cost_item_id` VARCHAR(32) NOT NULL COMMENT '收费项目id',
                                                             `pricing_method` INT(2) NOT NULL COMMENT '计价方式（按顺序从1开始）：按剂收取：按照处方剂数，计算公式：数量=剂数；总价=剂数*单价；按斤收取：按照处方总重量收取，计算公式：数量=(处方总重量g/500)；总价=(处方总重量g/500)*单价；按药味数收取：按照药味数收取，计算公式：数量=药味数；总价=药味数*单价；按克收费：按照处方总重量收取，计算公式：数量=处方总重量；总价=(处方总重量g)*单价；按次收取：处方张数计价，计算公式:数量=1；总价=1*单价；',
                                                             `doctor_edit` INT(1) NOT NULL DEFAULT 1 COMMENT '医生可编辑0可以 1不可以',
                                                             `sort` INT NOT NULL AUTO_INCREMENT COMMENT '顺序',
                                                             KEY(`sort`),
                                                             PRIMARY KEY (`dosage_cost_id`)
);

REPLACE INTO cbkj_web_parameter.`t_dic_base` (
    `dic_id`,
    `dic_code`,
    `dic_name`,
    `parent_id`,
    `dic_desc`,
    `is_default`,
    `other_json`,
    `sort`,
    `app_id`,
    `ins_code`,
    `dept_id`,
    `pha_id`,
    `display_id`,
    `create_date`,
    `create_user`,
    `create_user_name`,
    `status`
)
VALUES
    (
        'item_detail_type_id',
        'item_detail_type',
        '收费项目分类',
        '0',
        NULL,
        '0',
        NULL,
        NULL,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    );

REPLACE INTO cbkj_web_parameter.`t_dic_base` (
    `dic_id`,
    `dic_code`,
    `dic_name`,
    `parent_id`,
    `dic_desc`,
    `is_default`,
    `other_json`,
    `sort`,
    `app_id`,
    `ins_code`,
    `dept_id`,
    `pha_id`,
    `display_id`,
    `create_date`,
    `create_user`,
    `create_user_name`,
    `status`
)
VALUES
    (
        'dic_charge_binding_id',
        'charge_binding',
        '收费项目绑定',
        '0',
        NULL,
        '0',
        NULL,
        NULL,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    );


REPLACE INTO cbkj_web_parameter.`t_dic_base` (
    `dic_id`,
    `dic_code`,
    `dic_name`,
    `parent_id`,
    `dic_desc`,
    `is_default`,
    `other_json`,
    `sort`,
    `app_id`,
    `ins_code`,
    `dept_id`,
    `pha_id`,
    `display_id`,
    `create_date`,
    `create_user`,
    `create_user_name`,
    `status`
)
VALUES
    (
        '19a6d71c71a811f0a36000163f006620',
        '01',
        '灸法',
        'item_detail_type_id',
        NULL,
        '0',
        NULL,
        1,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '4d16046871a811f0a36000163f006620',
        '02',
        '中医特殊疗法',
        'item_detail_type_id',
        NULL,
        '0',
        NULL,
        2,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '63abdf4871a811f0a36000163f006620',
        '03',
        '推拿疗法',
        'item_detail_type_id',
        NULL,
        '0',
        NULL,
        3,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '7145b86771a811f0a36000163f006620',
        '04',
        '中医骨伤',
        'item_detail_type_id',
        NULL,
        '0',
        NULL,
        4,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '808d261e71a811f0a36000163f006620',
        '05',
        '中医外治',
        'item_detail_type_id',
        NULL,
        '0',
        NULL,
        5,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '91560bdb71a811f0a36000163f006620',
        '06',
        '中医肛肠',
        'item_detail_type_id',
        NULL,
        '0',
        NULL,
        6,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '9d3d717a71a811f0a36000163f006620',
        '11',
        '煎药费',
        'item_detail_type_id',
        NULL,
        '1',
        NULL,
        11,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    );





REPLACE INTO cbkj_web_parameter.`t_dic_base` (
    `dic_id`,
    `dic_code`,
    `dic_name`,
    `parent_id`,
    `dic_desc`,
    `is_default`,
    `other_json`,
    `sort`,
    `app_id`,
    `ins_code`,
    `dept_id`,
    `pha_id`,
    `display_id`,
    `create_date`,
    `create_user`,
    `create_user_name`,
    `status`
)
VALUES
    (
        '614ee12671a911f0a36000163f006620',
        '-1',
        '针灸',
        'dic_charge_binding_id',
        NULL,
        '0',
        NULL,
        1,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '8a89a89271a911f0a36000163f006620',
        '0',
        '艾灸',
        'dic_charge_binding_id',
        NULL,
        '0',
        NULL,
        1,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
        '8e44fc8771a911f0a36000163f006620',
        '1',
        '拔罐',
        'dic_charge_binding_id',
        NULL,
        '0',
        NULL,
        2,
        '000000',
        '000000',
        '000000',
        '000000',
        '000000',
        now(),
        'zjh',
        '导入',
        '0'
    ),(
    '1967c2dd825611f0aa7a00163f006620',
    '2',
    '推拿',
    'dic_charge_binding_id',
    NULL,
    '0',
    NULL,
    3,
    '000000',
    '000000',
    '000000',
    '000000',
    '000000',
    now(),
    'zjh',
    '导入',
    '0'
);




COMMIT;

