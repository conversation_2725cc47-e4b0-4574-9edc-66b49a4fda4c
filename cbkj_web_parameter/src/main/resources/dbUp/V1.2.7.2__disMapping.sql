START TRANSACTION;

REPLACE INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('89af44331abda15b3530edd2ad1fdf12', 'dis_GB2021', '中医疾病GB2021', '0', NULL, '0', NULL, '35', '000000', '000000', '000000', '000000', '000000', '2025-03-04 16:12:25', 'zjh', NULL, '0');
REPLACE INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('798068e6b349134d600c62e8d3e81cd0', 'sym_GB2021', '中医证型GB2021', '0', NULL, '0', NULL, '36', '000000', '000000', '000000', '000000', '000000', '2025-03-04 16:12:25', 'zjh', NULL, '0');
REPLACE INTO `t_dic_base` (`dic_id`, `dic_code`, `dic_name`, `parent_id`, `dic_desc`, `is_default`, `other_json`, `sort`, `app_id`, `ins_code`, `dept_id`, `pha_id`, `display_id`, `create_date`, `create_user`, `create_user_name`, `status`) VALUES ('b095ee6565657ad26bf94d3c8a9f6194', 'the_GB2021', '中医治法GB2021', '0', NULL, '0', NULL, '37', '000000', '000000', '000000', '000000', '000000', '2025-03-04 16:12:25', 'zjh', NULL, '0');

DELETE FROM t_dic_mapping WHERE dic_id = '89af44331abda15b3530edd2ad1fdf12' ;
DELETE FROM t_dic_mapping WHERE dic_id = '798068e6b349134d600c62e8d3e81cd0' ;
DELETE FROM t_dic_mapping WHERE dic_id = 'b095ee6565657ad26bf94d3c8a9f6194' ;


REPLACE INTO t_dic_mapping(id,dic_id,stan_id,stan_type)
VALUES(
          'dis_his_mapping',
          '89af44331abda15b3530edd2ad1fdf12','d199a149ae6511eca58a0800270fef61','0');

REPLACE INTO t_dic_mapping(id,dic_id,stan_id,stan_type)
VALUES(
          'sym_his_mapping',
          '798068e6b349134d600c62e8d3e81cd0','d199a149ae6511eca58a0800270fef62','0');

REPLACE INTO t_dic_mapping(id,dic_id,stan_id,stan_type)
VALUES(
          'the_his_mapping',
          'b095ee6565657ad26bf94d3c8a9f6194','d199a149ae6511eca58a0800270fef65','0');



UPDATE cbkj_web_api.b_mapping_disease SET DIS_ID_HIS = REPLACE(MD5(UUID()),'-','') where DIS_ID_HIS is null;



UPDATE cbkj_web_api.b_mapping_symptom SET SYM_ID_HIS = REPLACE(MD5(UUID()),'-','') where SYM_ID_HIS is null;



INSERT  into `t_dic_base`
(`dic_id`, `dic_code`,
 `dic_name`, `parent_id`,
 `dic_desc`, `is_default`,
 `other_json`, `sort`,
 `app_id`, `ins_code`,
 `dept_id`, `pha_id`,
 `display_id`, `create_date`,
 `create_user`, `create_user_name`,
 `status`)
SELECT DIS_ID_HIS,DIS_CODE_HIS,
       DIS_NAME_HIS,'89af44331abda15b3530edd2ad1fdf12',
       NULL,0,
       NULL,'0',APP_ID,'000000','000000','000000','000000',NOW(),'zjh','zjh','0' from cbkj_web_api.b_mapping_disease where DIS_CODE_HIS IS NOT NULL and DIS_CODE_HIS <> '';


INSERT  into `t_dic_base`
(`dic_id`, `dic_code`,
 `dic_name`, `parent_id`,
 `dic_desc`, `is_default`,
 `other_json`, `sort`,
 `app_id`, `ins_code`,
 `dept_id`, `pha_id`,
 `display_id`, `create_date`,
 `create_user`, `create_user_name`,
 `status`)
SELECT SYM_ID_HIS,SYM_CODE_HIS,
       SYM_NAME_HIS,'798068e6b349134d600c62e8d3e81cd0',
       NULL,0,
       NULL,'0',APP_ID,'000000','000000','000000','000000',NOW(),'zjh','zjh','0' FROM cbkj_web_api.b_mapping_symptom where SYM_CODE_HIS IS NOT NULL and SYM_CODE_HIS <> '';





replace into t_dic_standard(
    stan_id,stan_code,stan_name
    ,parent_id,stan_desc,stan_type,
    sort,create_date,create_user,create_user_name,
    STATUS
)
SELECT
    DIS_ID_SYS, DIS_CODE_SYS,DIS_NAME_SYS,
    'd199a149ae6511eca58a0800270fef61',NULL,'0',
    '0',NOW(),'zjh','zjh','0'
FROM cbkj_web_api.b_mapping_disease WHERE DIS_CODE_SYS IS NOT NULL AND DIS_CODE_SYS <> ''  ;


replace into t_dic_standard(
    stan_id,stan_code,stan_name
    ,parent_id,stan_desc,stan_type,
    sort,create_date,create_user,create_user_name,
    STATUS
)
SELECT
    SYM_ID_SYS, SYM_CODE_SYS,SYM_NAME_SYS,
    'd199a149ae6511eca58a0800270fef62',NULL,'0',
    '0',NOW(),'zjh','zjh','0'
FROM cbkj_web_api.b_mapping_symptom WHERE SYM_CODE_SYS IS NOT NULL AND SYM_CODE_SYS <> '' ;


INSERT INTO t_dic_mapping(
    id,dic_id,stan_id,stan_type
)
SELECT REPLACE(MD5(UUID()),'-',''),DIS_ID_HIS,DIS_ID_SYS,'0' FROM
    cbkj_web_api.`b_mapping_disease` WHERE DIS_CODE_SYS IS NOT NULL AND DIS_CODE_SYS <> ''  ;



INSERT INTO t_dic_mapping(
    id,dic_id,stan_id,stan_type
)
SELECT REPLACE(MD5(UUID()),'-',''),SYM_ID_HIS,SYM_ID_SYS,'0' FROM
    cbkj_web_api.`b_mapping_symptom` WHERE SYM_CODE_SYS IS NOT NULL AND SYM_CODE_SYS <> '' ;








ALTER TABLE `t_dic_mapping`
    ADD INDEX (`dic_id`),
    ADD INDEX (`stan_id`);

COMMIT;