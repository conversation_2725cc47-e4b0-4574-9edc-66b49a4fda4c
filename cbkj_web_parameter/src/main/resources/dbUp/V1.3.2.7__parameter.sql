START TRANSACTION;
replace into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('304','收费项目管理',NULL,NULL,'0','3',NULL,NULL,'1',NULL,NULL,'1','2','2','0','1','1');
replace into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('3041','收费目录','/charge-manage/catalog/list',NULL,'0','304',NULL,NULL,'1',NULL,NULL,'1','1','3','0','1','1');
replace into `sys_admin_menu` (`menu_id`, `menu_name`, `menu_path`, `menu_class`, `status`, `parent_menu_id`, `create_date`, `cteate_user`, `menu_type`, `btn_class`, `btn_type`, `btn_weight`, `sort`, `menu_level`, `menu_open`, `open_type`, `modual_code`) values('3042','收费项目明细','/charge-manage/directory/list',NULL,'0','304',NULL,NULL,'1',NULL,NULL,'1','2','3','0','1','1');
COMMIT;

