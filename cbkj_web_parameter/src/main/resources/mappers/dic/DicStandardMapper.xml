<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.dic.DicStandardMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.dic.TDicStandard">
        <id column="stan_id" jdbcType="VARCHAR" property="stanId"/>
        <result column="stan_code" jdbcType="VARCHAR" property="stanCode"/>
        <result column="stan_name" jdbcType="VARCHAR" property="stanName"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="stan_desc" jdbcType="VARCHAR" property="stanDesc"/>
        <result column="stan_type" jdbcType="VARCHAR" property="stanType"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="dic_id" jdbcType="VARCHAR" property="dicId"/>
    </resultMap>


    <sql id="Base_Column_List">
        stan_id
        ,stan_code,stan_name,parent_id,stan_desc,stan_type,sort,create_date,create_user,create_user_name,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDicStandard">
        delete
        from t_dic_standard
        where stan_id = #{ stanId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_dic_standard where stan_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="TDicStandard">
        insert into t_dic_standard (<include refid="Base_Column_List"/>) values
        (#{stanId},#{stanCode},#{stanName},#{parentId},#{stanDesc},#{stanType},#{sort},#{createDate},#{createUser},#{createUserName},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_dic_standard (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.stanId},#{item.stanCode},#{item.stanName},#{item.parentId},#{item.stanDesc},#{item.stanType},#{item.sort},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDicStandard">
        update t_dic_standard
        <set>
            <if test="stanCode != null and stanCode!=''">
                stan_code = #{ stanCode },
            </if>
            <if test="stanName != null and stanName!=''">
                stan_name = #{ stanName },
            </if>
            <if test="parentId != null and parentId!=''">
                parent_id = #{ parentId },
            </if>
            <if test="stanDesc != null and stanDesc!=''">
                stan_desc = #{ stanDesc },
            </if>
            <if test="stanType != null and stanType!=''">
                stan_type = #{ stanType },
            </if>
            <if test="sort != null and sort!=''">
                sort = #{ sort },
            </if>
            <if test="createDate != null and createDate!=''">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null and createUser!=''">
                create_user = #{ createUser },
            </if>
            <if test="createUserName != null and createUserName!=''">
                create_user_name = #{ createUserName },
            </if>
            <if test="status != null and status!=''">
                status = #{status},
            </if>
        </set>
        where stan_id = #{ stanId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_dic_standard where stan_id = #{ stanId }
    </select>

    <select id="getPageListByObj" parameterType="TDicStandard" resultMap="BaseResultMap">
        SELECT
        tdb.stan_id,
        tdb.stan_code,
        tdb.stan_name,
        tdb.stan_desc,
        tdb.stan_type,
        tdb.status,
        tdb.sort
        FROM t_dic_standard AS tdb
        <!-- INNER JOIN t_dic_standard AS tdbb ON -->
        WHERE  tdb.parent_id = (SELECT stan_id
        FROM t_dic_mapping
        where dic_id = #{dicId} limit 1)
        <if test="keyWord !=null and keyWord!='' ">
            and (tdb.stan_name like CONCAT('%',trim(#{keyWord}),'%') or tdb.stan_code like
            CONCAT('%',trim(#{keyWord}),'%') )
        </if>
        <if test=" stanType != null and stanType!='' ">
            and tdb.stan_type=#{stanType}
        </if>
        and tdb.status='0'
    </select>
    <select id="getFname" resultMap="BaseResultMap">
        select stan_id, stan_name, stan_code
        from t_dic_standard
        where parent_id = '0'
    </select>
    <select id="getFCode" parameterType="TDicStandard" resultMap="BaseResultMap">
        select tb.stan_id, tb.stan_name, tb.stan_code
        from t_dic_standard as tb
                 left join t_dic_mapping as s on s.stan_id = tb.stan_id
        where parent_id = '0'
          and s.dic_id = #{dicId}
    </select>

    <select id="getTDicStandard" parameterType="TDicStandard" resultMap="BaseResultMap">
        SELECT


        tds.stan_id,
        tds.stan_code,
        tds.stan_name,
        tds.stan_desc,
        tds.stan_type,
        tds.status,
        tds.sort
        FROM t_dic_standard tds where tds.parent_id = #{stanId}
        <if test=" stanType != null and stanType!='' ">
            and tds.stan_type=#{stanType}
        </if>
        <if test=" keyWord != null and keyWord!='' ">
            and (tds.stan_name like CONCAT('%',trim(#{keyWord}),'%') or tds.stan_code like
            CONCAT('%',trim(#{keyWord}),'%') )
        </if>
<!--     SELECT distinct


        tds.stan_id,
        tds.stan_code,
        tds.stan_name,
        tds.stan_desc,
        tds.stan_type,
        tds.status,
        tds.sort
        FROM t_dic_standard AS tds
        inner JOIN t_dic_standard AS tdbb ON tds.parent_id = #{stanId}
        where 1=1
        <if test=" keyWord != null and keyWord!='' ">
            and (tds.stan_name like CONCAT('%',trim(#{keyWord}),'%') or tds.stan_code like
            CONCAT('%',trim(#{keyWord}),'%') )
        </if>
        <if test=" stanType != null and stanType!='' ">
            and tds.stan_type=#{stanType}
        </if>
        -->
    </select>
    <select id="getMaxSort" parameterType="string" resultMap="BaseResultMap">
        SELECT MAX(sort)
        FROM t_dic_standard
        WHERE stan_id = #{stanId}
    </select>
    <select id="findOneTDicStandard" parameterType="string" resultMap="BaseResultMap">
        SELECT *
        from t_dic_standard
        where stan_code = #{stanCode} and stan_type=#{stanType}
    </select>

</mapper>