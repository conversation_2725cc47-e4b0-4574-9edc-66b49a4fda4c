<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.dic.DicBaseMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.dic.TDicBase">
        <id column="dic_id" jdbcType="VARCHAR" property="dicId"/>
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
        <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
        <result column="stan_code" jdbcType="VARCHAR" property="stanCode"/>
        <result column="stan_name" jdbcType="VARCHAR" property="stanName"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <result column="dic_desc" jdbcType="VARCHAR" property="dicDesc"/>
        <result column="is_default" jdbcType="VARCHAR" property="isDefault"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="display_id" jdbcType="VARCHAR" property="displayId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="mapping" jdbcType="VARCHAR" property="mapping"/>
        <result column="id" jdbcType="VARCHAR" property="mapId"/>
        <!---->
        <result column="display_name" jdbcType="VARCHAR" property="displayName"/>
        <result column="app_name" jdbcType="VARCHAR" property="appName"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="other_json" jdbcType="VARCHAR" property="otherJson"/>

    </resultMap>

    <resultMap id="BaseResultMap1" type="com.jiuzhekan.cbkj.beans.dic.DicNodeDto">
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
        <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.dic.DicNode">
        <id column="dic_id" jdbcType="VARCHAR" property="dicId"/>
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
        <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="dic_desc" jdbcType="VARCHAR" property="dicDesc"/>
        <result column="is_default" jdbcType="VARCHAR" property="isDefault"/>
        <result column="other_json" jdbcType="VARCHAR" property="otherJson"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="display_id" jdbcType="VARCHAR" property="displayId"/>
        <result column="stan_id" jdbcType="VARCHAR" property="stanId"/>
        <result column="stan_code" jdbcType="VARCHAR" property="stanCode"/>
        <result column="stan_name" jdbcType="VARCHAR" property="stanName"/>
    </resultMap>


    <sql id="Base_Column_List">
        dic_id
        ,
        dic_code,
        dic_name,
        parent_id,
        is_default,
        dic_desc,
        sort,
        app_id,
        ins_code,
        dept_id,
        display_id,
        create_date,
        create_user,
        create_user_name,
        status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDicBase">
        update t_dic_base
        set status='1'
        where dic_id = #{ dicId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_dic_base where dic_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="TDicBase">
        insert into t_dic_base (<include refid="Base_Column_List"/>) values
        (#{dicId},#{dicCode},#{dicName},#{parentId},#{isDefault},#{dicDesc},#{sort},#{appId},#{insCode},#{deptId},#{displayId},#{createDate},#{createUser},#{createUserName},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_dic_base (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.dicId},#{item.dicCode},#{item.dicName},#{item.parentId},#{item.isDefault},#{item.dicDesc},#{item.sort},#{item.appId},#{item.insCode},#{item.deptId},#{item.displayId},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDicBase">
        update t_dic_base
        <set>
            <if test="dicCode != null and dicCode!=''">
                dic_code = #{ dicCode },
            </if>
            <if test="dicName != null and dicName!=''">
                dic_name = #{ dicName },
            </if>
            <if test="parentId != null and parentId!=''">
                parent_id = #{ parentId },
            </if>
            <if test="isDefault != null and isDefault!=''">
                is_default = #{isDefault},
            </if>
            <if test="dicDesc != null and dicDesc!=''">
                dic_desc = #{ dicDesc },
            </if>
            <if test="sort != null ">
                sort = #{ sort },
            </if>
            <if test="appId != null and appId!=''">
                app_id = #{ appId },
            </if>
            <if test="insCode != null and insCode!=''">
                ins_code = #{ insCode },
            </if>
            <if test="deptId != null and deptId!=''">
                dept_id = #{ deptId },
            </if>
            <if test="displayId != null and displayId!=''">
                display_id = #{ displayId },
            </if>


            <if test="otherJson != null and otherJson!=''">
                other_json = #{ otherJson },
            </if>
        </set>
        where dic_id = #{ dicId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        SELECT tdb.dic_id,
               tdb.dic_code,
               tdb.dic_name,
               sa.app_name,
               si.ins_name,
               tdb.parent_id,
               tb.dic_name as parent_name,
               tdb.dic_desc,
               tdb.is_default,
               tdb.other_json,
               tdb.sort,
               tdb.app_id,
               tdb.ins_code,
               tdb.dept_id,
               tdb.display_id,
               tdb.create_date,
               tdb.create_user,
               tdb.create_user_name,
               tdb.status
        FROM t_dic_base as tdb
                 JOIN t_dic_base AS tb ON tb.dic_id = tdb.parent_id and tb.status = '0'
                 left join sys_app sa on tdb.app_id = sa.app_id and sa.status = '0'
                 left join sys_institution si on tdb.ins_code = si.ins_code and si.status = '0'
        where tdb.dic_id = #{dicId}
    </select>

    <select id="getPageListByObj" parameterType="TDicBase" resultMap="BaseResultMap">
        SELECT
        dic_id,dic_code,dic_name,parent_id,dic_desc,sort,app_id,ins_code,dept_id,display_id,create_date,create_user,create_user_name,status
        from t_dic_base
        <where>
            <if test=" dicName != null and dicName!='' ">
                and dic_name like CONCAT('%',trim(#{dicName}),'%')
            </if>
            <if test=" dicCode != null and dicCode!='' ">
                and dic_code like CONCAT('%',trim(#{dicCode}),'%')
            </if>
        </where>
    </select>
    <!-- tdb.dic_code,tdb.dic_name,tdb.sort,tds.stan_code,tds.stan_name,tdm.id-->
    <select id="getPageListNotInMapping" parameterType="TDicBase" resultMap="BaseResultMap">
        SELECT
        tdb.dic_id,
        tdb.dic_code,
        tdb.dic_name,
        tdb.sort,
        tdb.parent_id,
        tdb.dic_desc,
        tdb.app_id,
        tdb.ins_code,
        tdb.dept_id,
        tdb.display_id,
        tds.stan_code,
        tds.stan_name,
        tdm.id
        FROM
        t_dic_base AS tdb
        left JOIN t_dic_base AS tb ON tb.dic_id=tdb.parent_id
        LEFT JOIN t_dic_mapping AS tdm ON tdb.dic_id=tdm.dic_id
        LEFT JOIN t_dic_standard AS tds ON tds.stan_id=tdm.stan_id
        WHERE tdb.dic_id NOT IN(SELECT dic_id FROM t_dic_mapping)
        AND
        tdb.parent_id=#{dicId} and tb.status='0'
        <if test=" keyWord != null and keyWord!='' ">
            and (tdb.dic_name like CONCAT('%',trim(#{keyWord}),'%') or tdb.dic_code like
            CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        <if test=" appId != null and appId!='' ">
            and tdb.app_id =#{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and tdb.ins_code =#{insCode}
        </if>
        <if test=" deptId != null and deptId!='' ">
            and tdb.dept_id =#{deptId}
        </if>
        <if test=" displayId != null and displayId!='' ">
            and tdb.display_id =#{displayId}
        </if>
    </select>
    <select id="getPageListInMapping" parameterType="TDicBase" resultMap="BaseResultMap">
        SELECT
        tdm.id,
        tdb.dic_id,
        tdb.dic_code,
        tdb.dic_name,
        tdb.sort,
        tdb.parent_id,
        tdb.dic_desc,
        tdb.app_id,
        tdb.ins_code,
        tdb.dept_id,
        tdb.display_id,
        tds.stan_code,
        tds.stan_name
        FROM
        t_dic_base AS tdb
        LEFT JOIN t_dic_mapping AS tdm ON tdb.dic_id=tdm.dic_id
        LEFT JOIN t_dic_standard AS tds ON tds.stan_id=tdm.stan_id
        LEFT JOIN t_dic_base AS tb ON tb.dic_id=tdb.parent_id
        WHERE tdb.dic_id IN(SELECT dic_id FROM t_dic_mapping)AND tdb.`parent_id`=#{dicId} and tb.status='0'
        <if test=" keyWord != null and keyWord!='' ">
            and (tdb.dic_name like CONCAT('%',trim(#{keyWord}),'%') or tdb.dic_code like
            CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        <if test=" appId != null and appId!='' ">
            and tdb.app_id =#{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and tdb.ins_code =#{insCode}
        </if>
        <if test=" deptId != null and deptId!='' ">
            and tdb.dept_id =#{deptId}
        </if>
        <if test=" displayId != null and displayId!='' ">
            and tdb.display_id =#{displayId}
        </if>
    </select>
    <select id="getFname" resultMap="BaseResultMap">
        SELECT dic_id, dic_code, dic_name
        FROM t_dic_base
        WHERE parent_id = '0'
          and status = '0'
        ORDER BY sort ASC
    </select>
    <select id="getDicByName" resultMap="BaseResultMap" parameterType="String">
        select *
        from t_dic_base
        where dic_name = #{dicName}
    </select>
    <select id="getCareer" parameterType="string" resultMap="BaseResultMap">
        SELECT tdb.dic_id, tdb.dic_code, tdb.dic_name
        FROM t_dic_base AS tdb
                 LEFT JOIN t_dic_base AS tds ON tdb.parent_id = tds.dic_id
        WHERE tds.`dic_code` = #{dicCode}
          and tdb.status = '0' ORDER BY tdb.sort
    </select>

    <select id="getParentName" parameterType="TDicBase" resultMap="BaseResultMap">
        select *,
        IF(
        (SELECT COUNT(*)

        FROM t_dic_base AS td
        JOIN t_dic_base AS tb ON tb.dic_id = td.parent_id AND tb.status='0'
        WHERE td.parent_id = a.`dic_id`
        AND td.app_id = '000000'
        AND td.ins_code = '000000'
        AND td.dept_id = '000000'
        AND td.display_id = '000000'
        AND td.status !='1'  )>0,'true','false'


        ) AS sysChecked,
        IF(

        (

        SELECT COUNT(*)
        FROM t_dic_base AS tdb
        JOIN t_dic_base AS tb ON tb.dic_id = tdb.parent_id AND tb.status='0'
        LEFT JOIN sys_app sa ON tdb.app_id = sa.app_id AND sa.status = '0'
        LEFT JOIN sys_institution si ON tdb.ins_code = si.ins_code AND si.status = '0'
        WHERE tdb.parent_id =a.`dic_id` AND tdb.app_id !='000000' AND tdb.display_id='000000'
        AND tdb.status !='1'
        )  > 0 ,'true','false'

        ) AS hisChecked,

        IF(
        (
        SELECT COUNT(*)
        FROM t_dic_base
        WHERE parent_id = a.`dic_id`
        AND display_id!='000000' AND STATUS !='1'
        )  > 0,'true','false'
        ) AS displayChecked

               from t_dic_base as a where a.parent_id='0' and a.status != '1'
        <if test=" dicId  != null and  dicId !=''">
            and a.dic_id = #{dicId}
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (a.dic_name like CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        order by a.create_date desc
    </select>
    <select id="getSystemCodeItem" parameterType="TDicBase" resultMap="BaseResultMap">
        SELECT td.dic_id,
        td.dic_code,
        td.dic_name,
        td.parent_id,
        tb.dic_name as parent_name,
        td.dic_desc,
        td.is_default,
        td.other_json,
        td.sort,
        td.app_id,
        td.ins_code,
        td.dept_id,
        td.display_id,
        td.create_date,
        td.create_user,
        td.create_user_name,
        td.status,
        if( (select count(*) from t_display_dosage_cost as a where a.dic_id=td.dic_id) > 0, 1, 0 ) as dosageCostStatus,
        if( (select count(*) from t_display_dosage_describe as a where a.dic_id=td.dic_id) > 0, 1, 0 ) as dosageDescribeStatus
        FROM t_dic_base AS td
        JOIN t_dic_base AS tb ON tb.dic_id = td.parent_id and tb.status='0'
        WHERE td.parent_id = #{parentId}
        AND td.app_id = '000000'
        AND td.ins_code = '000000'
        AND td.dept_id = '000000'
        AND td.display_id = '000000'
        and td.status !='1'
        <if test="keyWord!=null and keyWord!=''">
            and (td.dic_name like CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        order by td.sort asc
    </select>
    <select id="getHisItem" parameterType="TDicBase" resultMap="BaseResultMap">
        SELECT tdb.dic_id,
        tdb.dic_code,
        tdb.dic_name,
        sa.app_name,
        si.ins_name,
        tdb.parent_id,
        tb.dic_name as parent_name,
        tdb.dic_desc,
        tdb.is_default,
        tdb.other_json,
        tdb.sort,
        tdb.app_id,
        tdb.ins_code,
        tdb.dept_id,
        tdb.display_id,
        tdb.create_date,
        tdb.create_user,
        tdb.create_user_name,
        tdb.status,
        if( (select count(*) from t_display_dosage_cost as a where a.dic_id=tdb.dic_id) > 0, 1, 0 ) as dosageCostStatus,
        if( (select count(*) from t_display_dosage_describe as a where a.dic_id=tdb.dic_id) > 0, 1, 0 ) as dosageDescribeStatus
        FROM t_dic_base as tdb
        JOIN t_dic_base AS tb ON tb.dic_id = tdb.parent_id and tb.status='0'
        left join sys_app sa on tdb.app_id = sa.app_id and sa.status = '0'
        left join sys_institution si on tdb.ins_code = si.ins_code and si.status = '0'
        WHERE tdb.parent_id =#{parentId} AND tdb.app_id !='000000' AND tdb.display_id='000000'
        and tdb.status !='1'
        <if test=" appId != null and appId!='' ">
            and tdb.app_id =#{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and tdb.ins_code =#{insCode}
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (tdb.dic_name like CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        order by tdb.sort asc
    </select>
    <select id="getPharmacyItem" parameterType="TDicBase" resultMap="BaseResultMap">
        SELECT tdb.dic_id,
        tdb.dic_code,
        tdb.dic_name,
        td.display_name,
        td.app_id,
        td.ins_code,
        tdb.parent_id,
        tb.dic_name as parent_name,
        tdb.dic_desc,
        tdb.is_default,
        tdb.other_json,
        tdb.sort,
        tdb.dept_id,
        tdb.display_id,
        tdb.create_date,
        tdb.create_user,
        tdb.create_user_name,
        tdb.status,
        if( (select count(*) from t_display_dosage_cost as a where a.dic_id=tdb.dic_id) > 0, 1, 0 ) as dosageCostStatus,
        if( (select count(*) from t_display_dosage_describe as a where a.dic_id=tdb.dic_id) > 0, 1, 0 ) as dosageDescribeStatus
        FROM t_dic_base tdb
        JOIN t_dic_base AS tb ON tb.dic_id = tdb.parent_id and tb.status='0'
        join t_display td on tdb.display_id = td.id and td.status = '0'
        WHERE tdb.parent_id = #{parentId}
        AND tdb.display_id = #{displayId}
        and tdb.status != '1'
        <if test="keyWord!=null and keyWord!=''">
            and (tdb.dic_name like CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        order by tdb.sort asc
    </select>
    <select id="getDisplayItem" parameterType="TDicBase" resultMap="BaseResultMap">
        select *
        from t_dic_base
        where parent_id = #{parentId}
          and display_id!='000000' and status !='1'
    </select>
    <select id="getDisplayByDicCodeAndParentId" parameterType="TDicBase" resultMap="BaseResultMap">
        select *
        from t_dic_base
        where parent_id = #{parentId}
        and dic_code = #{dicCode}
        <if test=" appId != null and appId!='' ">
            and app_id=#{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and ins_code =#{insCode}
        </if>
        <if test=" deptId != null and deptId!='' ">
            and dept_id =#{deptId}
        </if>
        <if test=" displayId != null and displayId!='' ">
            and display_id =#{displayId}
        </if>
        and status !='1'
    </select>
    <select id="getItemByParentId" parameterType="string" resultMap="BaseResultMap">
        SELECT *
        from t_dic_base
        where dic_id = #{dicId}
    </select>
    <select id="getFeeDictionaryParent" parameterType="string" resultMap="BaseResultMap">
        SELECT dic_id,dic_name
        FROM t_dic_base
        where parent_id=#{parentId}
    </select>


    <select id="getDetailByCode" parameterType="String" resultMap="BaseResultMap2">
        SELECT dic_id,dic_code,dic_name,parent_id,dic_desc, is_default, other_json
        from t_dic_base
        where status = '0' and dic_code = #{dicCode}
        order by app_id,ins_code,dept_id,display_id,sort
            limit 1
    </select>

    <select id="getChildren" parameterType="com.jiuzhekan.cbkj.beans.dic.DicNode" resultMap="BaseResultMap1">
        select   child.dic_code, child.dic_name
        from t_dic_base child
        join t_dic_base parent on child.parent_id = parent.dic_id and parent.status = '0'
        where child.status = '0'
        <if test=" dicId != null and dicId!='' ">
            and parent.dic_id = #{dicId}
        </if>
        <if test=" dicCode != null and dicCode!='' ">
            and parent.dic_code = #{dicCode}
        </if>
        <if test=" appId != null and appId!='' ">
            and child.app_id = #{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and child.ins_code = #{insCode}
        </if>
        <if test=" deptId != null and deptId!='' ">
            and child.dept_id = #{deptId}
        </if>
        <if test=" displayId != null and displayId!='' ">
            and child.display_id = #{displayId}
        </if>
        order by child.sort
    </select>
</mapper>