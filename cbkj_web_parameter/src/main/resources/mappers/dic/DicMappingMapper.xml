<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.dic.DicMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.dic.TDicMapping">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="dic_id" jdbcType="VARCHAR" property="dicId" />
        <result column="stan_id" jdbcType="VARCHAR" property="stanId" />
        <result column="stan_type" jdbcType="VARCHAR" property="stanType" />
    </resultMap>


    <sql id="Base_Column_List">
    id,dic_id,stan_id,stan_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDicMapping">
        delete from t_dic_mapping where id = #{ id }
    </delete>

    <delete id="deletePrimaryKey" parameterType="string">
        delete from t_dic_mapping where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_dic_mapping where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDicMapping">
        insert into t_dic_mapping (<include refid="Base_Column_List" />) values
        (#{id},#{dicId},#{stanId},#{stanType})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_dic_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.dicId},#{item.stanId},#{item.stanType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDicMapping">
        update t_dic_mapping
        <set>
             <if test="dicId != null">
                dic_id = #{ dicId },
             </if>
             <if test="stanId != null">
                stan_id = #{ stanId },
             </if>
             <if test="stanType != null">
                stan_type = #{ stanType },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_dic_mapping where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDicMapping" resultMap="BaseResultMap">
        SELECT id,dic_id,stan_id,stan_type
        from t_dic_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getInMapping" resultMap="BaseResultMap">
        select * from t_dic_mapping where dic_id=#{ dicId } and stan_id=#{ stanId }
    </select>

</mapper>