<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.bs.BsCityMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.bs.BsCity">
        <id column="CITY_ID" jdbcType="INTEGER"  property="cityId" />
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
        <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName" />
        <result column="CITY_SHORT_NAME" jdbcType="VARCHAR" property="cityShortName" />
        <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
        <result column="CITY_LNG" jdbcType="VARCHAR" property="cityLng" />
        <result column="CITY_LAT" jdbcType="VARCHAR" property="cityLat" />
        <result column="SORT" jdbcType="INTEGER" property="sort" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="MODIFY_DATE" jdbcType="TIMESTAMP" property="modifiedDate" />
        <result column="DESC" jdbcType="VARCHAR" property="desc" />
        <result column="status" jdbcType="INTEGER" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    CITY_ID,CITY_CODE,CITY_NAME,CITY_SHORT_NAME,PROVINCE_CODE,CITY_LNG,CITY_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BsCity">
        delete from sys_city where CITY_ID = #{ cityId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_city where CITY_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="BsCity">
        insert into sys_city (CITY_ID,CITY_CODE,CITY_NAME,CITY_SHORT_NAME,PROVINCE_CODE,CITY_LNG,CITY_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        (#{cityId},#{cityCode},#{cityName},#{cityShortName},#{provinceCode},#{cityLng},#{cityLat},#{sort},#{createDate},#{modifiedDate},#{desc},#{status})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_city (CITY_ID,CITY_CODE,CITY_NAME,CITY_SHORT_NAME,PROVINCE_CODE,CITY_LNG,CITY_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.cityId},#{item.cityCode},#{item.cityName},#{item.cityShortName},#{item.provinceCode},#{item.cityLng},#{item.cityLat},#{item.sort},#{item.createDate},#{item.modifiedDate},#{item.desc},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BsCity">
        update sys_city
        <set>
             <if test="cityCode != null">
                CITY_CODE = #{ cityCode },
             </if>
             <if test="cityName != null">
                CITY_NAME = #{ cityName },
             </if>
             <if test="cityShortName != null">
                CITY_SHORT_NAME = #{ cityShortName },
             </if>
             <if test="provinceCode != null">
                PROVINCE_CODE = #{ provinceCode },
             </if>
             <if test="cityLng != null">
                CITY_LNG = #{ cityLng },
             </if>
             <if test="cityLat != null">
                CITY_LAT = #{ cityLat },
             </if>
             <if test="sort != null">
                SORT = #{ sort },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="modifiedDate != null">
                MODIFY_DATE = #{ modifiedDate },
             </if>
             <if test="desc != null">
                `DESC` = #{ desc },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where CITY_ID = #{ cityId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_city set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_city where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_city where CITY_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_city where CITY_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="BsCity" resultType="Map">
        SELECT CITY_ID,CITY_CODE,CITY_NAME,CITY_SHORT_NAME,PROVINCE_CODE,CITY_LNG,CITY_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status  from sys_city
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="BsCity" resultMap="BaseResultMap">
        SELECT CITY_ID,CITY_CODE,CITY_NAME,CITY_SHORT_NAME,PROVINCE_CODE,CITY_LNG,CITY_LAT,status
        from sys_city
        where 1=1
        <if test="provinceCode != null and provinceCode!='' ">
        and PROVINCE_CODE  = #{provinceCode}
        </if>
        order by SORT
    </select>
    <select id="getBsCityByCode" parameterType="string" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" />
        from sys_city
        where CITY_CODE=#{cityCode}
    </select>

</mapper>