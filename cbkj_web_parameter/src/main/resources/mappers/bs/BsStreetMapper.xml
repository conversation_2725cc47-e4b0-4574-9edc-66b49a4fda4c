<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.bs.BsStreetMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.bs.BsStreet">
        <id column="STREET_ID" jdbcType="INTEGER"  property="streetId" />
        <result column="STREET_CODE" jdbcType="VARCHAR" property="streetCode" />
        <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
        <result column="STREET_NAME" jdbcType="VARCHAR" property="streetName" />
        <result column="STREET_SHORT_NAME" jdbcType="VARCHAR" property="streetShortName" />
        <result column="STREET_LNG" jdbcType="VARCHAR" property="streetLng" />
        <result column="STREET_LAT" jdbcType="VARCHAR" property="streetLat" />
        <result column="SORT" jdbcType="INTEGER" property="sort" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="MODIFY_DATE" jdbcType="TIMESTAMP" property="modifyDate" />
        <result column="MEMO" jdbcType="VARCHAR" property="desc" />
        <result column="status" jdbcType="INTEGER" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    STREET_ID,STREET_CODE,AREA_CODE,STREET_NAME,STREET_SHORT_NAME,STREET_LNG,STREET_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BsStreet">
        delete from sys_street where STREET_ID = #{ streetId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_street where STREET_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="BsStreet">
        insert into sys_street (STREET_ID,STREET_CODE,AREA_CODE,STREET_NAME,STREET_SHORT_NAME,STREET_LNG,STREET_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        (#{streetId},#{streetCode},#{areaCode},#{streetName},#{streetShortName},#{streetLng},#{streetLat},#{sort},#{createDate},#{modifyDate},#{desc},#{status})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_street (STREET_ID,STREET_CODE,AREA_CODE,STREET_NAME,STREET_SHORT_NAME,STREET_LNG,STREET_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.streetId},#{item.streetCode},#{item.areaCode},#{item.streetName},#{item.streetShortName},#{item.streetLng},#{item.streetLat},#{item.sort},#{item.createDate},#{item.modifyDate},#{item.desc},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BsStreet">
        update sys_street
        <set>
             <if test="streetCode != null">
                STREET_CODE = #{ streetCode },
             </if>
             <if test="areaCode != null">
                AREA_CODE = #{ areaCode },
             </if>
             <if test="streetName != null">
                STREET_NAME = #{ streetName },
             </if>
             <if test="streetShortName != null">
                STREET_SHORT_NAME = #{ streetShortName },
             </if>
             <if test="streetLng != null">
                STREET_LNG = #{ streetLng },
             </if>
             <if test="streetLat != null">
                STREET_LAT = #{ streetLat },
             </if>
             <if test="sort != null">
                SORT = #{ sort },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="modifyDate != null">
                MODIFY_DATE = #{ modifyDate },
             </if>
             <if test="desc != null">
                 `DESC` = #{ desc },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where STREET_ID = #{ streetId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_street set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_street where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_street where STREET_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_street where STREET_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="BsStreet" resultType="Map">
        SELECT STREET_ID,STREET_CODE,AREA_CODE,STREET_NAME,STREET_SHORT_NAME,STREET_LNG,STREET_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status  from sys_street
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="BsStreet" resultMap="BaseResultMap">
        SELECT STREET_ID,STREET_CODE,AREA_CODE,STREET_NAME,STREET_SHORT_NAME,STREET_LNG,STREET_LAT,status
        from sys_street
        where 1=1
        <if test="areaCode != null and areaCode!='' ">
            and AREA_CODE  = #{areaCode}
        </if>
        order by SORT
    </select>
    <select id="getBsStreetByCode" parameterType="string" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" />
        from sys_street
        where STREET_CODE=#{streetCode}
    </select>

</mapper>