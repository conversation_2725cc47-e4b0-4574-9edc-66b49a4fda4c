<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.bs.BsAreaMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.bs.BsArea">
        <id column="AREA_ID" jdbcType="INTEGER"  property="areaId" />
        <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
        <result column="AREA_NAME" jdbcType="VARCHAR" property="areaName" />
        <result column="AREA_SHORT_NAME" jdbcType="VARCHAR" property="areaShortName" />
        <result column="AREA_LNG" jdbcType="VARCHAR" property="areaLng" />
        <result column="AREA_LAT" jdbcType="VARCHAR" property="areaLat" />
        <result column="SORT" jdbcType="INTEGER" property="sort" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="MODIFY_DATE" jdbcType="TIMESTAMP" property="modifyDate" />
        <result column="DESC" jdbcType="VARCHAR" property="desc" />
        <result column="status" jdbcType="INTEGER" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    AREA_ID,AREA_CODE,CITY_CODE,AREA_NAME,AREA_SHORT_NAME,AREA_LNG,AREA_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BsArea">
        delete from sys_area where AREA_ID = #{ areaId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_area where AREA_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="BsArea">
        insert into sys_area (AREA_ID,AREA_CODE,CITY_CODE,AREA_NAME,AREA_SHORT_NAME,AREA_LNG,AREA_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        (#{areaId},#{areaCode},#{cityCode},#{areaName},#{areaShortName},#{areaLng},#{areaLat},#{sort},#{createDate},#{modifyDate},#{desc},#{status})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_area (AREA_ID,AREA_CODE,CITY_CODE,AREA_NAME,AREA_SHORT_NAME,AREA_LNG,AREA_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.areaId},#{item.areaCode},#{item.cityCode},#{item.areaName},#{item.areaShortName},#{item.areaLng},#{item.areaLat},#{item.sort},#{item.createDate},#{item.modifyDate},#{item.desc},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BsArea">
        update sys_area
        <set>
             <if test="areaCode != null">
                AREA_CODE = #{ areaCode },
             </if>
             <if test="cityCode != null">
                CITY_CODE = #{ cityCode },
             </if>
             <if test="areaName != null">
                AREA_NAME = #{ areaName },
             </if>
             <if test="areaShortName != null">
                AREA_SHORT_NAME = #{ areaShortName },
             </if>
             <if test="areaLng != null">
                AREA_LNG = #{ areaLng },
             </if>
             <if test="areaLat != null">
                AREA_LAT = #{ areaLat },
             </if>
             <if test="sort != null">
                SORT = #{ sort },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="modifyDate != null">
                MODIFY_DATE = #{ modifyDate },
             </if>
             <if test="desc != null">
                 `DESC` = #{ desc },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where AREA_ID = #{ areaId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_area set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_area where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_area where AREA_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_area where AREA_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="BsArea" resultType="Map">
        SELECT AREA_ID,AREA_CODE,CITY_CODE,AREA_NAME,AREA_SHORT_NAME,AREA_LNG,AREA_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status  from sys_area
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="BsArea" resultMap="BaseResultMap">
        SELECT AREA_ID,AREA_CODE,CITY_CODE,AREA_NAME,AREA_SHORT_NAME,AREA_LNG,AREA_LAT,status
        from sys_area
        where 1=1
        <if test="cityCode != null and cityCode!='' ">
            and CITY_CODE  = #{cityCode}
        </if>
        order by SORT
    </select>
    <select id="getAreaByCode" parameterType="string" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from sys_area
        where AREA_CODE=#{areaCode}
    </select>

</mapper>