<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.bs.BsProvinceMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.bs.BsProvince">
        <id column="PROVINCE_ID" jdbcType="INTEGER"  property="provinceId" />
        <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
        <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName" />
        <result column="PROVINCE_SHORT_NAME" jdbcType="VARCHAR" property="provinceShortName" />
        <result column="PROVINCE_LNG" jdbcType="VARCHAR" property="provinceLng" />
        <result column="PROVINCE_LAT" jdbcType="VARCHAR" property="provinceLat" />
        <result column="SORT" jdbcType="INTEGER" property="sort" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="MODIFY_DATE" jdbcType="TIMESTAMP" property="modifyDate" />
        <result column="DESC" jdbcType="VARCHAR" property="desc" />
        <result column="status" jdbcType="INTEGER" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    PROVINCE_ID,PROVINCE_CODE,PROVINCE_NAME,PROVINCE_SHORT_NAME,PROVINCE_LNG,PROVINCE_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BsProvince">
        delete from sys_province where PROVINCE_ID = #{ provinceId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_province where PROVINCE_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="BsProvince">
        insert into sys_province (PROVINCE_ID,PROVINCE_CODE,PROVINCE_NAME,PROVINCE_SHORT_NAME,PROVINCE_LNG,PROVINCE_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        (#{provinceId},#{provinceCode},#{provinceName},#{provinceShortName},#{provinceLng},#{provinceLat},#{sort},#{createDate},#{modifyDate},#{desc},#{status})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_province (PROVINCE_ID,PROVINCE_CODE,PROVINCE_NAME,PROVINCE_SHORT_NAME,PROVINCE_LNG,PROVINCE_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.provinceId},#{item.provinceCode},#{item.provinceName},#{item.provinceShortName},#{item.provinceLng},#{item.provinceLat},#{item.sort},#{item.createDate},#{item.modifyDate},#{item.desc},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BsProvince">
        update sys_province
        <set>
             <if test="provinceCode != null">
                PROVINCE_CODE = #{ provinceCode },
             </if>
             <if test="provinceName != null">
                PROVINCE_NAME = #{ provinceName },
             </if>
             <if test="provinceShortName != null">
                PROVINCE_SHORT_NAME = #{ provinceShortName },
             </if>
             <if test="provinceLng != null">
                 PROVINCE_LNG = #{provinceLng},
             </if>
             <if test="provinceLat != null">
                 PROVINCE_LAT = #{provinceLat},
             </if>
             <if test="sort != null">
                SORT = #{ sort },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="modifyDate != null">
                MODIFY_DATE = #{ modifyDate },
             </if>
             <if test="desc != null">
                `DESC` = #{ desc },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where PROVINCE_ID = #{ provinceId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_province set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_province where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_province where PROVINCE_ID = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_province where PROVINCE_ID = #{id}
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="BsProvince" resultType="Map">
        SELECT PROVINCE_ID,PROVINCE_CODE,PROVINCE_NAME,PROVINCE_SHORT_NAME,PROVINCE_LNG,PROVINCE_LAT,SORT,CREATE_DATE,MODIFY_DATE,`DESC`,status  from sys_province
        where 1=1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="BsProvince" resultMap="BaseResultMap">
        SELECT PROVINCE_ID,PROVINCE_CODE,PROVINCE_NAME,PROVINCE_SHORT_NAME,PROVINCE_LNG,PROVINCE_LAT,status
        from sys_province
        order by SORT
    </select>
    <select id="getProvinceByCode" parameterType="string" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from sys_province where PROVINCE_CODE=#{provinceCode}
    </select>

</mapper>