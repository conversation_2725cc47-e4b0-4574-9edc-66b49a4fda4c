<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysParam.TSysParamInitDescMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc">
        <result column="param_id" jdbcType="VARCHAR"  property="paramId" />
        <result column="param_init_code" jdbcType="VARCHAR" property="paramInitCode" />
        <result column="param_init_name" jdbcType="VARCHAR" property="paramInitName" />
        <result column="option_diagram" jdbcType="VARCHAR" property="optionDiagram" />
        <result column="sort" jdbcType="VARCHAR" property="sort" />
    </resultMap>


    <sql id="Base_Column_List">
    param_id,param_init_code,param_init_name,option_diagram,sort
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TSysParamInitDesc">
        delete from t_sys_param_init_desc where param_id = #{ paramId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_sys_param_init_desc where param_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TSysParamInitDesc">
        insert into t_sys_param_init_desc (<include refid="Base_Column_List" />) values
        (#{paramId},#{paramInitCode},#{paramInitName},#{optionDiagram},#{sort})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_sys_param_init_desc (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.paramId},#{item.paramInitCode},#{item.paramInitName},#{item.optionDiagram},#{item.sort})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TSysParamInitDesc">
        update t_sys_param_init_desc
        <set>
             <if test="paramInitCode != null">
                param_init_code = #{ paramInitCode },
             </if>
             <if test="paramInitName != null">
                param_init_name = #{ paramInitName },
             </if>
             <if test="optionDiagram != null">
                option_diagram = #{ optionDiagram },
             </if>
        </set>
        where param_id = #{ paramId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_sys_param_init_desc where param_id = #{ paramId }
    </select>

    <select id="getPageListByObj" parameterType="TSysParamInitDesc" resultMap="BaseResultMap">
        SELECT param_id,param_init_code,param_init_name,option_diagram,sort
        from t_sys_param_init_desc
        <where>
            <if test=" paramInitName != null and paramInitName!='' ">
                and param_init_name like CONCAT('%',trim(#{paramInitName}),'%')
            </if>
            <if test=" paramInitCode != null and paramInitCode!='' ">
                and param_init_code =#{paramInitCode}
            </if>
            <if test=" paramId != null and paramId!='' ">
                and param_id =#{paramId}
            </if>
            <if test=" paramInitCodes != null and paramInitCodes!='' ">
                and param_init_code in
                <foreach collection="paramInitCodes.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
order by sort asc
        </where>
    </select>
    <select id="selectTSysParamInitDesc" parameterType="string" resultMap="BaseResultMap">
        select * from t_sys_param_init_desc where param_id=#{parId}
    </select>

    <select id="getInitByCondition" parameterType="TSysParamInitDesc" resultType="com.jiuzhekan.cbkj.beans.sysParam.TSysParamInitDesc">
        SELECT param_id paramId,
               param_init_code paramInitCode,
               param_init_name paramInitName,
               option_diagram optionDiagram
        from t_sys_param_init_desc
        <where>
        <if test=" paramInitName != null and paramInitName!='' ">
            and param_init_name like CONCAT('%',trim(#{paramInitName}),'%')
        </if>

        <if test=" paramInitCode != null and paramInitCode!='' ">
            and param_init_code =#{paramInitCode}
        </if>
        <if test=" paramId != null and paramId!='' ">
            and param_id =#{paramId}
        </if>
        </where>
    </select>
    <select id="getCountByO" resultType="java.lang.Long" parameterType="TSysParamInitDesc">
        SELECT count(*)
        <where>
            <if test=" paramInitName != null and paramInitName!='' ">
                and param_init_name like CONCAT('%',trim(#{paramInitName}),'%')
            </if>

            <if test=" paramInitCode != null and paramInitCode!='' ">
                and param_init_code =#{paramInitCode}
            </if>
            <if test=" paramId != null and paramId!='' ">
                and param_id =#{paramId}
            </if>
        </where>
    </select>

</mapper>