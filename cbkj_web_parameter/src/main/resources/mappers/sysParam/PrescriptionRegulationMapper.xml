<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysParam.PrescriptionRegulationMapper">



    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulation">
        <result column="level" jdbcType="VARCHAR"  property="level" />
        <result column="event" jdbcType="VARCHAR" property="event" />
        <collection property="children" ofType="com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulationItem">
            <result column="category" jdbcType="VARCHAR" property="category" />
            <result column="options" jdbcType="VARCHAR" property="opt" />
        </collection>
    </resultMap>

    <select id="getCondition" parameterType="string" resultType="map">

        SELECT  a.code,a.name,a.type FROM `t_prescription_regulation_param` a JOIN
        `t_prescription_regulation_param` b ON a.parent_id = b.id  WHERE b.id ='2'
        <if test="name !=null and name !=''">
            and a.name like CONCAT('%',trim(#{name}),'%')
        </if>
    </select>

    <select id="getEvent" resultType="map">
        SELECT a.code as value, a.name as label
        FROM `t_prescription_regulation_param` a
                 JOIN
             `t_prescription_regulation_param` b ON a.parent_id = b.id
        WHERE b.id = '1'
    </select>

    <delete id="prescriptionRegulationDel">
        delete from t_prescription_regulation ;
        delete from t_prescription_regulation_item;
    </delete>


    <insert id="prescriptionRegulationInsertOrUpdate" parameterType="list">
        delete from t_prescription_regulation ;
        delete from t_prescription_regulation_item;

        insert into t_prescription_regulation (level,event,IS_DEL,CREATE_DATE)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.level},#{item.event},'0',NOW())
        </foreach>

    </insert>
    <insert id="prescriptionRegulationItemInsertOrUpdate" parameterType="list">
        insert into t_prescription_regulation_item(level,category,options,IS_DEL)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.level},#{item.category},#{item.opt},'0')
        </foreach>
    </insert>


    <select id="getPrescriptionRegulation" resultMap="BaseResultMap">
        select a.level,a.event,b.category,b.options from t_prescription_regulation a
            left join t_prescription_regulation_item b on a.level = b.level
        where a.IS_DEL = '0' and b.IS_DEL = '0'
    </select>


</mapper>

