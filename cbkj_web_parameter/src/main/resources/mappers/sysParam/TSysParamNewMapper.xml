<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysParam.TSysParamNewMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysParam.TSysParamNew">
        <id column="PAR_ID" jdbcType="VARCHAR" property="parId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId"/>
        <result column="PAR_CODE" jdbcType="VARCHAR" property="parCode"/>
        <result column="PAR_NAME" jdbcType="VARCHAR" property="parName"/>
        <result column="PAR_VALUES" jdbcType="VARCHAR" property="parValues"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="param_type" jdbcType="VARCHAR" property="paramType"/>
        <result column="is_global" jdbcType="VARCHAR" property="isGlobal"/>
        <result column="param_init_value" jdbcType="VARCHAR" property="paramInitValue"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="menu_id" jdbcType="VARCHAR" property="menuId"/>
        <result column="param_desc" jdbcType="VARCHAR" property="paramDesc"/>
        <result column="parameter_diagram" jdbcType="VARCHAR" property="parameterDiagram"/>
        <result column="option_diagram" jdbcType="VARCHAR" property="optionDiagram"/>
        <result column="par_number" jdbcType="VARCHAR" property="parNumber"/>
        <result column="parent_menu_id" jdbcType="VARCHAR" property="parentMenuId"/>
    </resultMap>


    <sql id="Base_Column_List">
        PAR_ID
        ,APP_ID,INS_CODE,PAR_CODE,PAR_NAME,PAR_VALUES,CREATE_DATE,CREATE_USER,create_user_name,status,param_type,is_global,param_init_value,sort,menu_id,param_desc,DEPT_ID,par_number
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TSysParam">
        delete
        from t_sys_param
        where PAR_ID = #{ parId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_sys_param where PAR_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteParamByCode" parameterType="string">
            delete from t_sys_param where PAR_CODE=#{parCode} and APP_ID!='000000'
    </delete>

    <insert id="insert" parameterType="TSysParam">
        insert into t_sys_param (<include refid="Base_Column_List"/>) values
        (#{parId},#{appId},#{insCode},#{parCode},#{parName},#{parValues},#{createDate},#{createUser},#{createUserName},#{status},#{paramType},#{isGlobal},#{paramInitValue},#{sort},#{menuId},#{paramDesc},#{deptId},#{parNumber})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_sys_param (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.parId},#{item.appId},#{item.insCode},#{item.parCode},#{item.parName},#{item.parValues},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status},#{item.paramType},#{item.isGlobal},
            #{item.paramInitValue},#{item.sort},#{item.menuId},#{item.paramDesc},#{item.deptId},#{item.parNumber})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TSysParam">
        update t_sys_param
        <set>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="parCode != null">
                PAR_CODE = #{ parCode },
            </if>
            <if test="parName != null">
                PAR_NAME = #{ parName },
            </if>
            <if test="parValues != null">
                PAR_VALUES = #{ parValues },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="paramType != null">
                param_type = #{ paramType },
            </if>
            <if test="isGlobal != null">
                is_global = #{ isGlobal },
            </if>
            <if test="paramInitValue != null">
                param_init_value = #{ paramInitValue },
            </if>
            <if test="sort != null">
                sort = #{ sort },
            </if>
            <if test="menuId != null">
                menu_id = #{ menuId },
            </if>
            <if test="paramDesc != null">
                param_desc = #{ paramDesc },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{deptId},
            </if>
            <if test="parNumber != null">
                par_number = #{parNumber},
            </if>
        </set>
        where PAR_ID = #{ parId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_sys_param where PAR_ID = #{id}
    </select>

    <select id="getDiagnosisParameterList" parameterType="DiagnosisParamDTO" resultMap="BaseResultMap">
        SELECT p.PAR_ID,p.APP_ID,p.INS_CODE,p.PAR_CODE,p.PAR_NAME,p.PAR_VALUES,p.CREATE_DATE,
        p.CREATE_USER,p.create_user_name,p.status,p.param_type,p.is_global,p.param_init_value,p.sort,p.par_number,
        p.menu_id,p.param_desc,p.DEPT_ID,
        i.INS_NAME as insName,
        a.app_name as appName,
        sam.menu_name as menuName,
        sam.menu_type as menuType
        from t_sys_param p
        join sys_admin_menu as sam on (sam.menu_id=p.menu_id)
        left join sys_institution i on i.INS_CODE = p.INS_CODE and p.APP_ID = i.APP_ID
        left join sys_app a on a.APP_ID = p.APP_ID
        where
        p.status = '0'
        <if test=" menuId != null and menuId!='' ">
            and p.menu_id = #{menuId}
        </if>
        <if test=" menuIds != null and menuIds!='' ">
            and p.menu_id in
            <foreach collection="menuIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="appId != null and appId!='' ">
            and p.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and p.INS_CODE = #{insCode}
        </if>
        <if test="deptId != null and deptId!='' ">
            and p.DEPT_ID = #{deptId}
        </if>
        GROUP BY p.PAR_CODE
        order by p.sort asc
    </select>
    <select id="selectParam" parameterType="string" resultMap="BaseResultMap">
        select *
        from t_sys_param
        where PAR_ID = #{parId}
    </select>

    <update id="keepParam" parameterType="TSysParamNew">
        update t_sys_param
        <set>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="parCode != null">
                PAR_CODE = #{ parCode },
            </if>
            <if test="parName != null">
                PAR_NAME = #{ parName },
            </if>
            <if test="parValues != null">
                PAR_VALUES = #{ parValues },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="paramType != null">
                param_type = #{ paramType },
            </if>
            <if test="isGlobal != null">
                is_global = #{ isGlobal },
            </if>
            <if test="paramInitValue != null">
                param_init_value = #{ paramInitValue },
            </if>
            <if test="sort != null">
                sort = #{ sort },
            </if>
            <if test="menuId != null">
                menu_id = #{ menuId },
            </if>
            <if test="paramDesc != null">
                param_desc = #{ paramDesc },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{deptId},
            </if>
            <if test="parNumber != null">
                par_number = #{parNumber},
            </if>
        </set>
        where PAR_ID = #{ parId }
    </update>
    <select id="selectParamByCondition" resultMap="BaseResultMap" parameterType="TSysParamNew">
        select * from t_sys_param where
        <if test="appId != null and appId != ''">
            APP_ID = #{ appId }
        </if>
        <if test="insCode != null and insCode != ''">
            and INS_CODE = #{ insCode }
        </if>
        <if test="parCode != null and parCode != ''">
            and PAR_CODE = #{ parCode }
        </if>
        <if test="deptId != null and deptId != ''">
            and DEPT_ID = #{deptId}
        </if>
    </select>
    <select id="searchParam" resultMap="BaseResultMap" parameterType="SysParamSearch">
        SELECT
        t1.`PAR_ID`,
        m2.`parent_menu_id` AS parent_menu_id,
        t1.`APP_ID`,
        t1.`INS_CODE`,
        t1.`DEPT_ID`,
        t1.PAR_CODE,
        t1.`PAR_NAME`,
        t1.`menu_id`,
        t1.`PAR_VALUES`,
        t1.`CREATE_DATE`,
        t1.`param_type`,
        t1.`is_global`,
        t1.`sort`,
        t1.`param_desc`,
        t1.`parameter_diagram`,
        t1.`par_number`
        FROM `t_sys_param` AS t1
        JOIN `sys_admin_menu` AS m2 ON m2.`menu_id` = t1.`menu_id`
        WHERE t1.par_code NOT IN
        (
        SELECT t.*
        FROM (
        SELECT t2.`PAR_CODE`
        FROM `t_sys_param` AS t2
        WHERE t2.`APP_ID` = #{appId}
        AND t2.`INS_CODE` =#{insCode}
        AND t2.`DEPT_ID` = #{deptId}
        ) AS t
        )
        AND t1.`menu_id` IN (SELECT DISTINCT sarm.menu_id
        FROM sys_admin_rule_menu AS sarm
        JOIN sys_admin_info_rule AS sair ON sarm.role_id = sair.role_id
        JOIN sys_admin_menu AS sam ON sam.menu_id = sarm.menu_id
        WHERE sair.user_id =#{userId}
        AND sam.modual_code = '1')
        AND t1.`APP_ID` = '000000'
        AND t1.`INS_CODE` = '000000'
        AND t1.`DEPT_ID` = '000000'
        AND t1.status = '0'
        <if test="keyWord != null and keyWord !=''">
            AND (
            t1.`PAR_NAME` LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            OR t1.`PAR_CODE` LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            OR m2.`menu_name` LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            OR t1.par_number LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            )
        </if>
        UNION
        SELECT
        t3.`PAR_ID`,
        m2.`parent_menu_id` AS parent_menu_id,
        t3.`APP_ID`,
        t3.`INS_CODE`,
        t3.`DEPT_ID`,
        t3.PAR_CODE,
        t3.`PAR_NAME`,
        t3.`menu_id` ,
        t3.`PAR_VALUES`,
        t3.`CREATE_DATE`,
        t3.`param_type`,
        t3.`is_global`,
        t3.`sort`,
        t3.`param_desc`,
        t3.`parameter_diagram`,
        t3.`par_number`
        FROM `t_sys_param` AS t3 JOIN `sys_admin_menu` AS m2 ON m2.`menu_id`=t3.`menu_id`
        WHERE t3.`APP_ID`=#{appId} AND t3.`INS_CODE`=#{insCode} AND t3.`DEPT_ID`=#{deptId} and t3.status='0'
        AND t3.`menu_id` IN (SELECT DISTINCT sarm.menu_id
        FROM sys_admin_rule_menu AS sarm
        JOIN sys_admin_info_rule AS sair ON sarm.role_id = sair.role_id
        JOIN sys_admin_menu AS sam ON sam.menu_id = sarm.menu_id
        WHERE sair.user_id =#{userId}
        AND sam.modual_code = '1')
        <if test="keyWord != null and keyWord !=''">
            AND (
            t3.`PAR_NAME` LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            OR t3.`PAR_CODE` LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            OR m2.`menu_name` LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            OR t3.par_number LIKE CONCAT('%',TRIM(#{keyWord}),'%')
            )
        </if>

    </select>
    <select id="getParamsByCode" parameterType="string" resultMap="BaseResultMap">

        SELECT p.PAR_ID
             , p.APP_ID
             , sys_app.`app_name`         as appName
             , p.INS_CODE
             , sys_institution.`ins_name` as insName
             , p.DEPT_ID
             , sys_department.`dept_name` as deptName
             , p.PAR_CODE
             , p.PAR_NAME
             , p.PAR_VALUES
             , p.CREATE_DATE
             , p.CREATE_USER
             , p.create_user_name
             , p.STATUS
             , p.param_type
             , p.is_global
             , p.param_init_value
             , p.sort
             , p.menu_id
             , p.param_desc
             , p.par_number
        FROM t_sys_param AS p
                 left JOIN sys_app ON sys_app.`app_id` = p.`APP_ID`
                 LEFT JOIN sys_institution ON sys_institution.`ins_code` = p.`INS_CODE` AND sys_institution.`app_id`=p.`APP_ID`
                 LEFT JOIN sys_department ON (sys_department.`dept_origin_id` = p.`DEPT_ID` OR sys_department.`dept_id`=p.`DEPT_ID`)  AND p.`APP_ID`=sys_department.`app_id` AND p.`INS_CODE`=sys_department.`ins_code`
        WHERE p.PAR_CODE = #{parCode}
          AND p.APP_ID!='000000' AND p.STATUS ='0'
        GROUP BY p.`PAR_ID`;
    </select>

    <select id="getDiagnosisParameterListAll" parameterType="DiagnosisParamDTO" resultMap="BaseResultMap">
        SELECT p.PAR_ID,p.APP_ID,p.INS_CODE,p.PAR_CODE,p.PAR_NAME,p.PAR_VALUES,p.CREATE_DATE,
        p.CREATE_USER,p.create_user_name,p.status,p.param_type,p.is_global,p.param_init_value,p.sort,p.par_number,
        p.menu_id,p.param_desc,p.DEPT_ID,
        i.INS_NAME as insName,
        a.app_name as appName,
        sam.menu_name as menuName,
        sam.menu_type as menuType
        from t_sys_param p
        join sys_admin_menu as sam on (sam.menu_id=p.menu_id)
        left join sys_institution i on i.INS_CODE = p.INS_CODE and p.APP_ID = i.APP_ID
        left join sys_app a on a.APP_ID = p.APP_ID
        where
        p.status = '0'
        <if test=" menuId != null and menuId!='' ">
            and p.menu_id = #{menuId}
        </if>
        <if test=" menuIds != null and menuIds!='' ">
            and p.menu_id in
            <foreach collection="menuIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND p.APP_ID='000000' AND p.INS_CODE='000000' AND p.DEPT_ID='000000'
        order by p.sort asc
    </select>
    <select id="getSystemParamByCode" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_sys_param where APP_ID='000000' and PAR_CODE=#{parCode}
    </select>
</mapper>

