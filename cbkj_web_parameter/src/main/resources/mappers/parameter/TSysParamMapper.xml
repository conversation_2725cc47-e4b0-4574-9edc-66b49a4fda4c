<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TSysParamMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.TSysParam">
        <id column="PAR_ID" jdbcType="VARCHAR"  property="parId" />
        <result column="PAR_CODE" jdbcType="VARCHAR"  property="parCode" />
        <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode" />
        <result column="DEPT_ID" jdbcType="VARCHAR" property="deptId" />
        <result column="PAR_NAME" jdbcType="VARCHAR" property="parName" />
        <result column="PAR_VALUES" jdbcType="VARCHAR" property="parValues" />
        <result column="PAR_DES" jdbcType="VARCHAR" property="parDes" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="CREATE_USERNAME" jdbcType="VARCHAR" property="createUsername" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
        <result column="UPDATE_USERNAME" jdbcType="VARCHAR" property="updateUsername" />
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate" />
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser" />
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername" />
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel" />
        <result column="SEQN" jdbcType="INTEGER" property="seqn" />
        <result column="PAR_CLASSIFY" jdbcType="VARCHAR" property="parClassify" />
        <result column="PAR_NUMBER" jdbcType="VARCHAR" property="parNumber" />
    </resultMap>

    <sql id="Base_Column_List">
    PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,PAR_DES,CREATE_DATE,CREATE_USER,CREATE_USERNAME,UPDATE_DATE,UPDATE_USER,UPDATE_USERNAME,
DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL,SEQN,PAR_CLASSIFY,PAR_NUMBER
    </sql>

    <delete id="deleteByCondition" parameterType="TSysParamNew">
        delete from t_sys_param where PAR_CODE=#{parCode}
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="TSysParam">
        insert into t_sys_param (PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,
                                 PAR_VALUES,PAR_DES,CREATE_DATE,CREATE_USER,CREATE_USERNAME,SEQN,PAR_CLASSIFY,PAR_NUMBER) values
        (#{parId},#{appId},#{insCode},#{deptId},#{parCode},#{parName},
         #{parValues},#{parDes},#{createDate},#{createUser},#{createUsername},#{seqn},#{parClassify},#{parNumber})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_sys_param (PAR_ID,APP_ID,INS_CODE,DEPT_ID,PAR_CODE,PAR_NAME,PAR_VALUES,PAR_DES,
                                 CREATE_DATE,CREATE_USER,CREATE_USERNAME,SEQN,PAR_CLASSIFY,PAR_NUMBER) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{ item.parId },#{item.appId},#{item.insCode},#{item.deptId},#{item.parCode},#{item.parName},#{item.parValues},#{item.parDes},
             #{item.createDate},#{item.createUser},#{item.createUsername},#{item.seqn},#{item.parClassify},#{item.parNumber})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TSysParamNew">
        update t_sys_param
        <set>
            <if test="parCode != null">
                PAR_CODE = #{ parCode },
            </if>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="deptId != null">
                DEPT_ID = #{ deptId },
            </if>
            <if test="parName != null">
                PAR_NAME = #{ parName },
            </if>
            <if test="parValues != null">
                PAR_VALUES = #{ parValues },
            </if>
            <if test="parDes != null">
                PAR_DES = #{ parDes },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUsername != null">
                CREATE_USERNAME = #{ createUsername },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
            </if>
            <if test="updateUsername != null">
                UPDATE_USERNAME = #{ updateUsername },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="delUser != null">
                DEL_USER = #{ delUser },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
            <if test="parClassify != null">
                PAR_CLASSIFY = #{parClassify},
            </if>
            <if test="parNumber != null">
                PAR_NUMBER = #{parNumber},
            </if>
        </set>
        where PAR_ID = #{ parId }
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="String" resultType="int">
        select count(1) cun from t_sys_param where is_del = '0' and PAR_CODE = #{parCode}
    </select>

    <select id="getMapById" resultType="Map" parameterType="TSysParamNew">
        select <include refid="Base_Column_List" />
        from t_sys_param where PAR_ID = #{ parId }
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="TSysParamNew">
        select <include refid="Base_Column_List" />
        from t_sys_param where PAR_ID = #{ parId }
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="TSysParam" resultMap="BaseResultMap">
        SELECT p.PAR_ID,p.APP_ID,p.INS_CODE,p.DEPT_ID,p.PAR_CODE,p.PAR_NAME,p.PAR_VALUES,p.PAR_DES,p.CREATE_DATE,p.CREATE_USER,p.CREATE_USERNAME,
        DATE_FORMAT(p.UPDATE_DATE,'%Y-%m-%d %H:%i:%s') UPDATE_DATE,p.UPDATE_USER,p.UPDATE_USERNAME,p.SEQN,p.PAR_CLASSIFY,p.PAR_NUMBER,
        i.INS_NAME as insName,
        a.app_name as appName
        from t_sys_param p
        left join sys_institution i on i.INS_CODE = p.INS_CODE and p.APP_ID = i.APP_ID
        left join sys_app a on a.APP_ID = p.APP_ID
        where p.IS_DEL = 0
        <if test="parName != null and parName!='' ">
          and (p.PAR_NAME like CONCAT('%',trim(#{parName}),'%') or p.PAR_CODE like CONCAT('%',trim(#{parName}),'%'))
        </if>
        <if test="appId != null and appId!='' ">
            and p.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and p.INS_CODE = #{insCode}
        </if>
        <if test="deptId != null and deptId!='' ">
            and p.DEPT_ID = #{deptId}
        </if>
        <if test="parCode != null and parCode!='' ">
            and p.PAR_CODE = #{parCode}
        </if>
        <if test="parClassify != null and parClassify!='' ">
            and p.PAR_CLASSIFY = #{parClassify}
        </if>
        <if test="parNumber != null and parNumber!='' ">
            and p.PAR_NUMBER = #{parNumber}
        </if>
        GROUP BY p.PAR_CODE
        ORDER BY p.PAR_NUMBER,p.PAR_CODE, p.SEQN
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getListByCode" parameterType="TSysParam" resultMap="BaseResultMap">
        SELECT p.PAR_ID,p.APP_ID,p.INS_CODE,p.DEPT_ID,p.PAR_CODE,p.PAR_NAME,p.PAR_VALUES,p.PAR_DES,p.SEQN,p.PAR_CLASSIFY,p.PAR_NUMBER,
            i.INS_NAME as insName, a.app_name as appName
        from t_sys_param p
        left join sys_institution i on i.INS_CODE = p.INS_CODE and p.APP_ID = i.APP_ID
        left join sys_app a on a.APP_ID = p.APP_ID
        where p.IS_DEL = 0
        <if test="appId != null and appId!='' ">
            and p.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and p.INS_CODE = #{insCode}
        </if>
        <if test="deptId != null and deptId!='' ">
            and p.DEPT_ID = #{deptId}
        </if>
        <if test="parCode != null and parCode!='' ">
            and p.PAR_CODE = #{parCode}
        </if>
        order by p.PAR_NUMBER,p.PAR_CODE, p.SEQN
    </select>

    <select id="getObjectByAppIdInsCodeAndCode" resultMap="BaseResultMap" parameterType="TSysParamNew">
        select <include refid="Base_Column_List" />
        from t_sys_param where IS_DEL = '0'
        <if test="appId != null and appId!='' ">
            and APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and INS_CODE = #{insCode}
        </if>
        <if test="deptId != null and deptId!='' ">
            and DEPT_ID = #{deptId}
        </if>
        <if test="parCode != null and parCode!='' ">
            and PAR_CODE = #{parCode}
        </if>
        limit 1
    </select>

    <select id="getAllParamCode" resultMap="BaseResultMap">
        select PAR_CODE
        from t_sys_param where IS_DEL='0'
        group by PAR_CODE
    </select>
    <select id="getAllParamClassifyList" resultType="java.util.Map">
        select
            PAR_CLASSIFY as parClassify
        from t_sys_param where IS_DEL = '0' AND  PAR_CLASSIFY IS NOT NULL
        GROUP BY PAR_CLASSIFY
    </select>
    <select id="getParamNumberRepeat" resultType="java.lang.Integer" parameterType="TSysParam">
        select count(1) cun from t_sys_param where is_del = '0'
                                               and PAR_NUMBER = #{parNumber}
        <if test="parId != null and parId!='' ">
            and PAR_ID = #{parId}
        </if>

    </select>
</mapper>