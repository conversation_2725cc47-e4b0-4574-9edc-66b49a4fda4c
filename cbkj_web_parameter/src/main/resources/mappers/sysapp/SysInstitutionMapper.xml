<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysapp.SysInstitutionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysapp.SysInstitution">
        <id column="INS_ID" jdbcType="VARCHAR" property="insId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="appName" jdbcType="VARCHAR" property="appName"/>
        <result column="ins_parent_code" jdbcType="VARCHAR" property="insParentCode"/>
        <result column="INS_CATEGORY" jdbcType="VARCHAR" property="insCategory"/>
        <result column="INS_IS_LAST" jdbcType="INTEGER" property="insIslast"/>
        <result column="INS_PINYIN" jdbcType="VARCHAR" property="insPinyin"/>
        <result column="INS_WUBI" jdbcType="VARCHAR" property="insWubi"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="area_code" jdbcType="VARCHAR" property="areaCode"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="street_code" jdbcType="VARCHAR" property="streetCode"/>
        <result column="street_name" jdbcType="VARCHAR" property="streetName"/>
        <result column="INS_ADDRESS" jdbcType="VARCHAR" property="insAddress"/>
        <result column="INS_ISONLINE" jdbcType="VARCHAR" property="insIsonline"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="ins_shorter_name" jdbcType="VARCHAR" property="insShorterName"/>
        <result column="upload" jdbcType="VARCHAR" property="upload"/>
        <result column="yard_name" jdbcType="VARCHAR" property="yardName"/>
        <result column="yard_code" jdbcType="VARCHAR" property="yardCode"/>
        <result column="prescription_institution_name" jdbcType="VARCHAR" property="prescriptionInstitutionName"/>
        <result column="prescription_institution_code" jdbcType="VARCHAR" property="prescriptionInstitutionCode"/>
        <result column="social_code" jdbcType="VARCHAR" property="socialCode"/>
        <result column="commit_key" jdbcType="VARCHAR" property="commitKey"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="INS_PNAME" jdbcType="VARCHAR" property="insPname"/>
        <result column="APP_NAME" jdbcType="VARCHAR" property="appName"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
        <result column="ins_level" jdbcType="VARCHAR" property="insLevel"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.sysapp.SysInstitution">
        <id column="INS_ID" jdbcType="VARCHAR" property="insId"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="ins_parent_code" jdbcType="VARCHAR" property="insParentCode"/>
        <result column="INS_IS_LAST" jdbcType="INTEGER" property="insIslast"/>
        <result column="INS_ISONLINE" jdbcType="VARCHAR" property="insIsonline"/>
        <collection property="insList" ofType="com.jiuzhekan.cbkj.beans.sysapp.SysInstitution">
            <id column="INS_ID2" jdbcType="VARCHAR" property="insId"/>
            <result column="APP_ID2" jdbcType="VARCHAR" property="appId"/>
            <result column="INS_CODE2" jdbcType="VARCHAR" property="insCode"/>
            <result column="INS_NAME2" jdbcType="VARCHAR" property="insName"/>
            <result column="INS_PCODE2" jdbcType="VARCHAR" property="insParentCode"/>
            <result column="INS_ISLAST2" jdbcType="INTEGER" property="insIslast"/>
            <result column="INS_ISONLINE2" jdbcType="VARCHAR" property="insIsonline"/>
            <collection property="insList" ofType="com.jiuzhekan.cbkj.beans.sysapp.SysInstitution">
                <id column="INS_ID3" jdbcType="VARCHAR" property="insId"/>
                <result column="APP_ID3" jdbcType="VARCHAR" property="appId"/>
                <result column="INS_CODE3" jdbcType="VARCHAR" property="insCode"/>
                <result column="INS_NAME3" jdbcType="VARCHAR" property="insName"/>
                <result column="INS_PCODE3" jdbcType="VARCHAR" property="insParentCode"/>
                <result column="INS_ISLAST3" jdbcType="INTEGER" property="insIslast"/>
                <result column="INS_ISONLINE3" jdbcType="VARCHAR" property="insIsonline"/>
            </collection>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
        INS_ID
        ,APP_ID,INS_CODE,INS_NAME,ins_parent_code,INS_CATEGORY,INS_IS_LAST,INS_PINYIN,INS_WUBI,province_code,province_name,city_code,city_name,area_code,area_name,street_code,street_name,INS_ADDRESS,INS_ISONLINE,SORT,CREATE_DATE,CREATE_USER,create_user_name,status,platform_code,ins_level
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysInstitution">
        update sys_institution
        set status ='1'
        where INS_ID = #{ insId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_institution where INS_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="SysInstitution">
        insert into sys_institution (INS_ID, APP_ID, INS_CODE, INS_NAME, ins_parent_code, INS_CATEGORY, INS_IS_LAST,
                                     INS_PINYIN, INS_WUBI, province_code, province_name, city_code, city_name,
                                     area_code, area_name, street_code, street_name, INS_ADDRESS, INS_ISONLINE, SORT,
                                     ins_shorter_name, upload, yard_name, yard_code, prescription_institution_name,
                                     prescription_institution_code, social_code, commit_key,
                                     CREATE_DATE, CREATE_USER, create_user_name, update_date, update_user,
                                     update_user_name, status, platform_code, ins_level)
        values (#{insId}, #{appId}, #{insCode}, #{insName}, #{insParentCode}, #{insCategory}, #{insIslast},
                #{insPinyin}, #{insWubi}, #{provinceCode}, #{provinceName}, #{cityCode}, #{cityName}, #{areaCode},
                #{areaName}, #{streetCode}, #{streetName}, #{insAddress}, #{insIsonline}, #{sort}, #{insShorterName},
                #{upload}, #{yardName}, #{yardCode}, #{prescriptionInstitutionName}, #{prescriptionInstitutionCode},
                #{socialCode}, #{commitKey}, #{createDate},
                #{createUser}, #{createUserName}, #{updateDate}, #{updateUser}, #{updateUserName}, #{status},
                #{platformCode}, #{insLevel})
    </insert>

    <!--批量插入 少处方上传字段-->
    <insert id="insertList" parameterType="List">
        insert into sys_institution
        (INS_ID, APP_ID, INS_CODE, INS_NAME, ins_parent_code, INS_CATEGORY, INS_IS_LAST,
        INS_PINYIN, INS_WUBI, province_code, province_name, city_code, city_name,
        area_code, area_name, street_code, street_name, INS_ADDRESS, INS_ISONLINE, SORT,
        ins_shorter_name,upload,yard_name,yard_code,prescription_institution_name,prescription_institution_code,
        social_code,commit_key,
        CREATE_DATE, CREATE_USER, create_user_name, update_date, update_user,
        update_user_name, status, platform_code, ins_level)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.insId}, #{item.appId}, #{item.insCode}, #{item.insName}, #{item.insParentCode}, #{item.insCategory},
            #{item.insIslast},
            #{item.insPinyin}, #{item.insWubi}, #{item.provinceCode}, #{item.provinceName}, #{item.cityCode},
            #{item.cityName}, #{item.areaCode},
            #{item.areaName}, #{item.streetCode}, #{item.streetName}, #{item.insAddress}, #{item.insIsonline},
            #{item.sort},#{item.insShorterName},#{item.upload},#{item.yardName},#{item.yardCode},#{item.prescriptionInstitutionName},#{item.prescriptionInstitutionCode},
            #{item.socialCode},#{item.commitKey}, #{item.createDate},
            #{item.createUser}, #{item.createUserName}, #{item.updateDate}, #{item.updateUser}, #{item.updateUserName},
            #{item.status},
            #{item.platformCode}, #{item.insLevel})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysInstitution">
        update sys_institution
        <set>
            <if test="appId != null and appId!=''">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null and insCode!=''">
                INS_CODE = #{ insCode },
            </if>
            <if test="insName != null and insName!=''">
                INS_NAME = #{ insName },
            </if>
            <if test="insParentCode != null and insParentCode != ''">
                ins_parent_code = #{ insParentCode },
            </if>
            <if test="insCategory != null and insCategory!=''">
                INS_CATEGORY = #{ insCategory },
            </if>
            <if test="insIslast != null and insIslast!=''">
                INS_IS_LAST = #{ insIslast },
            </if>
            <if test="insPinyin != null and insPinyin!=''">
                INS_PINYIN = #{ insPinyin },
            </if>
            <if test="insWubi != null and insWubi!=''">
                INS_WUBI = #{ insWubi },
            </if>
            <if test="provinceCode != null and provinceCode!=''">
                province_code = #{ provinceCode },
            </if>
            <if test="provinceName != null and provinceName!=''">
                province_name = #{ provinceName },
            </if>
            <if test="cityCode != null and cityCode!=''">
                city_code = #{ cityCode },
            </if>
            <if test="cityName != null and cityName!=''">
                city_name = #{ cityName },
            </if>
            <if test="areaCode != null and areaCode!=''">
                area_code = #{ areaCode },
            </if>
            <if test="areaName != null and areaName!=''">
                area_name = #{ areaName },
            </if>
            <if test="streetCode != null and streetCode!=''">
                street_code = #{ streetCode },
            </if>
            <if test="streetName != null and streetName!=''">
                street_name = #{ streetName },
            </if>
            <if test="insAddress != null and insAddress!=''">
                INS_ADDRESS = #{ insAddress },
            </if>
            <if test="insIsonline != null and insIsonline!=''">
                INS_ISONLINE = #{ insIsonline },
            </if>
            <if test="sort != null and sort!=''">
                SORT = #{ sort },
            </if>
            <if test="insShorterName != null and insShorterName!=''">
                ins_shorter_name = #{insShorterName},
            </if>
            <if test="upload != null and upload!=''">
                upload = #{upload},
            </if>
            <if test="yardName != null and yardName!=''">
                yard_name = #{yardName},
            </if>
            <if test="yardCode != null and yardCode!=''">
                yard_code = #{yardCode},
            </if>
            <if test="prescriptionInstitutionName != null and prescriptionInstitutionName!=''">
                prescription_institution_name = #{prescriptionInstitutionName},
            </if>
            <if test="prescriptionInstitutionCode != null and prescriptionInstitutionCode!=''">
                prescription_institution_code = #{prescriptionInstitutionCode},
            </if>
            <if test="socialCode != null and socialCode!=''">
                social_code = #{socialCode},
            </if>
            <if test="commitKey != null and commitKey!=''">
                commit_key = #{commitKey},
            </if>


            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="updateUserName != null and updateUserName!=''">
                update_user_name = #{updateUserName},
            </if>

            <if test="status != null and status!=''">
                status = #{ status },
            </if>

            <if test="platformCode != null and platformCode!=''">
                platform_code = #{platformCode},
            </if>
            <if test="insLevel != null and insLevel!=''">
                ins_level = #{insLevel},
            </if>

        </set>
        where INS_ID = #{ insId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_institution set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''">and ${key1}=#{value1}</if>
        <if test="key2!=null and key2!=''">and ${key2}=#{value2}</if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_institution where 1=1 and status='0'
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from sys_institution where INS_ID = #{id} and status='0'
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        SELECT ins.*, (SELECT INS_NAME FROM sys_institution WHERE INS_CODE = ins.ins_parent_code limit 1) as INS_PNAME
        from sys_institution ins
        where ins.INS_ID = #{id} and ins.status='0'
    </select>

    <select id="getObjectById2" resultMap="BaseResultMap" parameterType="String">
        SELECT ins.INS_ID,
               ins.APP_ID,
               (select app_name from sys_app where sys_app.app_id = ins.APP_ID) as        appName,
               ins.INS_CODE,
               ins.INS_NAME,
               ins.ins_parent_code,
               ins.INS_CATEGORY,
               ins.INS_IS_LAST,
               ins.INS_PINYIN,
               ins.INS_WUBI,
               ins.province_code,
               ins.province_name,
               ins.city_code,
               ins.city_name,
               ins.area_code,
               ins.area_name,
               ins.street_code,
               ins.street_name,
               ins.INS_ADDRESS,
               ins.INS_ISONLINE,
               ins.SORT,
               ins.ins_shorter_name,
               ins.upload,
               ins.yard_name,
               ins.yard_code,
               ins.prescription_institution_name,
               ins.prescription_institution_code,
               ins.social_code,
               ins.commit_key,
               ins.CREATE_DATE,
               ins.CREATE_USER,
               ins.create_user_name,
               ins.update_date,
               ins.update_user,
               ins.update_user_name,
               ins.status,
               ins.platform_code,
               ins.ins_level,
               (SELECT INS_NAME FROM sys_institution WHERE INS_CODE = ins.ins_parent_code limit 1) as INS_PNAME
        from sys_institution ins
        where ins.INS_ID = #{insId} and ins.status!='1'
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="SysInstitution" resultType="Map">
        SELECT INS_ID,
               APP_ID,
               INS_CODE,
               INS_NAME,
               ins_parent_code,
               INS_CATEGORY,
               INS_IS_LAST,
               INS_PINYIN,
               INS_WUBI,
               province_code,
               province_name,
               city_code,
               city_name,
               area_code,
               area_name,
               INS_ADDRESS,
               INS_ISONLINE,
               SORT,
               CREATE_DATE,
               CREATE_USER,
               create_user_name,
               update_date,
               update_user,
               update_user_name,
               status,
               platform_code
        from sys_institution
        where 1 = 1
          and status = '0'
    </select>


    <!--分页查询基础语句返回对象 status='0'排序规则 ins.SORT, ins.INS_CODE-->
    <select id="getPageListByObj" parameterType="SysInstitution" resultType="map">
        SELECT
        INS_ID as insId,
        APP_ID as appId,
        INS_CODE as insCode,
        INS_NAME as insName,
        ins_parent_code as insParentCode,
        INS_CATEGORY as insCategory,
        INS_IS_LAST as insIslast,
        INS_PINYIN as insPinyin,
        INS_WUBI as insWubi,
        province_code as provinceCode,
        province_name as provinceName,
        city_code as cityCode,
        city_name as cityName,
        area_code as areaCode,
        area_name as areaName,
        street_code as streetCode,
        street_name as streetName,
        INS_ADDRESS as insAddress,
        INS_ISONLINE as insIsonline,
        SORT as sort,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') as createDate,
        CREATE_USER as createUser,
        create_user_name as createUserName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') as updateDate,
        update_user as updateUser,
        update_user_name as updateUserName,
        status as status,
        ins_shorter_name as insShorterName,
        upload as upload,
        yard_name as yardName,
        yard_code as yardCode,
        prescription_institution_name as prescriptionInstitutionName,
        prescription_institution_code as prescriptionInstitutionCode,
        social_code as socialCode,
        commit_key as commitKey,
        platform_code as platformCode,
        ins_level as insLevel,
        (SELECT INS_NAME FROM sys_institution WHERE INS_CODE = ins.ins_parent_code and status ='0' limit 1) as insPname,
        (SELECT app_name FROM sys_app WHERE app_id = ins.APP_ID and status ='0' LIMIT 1) AS appName
        from sys_institution ins
        where ins.status !='1'
        <if test="appId != null and appId!='' ">
            and ins.APP_ID = #{appId}
        </if>
        <if test="insName != null and insName!='' ">
            and INS_NAME like CONCAT('%',trim(#{insName}),'%')
        </if>
        <if test="insCode != null and insCode!='' ">
            and ( INS_CODE = #{insCode}
            <if test="lower != null and lower ">
                or ins_parent_code = #{insCode}
            </if>
            )
        </if>
        <if test="provinceCode != null and provinceCode!='' ">
            and province_code =#{provinceCode}
        </if>
        <if test="cityCode != null and cityCode!='' ">
            and city_code =#{cityCode}
        </if>
        <if test="areaCode != null and areaCode!='' ">
            and area_code =#{areaCode}
        </if>
        <if test="platformCode != null and platformCode!='' ">
            and platform_code =#{platformCode}
        </if>
        order by ifnull(ins.update_date, ins.create_date) desc

    </select>
    <select id="getSubIns" parameterType="String" resultType="String">
        SELECT INS_CODE
        FROM sys_institution
        where ins_parent_code = #{insParentCode};
    </select>
    <!--   <if test="isSj != null and isSj!='' ">
           and INS_CATEGORY !='3'
       </if>-->
    <select id="getSysInstitutionByOryCateGory" parameterType="SysInstitution" resultMap="BaseResultMap">
        SELECT INS_ID,INS_CODE,INS_NAME, ins_parent_code
        from sys_institution
        where 1=1 and status='0'
        <!--条件-->
        <if test="insCategory != null and insCategory!='' ">
            and INS_CATEGORY = #{insCategory}
        </if>
        <!--条件-->
        <if test="appId != null and appId!='' ">
            and APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and INS_CODE =#{insCode}
        </if>
        order by SORT, INS_CODE
    </select>

    <select id="getInsCountByIns" resultType="Integer" parameterType="String">
        SELECT count(*)
        from sys_institution
        where status = '0' and APP_ID = #{appId} and INS_CODE = #{insCode}
        <if test="insId != null and insId!='' ">
            and INS_ID !=#{insId}
        </if>
    </select>

    <!--getMapByAppId-->
    <select id="getMapByAppId" parameterType="Map" resultType="Map">
        SELECT
        s.APP_ID appId,
        s.INS_ID insId,
        s.INS_CODE insCode,
        s.INS_NAME insName,
        s.ins_parent_code insParentCode
        FROM
        sys_institution AS s
        where s.status = '0'
        <if test="appId != null and appId != ''">
            and s.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode != ''">
            and s.INS_CODE = #{insCode}
        </if>
        order by s.SORT, s.INS_CODE
    </select>


    <select id="getInsByIns" resultMap="BaseResultMap" parameterType="SysInstitution">
        SELECT *
        from sys_institution
        where status = '0'
        <if test="insId != null and insId!='' ">
            and INS_ID =#{insId}
        </if>
        <if test="appId != null and appId!='' ">
            and APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and INS_CODE =#{insCode}
        </if>
        order by SORT, INS_CODE
        limit 1
    </select>

    <select id="getInsAddressByIns" resultType="Map" parameterType="SysInstitution">
        SELECT
        province_code dcCountyCode,
        province_name dcCounty,
        city_code dcTownCode,
        city_name dcTown,
        area_code dcVillageCode,
        area_name dcVillage,
        street_code dcStreetCode,
        street_name dcStreet,
        INS_ADDRESS dcAddress
        from sys_institution
        where status = '0'
        <if test="appId != null and appId!='' ">
            and APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and INS_CODE =#{insCode}
        </if>
        order by SORT, INS_CODE
        limit 1
    </select>


    <select id="getInsListByIns" resultMap="BaseResultMap2" parameterType="SysInstitution">
        SELECT ins1.INS_ID,ins1.INS_CODE,ins1.INS_NAME,
        ins2.INS_ID,ins2.INS_CODE,ins2.INS_NAME,
        ins3.INS_ID,ins3.INS_CODE,ins3.INS_NAME
        from sys_institution ins1
        left join sys_institution ins2 on ins1.INS_CODE = ins2.ins_parent_code and ins2.INS_CATEGORY = '2'
        left join sys_institution ins3 on ins2.INS_CODE = ins3.ins_parent_code and ins3.INS_CATEGORY = '3'
        where ins1.INS_CATEGORY = '1'
        <if test="insId != null and insId!='' ">
            and ins1.INS_ID =#{insId}
        </if>
        <if test="appId != null and appId!='' ">
            and ins1.APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and ins1.INS_CODE =#{insCode}
        </if>
        <if test="searchIns != null and searchIns!='' ">
            and (ins1.INS_NAME like CONCAT('%',trim(#{searchIns}),'%'
            or ins2.INS_NAME like CONCAT('%',trim(#{searchIns}),'%'
            or ins3.INS_NAME like CONCAT('%',trim(#{searchIns}),'%'
            or ins1.INS_PINYIN like CONCAT('%',trim(#{searchIns}),'%'
            or ins2.INS_PINYIN like CONCAT('%',trim(#{searchIns}),'%'
            or ins3.INS_PINYIN like CONCAT('%',trim(#{searchIns}),'%'
            or ins1.INS_WUBI like CONCAT('%',trim(#{searchIns}),'%'
            or ins2.INS_WUBI like CONCAT('%',trim(#{searchIns}),'%'
            or ins3.INS_WUBI like CONCAT('%',trim(#{searchIns}),'%'
            )
        </if>
        order by SORT, INS_CODE
    </select>

    <select id="getInsByAppId" resultMap="BaseResultMap" parameterType="SysInstitution">
        SELECT
        <include refid="Base_Column_List"/>
        from sys_institution
        <where>
            <if test="appId != null and appId!='' ">
                and APP_ID =#{appId}
            </if>
        </where>
        order by SORT, INS_CODE
    </select>

    <select id="getRootInsByAppId" resultMap="BaseResultMap" parameterType="SysInstitution">
        SELECT
        <include refid="Base_Column_List"/>
        from sys_institution
        where
        ( ins_parent_code='' or ins_parent_code is null )
        <if test="appId != null and appId!='' ">
            and APP_ID =#{appId}
        </if>
        order by SORT, INS_CODE

    </select>
    <select id="getSysInstitutionByInsCode" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_institution where INS_CODE=#{insCode}
    </select>
    <select id="getSysInstitutionByInsCodeAndAppId" parameterType="string" resultMap="BaseResultMap">
        select
        (select app_name from sys_app where sys_app.app_id=ins.APP_ID) as appName,
        <include refid="Base_Column_List"/>
        from sys_institution as ins where ins.INS_CODE=#{insCode} and ins.APP_ID=#{appId}
    </select>
    <select id="getInstitutionByAppId" parameterType="string" resultMap="BaseResultMap">
        select *
        from sys_institution
        where APP_ID = #{appId}
          and status !='1'
    </select>
    <select id="getAllInstitution" resultMap="BaseResultMap">
        select *
        from sys_institution
        where status = '0'
    </select>

    <update id="updateByIns" parameterType="SysInstitution">
        update sys_institution
        <set>
            <if test="insName != null">
                INS_NAME = #{ insName },
            </if>
            <if test="insParentCode != null">
                ins_parent_code = #{ insParentCode },
            </if>
            <if test="insCategory != null">
                INS_CATEGORY = #{ insCategory },
            </if>
            <if test="insIslast != null">
                INS_IS_LAST = #{ insIslast },
            </if>
            <if test="insPinyin != null">
                INS_PINYIN = #{ insPinyin },
            </if>
            <if test="insWubi != null">
                INS_WUBI = #{ insWubi },
            </if>
            <if test="provinceCode != null">
                province_code = #{ provinceCode },
            </if>
            <if test="provinceName != null">
                province_name = #{ provinceName },
            </if>
            <if test="cityCode != null">
                city_code = #{ cityCode },
            </if>
            <if test="cityName != null">
                city_name = #{ cityName },
            </if>
            <if test="areaCode != null">
                area_code = #{ areaCode },
            </if>
            <if test="areaName != null">
                area_name = #{ areaName },
            </if>
            <if test="insAddress != null">
                INS_ADDRESS = #{ insAddress },
            </if>
            <if test="insIsonline != null">
                INS_ISONLINE = #{ insIsonline },
            </if>
            <if test="sort != null">
                SORT = #{ sort },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName},
            </if>

            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="updateUserName != null">
                update_user_name = #{updateUserName},
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="platformCode != null">
                platform_code = #{platformCode},
            </if>
            <if test="insLevel != null">
                ins_level = #{insLevel},
            </if>

        </set>
        <where>
            <if test="insId != null and insId != ''">
                INS_ID = #{ insId }
            </if>
            <if test="insCode != null and insCode != ''">
                and INS_CODE = #{ insCode }
            </if>
        </where>

    </update>
</mapper>