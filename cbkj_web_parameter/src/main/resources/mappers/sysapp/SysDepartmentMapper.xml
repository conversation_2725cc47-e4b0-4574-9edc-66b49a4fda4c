<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysapp.SysDepartmentMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysExt.SysDepartment">
        <id column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="appName" jdbcType="VARCHAR" property="appName"/>
        <result column="insName" jdbcType="VARCHAR" property="insName"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_origin_id" jdbcType="VARCHAR" property="depOriginId"/>
        <result column="dept_parent_id" jdbcType="VARCHAR" property="depParentId"/>
        <result column="dept_parent_name" jdbcType="VARCHAR" property="depParentName"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="dept_type" jdbcType="VARCHAR" property="deptType"/>
        <result column="APP_NAME" jdbcType="VARCHAR" property="appName"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="ins_parent_code" jdbcType="VARCHAR" property="insParentCode"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="dept_address" jdbcType="VARCHAR" property="deptAddress"/>
        <result column="frmzbz" jdbcType="INTEGER" property="frmzbz"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.sysExt.SysHisDepartment">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DEP_ID" jdbcType="VARCHAR" property="depId"/>
        <result column="DEP_ID_HIS" jdbcType="VARCHAR" property="depIdHis"/>
        <result column="dept_name" jdbcType="VARCHAR" property="depName"/>
        <result column="DEP_INS_NAME" jdbcType="VARCHAR" property="depInsName"/>
    </resultMap>


    <sql id="Base_Column_List">
        dept_id
        ,dept_name,APP_ID,INS_CODE,dept_origin_id,dept_parent_id,dept_parent_name,CREATE_DATE,CREATE_USER,create_user_name,update_date,update_user,update_user_name,status,dept_type,sort,frmzbz
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysDepartment">
        update sys_department
        set status ='1'
        where dept_id = #{deptId}
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_department where dept_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="SysDepartment">
        insert into sys_department (dept_id, dept_name, APP_ID, INS_CODE, dept_origin_id, dept_parent_id,
                                    dept_parent_name,
                                    CREATE_DATE, CREATE_USER, create_user_name, update_date, update_user,
                                    update_user_name, status, dept_type, sort, dept_address, frmzbz)
        values (#{deptId}, #{deptName}, #{appId}, #{insCode}, #{depOriginId}, #{depParentId}, #{depParentName},
                #{createDate}, #{createUser}, #{createUserName}, #{updateDate}, #{updateUser}, #{updateUserName},
                #{status}, #{deptType}, #{sort}, #{deptAddress},#{frmzbz})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_department
        (dept_id,dept_name,APP_ID,INS_CODE,dept_origin_id,dept_parent_id,dept_parent_name,CREATE_DATE,CREATE_USER,create_user_name,update_date,
        update_user,update_user_name,status,dept_type,sort,dept_address,frmzbz)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deptId},#{item.deptName},#{item.appId},#{item.insCode},
             #{item.depOriginId},#{item.depParentId},#{item.depParentName},
             #{item.createDate},#{item.createUser},#{item.createUserName},
             #{updateDate},#{updateUser},#{updateUserName},#{item.status},
             #{item.dept_type},#{item.sort},#{item.deptAddress},#{item.frmzbz})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysDepartment">
        update sys_department
        <set>
            <if test="deptName != null and deptName!=''">
                dept_name = #{ deptName },
            </if>
            <if test="appId != null and appId!=''">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null and insCode!=''">
                INS_CODE = #{ insCode },
            </if>
            <if test="depOriginId != null and depOriginId!=''">
                dept_origin_id = #{ depOriginId },
            </if>
            <if test="depParentId != null and depParentId!=''">
                dept_parent_id = #{ depParentId },
            </if>
            <if test="depParentName != null and depParentName!=''">
                dept_parent_name = #{ depParentName },
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateUser != null and updateUser!=''">
                update_user = #{updateUser},
            </if>
            <if test="updateUserName != null and updateUserName!=''">
                update_user_name = #{updateUserName},
            </if>
            <if test="status != null and status!=''">
                status = #{ status },
            </if>
            <if test="deptType != null and deptType!=''">
                dept_type = #{deptType},
            </if>
            <if test="sort != null and sort!=''">
                sort = #{sort},
            </if>
            <if test="deptAddress != null and deptAddress!=''">
                dept_address = #{deptAddress},
            </if>
            <if test="frmzbz != null">
                frmzbz = #{frmzbz},
            </if>
        </set>
        where dept_id = #{deptId}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select dept_id,
               dept_name,
               APP_ID,
               INS_CODE,
               dept_origin_id,
               dept_parent_id,
               dept_parent_name,
               DATE_FORMAT(create_date, '%Y-%m-%d %H:%i:%s') as createDate,
               CREATE_USER,
               create_user_name,
               DATE_FORMAT(update_date, '%Y-%m-%d %H:%i:%s') as updateDate,
               update_user,
               update_user_name,
               status,
               dept_type,
               sort,
               frmzbz
        from sys_department
        where dept_id = #{id}
    </select>
    <select id="getObjectById2" resultMap="BaseResultMap" parameterType="String">
        SELECT dept_id,
               dept_name,
               sys_department.app_id,
               (SELECT sys_app.app_name
                FROM sys_app
                WHERE sys_department.`app_id` = sys_app.`app_id`
                  AND sys_app.`status` = '0')         AS appName,
               sys_department.INS_CODE,
               (SELECT sys_institution.ins_name
                FROM sys_institution
                WHERE sys_department.`ins_code` = sys_institution.`ins_code`
                  AND sys_department.`app_id` = sys_institution.`app_id`
                  AND sys_institution.`status` = '0') AS insName,
               dept_origin_id,
               dept_parent_id,
               dept_parent_name,
               create_date,
               CREATE_USER,
               create_user_name,
               update_date,
               update_user,
               update_user_name,
               STATUS,
               dept_type,
               dept_address,
               sort,
               frmzbz
        FROM sys_department
        WHERE dept_id = #{deptId}
    </select>
    <!--    DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,-->
    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="SysDepartment" resultType="map">
        SELECT
        dep.dept_id as deptId,
        dep.dept_name as deptName,
        dep.APP_ID as appId,
        dep.INS_CODE as insCode,
        dep.dept_origin_id as depOriginId,
        dep.dept_parent_id as depParentId,
        dep.dept_parent_name as depParentName,
        DATE_FORMAT(dep.CREATE_DATE,'%Y-%m-%d %H:%i:%s') AS createDate,
        dep.create_user as createUser,
        dep.create_user_name as createUserName,
        DATE_FORMAT(dep.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        dep.update_user as updateUser,
        dep.update_user_name as updateUserName,
        dep.status as status,
        dep.dept_type as deptType,
        dep.sort as sort,
        dep.frmzbz as frmzbz,
        app.app_name as appName,
        ins.ins_name as insName,
        ins.ins_parent_code as insParentCode
        FROM sys_department dep
        LEFT JOIN sys_app app ON dep.app_id = app.app_id AND app.status = '0' AND app.status = '0'
        LEFT JOIN sys_institution ins ON dep.ins_code = ins.ins_code AND ins.status = '0'
        WHERE 1=1
        <if test="appName != null and appName!='' ">
            and app.APP_NAME like CONCAT('%',trim(#{appName}),'%')
        </if>
        <if test="deptName != null and deptName!='' ">
            and dep.dept_name like CONCAT('%',trim(#{deptName}),'%')
        </if>
        <if test="appId != null and appId!='' ">
            and dep.APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and dep.INS_CODE =#{insCode}
        </if>
        order by dep.app_id, dep.ins_code, dep.dept_origin_id
    </select>

    <select id="findPageListByObj" parameterType="SysDepartment" resultType="map">
        SELECT
        dep.dept_id as deptId,
        dep.dept_name as deptName,
        dep.APP_ID as appId,
        dep.INS_CODE as insCode,
        dep.dept_origin_id as depOriginId,
        dep.dept_parent_id as depParentId,
        dep.dept_parent_name as depParentName,
        DATE_FORMAT(dep.CREATE_DATE,'%Y-%m-%d %H:%i:%s') AS createDate,
        dep.create_user as createUser,
        dep.create_user_name as createUserName,
        DATE_FORMAT(dep.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        dep.update_user as updateUser,
        dep.update_user_name as updateUserName,
        dep.status as status,
        dep.dept_type as deptType,
        dep.dept_address as deptAddress,
        dep.sort as sort,
        dep.frmzbz as frmzbz,
        app.app_name as appName,
        ins.ins_name as insName,
        dep.dept_address,
        ins.ins_parent_code as insParentCode
        FROM sys_department dep
        LEFT JOIN sys_app app ON dep.app_id = app.app_id AND app.status = '0'
        LEFT JOIN sys_institution ins ON dep.ins_code = ins.ins_code AND ins.status = '0'
        WHERE dep.status !='1'
        <if test="appName != null and appName!='' ">
            and app.APP_NAME like CONCAT('%',trim(#{appName}),'%')
        </if>
        <if test="deptName != null and deptName!='' ">
            and dep.dept_name like CONCAT('%',trim(#{deptName}),'%')
        </if>
        <if test="appId != null and appId!='' ">
            and dep.APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and dep.INS_CODE =#{insCode}
        </if>
        <if test="frmzbz != null">
            <if test="frmzbz == 1 ">
              and (  dep.frmzbz = 1 or dep.frmzbz is null)
            </if>
            <if test="frmzbz == 0 ">
                and dep.frmzbz =0
            </if>
        </if>
        <if test="deptAddress != null and deptAddress!='' ">
            and dep.dept_address like CONCAT('%',trim(#{deptAddress}),'%')
        </if>
        ORDER BY IFNULL(dep.update_date,dep.CREATE_DATE) DESC
    </select>

    <select id="getPageListByObj2" parameterType="SysDepartment" resultMap="BaseResultMap">
        SELECT
        dep.dept_id as deptId,
        dep.dept_name as deptName,
        dep.APP_ID as appId,
        dep.INS_CODE as insCode,
        dep.dept_origin_id as depOriginId,
        dep.dept_parent_id as depParentId,
        dep.dept_parent_name as depParentName,
        DATE_FORMAT(dep.CREATE_DATE,'%Y-%m-%d %H:%i:%s') AS createDate,
        dep.create_user as createUser,
        dep.create_user_name as createUserName,
        DATE_FORMAT(dep.update_date,'%Y-%m-%d %H:%i:%s') AS updateDate,
        dep.update_user as updateUser,
        dep.update_user_name as updateUserName,
        dep.status as status,
        dep.frmzbz as frmzbz,
        dep.dept_type as deptType,
        app.app_name as appName,
        ins.ins_name as insName,
        ins.ins_parent_code as insParentCode
        FROM sys_department dep
        LEFT JOIN sys_app app ON dep.app_id = app.app_id AND app.status = '0' AND app.status = '0'
        LEFT JOIN sys_institution ins ON dep.ins_code = ins.ins_code AND ins.status = '0'
        WHERE 1=1
        <if test="appName != null and appName!='' ">
            and app.APP_NAME like CONCAT('%',trim(#{appName}),'%')
        </if>
        <if test="deptName != null and deptName!='' ">
            and dep.dept_name like CONCAT('%',trim(#{deptName}),'%')
        </if>
        <if test="appId != null and appId!='' ">
            and dep.APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and dep.INS_CODE =#{insCode}
        </if>
        order by dep.app_id, dep.ins_code, dep.dept_origin_id
    </select>

    <select id="getPageListByObjTree" parameterType="SysDepartment" resultMap="BaseResultMap">
        SELECT dep.*, app.app_name, ins.ins_name, ins.ins_parent_code
        from sys_department dep
                 inner join sys_app app on dep.app_id = app.app_id AND app.status = '0' AND app.status = '0'
                 inner join sys_institution ins on dep.ins_code = ins.ins_code AND ins.status = '0'
        where dep.status = '0'
    </select>


    <select id="getDepCountByName" resultType="Integer" parameterType="SysDepartment">
        select count(*)
        from sys_department
        where dept_origin_id = #{depOriginId}
          and APP_ID = #{appId}
          and INS_CODE = #{insCode}
          and status = '0'
    </select>
    <select id="getDepCountByDepOriginId" resultType="Integer" parameterType="SysDepartment">
        select count(*)
        from sys_department
        where APP_ID = #{appId}
          and INS_CODE = #{insCode}
          and dept_name = #{deptName}
    </select>
    <select id="getDepCountByParent" parameterType="SysDepartment" resultType="Integer">
        SELECT count(*)
        from sys_department
        where status = '0'
          and dept_parent_id = #{depParentId}
    </select>

    <select id="queryByAppIdInsCode" resultType="java.util.Map" parameterType="SysDepartment">
        select
        dept_name as name,
        coalesce(dept_origin_id,dept_id) as id
        from sys_department where status='0'
        <if test="appId != null and appId != ''">
            and app_id=#{appId}
        </if>
        <if test="insCode != null and insCode != ''">
            and ins_code=#{insCode}
        </if>
    </select>
    <select id="getDepartmentByAppId" parameterType="string" resultMap="BaseResultMap">
        select *
        from sys_department
        where APP_ID = #{appId}
          and status !='1'
    </select>
    <select id="getObjectByDeptOriginId" resultMap="BaseResultMap" parameterType="SysDepartment">
        select *
        from sys_department
        where app_id = #{appId}
          and ins_code = #{insCode}
          and dept_origin_id = #{depOriginId}
    </select>

    <select id="getAllDeptNotInMapping" parameterType="TDicBase" resultType="Map">
        SELECT distinct
        dept.dept_id AS dicId,
        dept.dept_name AS dicName,
        dept.dept_origin_id AS dicCode,
        tdm.id AS mapId,
        tds.stan_code AS stanCode,
        tds.stan_name AS stanName
        FROM sys_department AS dept
        LEFT JOIN t_dic_mapping AS tdm ON tdm.dic_id=dept.dept_id
        LEFT JOIN t_dic_standard AS tds ON tdm.stan_id=tds.stan_id
        WHERE dept.status='0' AND dept.dept_id NOT IN(SELECT tm.dic_id FROM t_dic_mapping AS tm)
        <if test=" keyWord != null and keyWord!='' ">
            and (dept.dept_name like CONCAT('%',trim(#{keyWord}),'%') or dept.dept_origin_id like
            CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        <if test="appId != null and appId!='' ">
            and dept.APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and dept.INS_CODE =#{insCode}
        </if>
        <if test="deptId != null and deptId!='' ">
            and dept.dept_id != #{deptId}
        </if>
    </select>

    <select id="getAllDeptInMapping" parameterType="TDicBase" resultType="Map">
        SELECT distinct
        dept.dept_id AS dicId,
        dept.dept_name AS dicName,
        dept.dept_origin_id AS dicCode,
        tdm.id AS mapId,
        tds.stan_code AS stanCode,
        tds.stan_name AS stanName
        FROM sys_department AS dept
        inner JOIN t_dic_mapping AS tdm ON tdm.dic_id=dept.dept_id
        inner JOIN t_dic_standard AS tds ON tdm.stan_id=tds.stan_id
        WHERE dept.status='0'
        <if test=" keyWord != null and keyWord!='' ">
            and (dept.dept_name like CONCAT('%',trim(#{keyWord}),'%') or dept.dept_origin_id like
            CONCAT('%',trim(#{keyWord}),'%'))
        </if>
        <if test="appId != null and appId!='' ">
            and dept.APP_ID =#{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and dept.INS_CODE =#{insCode}
        </if>
        <if test="deptId != null and deptId!='' ">
            and dept.dept_id != #{deptId}
        </if>
    </select>
    <select id="getDeptNameByAppInCodeDeptID" resultType="java.lang.String" parameterType="SysDepartment">
        SELECT a.`dept_name`
        FROM `sys_department` AS a
        WHERE a.`app_id` = #{appId} AND a.`ins_code` = #{insCode}
            AND a.`dept_id` = #{deptId}
    </select>
    <select id="getDeptNameByAppInCodeDeptOriginId" resultType="java.lang.String" parameterType="SysDepartment">
        SELECT a.`dept_name`
        FROM `sys_department` AS a
        WHERE a.`app_id` = #{appId}
          AND a.`ins_code` = #{insCode}
          AND a.`dept_origin_id` = #{deptId} limit 1
    </select>
    <select id="countDeptNameByAppInCodeDept" resultType="java.lang.Integer" parameterType="SysDepartment">
        SELECT  count(*) FROM `sys_department` AS a
                              WHERE a.`app_id`=#{appId} AND a.`ins_code`=#{insCode}
                                                           AND (a.`dept_id`=#{deptId} OR a.`dept_origin_id`=#{deptId})
    </select>

</mapper>