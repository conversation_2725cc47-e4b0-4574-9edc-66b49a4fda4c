<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysapp.SysProductMatrixMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix">
        <id column="product_matrix_id" jdbcType="VARCHAR" property="productMatrixId"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="tag" jdbcType="VARCHAR" property="tag"/>
        <result column="link" jdbcType="VARCHAR" property="link"/>
        <result column="video_url" jdbcType="VARCHAR" property="videoUrl"/>
        <result column="logo_url" jdbcType="VARCHAR" property="logoUrl"/>
        <result column="is_del" jdbcType="VARCHAR" property="isDel"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_username" jdbcType="VARCHAR" property="updateUsername"/>
        <result column="del_date" jdbcType="TIMESTAMP" property="delDate"/>
        <result column="del_user" jdbcType="VARCHAR" property="delUser"/>
        <result column="del_username" jdbcType="VARCHAR" property="delUsername"/>
    </resultMap>

    <sql id="Base_Column_List">
        product_matrix_id, category, order_num, status, name, type, tag, link, video_url, logo_url,
        is_del, create_date, create_user, create_username, update_date, update_user, update_username,
        del_date, del_user, del_username
    </sql>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix" resultMap="BaseResultMap">
        SELECT
            pm.*,
            dict.sort as categoryOrderNum
        FROM
        sys_product_matrix as pm
        LEFT JOIN
        (
            SELECT tdb.dic_id, tdb.dic_code, tdb.dic_name,tdb.sort
            FROM t_dic_base AS tdb
            LEFT JOIN t_dic_base AS tds ON tdb.parent_id = tds.dic_id
            WHERE tds.`dic_code` = 'product-matrix-category'
            and tdb.status = '0' ORDER BY tdb.sort
        ) as dict on dict.dic_code = pm.category
        <where>
            pm.is_del != '1'
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
        </where>
        order by dict.sort,pm.order_num
    </select>

    <select id="getObjectById" parameterType="String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_product_matrix
        WHERE product_matrix_id = #{id} AND is_del != '1'
    </select>

    <insert id="insert" parameterType="com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix">
        INSERT INTO sys_product_matrix (
        product_matrix_id, category, order_num, status, name, type, tag, link, video_url, logo_url,
        is_del, create_date, create_user, create_username, update_date, update_user, update_username
        ) VALUES (
        #{productMatrixId}, #{category}, #{orderNum}, #{status}, #{name}, #{type}, #{tag}, #{link}, 
        #{videoUrl}, #{logoUrl}, #{isDel}, #{createDate}, #{createUser}, #{createUsername},
        #{updateDate}, #{updateUser}, #{updateUsername}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix">
        UPDATE sys_product_matrix
        <set>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="tag != null">
                tag = #{tag},
            </if>
            <if test="link != null">
                link = #{link},
            </if>
            <if test="videoUrl != null">
                video_url = #{videoUrl},
            </if>
            <if test="logoUrl != null">
                logo_url = #{logoUrl},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="updateUsername != null and updateUsername != ''">
                update_username = #{updateUsername},
            </if>
        </set>
        WHERE product_matrix_id = #{productMatrixId}
    </update>

    <update id="deleteByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix">
        UPDATE sys_product_matrix
        SET is_del = '1',
        del_date = #{delDate},
        del_user = #{delUser},
        del_username = #{delUsername}
        WHERE product_matrix_id = #{productMatrixId}
    </update>

    <select id="listByUserId" resultMap="BaseResultMap">
        SELECT
        sys_product_matrix.*,
        dict.sort as categoryOrderNum
        FROM sys_product_matrix
        LEFT JOIN sys_role_matrix ON sys_product_matrix.product_matrix_id = sys_role_matrix.matrix_id
        LEFT JOIN sys_admin_info_rule ON sys_admin_info_rule.role_id = sys_role_matrix.role_id
        LEFT JOIN
        (
            SELECT tdb.dic_id, tdb.dic_code, tdb.dic_name,tdb.sort
            FROM t_dic_base AS tdb
            LEFT JOIN t_dic_base AS tds ON tdb.parent_id = tds.dic_id
            WHERE tds.`dic_code` = 'product-matrix-category'
            and tdb.status = '0' ORDER BY tdb.sort
        ) as dict on dict.dic_code = sys_product_matrix.category
        WHERE sys_product_matrix.is_del != '1'
        and sys_product_matrix.status = '1'
        and sys_admin_info_rule.user_id = #{userId}
        GROUP BY sys_product_matrix.product_matrix_id
        order by dict.sort,sys_product_matrix.order_num
    </select>

    <select id="getMaxOrderNum" resultType="int">
        SELECT order_num from sys_product_matrix
        where category = #{category} and is_del!='1' order by order_num desc limit 1
    </select>
</mapper>
