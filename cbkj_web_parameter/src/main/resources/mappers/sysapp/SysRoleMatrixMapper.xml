<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysapp.SysRoleMatrixMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysExt.SysRoleMatrix">
        <id column="role_matrix_id" jdbcType="VARCHAR" property="roleMatrixId"/>
        <result column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="matrix_id" jdbcType="VARCHAR" property="matrixId"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_matrix_id, role_id, matrix_id
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM sys_role_matrix
    </select>

    <select id="selectByPrimaryKey" parameterType="string" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM sys_role_matrix
        WHERE role_matrix_id = #{roleMatrixId}
    </select>

    <select id="selectByRoleId" parameterType="string" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM sys_role_matrix
        WHERE role_id = #{roleId}
    </select>

    <select id="selectByMatrixId" parameterType="string" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM sys_role_matrix
        WHERE matrix_id = #{matrixId}
    </select>

    <insert id="insert" parameterType="com.jiuzhekan.cbkj.beans.sysExt.SysRoleMatrix">
        INSERT INTO sys_role_matrix (role_matrix_id, role_id, matrix_id)
        VALUES (#{roleMatrixId}, #{roleId}, #{matrixId})
    </insert>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO sys_role_matrix
        (role_matrix_id, role_id, matrix_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleMatrixId}, #{item.roleId}, #{item.matrixId})
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.sysExt.SysRoleMatrix">
        UPDATE sys_role_matrix
        <set>
            <if test="roleId != null and roleId != ''">
                role_id = #{roleId},
            </if>
            <if test="matrixId != null and matrixId != ''">
                matrix_id = #{matrixId},
            </if>
        </set>
        WHERE role_matrix_id = #{roleMatrixId}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="string">
        DELETE FROM sys_role_matrix
        WHERE role_matrix_id = #{roleMatrixId}
    </delete>

    <select id="listMatrixIdsByRoleIds" resultType="String">
        SELECT
            distinct(matrix_id)
        FROM sys_role_matrix
        WHERE role_id in
        <foreach item="item" collection="roleIdList" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByRoleIds">
        DELETE FROM sys_role_matrix
        WHERE role_id in
        <foreach item="item" collection="roleIdList" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
