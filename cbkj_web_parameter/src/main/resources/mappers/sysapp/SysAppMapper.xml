<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysapp.SysAppMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysapp.SysApp">
        <id column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="app_name" jdbcType="VARCHAR" property="appName"/>
        <result column="app_pwd" jdbcType="VARCHAR" property="appPwd"/>
        <result column="app_desc" jdbcType="VARCHAR" property="appDesc"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
    </resultMap>


    <sql id="Base_Column_List">
        app_id
        ,app_name,app_pwd,app_desc,create_date,create_user,create_user_name,
        update_date,update_user,update_user_name,sort,status,platform_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysApp">
        update sys_app
        set status = '1'
        where app_id = #{ appId }
    </delete>
    <delete id="deleteByPrimaryKey2" parameterType="SysApp">
        delete
        from sys_app
        where app_id = #{ appId }
    </delete>
    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_app where app_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert" parameterType="SysApp">
        insert into sys_app (app_id, app_name, app_pwd, app_desc, create_date, create_user, create_user_name, status,
                             update_date, update_user, update_user_name,
                             sort, platform_code)
        values (#{appId}, #{appName}, #{appPwd}, #{appDesc}, #{createDate}, #{createUser}, #{createUserName}, #{status},
                #{updateDate}, #{updateUser}, #{updateUserName},
                #{sort}, #{platformCode})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_app
        (app_id,app_name,app_pwd,app_desc,create_date,create_user,create_user_name,update_date,update_user,update_user_name,status,sort,platform_code)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.appId},#{item.appName},#{item.appPwd},#{item.appDesc},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.updateDate},#{item.updateUser},#{item.updateUserName},
            #{item.status},#{sort},#{item.platformCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysApp">
        update sys_app
        <set>
            <if test="appName != null and appName!=''">
                app_name = #{ appName },
            </if>
            <if test="appPwd != null and appPwd!=''">
                app_pwd = #{ appPwd },
            </if>
            <if test="appDesc != null and appDesc!=''">
                app_desc = #{ appDesc },
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="updateUserName != null">
                update_user_name = #{updateUserName},
            </if>
            <if test="status != null and status!=''">
                status = #{status},
            </if>
            <if test="sort != null and sort!=''">
                sort = #{sort},
            </if>

            <if test="platformCode != null and platformCode!=''">
                platform_code = #{platformCode},
            </if>

        </set>
        where app_id = #{ appId }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_app set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''">and ${key1}=#{value1}</if>
        <if test="key2!=null and key2!=''">and ${key2}=#{value2}</if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_app where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getMapById" resultType="Map" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from sys_app where app_id = #{id}
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from sys_app where app_id = #{id} and status != '1'
    </select>

    <!--分页查询基础语句返回Map-->
    <select id="getPageDatas" parameterType="SysApp" resultType="Map">
        SELECT app_id, app_name, app_pwd, app_desc, create_date, create_user, create_user_name
        from sys_app
        where 1 = 1
    </select>


    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="SysApp" resultMap="BaseResultMap">
        SELECT app_id,app_name,sort,app_desc,update_date,update_user,update_user_name
        from sys_app
        where status !='1'
        <!--条件-->
        <if test="appName != null and appName!='' ">
            and app_name like CONCAT('%',trim(#{appName}),'%')
        </if>
        order by sort asc
    </select>

    <select id="getPageAssocation" parameterType="SysApp" resultType="com.jiuzhekan.cbkj.beans.sysapp.SysApp">
        SELECT app_id as appId,
        app_name as appName,
        sort as sort,
        status as status,
        app_desc as appDesc,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') as createDate,
        create_user as createUser,
        create_user_name as createUserName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') as updateDate,
        update_user as updateUser,
        update_user_name as updateUserName
        from sys_app
        where status !='1'
        <!--条件-->
        <if test="appName != null and appName!='' ">
            and app_name like CONCAT('%',trim(#{appName}),'%')
        </if>
        order by ifnull(update_date,create_date) desc
    </select>
    <!--   /*,sort,status,app_desc,update_date,update_user,update_user_name*/-->
    <select id="getApplist" parameterType="SysApp" resultMap="BaseResultMap">
        SELECT
        app_id,app_name
        from sys_app
        where status = '0'
        <if test="appId != null  and appId!=''">
            and app_id = #{ appId }
        </if>
    </select>

    <select id="getMaxAppId" resultType="String">
        SELECT app_id
        from sys_app
        ORDER BY app_id desc LIMIT 0,1
    </select>
    <select id="getAllAppList" resultMap="BaseResultMap">
        select * from sys_app where status='0'
    </select>
</mapper>