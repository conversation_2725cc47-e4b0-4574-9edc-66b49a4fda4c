<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.SysModualMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.SysModual">
        <id column="modual_code" jdbcType="VARCHAR"  property="modualCode" />
        <result column="modual_name" jdbcType="VARCHAR" property="modualName" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="desc" jdbcType="VARCHAR" property="desc" />
        <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    </resultMap>


    <sql id="Base_Column_List">
    modual_code,modual_name,status,desc,platform_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysModual">
        delete from sys_modual where modual_code = #{ modualCode }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_modual where modual_code in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="SysModual">
        insert into sys_modual (<include refid="Base_Column_List" />) values
        (#{modualCode},#{modualName},#{status},#{desc},#{platformCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_modual (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.modualCode},#{item.modualName},#{item.status},#{item.desc},#{item.platformCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysModual">
        update sys_modual
        <set>
             <if test="modualName != null">
                modual_name = #{ modualName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="desc != null">
                desc = #{ desc },
             </if>
             <if test="platformCode != null">
                platform_code = #{ platformCode },
             </if>
        </set>
        where modual_code = #{ modualCode }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_modual where modual_code = #{id}
    </select>

    <select id="getPageListByObj" parameterType="SysModual" resultMap="BaseResultMap">
        SELECT modual_code,modual_name,status,desc,platform_code
        from sys_modual
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getAllSysModual" resultMap="BaseResultMap">
        SELECT * FROM sys_modual
    </select>

</mapper>