<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.AdminInfoMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo">
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="last_ip" jdbcType="VARCHAR" property="lastIp"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="name_zh" jdbcType="VARCHAR" property="nameZh"/>
        <result column="user_heand" jdbcType="VARCHAR" property="userHeand"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="APP_ID" jdbcType="VARCHAR" property="appId"/>
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode"/>
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="EXPIRE_DATE" jdbcType="TIMESTAMP" property="expireDate"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUsername"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_qualifier" jdbcType="INTEGER" property="isQualifier"/>
        <result column="dept_id_his" jdbcType="VARCHAR" property="deptIdHis"/>
        <result column="dept_name_his" jdbcType="VARCHAR" property="deptNameHis"/>
        <result column="personal_share" jdbcType="VARCHAR" property="personalShare"/>
        <result column="qualifier_pic_path" jdbcType="VARCHAR" property="qualifierPicPath"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="professional" jdbcType="VARCHAR" property="professional"/>
        <result column="professional_pic_path" jdbcType="VARCHAR" property="professionalPicPath"/>
        <result column="sort" jdbcType="VARCHAR" property="sort"/>
        <result column="certificate" jdbcType="VARCHAR" property="certificate"/>
        <result column="doctor_sign_img_path" jdbcType="VARCHAR" property="doctorSignImgPath"/>
        <result column="last_update_pwd" jdbcType="VARCHAR" property="lastUpdatePwd"/>
        <result column="continue_edu_img" jdbcType="VARCHAR" property="continueEduImg"/>
        <result column="performance_appraisal_img" jdbcType="VARCHAR" property="performanceAppraisalImg"/>
        <result column="assessment_evaluation_img" jdbcType="VARCHAR" property="assessmentEvaluationImg"/>
    </resultMap>

    <resultMap id="lazyLoadRoles" type="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo" extends="BaseResultMap">
        <collection property="roles" ofType="com.jiuzhekan.cbkj.beans.sysBeans.AdminRule" select="getRolesByAdminId"
                    column="user_id">
        </collection>
    </resultMap>
    <select id="getRolesByAdminId" resultType="AdminRule">
        select r.role_id     roleId,
               r.obj_id      objId,
               r.role_name   roleName,
               r.role_desc   roleDesc,
               r.create_date createDate,
               r.create_user createUser,
               r.rname_zh    rnameZh,
               r.index_url   indexUrl
        from sys_admin_info_rule ar,
             sys_admin_rule r
        where ar.role_id = r.role_id
          AND ar.user_id = #{id}

    </select>

    <sql id="Base_Column_List">
        user_id
        ,user_name,password,sex,status,create_date,create_user,last_ip,phone,address,name_zh,user_heand,email,APP_ID,INS_CODE,dept_id,dept_name,EXPIRE_DATE,create_user_name,is_qualifier,dept_id_his,dept_name_his,qualifier_pic_path,personal_share,
update_user,update_date,professional,professional_pic_path,sort,certificate,doctor_sign_img_path,last_update_pwd,
        continue_edu_img,performance_appraisal_img,assessment_evaluation_img
    </sql>

    <select id="selectAdminRoleById" parameterType="java.lang.String" resultMap="lazyLoadRoles">
        select
        <include refid="Base_Column_List"/>
        ,(select ins_name from sys_institution ins where ins.status = '0' and ins.ins_code = ai.ins_code) as ins_name
        from sys_admin_info ai
        where user_id = #{id,jdbcType=VARCHAR} and status != '1'
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,(select ins_name from sys_institution ins where ins.status = '0' and ins.ins_code = ai.ins_code) as ins_name
        from sys_admin_info ai
        where user_id = #{id,jdbcType=VARCHAR} and status = '0'
    </select>

    <select id="selectByPrimaryOriginID" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo"
            resultMap="BaseResultMap">
        select
        user_id,user_name,password,sex,
        status,create_date,create_user,
        last_ip,phone,address,name_zh,
        user_heand,email,APP_ID,INS_CODE,dept_id,dept_name,
        EXPIRE_DATE,create_user_name,
        status,is_qualifier,dept_id_his,dept_name_his,
        personal_share,qualifier_pic_path,doctor_sign_img_path,continue_edu_img,performance_appraisal_img,assessment_evaluation_img
        from sys_admin_info
        where status='0'
        <if test="appId != null">
            and APP_ID = #{appId}
        </if>
        <if test="insCode != null">
            and INS_CODE = #{insCode}
        </if>

    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sys_admin_info
        where user_id = #{id,jdbcType=VARCHAR}
    </delete>

    <!--批量插入-->
    <insert id="insertAdminInfoList" parameterType="List">
        insert into sys_admin_info (user_id,user_name,password,sex,status,create_date,create_user,last_ip,phone
        ,address,name_zh,user_heand,email,APP_ID,INS_CODE,dept_id,dept_name,EXPIRE_DATE
        ,create_user_name,is_qualifier,dept_id_his,dept_name_his,qualifier_pic_path,personal_share,professional,sort,
                                    doctor_sign_img_path,
        continue_edu_img,performance_appraisal_img,assessment_evaluation_img)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId},#{item.userName},#{item.password},#{item.sex},#{item.status},#{item.createDate}
            ,#{item.createUser},#{item.lastIp},#{item.phone},#{item.address},#{item.nameZh},#{item.userHeand}
            ,#{item.email},#{item.appId},#{item.insCode},#{item.deptId},#{item.deptName},#{item.expireDate}
            ,#{item.createUsername}
            ,#{item.isQualifier},#{item.deptIdHis},#{item.deptNameHis},#{item.qualifierPicPath},#{item.personalShare},
             #{item.professional},#{item.sort},#{item.doctorSignImgPath},
            #{item.continueEduImg},#{item.performanceAppraisalImg},#{item.assessmentEvaluationImg})
        </foreach>
    </insert>

    <insert id="insert" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo">
        insert into sys_admin_info
        (user_id, user_name, password, sex, status, create_date, create_user, phone
        ,APP_ID,INS_CODE,dept_id, dept_name, create_user_name, personal_share
        <if test="lastIp != null">
            ,last_ip
        </if>
        <if test="address != null">
            ,address
        </if>
        <if test="nameZh != null">
            ,name_zh
        </if>
        <if test="userHeand != null">
            ,user_heand
        </if>
        <if test="email != null">
            ,email
        </if>
        <if test="expireDate != null">
            ,EXPIRE_DATE
        </if>
        <if test="isQualifier != null">
            ,is_qualifier
        </if>
        <if test="deptIdHis != null">
            ,dept_id_his
        </if>
        <if test="deptNameHis != null">
            ,dept_name_his
        </if>
        <if test="qualifierPicPath != null">
            ,qualifier_pic_path
        </if>
        <if test="professional != null">
            ,professional
        </if>
        <if test="professionalPicPath != null">
            ,professional_pic_path
        </if>
        <if test="sort != null">
            ,sort
        </if>
        <if test="certificate != null">
            ,certificate
        </if>
        <if test="doctorSignImgPath != null">
            ,doctor_sign_img_path
        </if>
        <if test="doctorSignImgPath != null">
            ,continue_edu_img
        </if>
        <if test="doctorSignImgPath != null">
            ,performance_appraisal_img
        </if>
        <if test="assessmentEvaluationImg != null">
            ,assessment_evaluation_img
        </if>
        )
        select #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
        #{sex,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},now(),
        #{createUser,jdbcType=VARCHAR} ,#{phone,jdbcType=VARCHAR},
        #{appId}, #{insCode},#{deptId},#{deptName},#{createUsername},#{personalShare}

        <if test="lastIp != null">
            ,#{lastIp}
        </if>
        <if test="address != null">
            ,#{address}
        </if>
        <if test="nameZh != null">
            ,#{nameZh}
        </if>
        <if test="email != null">
            ,#{email}
        </if>
        <if test="expireDate != null">
            ,#{expireDate}
        </if>
        <if test="isQualifier != null">
            ,#{isQualifier}
        </if>
        <if test="deptIdHis != null">
            ,#{deptIdHis}
        </if>
        <if test="deptNameHis != null">
            ,#{deptNameHis}
        </if>
        <if test="qualifierPicPath != null">
            ,#{qualifierPicPath}
        </if>
        <if test="professional != null">
            ,#{professional}
        </if>
        <if test="professionalPicPath != null">
            ,#{professionalPicPath}
        </if>
        <if test="sort != null">
            ,#{sort}
        </if>
        <if test="certificate != null">
            ,#{certificate}
        </if>
        <if test="doctorSignImgPath != null">
            ,#{doctorSignImgPath}
        </if>
        <if test="continueEduImg != null">
            ,#{continueEduImg}
        </if>
        <if test="performanceAppraisalImg != null">
            ,#{performanceAppraisalImg}
        </if>
        <if test="assessmentEvaluationImg != null">
            ,#{assessmentEvaluationImg}
        </if>
        FROM DUAL ;
    </insert>
    <!--上述sql删除了 WHERE NOT EXISTS(SELECT name FROM sys_admin_info WHERE name = #{name,jdbcType=VARCHAR} ) -->

    <insert id="insertSelective" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo">
        insert into sys_admin_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="createUser != null">
                create_user,
            </if>

            <if test="lastIp != null">
                last_ip,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="nameZh != null">
                name_zh,
            </if>
            <if test="userHeand != null">
                user_heand,
            </if>
            <if test="email != null">
                email,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>

            <if test="lastIp != null">
                #{lastIp,jdbcType=VARCHAR},
            </if>

            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="nameZh != null">
                #{nameZh,jdbcType=VARCHAR},
            </if>
            <if test="userHeand != null">
                #{userHeand,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo">
        update sys_admin_info
        <set>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null and password != ''">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>


            <if test="lastIp != null">
                last_ip = #{lastIp,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="nameZh != null">
                name_zh = #{nameZh,jdbcType=VARCHAR},
            </if>
            <if test="userHeand != null">
                user_heand = #{userHeand,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                APP_ID = #{appId},
            </if>
            <if test="insCode != null">
                INS_CODE = #{insCode},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="deptName != null ">
                dept_name = #{deptName},
            </if>

            EXPIRE_DATE = #{expireDate},

            <if test="personalShare != null ">
                personal_share = #{personalShare},
            </if>
            <if test="qualifierPicPath != null ">
                qualifier_pic_path = #{qualifierPicPath},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo">
        update sys_admin_info
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null and password != ''">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>


            <if test="updateDate != null">
                update_date = #{updateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="professional != null">
                professional = #{professional,jdbcType=VARCHAR},
            </if>
            <if test="professionalPicPath != null">
                professional_pic_path = #{professionalPicPath,jdbcType=VARCHAR},
            </if>


            <if test="lastIp != null">
                last_ip = #{lastIp,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="nameZh != null ">
                name_zh = #{nameZh,jdbcType=VARCHAR},
            </if>
            <if test="userHeand != null">
                user_heand = #{userHeand,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                APP_ID = #{appId},
            </if>
            <if test="insCode != null">
                INS_CODE = #{insCode},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="isQualifier != null">
                is_qualifier = #{isQualifier},
            </if>
            <if test="deptName != null">
                dept_name = #{deptName},
            </if>

            EXPIRE_DATE = #{expireDate},
            <if test="deptIdHis != null ">
                dept_id_his = #{deptIdHis},
            </if>
            <if test="deptNameHis != null ">
                dept_name_his = #{deptNameHis},
            </if>
            <if test="personalShare != null ">
                personal_share = #{personalShare},
            </if>
            <if test="qualifierPicPath != null ">
                qualifier_pic_path = #{qualifierPicPath},
            </if>
            <if test="certificate != null ">
                certificate = #{certificate},
            </if>
            <if test="sort != null ">
                sort = #{sort},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="doctorSignImgPath != null">
                doctor_sign_img_path = #{doctorSignImgPath,jdbcType=VARCHAR},
            </if>
            <if test="continueEduImg != null">
                continue_edu_img = #{continueEduImg,jdbcType=VARCHAR},
            </if>
            <if test="performanceAppraisalImg != null">
                performance_appraisal_img = #{performanceAppraisalImg,jdbcType=VARCHAR},
            </if>
            <if test="assessmentEvaluationImg != null">
                assessment_evaluation_img = #{assessmentEvaluationImg,jdbcType=VARCHAR}
            </if>

        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>


    <select id="loadUserByUsername" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,(select ins_name from sys_institution ins where ins.status = '0' and ins.app_id = ai.app_id and ins.ins_code =
        ai.ins_code) as ins_name
        from sys_admin_info ai
        where (user_name = #{username} ) and status = '0'
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.sysBeans.AppsInfo">
    </resultMap>
    <select id="getPageDatas" parameterType="AdminInfo" resultMap="BaseResultMap2">
        SELECT
        a.user_id as userId,
        a.user_name as userName,
        a.password,
        a.sex,
        a.status,
        a.professional_pic_path,
        DATE_FORMAT(
        a.create_date,
        '%Y-%m-%d %H:%i:%s'
        ) createDate ,
        a.create_user createUser,
        a.update_user updateUser,
        DATE_FORMAT(
        a.update_date,
        '%Y-%m-%d %H:%i:%s'
        ) updateDate,
        a.last_ip as lastIp,
        a.phone,
        a.address,
        a.name_zh AS nameZh,
        a.user_heand,
        a.email,
        a.app_id appId,
        a.ins_code insCode,
        a.dept_id deptId,
        a.dept_id_his deptIdHis,
        a.dept_name_his deptNameHis,
        group_concat(r.rname_zh) rnamess,
        group_concat(i.ins_name) insNames,
        c.user_name createUserName,
        c2.user_name updateUserName,
        group_concat(concat_ws('/',p.app_name,i.ins_name,ap.dept_name)) as doctorMultipoint
        FROM `sys_admin_info` a
        left join sys_admin_info_rule ar on a.user_id = ar.user_id
        left join sys_admin_rule r on ar.role_id = r.role_id
        left join sys_admin_info c on c.user_id = a.create_user
        left join sys_admin_info c2 on c2.user_id = a.update_user
        LEFT JOIN sys_doctor_multipoint ap ON ap.user_id = a.user_id and ap.status='0'
        left join sys_app p on ap.app_id = p.app_id
        left join sys_institution i on ap.app_id = i.APP_ID and ap.ins_code = i.INS_CODE

        WHERE a.status in ('0','2','3')
        <if test="userName != null and userName != ''">
            and (a.user_name like concat('%',trim(#{userName}),'%') or a.name_zh like concat('%',trim(#{userName}),'%')
            )
        </if>
        <if test="appId != null and appId != '' and appId != '000000'">
            and ap.app_id = #{appId}
        </if>
        <if test="insCode != null and insCode != '' and insCode != '000000'">
            and ap.ins_code = #{insCode}
        </if>
        <if test="insName != null and insName != ''">
            and i.ins_name like concat('%',#{insName},'%')
        </if>
        <if test="deptName != null and deptName != ''">
            and ap.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="roleName != null and roleName != ''">
            and r.role_name like concat('%',#{roleName},'%')
        </if>
        <if test="roleId != null and roleId != ''">
            and r.role_id =#{roleId}
        </if>
        <if test="sex != null and sex != ''">
            and a.sex = #{sex}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        GROUP BY
        a.user_id
        order by ifnull(a.update_date,a.create_date) DESC
    </select>

    <select id="getRoles" resultType="Map" parameterType="AdminRule">
        SELECT role_id,rname_zh rnameZh from sys_admin_rule
        <if test="roleId != null">
            where role_id != #{roleId}
        </if>
    </select>

    <select id="getRoleByObj" resultType="AdminRule" parameterType="AdminRule">
        SELECT role_id,role_name,rname_zh rnameZh from sys_admin_rule
        <where>
            <if test="roleId!=null and roleId!=''">
                and role_id = #{roleId}
            </if>
            <if test="roleName!=null and roleName!=''">
                and role_name = #{roleName}
            </if>
            <if test="rnameZh!=null and rnameZh!=''">
                and rname_zh = #{rnameZh}
            </if>
        </where>
        limit 1
    </select>

    <insert id="insertAdminRule" parameterType="AdmininfoRule">

        insert into sys_admin_info_rule (role_id, user_id)
        VALUES (#{roleId}, #{userId})
    </insert>

    <update id="updatePwd" parameterType="Map">
        update sys_admin_info
        set password = #{newPwd}, last_update_pwd = #{last_update_pwd}
        where user_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="UpdateAdminInfo" parameterType="String">
        update sys_admin_info
        set INS_CODE = #{insCode}
        where user_id = #{id}
    </update>
    <update id="UpdateDeptName" parameterType="String">
        update sys_admin_info
        set dept_name = #{deptName}
        where dept_id = #{deptId}
    </update>
    <update id="UpdateAdminInfoDep" parameterType="String">
        update sys_admin_info
        set dept_id = #{deptId}
        where user_id = #{id}
    </update>
    <update id="updateStatus" parameterType="Map">
        update sys_admin_info
        set status = #{status}
        where user_id = #{id}

    </update>

    <delete id="deleteAdminRole" parameterType="ArrayList">

        delete from sys_admin_info_rule where user_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="deleteBylis" parameterType="Map">
        update sys_admin_info set status = '1' where user_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>

    <select id="getAdmins" resultType="Map" parameterType="String">
        select user_id uuid, user_name, user_heand heand, email
        from sys_admin_info
        where user_id != #{id}
          and status ='0'

    </select>

    <insert id="insertList" parameterType="List">
        insert into sys_admin_info_rule (role_id,user_id) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleId},#{item.userId})
        </foreach>
    </insert>

    <delete id="deleteAdminInfoRuleByAdminId" parameterType="String">
        delete
        from sys_admin_info_rule
        where user_id = #{id}

    </delete>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_admin_info where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="validateParam" parameterType="Map" resultType="Map">
        SELECT
        a.`user_name`,
        a.name_zh
        FROM
        sys_admin_info AS a
        <where>
            a.status = '0'
            <if test="name != null and name !=''">
                and a.user_name = #{name}
            </if>

            <if test="appId != null and appId !=''">
                AND a.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode !=''">
                AND a.INS_CODE = #{insCode}
            </if>
            <if test="id != null and id != ''">
                AND a.user_id != #{id}
            </if>

        </where>

    </select>

    <select id="getLikeAdmins" parameterType="AdminInfo" resultType="Map">
        select
        user_id,user_name,password,sex,
        status,create_date,create_user,
        last_ip,phone,address,name_zh,
        user_heand,email,APP_ID,INS_CODE,dept_id,dept_name,
        EXPIRE_DATE,create_user_name,
        status
        from sys_admin_info
        where status='0'
        <if test="name != null">
            and (user_name like concat('%',#{name},'%') or name_zh like concat('%',#{name},'%'))
        </if>
    </select>

    <select id="getPicPathById" parameterType="String" resultType="AdminInfo">
        SELECT qualifier_pic_path qualifierPicPath
        from sys_admin_info
        where user_id = #{userId}
    </select>


    <select id="getListByAppInsDept" parameterType="AdminInfo" resultMap="BaseResultMap">
        select ai.user_id, ai.user_name, ai.name_zh,
        ap.app_id, ap.ins_code, ap.dept_id, ap.dept_name
        from sys_admin_info ai
        left join sys_doctor_multipoint ap on ap.user_id = ai.user_id
        <where>
            ai.status = '0'
            <if test="appId != null and appId != ''">
                and ap.app_id = #{appId}
            </if>
            <if test="insCode != null and insCode != ''">
                and ap.ins_code = #{insCode}
            </if>
            <if test="deptId != null and deptId != ''">
                and ap.dept_id = #{deptId}
            </if>
        </where>
    </select>
    <select id="getAdminInfoByAppId" parameterType="string" resultMap="BaseResultMap">
        select *
        from sys_admin_info
        where app_id = #{appId}
          and status != '1'
    </select>
    <select id="getUserNameById" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_info where user_id=#{createUserId}
    </select>
    <select id="getGrayscaleUserById" parameterType="string" resultMap="BaseResultMap">
        select sa.user_id, sa.name_zh, sa.user_name
        from sys_admin_grayscale as sag
                 join sys_admin_info sa on sa.user_id = sag.user_id
        where sag.user_id = #{createUserId}
    </select>

    <resultMap id="getGrayscaleStatus" type="com.jiuzhekan.cbkj.beans.sysBeans.Grayscale">
    </resultMap>
    <select id="getGrayscaleStatus" parameterType="AdminInfo" resultMap="getGrayscaleStatus">
        SELECT
        sa.`user_name` as userName,
        sa.`name_zh` as nameZh,
        sa.`user_id` as userId,
        (CASE sag.grayscale_status WHEN '0' THEN '0' ELSE '1' END ) AS grayscaleStatus,
        sag.create_user_name AS createUserName,
        GROUP_CONCAT(r.rname_zh) rnamess,
        GROUP_CONCAT(CONCAT_WS('/',p.app_name,i.ins_name,ap.dept_name)) AS doctorMultipoint,
        DATE_FORMAT(
        sag.create_date,
        '%Y-%m-%d %H:%i:%s'
        ) createDate
        FROM
        sys_admin_grayscale AS sag
        JOIN sys_admin_info AS sa ON sa.`user_id`=sag.`user_id`
        LEFT JOIN sys_admin_info_rule ar ON sa.user_id = ar.user_id
        LEFT JOIN sys_admin_rule r ON ar.role_id = r.role_id
        LEFT JOIN sys_doctor_multipoint ap ON ap.user_id = sa.user_id AND ap.status='0'
        LEFT JOIN sys_app p ON ap.app_id = p.app_id
        LEFT JOIN sys_institution i ON ap.app_id = i.APP_ID AND ap.ins_code = i.INS_CODE
        WHERE sa.status IN ('0','2','3')
        <if test="userName != null and userName != ''">
            and (sa.user_name like concat('%',trim(#{userName}),'%') or sa.name_zh like
            concat('%',trim(#{userName}),'%')
            )
        </if>
        <if test="appId != null and appId != '' and appId != '000000'">
            and ap.app_id = #{appId}
        </if>
        <if test="insCode != null and insCode != '' and insCode != '000000'">
            and ap.ins_code = #{insCode}
        </if>
        <if test="insName != null and insName != ''">
            and i.ins_name like concat('%',#{insName},'%')
        </if>
        <if test="deptName != null and deptName != ''">
            and ap.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="roleName != null and roleName != ''">
            and r.role_name like concat('%',#{roleName},'%')
        </if>
        <if test="roleId != null and roleId != ''">
            and r.role_id =#{roleId}
        </if>
        <if test="grayscaleStatus != null and grayscaleStatus != ''">
            <choose>
                <when test="grayscaleStatus == 1 ">
                    and sag.grayscale_status !='0'
                </when>
                <otherwise>
                    and sag.grayscale_status =#{grayscaleStatus}
                </otherwise>
            </choose>
        </if>
        group by sag.user_id
        order by sag.create_date DESC
    </select>
</mapper>
