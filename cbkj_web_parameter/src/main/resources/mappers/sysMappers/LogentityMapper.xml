<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.LogentityMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.Logentity">
        <id column="lid" jdbcType="VARCHAR"  property="lid" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="httpMethod" jdbcType="VARCHAR" property="httpMethod" />
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <result column="className" jdbcType="VARCHAR" property="className" />
        <result column="methodName" jdbcType="VARCHAR" property="methodName" />
        <result column="execuType" jdbcType="VARCHAR" property="execuType" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="descr" jdbcType="VARCHAR" property="descr" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_id" jdbcType="VARCHAR" property="createId" />
        <result column="errmsg" jdbcType="VARCHAR" property="errmsg" />
        <result column="is_ok" jdbcType="INTEGER" property="isOk" />
    </resultMap>

    <sql id="Base_Column_List">
    lid,url,httpMethod,ip,className,methodName,execuType,status,descr,create_date,create_id,errmsg,is_ok
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="Logentity">
        delete from sys_logentity where lid = #{ lid }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_logentity where lid in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="Logentity">
        insert into sys_logentity (lid,url,httpMethod,ip,className,methodName,execuType,status,descr,create_date,create_id,errmsg) values
        (#{lid},#{url},#{httpMethod},#{ip},#{className},#{methodName},#{execuType},#{status},#{descr},now(),#{createId},#{errmsg})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_logentity (lid,url,httpMethod,ip,className,methodName,execuType,status,descr,create_date,create_id,errmsg) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.lid},#{item.url},#{item.httpMethod},#{item.ip},#{item.className},#{item.methodName},#{item.execuType},#{item.status},#{item.descr},#{item.createDate},#{item.createId},#{item.errmsg})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="Logentity">
        update sys_logentity
        set
        url = #{ url },
        httpMethod = #{ httpMethod },
        ip = #{ ip },
        className = #{ className },
        methodName = #{ methodName },
        execuType = #{ execuType },
        status = #{ status },
        descr = #{ descr },
        create_date = #{ createDate },
        create_id = #{ createId },
        errmsg = #{ errmsg }
        where lid = #{ lid }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_logentity set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_logentity where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_logentity where lid = #{id}
    </select>

    <!--分页查询基础语句-->
    <select id="getPageDatas" parameterType="Logentity" resultType="Map">
        SELECT lid,url,httpMethod,ip,methodName,execuType,status,descr,DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') create_date,create_id,errmsg,is_ok  from sys_logentity
        where 1=1
        <!--条件-->
        <if test="descr != null and descr!='' ">
            and (descr like CONCAT('%',trim(#{descr}),'%') or methodName like CONCAT('%',trim(#{descr}),'%'))
        </if>
        <if test=" beginTime != null">
            and create_date >= #{beginTime}
        </if>
        <if test=" endTime!=null ">
            and create_date &lt;= #{endTime}
        </if>
        <if test=" createId != null and createId!='' ">
            and create_id =#{createId}
        </if>
        order by create_date desc
    </select>

    <update id="changeStatus" parameterType="Map">
        update sys_logentity set is_ok = 2,ok_id = #{okID} where lid in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getSysIdByName" parameterType="java.lang.String" resultType="java.lang.String">
        select id from sys_admin_info where name_zh = #{name} limit 1
    </select>
    <select id="getSysNameById" parameterType="java.lang.String" resultType="java.lang.String">
        select name_zh from sys_admin_info where id = #{id}
    </select>
</mapper>
