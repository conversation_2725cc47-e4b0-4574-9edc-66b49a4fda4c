<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.AdminRuleMapper">


    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.AdminRule">
        <id column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_desc" jdbcType="VARCHAR" property="roleDesc"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="rname_zh" jdbcType="VARCHAR" property="rnameZh"/>
        <result column="index_url" jdbcType="VARCHAR" property="indexUrl"/>
        <result column="obj_id" jdbcType="VARCHAR" property="objId"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.sysBeans.SysAdminRuleMenu">
        <id column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="menu_id" jdbcType="VARCHAR" property="menuId"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id
        , role_name, role_desc, create_date, create_user, rname_zh,index_url,obj_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_rule
        where role_id = #{roleId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sys_admin_rule
        where role_id = #{ids}
    </delete>

    <insert id="insertSysAdminRuleMenuList" parameterType="List">
        insert into sys_admin_rule_menu
        (role_id,menu_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>
    </insert>
    <insert id="insert" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminRule">
        insert into sys_admin_rule (role_id, role_name, role_desc,
                                    create_date, create_user, rname_zh, index_url, obj_id)
        values (#{roleId,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{roleDesc,jdbcType=VARCHAR},
                now(), #{createUser,jdbcType=VARCHAR}, #{rnameZh,jdbcType=VARCHAR}
                   , #{indexUrl,jdbcType=VARCHAR}, #{objId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminRule">
        insert into sys_admin_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="roleDesc != null">
                role_desc,
            </if>

            <if test="createUser != null">
                create_user,
            </if>
            <if test="rnameZh != null">
                rname_zh,
            </if>
            <if test="indexUrl != null">
                index_url,
            </if>
            <if test="objId != null">
                obj_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=VARCHAR},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleDesc != null">
                #{roleDesc,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="rnameZh != null">
                #{rnameZh,jdbcType=VARCHAR},
            </if>
            <if test="indexUrl != null">
                #{indexUrl,jdbcType=VARCHAR},
            </if>
            <if test="objId != null">
                #{objId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminRule">
        update sys_admin_rule
        <set>
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleDesc != null">
                role_desc = #{roleDesc,jdbcType=VARCHAR},
            </if>

            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="rnameZh != null">
                rname_zh = #{rnameZh,jdbcType=VARCHAR},
            </if>
            <if test="indexUrl != null">
                index_url = #{indexUrl,jdbcType=VARCHAR},
            </if>
            <if test="objId != null">
                obj_id = #{objId,jdbcType=VARCHAR},
            </if>
        </set>
        where role_id = #{roleId,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.AdminRule">
        update sys_admin_rule
        set role_name = #{roleName,jdbcType=VARCHAR},
            role_desc = #{roleDesc,jdbcType=VARCHAR},
            rname_zh  = #{rnameZh,jdbcType=VARCHAR},
            index_url = #{indexUrl,jdbcType=VARCHAR},
            obj_id    = #{objId,jdbcType=VARCHAR}
        where role_id = #{roleId,jdbcType=VARCHAR}
    </update>

    <select id="getPageDatas" parameterType="AdminRule" resultType="Map">
        select r.role_id, r.role_name, r.role_desc, DATE_FORMAT(r.create_date,'%Y-%m-%d %H:%i:%s') create_date,
        r.create_user, r.rname_zh,r.index_url
        ,a.user_name createName,obj_id objId
        from sys_admin_rule r,sys_admin_info a where a.user_id = r.create_user
        <if test="roleName != null and roleName.length()>0">
            and r.rname_zh like CONCAT('%',trim(#{roleName}),'%')
        </if>
        <if test="roleId != null">
            and r.role_id != #{roleId}
        </if>
    </select>

    <select id="getRuleRelesCount" parameterType="String" resultType="Long">
        select count(1) cun
        from sys_admin_info_rule
        where role_id = #{ids}
    </select>

    <delete id="deleteRuleMenuByRid" parameterType="java.lang.String">
        delete
        from sys_admin_info_rule
        where role_id = #{ids}
    </delete>

    <delete id="deleteRuleMenuByRids" parameterType="java.lang.String">
        delete
        from sys_admin_rule_menu
        where role_id = #{ids}
    </delete>

    <!-- getRepeatNameS 判断保存的时候中文名和英文名是否重复-->
    <select id="getRepeatNameS" parameterType="AdminRule" resultMap="BaseResultMap">
        select role_id , role_name,rname_zh from sys_admin_rule
        <where>
            (role_name = #{roleName} or rname_zh = #{rnameZh} )
        </where>
    </select>

    <select id="getAllAdminRulesByKeyWord" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_rule
      <where>
          <if test="keyWord != null and keyWord !='' ">
              and (role_name like CONCAT('%',trim(#{keyWord}),'%') or rname_zh like
              CONCAT('%',trim(#{keyWord}),'%'))
          </if>
      </where>
        order by create_date desc
    </select>
    <select id="getRepeatNameById" parameterType="string" resultMap="BaseResultMap">
        select role_id, role_name, rname_zh
        from sys_admin_rule
        where role_id!=#{roleId}
    </select>
    <select id="getAllAdminRuleMenuByRoleId" parameterType="string" resultMap="BaseResultMap2">
        select *
        from sys_admin_rule_menu
        where role_id = #{roleId}
    </select>
    <select id="getAdminRuleById" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_admin_rule
        where role_id = #{roleId}
    </select>

</mapper>