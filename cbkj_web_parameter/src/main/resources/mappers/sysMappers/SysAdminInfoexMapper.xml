<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.SysAdminInfoexMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex">
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="USER_EX_TEXT" jdbcType="VARCHAR" property="userExText" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="input_code" jdbcType="VARCHAR" property="inputCode" />
        <result column="introduce" jdbcType="VARCHAR" property="introduce" />
        <result column="prescription_share" jdbcType="VARCHAR" property="prescriptionShare" />
        <result column="description_share" jdbcType="VARCHAR" property="descriptionShare" />
        <result column="default_prescription_share" jdbcType="VARCHAR" property="defaultPrescriptionShare" />
        <result column="user_habit_oral" jdbcType="VARCHAR" property="userHabitOral" />
        <result column="user_habit_outward" jdbcType="VARCHAR" property="userHabitOutward" />
        <result column="user_habit_appropriate" jdbcType="VARCHAR" property="userHabitAppropriate" />
        <result column="one_month_no_divide" jdbcType="VARCHAR" property="oneMonthNoDivide" />
        <result column="special_drugs_use_permission" jdbcType="VARCHAR" property="specialDrugsUsePermission" />
    </resultMap>


    <sql id="Base_Column_List">
    user_id,USER_EX_TEXT,UPDATE_DATE,input_code,introduce,prescription_share,description_share,default_prescription_share,user_habit_oral,user_habit_outward,user_habit_appropriate,one_month_no_divide,special_drugs_use_permission
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysAdminInfoex">
        delete from sys_admin_infoex where user_id = #{userId}
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_admin_infoex where user_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="SysAdminInfoex">
        insert into sys_admin_infoex (<include refid="Base_Column_List" />) values
        (#{userId},#{userExText},#{updateDate},#{inputCode},#{introduce},#{prescriptionShare},#{descriptionShare},#{defaultPrescriptionShare},#{userHabitOral},#{userHabitOutward},#{userHabitAppropriate},#{oneMonthNoDivide},#{specialDrugsUsePermission})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_admin_infoex (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.userId},#{item.userExText},#{item.updateDate},#{item.inputCode},#{item.introduce},#{item.prescriptionShare},#{item.descriptionShare},#{item.defaultPrescriptionShare},#{item.userHabitOral},#{item.userHabitOutward},#{item.userHabitAppropriate},#{item.oneMonthNoDivide},#{item.specialDrugsUsePermission})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysAdminInfoex">
        update sys_admin_infoex
        <set>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="userExText != null">
                USER_EX_TEXT = #{ userExText },
             </if>
             <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
             </if>
             <if test="inputCode != null">
                input_code = #{ inputCode },
             </if>
             <if test="introduce != null">
                introduce = #{ introduce },
             </if>
             <if test="prescriptionShare != null">
                prescription_share = #{ prescriptionShare },
             </if>
             <if test="descriptionShare != null">
                description_share = #{ descriptionShare },
             </if>
             <if test="defaultPrescriptionShare != null">
                default_prescription_share = #{ defaultPrescriptionShare },
             </if>
             <if test="userHabitOral != null">
                user_habit_oral = #{ userHabitOral },
             </if>
             <if test="userHabitOutward != null">
                user_habit_outward = #{ userHabitOutward },
             </if>
             <if test="userHabitAppropriate != null">
                user_habit_appropriate = #{ userHabitAppropriate },
             </if>
             <if test="oneMonthNoDivide != null">
                one_month_no_divide = #{ oneMonthNoDivide },
             </if>
             <if test="specialDrugsUsePermission != null">
                special_drugs_use_permission = #{ specialDrugsUsePermission },
             </if>
        </set>
        where user_id = #{ userId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_admin_infoex where user_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="SysAdminInfoex" resultMap="BaseResultMap">
        SELECT user_id,USER_EX_TEXT,UPDATE_DATE,input_code,introduce,prescription_share,description_share,default_prescription_share,user_habit_oral,user_habit_outward,user_habit_appropriate,one_month_no_divide,special_drugs_use_permission
        from sys_admin_infoex
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getCountByObj" parameterType="SysAdminInfoex" resultType="Long">
        select count(*) from sys_admin_infoex
<where>
    <if test="userId != null and userId !=''">
        and user_id = #{ userId }
    </if>
</where>
    </select>

    <select id="getObjectByUserId" parameterType="SysAdminInfoex" resultMap="BaseResultMap">
        select * from sys_admin_infoex
        <where>
            <if test="userId != null and userId !=''">
                and user_id = #{ userId }
            </if>
        </where>
    </select>

</mapper>