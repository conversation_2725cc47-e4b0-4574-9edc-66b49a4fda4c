<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.SysLogInterfaceErrorMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.SysLogInterfaceError">
        <id column="ID" jdbcType="VARCHAR"  property="id" />
        <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode" />
        <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId" />
        <result column="DOCTOR_NAME" jdbcType="VARCHAR" property="doctorName" />
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId" />
        <result column="PATIENT_NAME" jdbcType="VARCHAR" property="patientName" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="INTERFACE_NAME" jdbcType="VARCHAR" property="interfaceName" />
        <result column="INTERFACE_DESC" jdbcType="VARCHAR" property="interfaceDesc" />
        <result column="INTERFACE_TOKEN" jdbcType="VARCHAR" property="interfaceToken" />
        <result column="INTERFACE_PARAMS" jdbcType="VARCHAR" property="interfaceParams" />
        <result column="ERROR_STACK" jdbcType="VARCHAR" property="errorStack" />
    </resultMap>


    <sql id="Base_Column_List">
    ID,APP_ID,INS_CODE,DOCTOR_ID,DOCTOR_NAME,PATIENT_ID,PATIENT_NAME,create_date,INTERFACE_NAME,INTERFACE_DESC,INTERFACE_TOKEN,INTERFACE_PARAMS,ERROR_STACK
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysLogInterfaceError">
        delete from sys_log_interface_error where ID = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_log_interface_error where ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="SysLogInterfaceError">
        insert into sys_log_interface_error (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{insCode},#{doctorId},#{doctorName},#{patientId},#{patientName},#{createDate},#{interfaceName},#{interfaceDesc},#{interfaceToken},#{interfaceParams},#{errorStack})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_log_interface_error (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.insCode},#{item.doctorId},#{item.doctorName},#{item.patientId},#{item.patientName},#{item.createDate},#{item.interfaceName},#{item.interfaceDesc},#{item.interfaceToken},#{item.interfaceParams},#{item.errorStack})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysLogInterfaceError">
        update sys_log_interface_error
        <set>
             <if test="appId != null">
                APP_ID = #{ appId },
             </if>
             <if test="insCode != null">
                INS_CODE = #{ insCode },
             </if>
             <if test="doctorId != null">
                DOCTOR_ID = #{ doctorId },
             </if>
             <if test="doctorName != null">
                DOCTOR_NAME = #{ doctorName },
             </if>
             <if test="patientId != null">
                PATIENT_ID = #{ patientId },
             </if>
             <if test="patientName != null">
                PATIENT_NAME = #{ patientName },
             </if>
             <if test="createDate != null">
                 create_date = #{ createDate },
             </if>
             <if test="interfaceName != null">
                INTERFACE_NAME = #{ interfaceName },
             </if>
             <if test="interfaceDesc != null">
                INTERFACE_DESC = #{ interfaceDesc },
             </if>
             <if test="interfaceToken != null">
                INTERFACE_TOKEN = #{ interfaceToken },
             </if>
             <if test="interfaceParams != null">
                INTERFACE_PARAMS = #{ interfaceParams },
             </if>
             <if test="errorStack != null">
                ERROR_STACK = #{ errorStack },
             </if>
        </set>
        where ID = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_log_interface_error where ID = #{id}
    </select>

    <select id="getPageListByObj" parameterType="SysLogInterfaceError" resultMap="BaseResultMap">
        SELECT ID,APP_ID,INS_CODE,DOCTOR_ID,DOCTOR_NAME,PATIENT_ID,PATIENT_NAME,create_date,INTERFACE_NAME,INTERFACE_DESC,INTERFACE_TOKEN
        from sys_log_interface_error
        <where>
            <if test=" interfaceDesc != null and interfaceDesc!='' ">
                and INTERFACE_DESC like CONCAT('%',trim(#{interfaceDesc}),'%')
            </if>
            <if test=" beginTime != null">
                and create_date >= #{beginTime}
            </if>
            <if test=" endTime!=null ">
                and create_date &lt;= #{endTime}
            </if>
        </where>
        order by create_date desc
    </select>

</mapper>
