<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.SysDoctorMultipointMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint">
        <id column="user_id" jdbcType="VARCHAR"  property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="is_qualifier" jdbcType="INTEGER" property="isQualifier" />
        <result column="dept_id_his" jdbcType="VARCHAR" property="deptIdHis" />
        <result column="dept_name_his" jdbcType="VARCHAR" property="deptNameHis" />
        <result column="qualifier_pic_path" jdbcType="VARCHAR" property="qualifierPicPath" />
        <result column="personal_share" jdbcType="VARCHAR" property="personalShare" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    </resultMap>


    <sql id="Base_Column_List">
    user_id,user_name,status,create_date,address,email,app_id,ins_code,dept_id,dept_name,create_user,create_user_name,is_qualifier,dept_id_his,dept_name_his,qualifier_pic_path,personal_share,employee_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysDoctorMultipoint">
        delete from sys_doctor_multipoint where user_id = #{ userId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_doctor_multipoint where user_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <delete id="deleteByAdminId" parameterType="String">
        delete from sys_doctor_multipoint where user_id = #{userId}
    </delete>
    <delete id="deleteByAdminIds" parameterType="Map">
        update sys_doctor_multipoint set status = '1' where user_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="SysDoctorMultipoint">
        insert into sys_doctor_multipoint (<include refid="Base_Column_List" />) values
        (#{userId},#{userName},#{status},#{createDate},#{address},#{email},#{appId},#{insCode},#{deptId},#{deptName},#{createUser},#{createUserName},#{isQualifier},#{deptIdHis},#{deptNameHis},#{qualifierPicPath},#{personalShare})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_doctor_multipoint (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.userId},#{item.userName},#{item.status},#{item.createDate},#{item.address},#{item.email},#{item.appId},#{item.insCode},#{item.deptId},#{item.deptName},#{item.createUser},#{item.createUserName},#{item.isQualifier},#{item.deptIdHis},
             #{item.deptNameHis},#{item.qualifierPicPath},#{item.personalShare},#{item.employeeId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysDoctorMultipoint">
        update sys_doctor_multipoint
        <set>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="address != null">
                address = #{ address },
             </if>
             <if test="email != null">
                email = #{ email },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="isQualifier != null">
                is_qualifier = #{ isQualifier },
             </if>
             <if test="deptIdHis != null">
                dept_id_his = #{ deptIdHis },
             </if>
             <if test="deptNameHis != null">
                dept_name_his = #{ deptNameHis },
             </if>
             <if test="qualifierPicPath != null">
                qualifier_pic_path = #{ qualifierPicPath },
             </if>
             <if test="personalShare != null">
                personal_share = #{ personalShare },
             </if>
        </set>
        where user_id = #{ userId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_doctor_multipoint where user_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="SysDoctorMultipoint" resultMap="BaseResultMap">
        SELECT user_id,user_name,status,create_date,address,email,app_id,ins_code,dept_id,dept_name,create_user,create_user_name,is_qualifier,dept_id_his,dept_name_his,qualifier_pic_path,personal_share
        from sys_doctor_multipoint
        <where>
            status='0'
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getPracticeList" resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint" >

        SELECT  p.user_id,
         p.user_name,
         p.employee_id,
         p.create_date,
         p.address,
         p.email,
         p.app_id,
         p.ins_code,
         p.dept_id,
         p.dept_name,
         p.create_user,
         p.create_user_name,
         p.is_qualifier,
         p.dept_id_his,
         p.dept_name_his,
         p.qualifier_pic_path,
         p.personal_share,
        i.ins_name insName,
        sa.app_name appName
        from sys_doctor_multipoint p
        left join sys_institution i on i.INS_CODE = p.ins_code and i.status='0' and i.app_id = p.app_id
        left join sys_app sa on sa.app_id = p.app_id
        where p.user_id = #{userId} and p.status='0' and sa.status='0' and i.status='0'
        <if test=" appId != null and appId != '' ">
            and p.app_id = #{appId}
        </if>
        <if test=" insCode != null and insCode != '' ">
            and p.ins_code = #{insCode}
        </if>

    </select>
    <select id="checkUserEmployeeId" resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.sysBeans.SysDoctorMultipoint" >

        SELECT  p.user_id
        from sys_doctor_multipoint p
        inner join sys_admin_info as s on s.user_id=p.user_id and s.status='0'
        where p.status='0'
        <if test=" appId != null and appId != '' ">
            and p.app_id = #{appId}
        </if>
        <if test=" insCode != null and insCode != '' ">
            and p.ins_code = #{insCode}
        </if>
        <if test=" employeeId != null and employeeId != '' ">
            and p.employee_id = #{employeeId}
        </if>
    </select>
</mapper>