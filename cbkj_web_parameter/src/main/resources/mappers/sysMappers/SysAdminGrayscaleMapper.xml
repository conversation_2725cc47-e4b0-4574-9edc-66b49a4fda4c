<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.SysAdminGrayscaleMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.AdminGrayscale">
        <id column="user_id" jdbcType="VARCHAR"  property="userId" />
        <result column="grayscale_status" jdbcType="INTEGER" property="grayscaleStatus" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    </resultMap>


    <sql id="Base_Column_List">
    user_id,grayscale_status,create_date,create_user,create_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="AdminGrayscale">
        delete from sys_admin_grayscale where user_id = #{ userId }
    </delete>

    <delete id="deleteAll">
        delete from sys_admin_grayscale
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_admin_grayscale where user_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="AdminGrayscale">
        insert into sys_admin_grayscale (<include refid="Base_Column_List" />) values
        (#{userId},#{grayscaleStatus},#{createDate},#{createUser},#{createUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_admin_grayscale (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.userId},#{item.grayscaleStatus},#{item.createDate},#{item.createUser},#{item.createUserName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="AdminGrayscale">
        update sys_admin_grayscale
        <set>
             <if test="grayscaleStatus != null">
                grayscale_status = #{ grayscaleStatus },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
        </set>
        where user_id = #{ userId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_admin_grayscale where user_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="AdminGrayscale" resultMap="BaseResultMap">
        SELECT user_id,grayscale_status,create_date,create_user,create_user_name
        from sys_admin_grayscale
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>