<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.sysmapper.OperationLogMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.sysBeans.OperationLog">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="operate_id" jdbcType="VARCHAR" property="operateId" />
        <result column="operate_name" jdbcType="VARCHAR" property="operateName" />

        <result column="method_params" jdbcType="VARCHAR" property="methodParams" />
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <result column="operate_date" jdbcType="TIMESTAMP" property="operateDate" />
        <result column="desc" jdbcType="VARCHAR" property="desc" />


        <result column="method_name" jdbcType="VARCHAR" property="methodName" />
        <result column="content" jdbcType="VARCHAR" property="content" />
    </resultMap>

    <sql id="Base_Column_List">
    id,operate_name,operate_id,`desc`,method_params,ip,operate_date,method_name,content
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="OperationLog">
        delete from sys_operation_log where id = #{ id }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_operation_log where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="OperationLog">
        insert into sys_operation_log (id,operate_name,operate_id,`desc`,method_params,ip,operate_date,method_name,content) values
        (#{id},#{operateName},#{operateId},#{desc},#{methodParams},#{ip},now(),#{methodName},#{content})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into sys_operation_log (id,operate_name,operate_id,`desc`,method_params,ip,operate_date,method_name,content) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.operateName},#{item.operateId},#{item.desc},#{item.methodParams},#{item.ip},#{item.operateDate},#{item.methodName},#{item.content})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="OperationLog">
        update sys_operation_log
        set
        operate_name = #{ operateName },
        operate_id = #{ operateId },
        `desc` = #{ desc },
        method_params = #{methodParams},
        ip = #{ ip },
        operate_date = #{ operateDate },
        method_name = #{ methodName },
        content = #{ content }
        where id = #{ id }
    </update>

    <!-- 更新多个或者单个字段 -->
    <update id="updateM" parameterType="Map">
        update sys_operation_log set ${filed}=#{filedValue}
        <if test="filed2!=null and filed2!=''">,${filed2}=#{filedValue2}</if>
        <if test="filed3!=null and filed3!=''">,${filed3}=#{filedValue3}</if>
        <if test="filed4!=null and filed4!=''">,${filed4}=#{filedValue4}</if>
        <!-- 可扩展 -->
        where 1=1
        <if test="key1!=null and key1!=''"> and ${key1}=#{value1} </if>
        <if test="key2!=null and key2!=''"> and ${key2}=#{value2} </if>
        <!-- 可扩展 -->
    </update>

    <!--查询某个字段的某条数据数量-->
    <select id="getObjExists" parameterType="Map" resultType="int">
        select count(1) cun from sys_operation_log where 1=1
        <if test="key!=null and key!=''">
            and ${key} = #{value}
        </if>
        <if test="key2!=null and key2!=''">
            and ${key2} = #{value2}
        </if>
        <!-- 不等于本身时使用 -->
        <if test="key3!=null and key3!=''">
            and ${key3} != #{value3}
        </if>
        <!-- 可扩展 -->
    </select>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_operation_log where id = #{id}
    </select>

    <!--分页查询基础语句-->
    <select id="getPageDatas" parameterType="OperationLog" resultType="Map">
        SELECT id,operate_name,operate_id,`desc`,method_params,ip,DATE_FORMAT(operate_date,'%Y-%m-%d %H:%i:%s') operate_date,method_name,content  from sys_operation_log
        where 1=1
        <!--条件-->
        <if test="desc != null and desc!='' ">
            and `desc` like CONCAT('%',trim(#{desc}),'%')
        </if>
        <if test="operateName != null and operateName!='' ">
            and operate_name = #{operateName}
        </if>
        <if test=" beginTime != null">
            and operate_date >= #{beginTime}
        </if>
        <if test=" endTime!=null ">
            and operate_date &lt;= #{endTime}
        </if>
        order by operate_date desc
    </select>

</mapper>
