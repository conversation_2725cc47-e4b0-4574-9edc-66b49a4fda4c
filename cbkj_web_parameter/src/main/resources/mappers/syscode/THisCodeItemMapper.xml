<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.syscode.THisCodeItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.sysCode.THisCodeItem">
        <id column="ITEM_ID" jdbcType="VARCHAR"  property="itemId" />
        <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
        <result column="INS_CODE" jdbcType="VARCHAR" property="insCode" />
        <result column="ITEM_HIS_ID" jdbcType="VARCHAR" property="itemHisId" />
        <result column="ITEM_HIS_NAME" jdbcType="VARCHAR" property="itemHisName" />
        <result column="CODE_ID" jdbcType="VARCHAR" property="codeId" />
        <result column="HIDE_PROJECT" jdbcType="VARCHAR" property="hideProject" />
        <result column="ZIFU1" jdbcType="VARCHAR" property="zifu1" />
        <result column="ZIFU2" jdbcType="VARCHAR" property="zifu2" />
        <result column="ZIFU3" jdbcType="VARCHAR" property="zifu3" />
        <result column="ZIFU4" jdbcType="VARCHAR" property="zifu4" />
        <result column="ZIFU5" jdbcType="VARCHAR" property="zifu5" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="DEL_DATE" jdbcType="TIMESTAMP" property="delDate" />
        <result column="DEL_USER" jdbcType="VARCHAR" property="delUser" />
        <result column="DEL_USERNAME" jdbcType="VARCHAR" property="delUsername" />
        <result column="IS_DEL" jdbcType="VARCHAR" property="isDel" />
        <result column="MAP_ID" jdbcType="VARCHAR" property="mapId" />
        <result column="APP_NAME" jdbcType="VARCHAR" property="appName" />
        <result column="INS_NAME" jdbcType="VARCHAR" property="insName" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="CODE_NAME" jdbcType="VARCHAR" property="codeName" />
        <result column="RATE" jdbcType="VARCHAR" property="rate" />
    </resultMap>


    <sql id="Base_Column_List">
    ITEM_ID,APP_ID,INS_CODE,ITEM_HIS_ID,ITEM_HIS_NAME,CODE_ID,HIDE_PROJECT,ZIFU1,ZIFU2,ZIFU3,ZIFU4,ZIFU5,CREATE_DATE,CREATE_USER,create_user_name,UPDATE_DATE,UPDATE_USER,update_user_name,DEL_DATE,DEL_USER,DEL_USERNAME,IS_DEL,RATE
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="THisCodeItem">
        delete from t_his_code_item where ITEM_ID = #{ itemId }
    </delete>

    <!--根据主键批量删除-->
    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_his_code_item where ITEM_ID in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--单个插入-->
    <insert id="insert"  parameterType="THisCodeItem">
        insert into t_his_code_item (<include refid="Base_Column_List" />) values
        (#{itemId},#{appId},#{insCode},#{itemHisId},#{itemHisName},#{codeId},#{hideProject},#{zifu1},#{zifu2},#{zifu3},#{zifu4},#{zifu5},#{createDate},#{createUser},#{createUserName},#{updateDate},#{updateUser},#{updateUserName},#{delDate},#{delUser},#{delUsername},#{isDel},#{rate})
    </insert>

    <!--批量插入-->
    <insert id="insertList" parameterType="List">
        insert into t_his_code_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.itemId},#{item.appId},#{item.insCode},#{item.itemHisId},#{item.itemHisName},#{item.codeId},#{item.hideProject},#{item.zifu1},#{item.zifu2},#{item.zifu3},#{item.zifu4},#{item.zifu5},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.updateDate},#{item.updateUser},#{item.updateUserName},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.isDel},#{item.rate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="THisCodeItem">
        update t_his_code_item
        <set>
            <if test="appId != null">
                APP_ID = #{ appId },
            </if>
            <if test="insCode != null">
                INS_CODE = #{ insCode },
            </if>
            <if test="itemHisId != null">
                ITEM_HIS_ID = #{ itemHisId },
            </if>
            <if test="itemHisName != null">
                ITEM_HIS_NAME = #{ itemHisName },
            </if>
            <if test="codeId != null">
                CODE_ID = #{ codeId },
            </if>
            <if test="hideProject != null">
                HIDE_PROJECT = #{ hideProject },
            </if>
            <if test="zifu1 != null">
                ZIFU1 = #{ zifu1 },
            </if>
            <if test="zifu2 != null">
                ZIFU2 = #{ zifu2 },
            </if>
            <if test="zifu3 != null">
                ZIFU3 = #{ zifu3 },
            </if>
            <if test="zifu4 != null">
                ZIFU4 = #{ zifu4 },
            </if>
            <if test="zifu5 != null">
                ZIFU5 = #{ zifu5 },
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{ createDate },
            </if>
            <if test="createUser != null">
                CREATE_USER = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{ updateDate },
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{ updateUser },
            </if>
            <if test="updateUserName != null">
                update_user_name = #{ updateUserName },
            </if>
            <if test="delDate != null">
                DEL_DATE = #{ delDate },
            </if>
            <if test="delUser != null">
                DEL_USER = #{ delUser },
            </if>
            <if test="delUsername != null">
                DEL_USERNAME = #{ delUsername },
            </if>
            <if test="isDel != null">
                IS_DEL = #{ isDel },
            </if>
            <if test="rate != null">
                RATE = #{ rate },
            </if>
        </set>
        where ITEM_ID = #{ itemId }
    </update>


    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_his_code_item where ITEM_ID = #{id}
    </select>

    <!--分页查询基础语句返回对象-->
    <select id="getPageListByObj" parameterType="THisCodeItem" resultMap="BaseResultMap">
        SELECT hci.*,tsc.CODE_NAME, app.APP_NAME, ins.INS_NAME
        from t_his_code_item hci
        left join t_sys_code tsc on hci.CODE_ID = tsc.CODE_ID
        left join sys_app app on app.APP_ID = hci.APP_ID
        left join sys_institution ins on ins.INS_CODE = hci.INS_CODE
        where hci.IS_DEL = '0'
        <!--条件-->
        <if test="appId != null and appId!='' ">
          and hci.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and hci.INS_CODE = #{insCode}
        </if>
        <if test="codeId != null and codeId!='' ">
            and hci.CODE_ID = #{codeId}
        </if>
        <if test=" itemHisName != null and itemHisName!='' ">
            and hci.ITEM_HIS_NAME like CONCAT('%',trim(#{itemHisName}),'%')
        </if>
        order by hci.APP_ID, hci.INS_CODE, hci.CODE_ID
    </select>

    <select id="getTHisCodeItemListByTHisCodeItem" parameterType="THisCodeItem" resultMap="BaseResultMap">
        SELECT hci.ITEM_HIS_ID,hci.ITEM_HIS_NAME,hci.CODE_ID,hci.APP_ID,hci.INS_CODE
        from t_his_code_item hci
        where 1=1 and hci.ITEM_HIS_ID not in(SELECT hiscod.ITEM_HIS_ID from t_sys_his_codeitem_mapping hiscod where hiscod.IS_DEL = '0')
        <if test="appId != null and appId!='' ">
            and hci.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and hci.INS_CODE = #{insCode}
        </if>
    </select>

    <delete id="delAllByInsCode" parameterType="ArrayList">
        delete from t_his_code_item where INS_CODE in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--根据大类获取his参数列表-->
    <select id="getHisCodeListByCode" parameterType="THisCodeItem" resultMap="BaseResultMap">
        SELECT hci.APP_ID,hci.INS_CODE,hci.CODE_ID,hci.ITEM_HIS_ID,hci.ITEM_HIS_NAME,sci.ITEM_ID,sci.ITEM_NAME
        from t_his_code_item hci
        left join t_sys_his_codeitem_mapping map on map.CODE_ITEM_ID = hci.CODE_ITEM_ID
        left join t_sys_code_item sci on map.ITEM_ID = sci.ITEM_ID
        where 1=1
        <if test="appId != null and appId!='' ">
            and hci.APP_ID = #{appId}
        </if>
        <if test="insCode != null and insCode!='' ">
            and hci.INS_CODE = #{insCode}
        </if>
        <if test="codeId != null and codeId!='' ">
            and hci.CODE_ID = #{codeId} and sci.CODE_ID = #{codeId}
        </if>
        order by hci.ITEM_HIS_NAME
    </select>

    <select id="getCodeList" parameterType="adminInfo" resultType="Map">
         SELECT
            i.CODE_ITEM_ID itemId,
            i.ITEM_HIS_ID  hisId,
            i.ITEM_HIS_NAME hisName,
            i.CODE_ID codeId
        FROM
            t_his_code_item AS i
        <where>
            i.is_del = '0'
            <if test="appId != null and appId != ''">
               and i.APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != ''">
                and i.INS_CODE = #{insCode}
            </if>
        </where>
          ORDER BY i.ITEM_HIS_NUM
    </select>

    <select id="getHisCodeItemList" parameterType="THisCodeItem" resultType="Map">
        SELECT ITEM_HIS_ID as itemId,
        ITEM_HIS_NAME as itemName,
        HIDE_PROJECT hideProject,
        zifu1 as extendDisplay,
        ZIFU5 as zifu5,
        zifu3 unitPrice,
        zifu4 priceNum,
        rate
        from t_his_code_item
        <where>
            IS_DEL = '0'
            and CODE_ID = #{codeId}
            <if test="appId != null and appId != ''">
                and APP_ID = #{appId}
            </if>
            <if test="insCode != null and insCode != ''">
                and INS_CODE = #{insCode}
            </if>
        </where>
        order by ITEM_HIS_ID
    </select>

    <update id="updateSiblingsNotDefault" parameterType="THisCodeItem">
        update t_his_code_item
        set ZIFU5 = '0'
        where CODE_ID = #{codeId} and ITEM_ID != #{ itemId }
    </update>
</mapper>