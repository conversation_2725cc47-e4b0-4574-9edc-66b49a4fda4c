<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.BStandardMatMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.BStandardMat">
        <id column="s_id" jdbcType="VARCHAR"  property="sId" />
        <result column="s_mat_code" jdbcType="VARCHAR" property="sMatCode" />
        <result column="s_mat_name" jdbcType="VARCHAR" property="sMatName" />
        <result column="s_mat_class" jdbcType="VARCHAR" property="sMatClass" />
        <result column="s_mat_type" jdbcType="VARCHAR" property="sMatType" />
    </resultMap>


    <sql id="Base_Column_List">
    s_id,s_mat_code,s_mat_name,s_mat_class,s_mat_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BStandardMat">
        delete from b_standard_mat where s_id = #{ sId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from b_standard_mat where s_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="BStandardMat">
        insert into b_standard_mat (<include refid="Base_Column_List" />) values
        (#{sId},#{sMatCode},#{sMatName},#{sMatClass},#{sMatType})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into b_standard_mat (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.sId},#{item.sMatCode},#{item.sMatName},#{item.sMatClass},#{item.sMatType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BStandardMat">
        update b_standard_mat
        <set>
             <if test="sMatCode != null">
                s_mat_code = #{ sMatCode },
             </if>
             <if test="sMatName != null">
                s_mat_name = #{ sMatName },
             </if>
             <if test="sMatClass != null">
                s_mat_class = #{ sMatClass },
             </if>
             <if test="sMatType != null">
                s_mat_type = #{ sMatType },
             </if>
        </set>
        where s_id = #{ sId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from b_standard_mat where s_id = #{ sId }
    </select>

    <select id="getPageListByObj" parameterType="BStandardMat" resultMap="BaseResultMap">
        SELECT s_id,s_mat_code,s_mat_name,s_mat_class,s_mat_type
        from b_standard_mat
      where
        1=1
            <if test=" sMatName != null and sMatName!='' and sMatCode != null and sMatCode!='' ">
                and (s_mat_name like CONCAT('%',trim(#{sMatName}),'%') or s_mat_code like CONCAT('%',trim(#{sMatCode}),'%'))
            </if>
            <if test=" (sMatName != null and sMatName!='') and (sMatCode == null or sMatCode=='') ">
                and s_mat_name like CONCAT('%',trim(#{sMatName}),'%')
            </if>
            <if test=" (sMatName == null or sMatName=='') and (sMatCode != null and sMatCode!='') ">
                and s_mat_code like CONCAT('%',trim(#{sMatCode}),'%')
            </if>
            <if test=" sMatType != null and sMatType !='' ">
                and s_mat_type = #{sMatType}
            </if>
            <if test=" keyWord != null and keyWord !='' ">
                and (s_mat_name like CONCAT('%',trim(#{keyWord}),'%') or s_mat_code like CONCAT('%',trim(#{keyWord}),'%'))
            </if>

    </select>

</mapper>