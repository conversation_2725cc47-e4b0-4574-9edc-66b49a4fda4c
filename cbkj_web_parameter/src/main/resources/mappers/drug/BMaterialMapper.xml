<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.BMaterialMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.BMaterial">
        <id column="k_mat_id" jdbcType="VARCHAR" property="kMatId"/>
        <result column="k_mat_name" jdbcType="VARCHAR" property="kMatName"/>
        <result column="k_mat_py" jdbcType="VARCHAR" property="kMatPy"/>
        <result column="k_mat_wb" jdbcType="VARCHAR" property="kMatWb"/>
        <result column="k_mat_yf" jdbcType="VARCHAR" property="kMatYf"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>


    <sql id="Base_Column_List">
        k_mat_id
        ,k_mat_name,k_mat_py,k_mat_wb,k_mat_yf,remarks
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BMaterial">
        delete
        from b_material
        where k_mat_id = #{ kMatId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from b_material where k_mat_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="BMaterial">
        insert into b_material (<include refid="Base_Column_List"/>) values
        (#{kMatId},#{kMatName},#{kMatPy},#{kMatWb},#{kMatYf},#{remarks})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into b_material (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.kMatId},#{item.kMatName},#{item.kMatPy},#{item.kMatWb},#{item.kMatYf},#{item.remarks})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BMaterial">
        update b_material
        <set>
            <if test="kMatName != null">
                k_mat_name = #{ kMatName },
            </if>
            <if test="kMatPy != null">
                k_mat_py = #{ kMatPy },
            </if>
            <if test="kMatWb != null">
                k_mat_wb = #{ kMatWb },
            </if>
            <if test="kMatYf != null">
                k_mat_yf = #{ kMatYf },
            </if>
            <if test="remarks != null">
                remarks = #{remarks},
            </if>
        </set>
        where k_mat_id = #{ kMatId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from b_material where k_mat_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="BMaterial" resultMap="BaseResultMap">
        SELECT k_mat_id,k_mat_name,k_mat_py,k_mat_wb,k_mat_yf,remarks
        from b_material
        <where>
            <if test=" kMatName != null and kMatName!='' ">
                and k_mat_name like CONCAT('%',trim(#{kMatName}),'%')
            </if>
            <if test=" keyWord != null and keyWord!='' ">
                and (k_mat_name like CONCAT('%',trim(#{keyWord}),'%') or
                k_mat_py like CONCAT('%',trim(#{keyWord}),'%') or
                k_mat_py like CONCAT('%',trim(#{keyWord}),'%'))
            </if>
        </where>
    </select>

</mapper>