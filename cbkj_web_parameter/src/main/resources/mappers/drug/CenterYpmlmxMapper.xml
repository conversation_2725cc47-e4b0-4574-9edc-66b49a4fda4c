<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.CenterYpmlmxMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.VCenterYpmlmx">
        <id column="drug_id" jdbcType="VARCHAR" property="drugId"/>
        <result column="drug_code" jdbcType="VARCHAR" property="drugCode"/>
        <result column="mat_price_id" jdbcType="VARCHAR" property="matPriceId"/>
        <result column="mat_name" jdbcType="VARCHAR" property="matName"/>
        <result column="mat_spe_id" jdbcType="VARCHAR" property="matSpeId"/>
        <result column="mat_spe" jdbcType="VARCHAR" property="matSpe"/>
        <result column="mat_origin_id" jdbcType="VARCHAR" property="matOriginId"/>
        <result column="mat_origin_name" jdbcType="VARCHAR" property="matOriginName"/>
        <result column="retail_price" jdbcType="DECIMAL" property="retailPrice"/>
        <result column="purchase_price" jdbcType="DECIMAL" property="purchasePrice"/>
        <result column="small_purchase_price" jdbcType="DECIMAL" property="smallPurchasePrice"/>
        <result column="small_retail_price" jdbcType="DECIMAL" property="smallRetailPrice"/>
        <result column="mat_dose" jdbcType="DECIMAL" property="matDose"/>
        <result column="mat_dose_unit" jdbcType="VARCHAR" property="matDoseUnit"/>
        <result column="mat_pack_mount" jdbcType="DECIMAL" property="matPackMount"/>
        <result column="mat_pack_unit" jdbcType="VARCHAR" property="matPackUnit"/>
        <result column="mat_type" jdbcType="CHAR" property="matType"/>
        <result column="mat_once_dose" jdbcType="VARCHAR" property="matOnceDose"/>
        <result column="mat_once_dose_unit" jdbcType="VARCHAR" property="matOnceDoseUnit"/>
        <result column="mat_class" jdbcType="VARCHAR" property="matClass"/>
        <result column="conversion_factor" jdbcType="INTEGER" property="conversionFactor"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="py" jdbcType="VARCHAR" property="py"/>
        <result column="wb" jdbcType="VARCHAR" property="wb"/>
        <result column="is_medical" jdbcType="VARCHAR" property="isMedical"/>
        <result column="is_del" jdbcType="VARCHAR" property="isDel"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="daily_max_dose_in" jdbcType="DOUBLE" property="dailyMaxDoseIn"/>
        <result column="daily_max_dose_ext" jdbcType="DOUBLE" property="dailyMaxDoseExt"/>
        <result column="daily_max_num_prep" jdbcType="DOUBLE" property="dailyMaxNumPrep"/>
        <result column="mat_usage" jdbcType="VARCHAR" property="matUsage"/>
        <result column="not_pay_alone" jdbcType="VARCHAR" property="notPayAlone"/>
        <result column="not_pay_in_fund" jdbcType="VARCHAR" property="notPayInFund"/>
        <result column="frequency_id" jdbcType="VARCHAR" property="frequencyId"/>
        <result column="frequency" jdbcType="VARCHAR" property="frequency"/>
        <result column="frequency_rate" jdbcType="DECIMAL" property="frequencyRate"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="usage_desc" jdbcType="VARCHAR" property="usageDesc"/>
        <result column="effect" jdbcType="VARCHAR" property="effect"/>
        <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber"/>
        <result column="mat_id" jdbcType="VARCHAR" property="matId"/>
        <result column="medicontrol_text" jdbcType="VARCHAR" property="medicontrolText"/>
        <result column="keyWord" jdbcType="VARCHAR" property="keyWord"/>
        <result column="toxicity_overdose_multiple" jdbcType="VARCHAR" property="toxicityOverdoseMultiple"/>
        <result column="external_use_only" jdbcType="VARCHAR" property="externalUseOnly"/>
        <result column="maxdose" jdbcType="DOUBLE" property="maxdose" />
        <result column="mindose" jdbcType="DOUBLE" property="mindose" />

        <result column="toxicity" jdbcType="VARCHAR" property="toxicity"/>
        <result column="mother_taboos" jdbcType="VARCHAR" property="motherTaboos"/>
        <result column="external_use_orally" jdbcType="VARCHAR" property="externalUseOrally"/>
        <result column="external_marusan" jdbcType="VARCHAR" property="externalMarusan"/>
        <result column="external_marusan_name" jdbcType="VARCHAR" property="externalMarusanName"/>
        <result column="special_usages" jdbcType="VARCHAR" property="specialUsages"/>
        <result column="special_usages" jdbcType="VARCHAR" property="dicName"/>
        <result column="usages_ustrict" jdbcType="VARCHAR" property="usagesAstrict"/>
        <result column="sdk_tags_codes" jdbcType="VARCHAR" property="sdkTagsCodes"/>

    </resultMap>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.drug.VCenterYpmlmxDetails">
        <id column="drug_id" jdbcType="VARCHAR" property="drugId"/>
        <result column="mat_id" jdbcType="VARCHAR" property="matId"/>
        <result column="mat_price_id" jdbcType="VARCHAR" property="matPriceId"/>
        <result column="drug_code" jdbcType="VARCHAR" property="drugCode"/>
        <result column="mat_name" jdbcType="VARCHAR" property="matName"/>
        <result column="mat_spe" jdbcType="VARCHAR" property="matSpe"/>
        <result column="mat_origin_name" jdbcType="VARCHAR" property="matOriginName"/>
        <result column="retail_price" jdbcType="DECIMAL" property="retailPrice"/>
        <result column="mat_pack_mount" jdbcType="DECIMAL" property="matPackMount"/>
        <result column="mat_pack_unit" jdbcType="VARCHAR" property="matPackUnit"/>
        <result column="mat_type" jdbcType="CHAR" property="matType"/>
        <result column="mat_once_dose" jdbcType="VARCHAR" property="matOnceDose"/>
        <result column="mat_once_dose_unit" jdbcType="VARCHAR" property="matOnceDoseUnit"/>
        <result column="py" jdbcType="VARCHAR" property="py"/>
        <result column="wb" jdbcType="VARCHAR" property="wb"/>
        <result column="mat_dose" jdbcType="VARCHAR" property="matDose"/>
        <result column="daily_max_dose_in" jdbcType="DOUBLE" property="dailyMaxDoseIn"/>
        <result column="daily_max_dose_ext" jdbcType="DOUBLE" property="dailyMaxDoseExt"/>
        <result column="not_pay_alone" jdbcType="VARCHAR" property="notPayAlone"/>
        <result column="not_pay_in_fund" jdbcType="VARCHAR" property="notPayInFund"/>
    </resultMap>

    <sql id="Base_Column_List">
        drug_id
        ,drug_code,mat_price_id,mat_name,mat_spe_id,mat_spe,mat_origin_id,mat_origin_name,retail_price,purchase_price,small_purchase_price,small_retail_price,mat_dose,mat_dose_unit,mat_pack_mount,mat_pack_unit,mat_type,mat_once_dose,mat_once_dose_unit,mat_class,conversion_factor,status,py,wb,is_medical,create_date,update_date,daily_max_dose_in,daily_max_dose_ext,daily_max_num_prep,mat_usage,not_pay_alone,not_pay_in_fund,frequency_id,frequency,frequency_rate,content,usage_desc,effect,approval_number,mat_id,medicontrol_text
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="VCenterYpmlmx">
        delete
        from v_center_ypmlmx
        where drug_id = #{ drugId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from v_center_ypmlmx where drug_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="VCenterYpmlmx">
        insert into v_center_ypmlmx (<include refid="Base_Column_List"/>) values
        (#{drugId},#{drugCode},#{matPriceId},#{matName},#{matSpeId},#{matSpe},#{matOriginId},#{matOriginName},#{retailPrice},#{purchasePrice},#{smallPurchasePrice},#{smallRetailPrice},#{matDose},#{matDoseUnit},#{matPackMount},#{matPackUnit},#{matType},#{matOnceDose},#{matOnceDoseUnit},#{matClass},#{conversionFactor},#{status},#{py},#{wb},#{isMedical},#{createDate},#{updateDate},#{dailyMaxDoseIn},#{dailyMaxDoseExt},#{dailyMaxNumPrep},#{matUsage},#{notPayAlone},#{notPayInFund},#{frequencyId},#{frequency},#{frequencyRate},#{content},#{usageDesc},#{effect},#{approvalNumber},#{matId},#{medicontrolText})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into v_center_ypmlmx (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.drugId},#{item.drugCode},#{item.matPriceId},#{item.matName},#{item.matSpeId},#{item.matSpe},#{item.matOriginId},#{item.matOriginName},#{item.retailPrice},#{item.purchasePrice},#{item.smallPurchasePrice},#{item.smallRetailPrice},#{item.matDose},#{item.matDoseUnit},#{item.matPackMount},#{item.matPackUnit},#{item.matType},#{item.matOnceDose},#{item.matOnceDoseUnit},#{item.matClass},#{item.conversionFactor},#{item.status},#{item.py},#{item.wb},#{item.isMedical},#{item.createDate},#{item.updateDate},#{item.dailyMaxDoseIn},#{item.dailyMaxDoseExt},#{item.dailyMaxNumPrep},#{item.matUsage},#{item.notPayAlone},#{item.notPayInFund},#{item.frequencyId},#{item.frequency},#{item.frequencyRate},#{item.content},#{item.usageDesc},#{item.effect},#{item.approvalNumber},#{item.matId},#{item.medicontrolText})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="VCenterYpmlmx">
        update v_center_ypmlmx
        <set>
            <if test="drugCode != null">
                drug_code = #{ drugCode },
            </if>
            <if test="matPriceId != null">
                mat_price_id = #{ matPriceId },
            </if>
            <if test="matName != null">
                mat_name = #{ matName },
            </if>
            <if test="matSpeId != null">
                mat_spe_id = #{ matSpeId },
            </if>
            <if test="matSpe != null">
                mat_spe = #{ matSpe },
            </if>
            <if test="matOriginId != null">
                mat_origin_id = #{ matOriginId },
            </if>
            <if test="matOriginName != null">
                mat_origin_name = #{ matOriginName },
            </if>
            <if test="retailPrice != null">
                retail_price = #{ retailPrice },
            </if>
            <if test="purchasePrice != null">
                purchase_price = #{ purchasePrice },
            </if>
            <if test="smallPurchasePrice != null">
                small_purchase_price = #{ smallPurchasePrice },
            </if>
            <if test="smallRetailPrice != null">
                small_retail_price = #{ smallRetailPrice },
            </if>
            <if test="matDose != null">
                mat_dose = #{ matDose },
            </if>
            <if test="matDoseUnit != null">
                mat_dose_unit = #{ matDoseUnit },
            </if>
            <if test="matPackMount != null">
                mat_pack_mount = #{ matPackMount },
            </if>
            <if test="matPackUnit != null">
                mat_pack_unit = #{ matPackUnit },
            </if>
            <if test="matType != null">
                mat_type = #{ matType },
            </if>
            <if test="matOnceDose != null">
                mat_once_dose = #{ matOnceDose },
            </if>
            <if test="matOnceDoseUnit != null">
                mat_once_dose_unit = #{ matOnceDoseUnit },
            </if>
            <if test="matClass != null">
                mat_class = #{ matClass },
            </if>
            <if test="conversionFactor != null">
                conversion_factor = #{ conversionFactor },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="py != null">
                py = #{ py },
            </if>
            <if test="wb != null">
                wb = #{ wb },
            </if>
            <if test="isMedical != null">
                is_medical = #{ isMedical },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="updateDate != null">
                update_date = #{ updateDate },
            </if>
            <if test="dailyMaxDoseIn != null">
                daily_max_dose_in = #{ dailyMaxDoseIn },
            </if>
            <if test="dailyMaxDoseExt != null">
                daily_max_dose_ext = #{ dailyMaxDoseExt },
            </if>
            <if test="dailyMaxNumPrep != null">
                daily_max_num_prep = #{ dailyMaxNumPrep },
            </if>
            <if test="matUsage != null">
                mat_usage = #{ matUsage },
            </if>
            <if test="notPayAlone != null">
                not_pay_alone = #{ notPayAlone },
            </if>
            <if test="notPayInFund != null">
                not_pay_in_fund = #{ notPayInFund },
            </if>
            <if test="frequencyId != null">
                frequency_id = #{ frequencyId },
            </if>
            <if test="frequency != null">
                frequency = #{ frequency },
            </if>
            <if test="frequencyRate != null">
                frequency_rate = #{ frequencyRate },
            </if>
            <if test="content != null">
                content = #{ content },
            </if>
            <if test="usageDesc != null">
                usage_desc = #{ usageDesc },
            </if>
            <if test="effect != null">
                effect = #{ effect },
            </if>
            <if test="approvalNumber != null">
                approval_number = #{ approvalNumber },
            </if>
            <if test="matId != null">
                mat_id = #{ matId },
            </if>
            <if test="medicontrolText != null">
                medicontrol_text = #{ medicontrolText },
            </if>
        </set>
        where drug_id = #{ drugId }
    </update>

    <select id="getObjectByIdDetail" parameterType="String" resultMap="BaseResultMap2" >
        select
            drug_id,
            mat_id,
            mat_price_id,
            drug_code,
            mat_name,
            mat_spe,
            mat_origin_name,
            retail_price,
            mat_pack_mount,
            mat_pack_unit,
            mat_type,
            mat_once_dose,
            mat_once_dose_unit,
            py,
            wb,
            mat_dose,
            daily_max_dose_in,
            daily_max_dose_ext,
            not_pay_alone,
            not_pay_in_fund
        from v_center_ypmlmx where mat_price_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="VCenterYpmlmx" resultMap="BaseResultMap">
        SELECT drug_id,
        mat_id,
        mat_price_id,
        drug_code,
        mat_name,
        mat_spe,
        mat_origin_name,
        retail_price,
        mat_type,
        status,
        py,
        wb,
        is_medical,
        create_date,
        update_date,
        daily_max_dose_in,
        daily_max_dose_ext,
        not_pay_alone,
        not_pay_in_fund,
        toxicity_overdose_multiple,
        external_use_only,
        maxdose,
        mindose,
        toxicity,
        mother_taboos,
        external_use_orally,
        external_marusan,
        external_marusan_name,
        special_usages,
        usages_ustrict,
        sdk_tags_codes
        from v_center_ypmlmx
        where 1=1
        <if test=" drugId != null and drugId!='' ">
            and drug_id=#{drugId}
        </if>
        <if test=" keyWord != null and keyWord!='' ">
            and (drug_code like CONCAT('%',trim(#{keyWord}),'%') or mat_name like CONCAT('%',trim(#{keyWord}),'%')
                or py like CONCAT('%',trim(#{keyWord}),'%') or wb like CONCAT('%',trim(#{keyWord}),'%')
            )
        </if>
        <if test=" isDel != null and isDel!='' ">
            and is_del=#{isDel}
        </if>
        <if test="sdkTagsCodesArray != null">
            <foreach collection="sdkTagsCodesArray" item="item" open="and (" separator="or" close=")">
                FIND_IN_SET( #{item}, sdk_tags_codes) > 0
            </foreach>
        </if>
        order by ifnull(update_date,create_date) desc
    </select>

</mapper>