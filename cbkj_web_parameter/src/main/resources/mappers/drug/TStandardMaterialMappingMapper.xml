<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.TStandardMaterialMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.TStandardMaterialMapping">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="s_id" jdbcType="VARCHAR" property="sId" />
        <result column="s_mat_name" jdbcType="VARCHAR" property="sMatName" />
        <result column="s_mat_code" jdbcType="VARCHAR" property="sMatCode" />
        <result column="s_mat_type" jdbcType="VARCHAR" property="sMatType" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="mat_id" jdbcType="VARCHAR" property="matId" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    </resultMap>


    <sql id="Base_Column_List">
    id,s_id,s_mat_name,s_mat_code,s_mat_type,drug_id,mat_id,create_date,create_user,create_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TStandardMaterialMapping">
        delete from t_standard_material_mapping where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_standard_material_mapping where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TStandardMaterialMapping">
        insert into t_standard_material_mapping (<include refid="Base_Column_List" />) values
        (#{id},#{sId},#{sMatName},#{sMatCode},#{sMatType},#{drugId},#{matId},NOW(),#{createUser},#{createUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_standard_material_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.sId},#{item.sMatName},#{item.sMatCode},#{item.sMatType},#{item.drugId},#{item.matId},NOW(),#{item.createUser},#{item.createUserName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TStandardMaterialMapping">
        update t_standard_material_mapping
        <set>
             <if test="sId != null">
                s_id = #{ sId },
             </if>
             <if test="sMatName != null">
                s_mat_name = #{ sMatName },
             </if>
             <if test="sMatCode != null">
                s_mat_code = #{ sMatCode },
             </if>
             <if test="sMatType != null">
                s_mat_type = #{ sMatType },
             </if>
             <if test="drugId != null">
                drug_id = #{ drugId },
             </if>
             <if test="matId != null">
                mat_id = #{ matId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_standard_material_mapping where id = #{id}
    </select>


    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.drug.TMaterialStandVO">
        <id column="matId" jdbcType="VARCHAR"  property="matId" />
        <id column="sMatCode" jdbcType="VARCHAR"  property="sMatCode" />
        <id column="sMatName" jdbcType="VARCHAR"  property="sMatName" />
        <id column="sId" jdbcType="VARCHAR"  property="sId" />
        <id column="mapId" jdbcType="VARCHAR"  property="mapId" />

    </resultMap>

    <select id="getPageListByObj" parameterType="TMaterialStandVO" resultMap="BaseResultMap2">
        SELECT
        tm2.mat_id matId,
        tm2.mat_name matName,
        tm2.mat_standard matStandard,
        bsm.s_mat_code sMatCode,
        bsm.s_mat_name sMatName,
        bsm.s_id sId,
        bsm.s_mat_type as sMatType,
        tsmm.id as mapId,
        tm2.drug_id as drugId
        FROM

         t_drug_list AS tdl
        JOIN t_material AS tm2 ON (tm2.drug_id = tdl.drug_id AND tm2.status = '0')
        LEFT JOIN t_standard_material_mapping AS tsmm ON(tsmm.drug_id = tm2.drug_id AND tsmm.mat_id = tm2.mat_id
        <if test=" sMatType !=null and sMatType !=''">
            AND tsmm.s_mat_type = #{sMatType}
        </if>
                                                             )
        LEFT JOIN b_standard_mat AS bsm ON(bsm.s_id=tsmm.s_id
        <if test=" sMatType !=null and sMatType !=''">
            AND bsm.s_mat_type=#{sMatType}
        </if>
                                               )

        <where>
            tdl.status='0'
            <if test="matType != null and matType !=''">
                and tm2.mat_class = #{matType}
            </if>
            <if test=" drugType !=null and drugType !=''">
                and tdl.drug_type=#{drugType}
            </if>
            <if test=" drugId !=null and drugId !=''">
                 and tdl.drug_id = #{drugId}
            </if>
            <if test="isMapping == 1 ">
                and bsm.s_id is not null
            </if>
            <if test="isMapping == 2 ">
                 and bsm.s_id is null
            </if>
            <if test=" matClass != null and matClass !=''">
                 and tm2.mat_class = #{matClass}
            </if>
            <if test=" keyWord != null and keyWord !=''">
                and (tm2.mat_name like CONCAT('%',trim(#{keyWord}),'%') or tm2.mat_standard like CONCAT('%',trim(#{keyWord}),'%') )
            </if>

        </where>
    </select>

    <select id="getCountByObj" resultType="java.lang.Long">
        select count(*)
        from t_standard_material_mapping
        <where>
            1=1
            <if test=" sId != null and sId !=''">
               and s_id = #{sId}
            </if>
            <if test=" sMatCode != null and sMatCode !=''">
                and s_mat_code = #{sMatCode}
            </if>
            <if test=" drugId != null and drugId !=''">
                and drug_id = #{drugId}
            </if>
            <if test=" matId != null and matId !=''">
                and mat_id = #{matId}
            </if>
            <if test=" sMatType != null and sMatType !=''">
                and s_mat_type = #{sMatType}
            </if>
        </where>
    </select>
    <insert id="insertautoMapping" parameterType="StandTMAutoMappingVO">

        INSERT INTO t_standard_material_mapping
        SELECT rand_string(32),
               m.s_id,
               m.s_mat_name,
               m.s_mat_code,
               m.s_mat_type,
               mx.drug_id,
               mx.mat_id,
               NOW(),
               #{createUser},
               #{createUserName}
        FROM t_material mx
                 LEFT JOIN b_standard_mat m ON  m.s_mat_class=mx.mat_class and SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(m.s_mat_name,'【配送】',''),'5',''),'1',''),'*',''),'d',''),'K',''),'G',''),'P',''),'Z',''),'配方颗粒',''),'颗粒',''),'(',1),'（',1),'[',1),'&lt;',1)  = substring_index(substring_index(substring_index(substring_index(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(REPLACE(mx.mat_name,'【配送】',''),'5',''),'1',''),'*',''),'d',''),'K',''),'G',''),'P',''),'Z',''),'配方颗粒',''),'颗粒',''),'(',1),'（',1),'[',1),'&lt;',1)
                 LEFT JOIN t_standard_material_mapping p ON (p.mat_id = mx.mat_id AND p.s_id=m.s_id)
        WHERE mx.status='0' and mx.drug_id = #{drugId} AND p.mat_id IS NULL AND m.s_id IS NOT NULL

    </insert>
    <select id="insertautoMappingCount" parameterType="StandTMAutoMappingVO" resultType="Long">
        SELECT
       count(*)
        FROM
        t_drug_list AS tdl
        JOIN t_material AS tm2 ON (tm2.drug_id = tdl.drug_id AND tm2.status = '0' and tm2.is_del='0')
        LEFT JOIN t_standard_material_mapping AS tsmm ON(tsmm.drug_id = tm2.drug_id AND tsmm.mat_id = tm2.mat_id)
        LEFT JOIN b_standard_mat AS bsm ON(bsm.s_id=tsmm.s_id)
        where
        tdl.status='0'  and tdl.drug_id = #{drugId} and tsmm.s_id IS NULL
    </select>

</mapper>