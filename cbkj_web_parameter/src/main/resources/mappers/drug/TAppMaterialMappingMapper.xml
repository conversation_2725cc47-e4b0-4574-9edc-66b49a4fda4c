<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.TAppMaterialMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.TAppMaterialMapping">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="k_mat_id" jdbcType="VARCHAR" property="kMatId" />
        <result column="k_mat_name" jdbcType="VARCHAR" property="kMatName" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="mat_price_id" jdbcType="VARCHAR" property="matPriceId" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.jiuzhekan.cbkj.beans.drug.TAppMaterial">
        <result column="k_mat_id" jdbcType="VARCHAR" property="kMatId" />
        <result column="k_mat_name" jdbcType="VARCHAR" property="kMatName" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="mat_price_id" jdbcType="VARCHAR" property="matPriceId" />
        <result column="toxicity" jdbcType="VARCHAR" property="toxicity" />
        <result column="mother_taboos" jdbcType="VARCHAR" property="motherTaboos" />
        <result column="maxdose" jdbcType="DOUBLE" property="maxdose" />
        <result column="mindose" jdbcType="DOUBLE" property="mindose" />
        <result column="special_usages" jdbcType="VARCHAR" property="specialUsages" />
    </resultMap>


    <sql id="Base_Column_List">
    id,k_mat_id,k_mat_name,drug_id,mat_price_id,CREATE_DATE,CREATE_USER,create_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TAppMaterialMapping">
        delete from t_app_material_mapping where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_app_material_mapping where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TAppMaterialMapping">
        insert into t_app_material_mapping (<include refid="Base_Column_List" />) values
        (#{id},#{kMatId},#{kMatName},#{drugId},#{matPriceId},NOW(),#{createUser},#{createUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_app_material_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.kMatId},#{item.kMatName},#{item.drugId},#{item.matPriceId},NOW(),#{item.createUser},#{item.createUserName})
        </foreach>
    </insert>

    <insert id="insertautoMapping" parameterType="StandTMAutoMappingVO">
        INSERT INTO t_app_material_mapping
        SELECT rand_string(32),
               m.k_mat_id,
               m.k_mat_name,
               tm2.drug_id,
               tmp.mat_price_id,
               NOW(),
               #{createUser},
               #{createUserName}
        FROM t_material_price tmp
                 JOIN t_material AS tm2 ON ( tm2.status = '0' AND tm2.mat_id = tmp.mat_id and tm2.drug_id=tmp.drug_id)
                 LEFT JOIN b_material m ON m.k_mat_name = substring_index(substring_index(substring_index(substring_index(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(REPLACE(tm2.mat_name,'【配送】',''),'5',''),'1',''),'*',''),'d',''),'K',''),'G',''),'P',''),'Z',''),'配方颗粒',''),'颗粒',''),'(',1),'（',1),'[',1),'&lt;',1)
                 LEFT JOIN t_app_material_mapping p ON( p.mat_price_id = tmp.mat_price_id and p.k_mat_id=m.k_mat_id and p.`drug_id`=tmp.drug_id)
        WHERE tmp.drug_id=#{drugId} and  tmp.status = '0' and tm2.drug_id = #{drugId} AND p.mat_price_id IS NULL AND m.k_mat_id IS NOT NULL
    </insert>


    <update id="updateByPrimaryKey" parameterType="TAppMaterialMapping">
        update t_app_material_mapping
        <set>
             <if test="kMatId != null">
                k_mat_id = #{ kMatId },
             </if>
             <if test="kMatName != null">
                k_mat_name = #{ kMatName },
             </if>
             <if test="drugId != null">
                drug_id = #{ drugId },
             </if>
             <if test="matPriceId != null">
                mat_price_id = #{ matPriceId },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="createUser != null">
                CREATE_USER = #{ createUser },
             </if>
             <if test="createUserName != null">
                 create_user_name = #{ createUserName },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_app_material_mapping where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TAppMaterialMapping" resultMap="BaseResultMap">
        SELECT id,k_mat_id,k_mat_name,drug_id,mat_price_id,CREATE_DATE,CREATE_USER,create_user_name
        from t_app_material_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <resultMap id="BaseResultMap2" type="TMaterialKnowVO">
        <id column="matId" jdbcType="VARCHAR"  property="matId" />

    </resultMap>
    <select id="getDataPage" parameterType="com.jiuzhekan.cbkj.beans.drug.TMaterialKnowVO" resultMap="BaseResultMap2">
        SELECT
            tm2.mat_id matId,
            aa.mat_type matClass,
            tm2.mat_name matName,
            tm2.mat_standard matStandard,
            tamm.id as mapId,
            tmp.mat_price_id as matPriceId,
        tm2.drug_id as drugId,
        tamm.k_mat_id as kMatId,
        tamm.k_mat_name as kMatName
        FROM
            t_material_price AS tmp
                JOIN t_material AS tm2 ON ( tm2.status = '0' AND tm2.mat_id = tmp.mat_id and tm2.drug_id=tmp.drug_id )
                join t_material_specification as aa on(aa.drug_id=tmp.drug_id AND aa.mat_spe_id=tmp.mat_spe_id)
            LEFT JOIN t_app_material_mapping AS tamm ON(tamm.drug_id = tm2.drug_id AND tamm.mat_price_id = tmp.mat_price_id AND tmp.`drug_id`=tamm.drug_id)

        where tmp.status='0'
        <if test=" isMapping == 1">
            and tamm.id is not null
        </if>
        <if test=" isMapping == 2">
            AND tamm.id IS NULL
        </if>
        <if test=" drugId != null and drugId != ''">
            and tm2.drug_id =  #{drugId}
        </if>
        <if test=" matType != null and matType !=''">
            and aa.mat_type =#{matType}
        </if>
        <if test=" keyWord != null and keyWord !=''">
            and (tm2.mat_name like CONCAT('%',trim(#{keyWord}),'%') or tm2.mat_standard like CONCAT('%',trim(#{keyWord}),'%')
                or tm2.mat_pinyin like  CONCAT('%',trim(#{keyWord}),'%') or tm2.mat_wubi like   CONCAT('%',trim(#{keyWord}),'%'))
        </if>

    </select>
    <select id="selectCountData" resultType="java.lang.Long" parameterType="TMaterialKnowVO">
        select count(*) from t_app_material_mapping
        <where>
            1=1
            <if test=" matPriceId != null and matPriceId !=''">
            and mat_price_id = #{matPriceId}
            </if>
            <if test=" drugId != null and drugId !=''">
                and drug_id = #{drugId}
            </if>
            <if test=" kMatId != null and kMatId !=''">
            and k_mat_id = #{kMatId}
            </if>
        </where>

    </select>
    <select id="selectautoMappingCount" parameterType="StandTMAutoMappingVO" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
            t_material_price AS tmp
                JOIN t_material AS tm2 ON ( tm2.status = '0' AND tm2.mat_id = tmp.mat_id and tm2.drug_id=tmp.drug_id)
                JOIN t_drug_list AS tdl ON (tm2.drug_id = tdl.drug_id AND tmp.`drug_id`=tdl.`drug_id` )
                LEFT JOIN t_app_material_mapping AS tamm ON(tamm.drug_id = tm2.drug_id AND tamm.mat_price_id = tmp.mat_price_id AND tmp.`drug_id`=tamm.drug_id)
                LEFT JOIN b_material AS bm ON(bm.k_mat_id=tamm.k_mat_id )
                LEFT JOIN t_pharmacy AS tp ON(tdl.drug_id = tp.drug_id)
        where tmp.status='0' and tdl.drug_id=#{drugId} and bm.k_mat_id is null
    </select>


    <select id="getObjectByDrugId" resultMap="BaseResultMap1" parameterType="String">
        select  tamm.k_mat_id,tamm.k_mat_name,
               tamm.drug_id,tamm.mat_price_id,tmp.maxdose,
               tmp.mindose,tmp.toxicity,tmp.mother_taboos,tmp.special_usages
        from t_app_material_mapping tamm join
            t_material_price  tmp  on (tamm.mat_price_id = tmp.mat_price_id AND tmp.`drug_id`=tamm.drug_id)

        where tamm.drug_id = #{drugId}
    </select>


</mapper>