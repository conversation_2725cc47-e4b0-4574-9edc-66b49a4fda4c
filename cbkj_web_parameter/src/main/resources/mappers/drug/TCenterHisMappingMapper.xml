<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.TCenterHisMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.TCenterHisMapping">
        <id column="id" jdbcType="VARCHAR"  property="id" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="mat_price_id" jdbcType="VARCHAR" property="matPriceId" />
        <result column="drug_id_his" jdbcType="VARCHAR" property="drugIdHis" />
        <result column="mat_price_id_his" jdbcType="VARCHAR" property="matPriceIdHis" />
        <result column="TYPE" jdbcType="TINYINT" property="type" />
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    id,drug_id,mat_price_id,drug_id_his,mat_price_id_his,TYPE,CREATE_DATE,CREATE_USER,create_user_name,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TCenterHisMapping">
        delete from t_center_his_mapping where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_center_his_mapping where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TCenterHisMapping">
        insert into t_center_his_mapping (<include refid="Base_Column_List" />) values
        (#{id},#{drugId},#{matPriceId},#{drugIdHis},#{matPriceIdHis},#{type},NOW(),#{createUser},#{createUserName},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_center_his_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.drugId},#{item.matPriceId},#{item.drugIdHis},#{item.matPriceIdHis},#{item.type},NOW(),#{item.createUser},#{item.createUserName},#{item.status})
        </foreach>
    </insert>
    <insert id="insertautoMapping" parameterType="StandTMAutoMappingVO">
        INSERT INTO t_app_material_mapping
        SELECT rand_string(32),
               tm2.drug_id,
               tmp.mat_price_id,
               m.drug_id as drug_id_his,
               tmp2.mat_price_id AS mat_price_id_his,
               '1',
               NOW(),
               #{createUser},
               #{createUserName},
               '1'
        FROM t_material_price tmp
                 JOIN t_material AS tm2 ON (tm2.status = '0' AND tm2.mat_id = tmp.mat_id)
                 left join t_material_price as tmp2 on(tmp2.status='0')
            LEFT JOIN t_material m ON (
            tmp2.mat_id=m.mat_id and m.status='0' and
            m.mat_name = substring_index(substring_index(substring_index(substring_index(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(REPLACE(tm2.mat_name,'【配送】',''),'5',''),'1',''),'*',''),'d',''),'K',''),'G',''),'P',''),'Z',''),'配方颗粒',''),'颗粒',''),'(',1),'（',1),'[',1),'&lt;',1)
            )
            left join t_drug_list as tdl on( tdl.drug_id=m.drug_id and tdl.drug_type='1' )
            LEFT JOIN t_center_his_mapping p ON (p.mat_price_id = tmp.mat_price_id and p.mat_price_id_his=tmp2.mat_price_id)
        WHERE  tmp.status = '0' and tm2.drug_id = #{drugId} AND p.mat_price_id IS NULL AND m.mat_id IS NOT NULL
    </insert>

    <update id="updateByPrimaryKey" parameterType="TCenterHisMapping">
        update t_center_his_mapping
        <set>
             <if test="drugId != null">
                drug_id = #{ drugId },
             </if>
             <if test="matPriceId != null">
                mat_price_id = #{ matPriceId },
             </if>
             <if test="drugIdHis != null">
                drug_id_his = #{ drugIdHis },
             </if>
             <if test="matPriceIdHis != null">
                mat_price_id_his = #{ matPriceIdHis },
             </if>
             <if test="type != null">
                TYPE = #{ type },
             </if>
             <if test="createDate != null">
                CREATE_DATE = #{ createDate },
             </if>
             <if test="createUser != null">
                CREATE_USER = #{ createUser },
             </if>
             <if test="createUserName != null">
                 create_user_name = #{ createUserName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_center_his_mapping where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TCenterHisMapping" resultMap="BaseResultMap">
        SELECT id,drug_id,mat_price_id,drug_id_his,mat_price_id_his,TYPE,CREATE_DATE,CREATE_USER,create_user_name,status
        from t_center_his_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.drug.TMaterialHISVO">
        <id column="matId" jdbcType="VARCHAR"  property="matId" />
    </resultMap>

    <select id="getPageDatas2" resultType="TMaterialHISVO" resultMap="BaseResultMap2">
        SELECT
            tm2.mat_id matId,
            tm2.mat_name matName,
            tm2.mat_standard matStandard,
            tm2.drug_id drugId,
            tmp.mat_price_id as matPriceId,
            tm22.mat_id matIdHis,
            tm22.mat_name matNameHis,
            tm22.mat_standard matStandardHis,
            tm22.drug_id drugIdHis,
            tmp11.mat_price_id as matPriceIdHis
        FROM
                t_material_price AS tmp
                JOIN t_material AS tm2 ON ( tm2.status = '0' AND tm2.is_del='0' AND tm2.mat_id = tmp.mat_id )
                JOIN t_drug_list AS tdl ON (tm2.drug_id = tdl.drug_id and tdl.drug_type='0')
                LEFT JOIN t_center_his_mapping AS tchm ON(tchm.drug_id = tm2.drug_id AND tchm.mat_price_id = tmp.mat_price_id )
            left join t_material_price AS tmp11 on (tmp11.status='0' and tmp11.mat_id=tchm.drug_id_his and tmp11.mat_price_id=tchm.mat_price_id_his)
            left JOIN t_material AS tm22 ON ( tm22.status = '0' AND tm22.is_del='0' AND tm22.mat_id = tmp11.mat_id )
            left JOIN t_drug_list AS tdl11 ON (tm22.drug_id = tdl11.drug_id and tdl11.drug_type='1')
            LEFT JOIN t_pharmacy AS tp ON(tdl.drug_id = tp.drug_id)
        where tmp.status='0'
        <if test=" isMapping == 1">
            and tm22.k_mat_id is not null
        </if>
        <if test=" isMapping == 2">
            and tm22.k_mat_id is null
        </if>
        <if test=" phaType != null and phaType !=''">
            and tp.pha_type =#{phaType}
        </if>
        <if test=" keyWord != null and keyWord !=''">
            and (tm2.mat_name like CONCAT('%',trim(#{keyWord}),'%') or tm2.mat_standard like CONCAT('%',trim(#{keyWord}),'%') )
        </if>
        GROUP BY tmp.mat_id
    </select>
    <select id="selectCountData" resultType="java.lang.Long" parameterType="TMaterialHISVO">
        SELECT count(*)
        from t_center_his_mapping
<where>
1=1
    <if test="matPriceId != null and matPriceId !='' ">
        and mat_price_id = #{matPriceId}
    </if>
    <if test="matPriceIdHis != null and matPriceIdHis !='' ">
    and mat_price_id_his = #{matPriceIdHis}
    </if>
</where>
    </select>

</mapper>