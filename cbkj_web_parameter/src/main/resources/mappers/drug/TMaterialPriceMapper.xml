<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.TMaterialPriceMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.TMaterialPrice">
        <id column="mat_price_id" jdbcType="VARCHAR"  property="matPriceId" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="mat_origin_id" jdbcType="VARCHAR" property="matOriginId" />
        <result column="mat_spe_id" jdbcType="VARCHAR" property="matSpeId" />
        <result column="mat_id" jdbcType="VARCHAR" property="matId" />
        <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
        <result column="purchase_price" jdbcType="DECIMAL" property="purchasePrice" />
        <result column="small_purchase_price" jdbcType="DECIMAL" property="smallPurchasePrice" />
        <result column="small_retail_price" jdbcType="DECIMAL" property="smallRetailPrice" />
        <result column="markup_rate" jdbcType="DECIMAL" property="markupRate" />
        <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
        <result column="is_give" jdbcType="VARCHAR" property="isGive" />
        <result column="price_seqn" jdbcType="SMALLINT" property="priceSeqn" />
        <result column="mat_origin_name" jdbcType="VARCHAR" property="matOriginName" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="disable_date" jdbcType="TIMESTAMP" property="disableDate" />
        <result column="disable_user" jdbcType="VARCHAR" property="disableUser" />
        <result column="disable_username" jdbcType="VARCHAR" property="disableUsername" />
        <result column="is_disable" jdbcType="VARCHAR" property="isDisable" />
        <result column="is_stock" jdbcType="VARCHAR" property="isStock" />
        <result column="daily_max_dose_in" jdbcType="DOUBLE" property="dailyMaxDoseIn" />
        <result column="daily_max_dose_ext" jdbcType="DOUBLE" property="dailyMaxDoseExt" />
        <result column="daily_max_num_prep" jdbcType="DOUBLE" property="dailyMaxNumPrep" />
        <result column="not_pay_alone" jdbcType="VARCHAR" property="notPayAlone" />
        <result column="not_pay_in_fund" jdbcType="VARCHAR" property="notPayInFund" />
        <result column="frequency_id" jdbcType="VARCHAR" property="frequencyId" />
        <result column="frequency" jdbcType="VARCHAR" property="frequency" />
        <result column="frequency_rate" jdbcType="DECIMAL" property="frequencyRate" />
        <result column="toxicity_overdose_multiple" jdbcType="VARCHAR" property="toxicityOverdoseMultiple" />
        <result column="external_use_only" jdbcType="VARCHAR" property="externalUseOnly" />
        <result column="toxicity" jdbcType="VARCHAR" property="toxicity" />
        <result column="mother_taboos" jdbcType="VARCHAR" property="motherTaboos" />
        <result column="maxdose" jdbcType="DOUBLE" property="maxdose" />
        <result column="mindose" jdbcType="DOUBLE" property="mindose" />
        <result column="special_usages" jdbcType="VARCHAR" property="specialUsages" />
        <result column="sdk_tags_codes" jdbcType="VARCHAR" property="sdkTagsCodes" />
    </resultMap>


    <sql id="Base_Column_List">
        special_usages,maxdose,mindose,toxicity,mother_taboos,mat_price_id,drug_id,mat_origin_id,mat_spe_id,mat_id,retail_price,purchase_price,small_purchase_price,small_retail_price,markup_rate,approval_number,is_give,price_seqn,mat_origin_name,create_date,create_user,create_user_name,status,disable_date,disable_user,disable_username,is_disable,is_stock,daily_max_dose_in,daily_max_dose_ext,daily_max_num_prep,not_pay_alone,not_pay_in_fund,frequency_id,frequency,frequency_rate,sdk_tags_codes
    </sql>



    <select id="getMaterialPrice" parameterType="com.jiuzhekan.cbkj.beans.drug.TMaterialKnowMapping"
            resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from t_material_price
        <where>
            <if test="drugId!=null and drugId!=''">
                drug_id = #{drugId}
            </if>
            <if test="matPriceId!=null ">
            and mat_price_id = #{matPriceId}
            </if>
        </where>
    </select>

    <update id="updateList" parameterType="list">
        <foreach collection="list" item="item" index="index" separator=";" >
            update t_material_price
            <set>
                <if test="item.maxdose != null">
                    maxdose = #{ item.maxdose },
                </if>
                <if test="item.mindose != null">
                    mindose = #{ item.mindose },
                </if>

                <if test="item.toxicity != null">
                    toxicity = #{item.toxicity},
                </if>
                <if test="item.motherTaboos != null">
                    mother_taboos = #{item.motherTaboos},
                </if>
                <if test="item.specialUsages != null">
                    special_usages = #{item.specialUsages},
                </if>
            </set>
            <where>
                <if test="item.drugId!=null and item.drugId!=''">
                    drug_id = #{item.drugId}
                </if>
                <if test="item.matPriceId!=null and item.matPriceId!=''">
                    and mat_price_id = #{item.matPriceId}
                </if>
            </where>
        </foreach>
    </update>



    <delete id="deleteByPrimaryKey" parameterType="TMaterialPrice">
        delete from t_material_price where mat_price_id = #{ matPriceId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_material_price where mat_price_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TMaterialPrice">
        insert into t_material_price (<include refid="Base_Column_List" />) values
        (#{matPriceId},#{matOriginId},#{matSpeId},#{matId},#{retailPrice},#{purchasePrice},#{smallPurchasePrice},#{smallRetailPrice},#{markupRate},#{approvalNumber},#{isGive},#{priceSeqn},#{matOriginName},#{createDate},#{createUser},#{createUserName},#{status},#{disableDate},#{disableUser},#{disableUsername},#{isDisable},#{isStock},#{dailyMaxDoseIn},#{dailyMaxDoseExt},#{dailyMaxNumPrep},#{notPayAlone},#{notPayInFund},#{frequencyId},#{frequency},#{frequencyRate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_material_price (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.matPriceId},#{item.matOriginId},#{item.matSpeId},#{item.matId},#{item.retailPrice},#{item.purchasePrice},#{item.smallPurchasePrice},#{item.smallRetailPrice},#{item.markupRate},#{item.approvalNumber},#{item.isGive},#{item.priceSeqn},#{item.matOriginName},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status},#{item.disableDate},#{item.disableUser},#{item.disableUsername},#{item.isDisable},#{item.isStock},#{item.dailyMaxDoseIn},#{item.dailyMaxDoseExt},#{item.dailyMaxNumPrep},#{item.notPayAlone},#{item.notPayInFund},#{item.frequencyId},#{item.frequency},#{item.frequencyRate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TMaterialPrice">
        update t_material_price
        <set>
             <if test="matOriginId != null">
                mat_origin_id = #{ matOriginId },
             </if>
             <if test="matSpeId != null">
                mat_spe_id = #{ matSpeId },
             </if>
             <if test="matId != null">
                mat_id = #{ matId },
             </if>
             <if test="retailPrice != null">
                retail_price = #{ retailPrice },
             </if>
             <if test="purchasePrice != null">
                purchase_price = #{ purchasePrice },
             </if>
             <if test="smallPurchasePrice != null">
                small_purchase_price = #{ smallPurchasePrice },
             </if>
             <if test="smallRetailPrice != null">
                small_retail_price = #{ smallRetailPrice },
             </if>
             <if test="markupRate != null">
                markup_rate = #{ markupRate },
             </if>
             <if test="approvalNumber != null">
                approval_number = #{ approvalNumber },
             </if>
             <if test="isGive != null">
                is_give = #{ isGive },
             </if>
             <if test="priceSeqn != null">
                price_seqn = #{ priceSeqn },
             </if>
             <if test="matOriginName != null">
                mat_origin_name = #{ matOriginName },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="disableDate != null">
                disable_date = #{ disableDate },
             </if>
             <if test="disableUser != null">
                disable_user = #{ disableUser },
             </if>
             <if test="disableUsername != null">
                disable_username = #{ disableUsername },
             </if>
             <if test="isDisable != null">
                is_disable = #{ isDisable },
             </if>
             <if test="isStock != null">
                is_stock = #{ isStock },
             </if>
             <if test="dailyMaxDoseIn != null">
                daily_max_dose_in = #{ dailyMaxDoseIn },
             </if>
             <if test="dailyMaxDoseExt != null">
                daily_max_dose_ext = #{ dailyMaxDoseExt },
             </if>
             <if test="dailyMaxNumPrep != null">
                daily_max_num_prep = #{ dailyMaxNumPrep },
             </if>
             <if test="notPayAlone != null">
                not_pay_alone = #{ notPayAlone },
             </if>
             <if test="notPayInFund != null">
                not_pay_in_fund = #{ notPayInFund },
             </if>
             <if test="frequencyId != null">
                frequency_id = #{ frequencyId },
             </if>
             <if test="frequency != null">
                frequency = #{ frequency },
             </if>
             <if test="frequencyRate != null">
                frequency_rate = #{ frequencyRate },
             </if>
            <if test="externalUseOnly != null">
                external_use_only = #{ externalUseOnly },
             </if>
            <if test="toxicityOverdoseMultiple != null">
                toxicity_overdose_multiple = #{ toxicityOverdoseMultiple },
             </if>
            <if test="maxdose != null">
                maxdose = #{ maxdose },
            </if>
            <if test="mindose != null">
                mindose = #{ mindose },
            </if>

            <if test="toxicity != null">
                toxicity = #{toxicity},
            </if>
            <if test="motherTaboos != null">
                mother_taboos = #{motherTaboos},
            </if>
            <if test="externalUseOrally != null">
                external_use_orally = #{externalUseOrally},
            </if>
            <if test="externalMarusan != null">
                external_marusan = #{externalMarusan},
            </if>
            <if test="externalMarusanName != null">
                external_marusan_name = #{externalMarusanName},
            </if>
            <if test="specialUsages != null">
                special_usages = #{specialUsages},
            </if>
            <if test="usagesAstrict != null">
                usages_ustrict = #{usagesAstrict},
            </if>
            <if test="sdkTagsCodes != null">
                sdk_tags_codes = #{sdkTagsCodes},
            </if>

        </set>
        where mat_price_id = #{ matPriceId } and drug_id=#{drugId}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_material_price where mat_price_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TMaterialPrice" resultMap="BaseResultMap">
        SELECT mat_price_id,mat_origin_id,mat_spe_id,mat_id,retail_price,purchase_price,small_purchase_price,small_retail_price,markup_rate,approval_number,is_give,price_seqn,mat_origin_name,create_date,create_user,create_user_name,status,disable_date,disable_user,disable_username,is_disable,is_stock,daily_max_dose_in,daily_max_dose_ext,daily_max_num_prep,not_pay_alone,not_pay_in_fund,frequency_id,frequency,frequency_rate
        from t_material_price
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>