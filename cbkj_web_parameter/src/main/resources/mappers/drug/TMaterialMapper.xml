<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.TMaterialMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.TMaterial">
        <id column="mat_id" jdbcType="VARCHAR"  property="matId" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="mat_name" jdbcType="VARCHAR" property="matName" />
        <result column="mat_class" jdbcType="VARCHAR" property="matClass" />
        <result column="mat_standard" jdbcType="VARCHAR" property="matStandard" />
        <result column="mat_medical_code" jdbcType="VARCHAR" property="matMedicalCode" />
        <result column="toxin_type" jdbcType="VARCHAR" property="toxinType" />
        <result column="worth_type" jdbcType="VARCHAR" property="worthType" />
        <result column="is_medical" jdbcType="VARCHAR" property="isMedical" />
        <result column="mat_pinyin" jdbcType="VARCHAR" property="matPinyin" />
        <result column="mat_wubi" jdbcType="VARCHAR" property="matWubi" />
        <result column="is_stock" jdbcType="VARCHAR" property="isStock" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="del_date" jdbcType="DATE" property="delDate" />
        <result column="del_user" jdbcType="VARCHAR" property="delUser" />
        <result column="del_username" jdbcType="VARCHAR" property="delUsername" />
        <result column="is_del" jdbcType="VARCHAR" property="isDel" />
        <result column="medicontrol_text" jdbcType="VARCHAR" property="medicontrolText" />
        <result column="mat_usage" jdbcType="VARCHAR" property="matUsage" />
        <result column="usage_desc" jdbcType="VARCHAR" property="usageDesc" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="mat_usage_code" jdbcType="VARCHAR" property="matUsageCode" />
        <result column="effect" jdbcType="VARCHAR" property="effect" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    mat_id,drug_id,mat_name,mat_class,mat_standard,mat_medical_code,toxin_type,worth_type,is_medical,mat_pinyin,mat_wubi,is_stock,create_date,create_user,create_user_name,update_date,update_user_name,update_user,del_date,del_user,del_username,is_del,medicontrol_text,mat_usage,usage_desc,content,mat_usage_code,effect,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TMaterial">
        delete from t_material where mat_id = #{ matId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_material where mat_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TMaterial">
        insert into t_material (<include refid="Base_Column_List" />) values
        (#{matId},#{drugId},#{matName},#{matClass},#{matStandard},#{matMedicalCode},#{toxinType},#{worthType},#{isMedical},#{matPinyin},#{matWubi},#{isStock},#{createDate},#{createUser},#{createUserName},#{updateDate},#{updateUserName},#{updateUser},#{delDate},#{delUser},#{delUsername},#{isDel},#{medicontrolText},#{matUsage},#{usageDesc},#{content},#{matUsageCode},#{effect},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_material (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.matId},#{item.drugId},#{item.matName},#{item.matClass},#{item.matStandard},#{item.matMedicalCode},#{item.toxinType},#{item.worthType},#{item.isMedical},#{item.matPinyin},#{item.matWubi},#{item.isStock},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.updateDate},#{item.updateUserName},#{item.updateUser},#{item.delDate},#{item.delUser},#{item.delUsername},#{item.isDel},#{item.medicontrolText},#{item.matUsage},#{item.usageDesc},#{item.content},#{item.matUsageCode},#{item.effect},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TMaterial">
        update t_material
        <set>
             <if test="drugId != null">
                drug_id = #{ drugId },
             </if>
             <if test="matName != null">
                mat_name = #{ matName },
             </if>
             <if test="matClass != null">
                mat_class = #{ matClass },
             </if>
             <if test="matStandard != null">
                mat_standard = #{ matStandard },
             </if>
             <if test="matMedicalCode != null">
                mat_medical_code = #{ matMedicalCode },
             </if>
             <if test="toxinType != null">
                toxin_type = #{ toxinType },
             </if>
             <if test="worthType != null">
                worth_type = #{ worthType },
             </if>
             <if test="isMedical != null">
                is_medical = #{ isMedical },
             </if>
             <if test="matPinyin != null">
                mat_pinyin = #{ matPinyin },
             </if>
             <if test="matWubi != null">
                mat_wubi = #{ matWubi },
             </if>
             <if test="isStock != null">
                is_stock = #{ isStock },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                 create_user_name = #{ createUserName },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
             <if test="updateUserName != null">
                 update_user_name = #{ updateUserName },
             </if>
             <if test="updateUser != null">
                update_user = #{ updateUser },
             </if>
             <if test="delDate != null">
                del_date = #{ delDate },
             </if>
             <if test="delUser != null">
                del_user = #{ delUser },
             </if>
             <if test="delUsername != null">
                del_username = #{ delUsername },
             </if>
             <if test="isDel != null">
                is_del = #{ isDel },
             </if>
             <if test="medicontrolText != null">
                medicontrol_text = #{ medicontrolText },
             </if>
             <if test="matUsage != null">
                mat_usage = #{ matUsage },
             </if>
             <if test="usageDesc != null">
                usage_desc = #{ usageDesc },
             </if>
             <if test="content != null">
                content = #{ content },
             </if>
             <if test="matUsageCode != null">
                mat_usage_code = #{ matUsageCode },
             </if>
             <if test="effect != null">
                effect = #{ effect },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where mat_id = #{ matId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_material where mat_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TMaterial" resultMap="BaseResultMap">
        SELECT mat_id,drug_id,mat_name,mat_class,mat_standard,mat_medical_code,toxin_type,worth_type,is_medical,mat_pinyin,mat_wubi,is_stock,create_date,create_user,create_user_name,update_date,update_user_name,update_user,del_date,del_user,del_username,is_del,medicontrol_text,mat_usage,usage_desc,content,mat_usage_code,effect,status
        from t_material
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getMaterialByDrugId" parameterType="string" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_material where drug_id=#{drugId} limit 5
    </select>

</mapper>