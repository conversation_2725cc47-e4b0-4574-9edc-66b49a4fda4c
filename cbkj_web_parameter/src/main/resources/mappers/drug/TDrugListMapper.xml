<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.TDrugListMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.TDrugList">
        <id column="drug_id" jdbcType="VARCHAR" property="drugId"/>
        <result column="drug_name" jdbcType="VARCHAR" property="drugName"/>
        <result column="drug_desc" jdbcType="VARCHAR" property="drugDesc"/>
        <result column="drug_type" jdbcType="VARCHAR" property="drugType"/>
        <result column="drug_version" jdbcType="VARCHAR" property="drugVersion"/>
        <result column="drug_syntime" jdbcType="TIMESTAMP" property="drugSyntime"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sync_source" jdbcType="VARCHAR" property="syncSource"/>
        <result column="drug_code" jdbcType="VARCHAR" property="drugCode"/>
        <result column="his_id" jdbcType="VARCHAR" property="hisId"/>
        <result column="his_url_type" jdbcType="VARCHAR" property="hisUrlType"/>
    </resultMap>


    <sql id="Base_Column_List">
        drug_id
        ,drug_name,drug_desc,drug_type,drug_version,drug_syntime,create_date,create_user,create_user_name,status,sync_source,drug_code,his_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDrugList">
        delete
        from t_drug_list
        where drug_id = #{ drugId }
    </delete>
    <update id="deleteByDrugId" parameterType="string">
        update t_drug_list
        set status='1'
        where drug_id = #{ drugId }
    </update>

    <update id="deleteBylist" parameterType="ArrayList">
        update t_drug_list set status = "0" where drug_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="insert" parameterType="TDrugList">
        insert into t_drug_list (<include refid="Base_Column_List"/>) values
        (#{drugId},#{drugName},#{drugDesc},#{drugType},#{drugVersion},#{drugSyntime},#{createDate},#{createUser},#{createUserName},#{status},#{syncSource},#{drugCode},#{hisId})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_drug_list (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.drugId},#{item.drugName},#{item.drugDesc},#{item.drugType},#{item.drugVersion},#{item.drugSyntime},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status},#{item.syncSource},#{item.drugCode},#{item.hisId})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDrugList">
        update t_drug_list
        <set>
            <if test="drugName != null">
                drug_name = #{ drugName },
            </if>
            <if test="drugDesc != null">
                drug_desc = #{ drugDesc },
            </if>
            <if test="drugType != null">
                drug_type = #{ drugType },
            </if>
            <if test="drugVersion != null">
                drug_version = #{ drugVersion },
            </if>
            <if test="drugSyntime != null">
                drug_syntime = #{ drugSyntime },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="drugCode != null">
                drug_code = #{drugCode},
            </if>
            <if test="hisId != null">
                his_id = #{hisId},
            </if>
        </set>
        where drug_id = #{ drugId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_drug_list where drug_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDrugList" resultMap="BaseResultMap">
        SELECT
        drug_id,drug_name,drug_desc,drug_type,drug_version,drug_syntime,create_date,create_user,create_user_name,status,sync_source
        from t_drug_list
        <where>
            status = '0'
            <if test=" drugName != null and drugName!='' ">
                and drug_name like CONCAT('%',trim(#{drugName}),'%')
            </if>
            <if test=" drugType != null and drugType!='' ">
                and drug_type = #{drugType}
            </if>
        </where>
    </select>
    <select id="getPageListByObj2" resultMap="BaseResultMap" parameterType="TDrugList">
        select
        <include refid="Base_Column_List"/>
        from t_drug_list
        <where>
            status = '0'
            <if test="drugName != null and drugName !=''">
                and drug_name like CONCAT('%',trim(#{drugName}),'%')
            </if>
            <if test="drugType != null and drugType != ''">
                and drug_type = #{ drugType }
            </if>
            <if test="drugId != null and drugId != ''">
                and drug_id = #{drugId}
            </if>
        </where>
    </select>

    <select id="getDrugPageList" parameterType="TDrugList" resultMap="BaseResultMap">
        select tdl.drug_id,
        tdl.drug_name,
        tdl.drug_desc,
        tdl.drug_type,
        tdl.drug_version,
        tdl.drug_syntime,
        tdl.create_date,
        tdl.create_user,
        tdl.create_user_name,
        tdl.status,
        tdl.sync_source,
        tdl.drug_code,
        tdl.his_id,
        tih.his_url_type
        from t_drug_list as tdl
        left join t_interface_his as tih on tdl.his_id=tih.his_id
        where tdl.status='0'
        <if test="drugName != null and drugName !=''">
            and drug_name like CONCAT('%',trim(#{drugName}),'%')
        </if>
    </select>
    <select id="getMaxDrugId" resultType="string">
        SELECT MAX(drug_id)
        FROM t_drug_list
    </select>
    <select id="getDrugByDrugCode" parameterType="string" resultMap="BaseResultMap">
        SELECT drug_id,
               drug_name,
               drug_desc,
               drug_type,
               drug_version,
               drug_syntime,
               create_date,
               create_user,
               create_user_name,
               status,
               sync_source,
               drug_code,
               his_id
        FROM t_drug_list
        WHERE drug_code = #{drugCode} and status='0'
    </select>

</mapper>