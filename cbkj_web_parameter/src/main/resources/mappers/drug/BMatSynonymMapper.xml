<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.drug.BMatSynonymMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.drug.BMatSynonym">
        <id column="mat_syn_id" jdbcType="VARCHAR"  property="matSynId" />
        <result column="k_mat_id" jdbcType="VARCHAR" property="kMatId" />
        <result column="k_mat_name" jdbcType="VARCHAR" property="kMatName" />
        <result column="k_mat_syn_name" jdbcType="VARCHAR" property="kMatSynName" />
        <result column="k_mat_syn_py" jdbcType="VARCHAR" property="kMatSynPy" />
        <result column="k_mat_syn_wb" jdbcType="VARCHAR" property="kMatSynWb" />
        <result column="k_mat_syn_origin" jdbcType="VARCHAR" property="kMatSynOrigin" />
        <result column="k_mat_type" jdbcType="VARCHAR" property="kMatType" />
        <result column="seqn" jdbcType="INTEGER" property="seqn" />
    </resultMap>


    <sql id="Base_Column_List">
    mat_syn_id,k_mat_id,k_mat_name,k_mat_syn_name,k_mat_syn_py,k_mat_syn_wb,k_mat_syn_origin,k_mat_type,seqn
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="BMatSynonym">
        delete from b_mat_synonym where mat_syn_id = #{ matSynId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from b_mat_synonym where mat_syn_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="BMatSynonym">
        insert into b_mat_synonym (<include refid="Base_Column_List" />) values
        (#{matSynId},#{kMatId},#{kMatName},#{kMatSynName},#{kMatSynPy},#{kMatSynWb},#{kMatSynOrigin},#{kMatType},#{seqn})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into b_mat_synonym (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.matSynId},#{item.kMatId},#{item.kMatName},#{item.kMatSynName},#{item.kMatSynPy},#{item.kMatSynWb},#{item.kMatSynOrigin},#{item.kMatType},#{item.seqn})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="BMatSynonym">
        update b_mat_synonym
        <set>
             <if test="kMatId != null">
                k_mat_id = #{ kMatId },
             </if>
             <if test="kMatName != null">
                k_mat_name = #{ kMatName },
             </if>
             <if test="kMatSynName != null">
                k_mat_syn_name = #{ kMatSynName },
             </if>
             <if test="kMatSynPy != null">
                k_mat_syn_py = #{ kMatSynPy },
             </if>
             <if test="kMatSynWb != null">
                k_mat_syn_wb = #{ kMatSynWb },
             </if>
             <if test="kMatSynOrigin != null">
                k_mat_syn_origin = #{ kMatSynOrigin },
             </if>
             <if test="kMatType != null">
                k_mat_type = #{ kMatType },
             </if>
             <if test="seqn != null">
                seqn = #{ seqn },
             </if>
        </set>
        where mat_syn_id = #{ matSynId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from b_mat_synonym where mat_syn_id = #{matSynId}
    </select>

    <select id="getPageListByObj" parameterType="BMatSynonym" resultMap="BaseResultMap">
        SELECT mat_syn_id,k_mat_id,k_mat_name,k_mat_syn_name,k_mat_syn_py,k_mat_syn_wb,k_mat_syn_origin,k_mat_type,seqn
        from b_mat_synonym
        <where>
            <if test=" kMatName != null and kMatName!='' ">
                and k_mat_name like CONCAT('%',trim(#{kMatName}),'%')
            </if>
            <if test=" kMatId != null and kMatId!='' ">
                and k_mat_id = #{kMatId}
            </if>
            <if test=" kMatType != null and kMatType!='' ">
                and k_mat_type = #{kMatType}
            </if>
        </where>
    </select>

</mapper>