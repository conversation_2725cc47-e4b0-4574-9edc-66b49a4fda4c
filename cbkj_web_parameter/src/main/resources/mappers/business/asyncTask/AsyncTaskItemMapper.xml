<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskItem">
        <id column="task_item_id" jdbcType="INTEGER"  property="taskItemId" />
        <result column="async_id" jdbcType="INTEGER" property="asyncId" />
        <result column="async_start_date" jdbcType="TIMESTAMP" property="asyncStartDate" />
        <result column="async_end_date" jdbcType="TIMESTAMP" property="asyncEndDate" />
        <result column="async_time" jdbcType="INTEGER" property="asyncTime" />
        <result column="async_result" jdbcType="VARCHAR" property="asyncResult" />
        <result column="async_result_detail" jdbcType="VARCHAR" property="asyncResultDetail" />
        <result column="async_type" jdbcType="VARCHAR" property="asyncType" />
        <result column="async_exec_type" jdbcType="VARCHAR" property="asyncExecType" />
        <result column="async_start_time" jdbcType="VARCHAR" property="asyncStartTime" />
        <result column="async_end_time" jdbcType="VARCHAR" property="asyncEndTime" />
        <result column="async_between_time" jdbcType="VARCHAR" property="asyncBetweenTime" />
        <result column="async_between_unit" jdbcType="VARCHAR" property="asyncBetweenUnit" />
        <result column="his_id" jdbcType="VARCHAR" property="hisId" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
        task_item_id,async_id,async_start_date,async_end_date,async_time,async_result,async_result_detail,async_type,async_exec_type,async_start_time,async_end_time,async_between_time,async_between_unit,his_id,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="AsyncTaskItem">
        delete from async_task_item where task_item_id = #{ taskItemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from async_task_item where task_item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="AsyncTaskItem">
        insert into async_task_item (<include refid="Base_Column_List" />) values
        (#{taskItemId},#{asyncId},#{asyncStartDate},#{asyncEndDate},#{asyncTime},#{asyncResult},#{asyncResultDetail},#{asyncType},#{asyncExecType},#{asyncStartTime},#{asyncEndTime},#{asyncBetweenTime},#{asyncBetweenUnit},#{hisId},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into async_task_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.taskItemId},#{item.asyncId},#{item.asyncStartDate},#{item.asyncEndDate},#{item.asyncTime},#{item.asyncResult},#{item.asyncResultDetail},#{item.asyncType},#{item.asyncExecType},#{item.asyncStartTime},#{item.asyncEndTime},#{item.asyncBetweenTime},#{item.asyncBetweenUnit},#{item.hisId},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="AsyncTaskItem">
        update async_task_item
        <set>
            <if test="asyncId != null">
                async_id = #{ asyncId },
            </if>
            <if test="asyncStartDate != null">
                async_start_date = #{ asyncStartDate },
            </if>
            <if test="asyncEndDate != null">
                async_end_date = #{ asyncEndDate },
            </if>
            <if test="asyncTime != null">
                async_time = #{ asyncTime },
            </if>
            <if test="asyncResult != null">
                async_result = #{ asyncResult },
            </if>
            <if test="asyncResultDetail != null">
                async_result_detail = #{ asyncResultDetail },
            </if>
            <if test="asyncType != null">
                async_type = #{ asyncType },
            </if>
            <if test="asyncExecType != null">
                async_exec_type = #{ asyncExecType },
            </if>
            <if test="asyncStartTime != null">
                async_start_time = #{ asyncStartTime },
            </if>
            <if test="asyncEndTime != null">
                async_end_time = #{ asyncEndTime },
            </if>
            <if test="asyncBetweenTime != null">
                async_between_time = #{ asyncBetweenTime },
            </if>
            <if test="asyncBetweenUnit != null">
                async_between_unit = #{ asyncBetweenUnit },
            </if>

            <if test="hisId != null">
                his_id = #{ hisId },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
        </set>
        where task_item_id = #{ taskItemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        SELECT
            a.`async_between_time`,
            a.`async_between_unit`,
            a.`async_end_date`,
            a.`async_end_time`,
            a.`async_exec_type`,
            a.`async_id`,
            a.`async_result`,
            a.`async_result_detail`,
            a.`async_start_date`,
            a.`async_start_time`,
            a.`async_time`,
            a.`async_type`,
            a.`task_item_id`,
            b.`async_name` as asyncName
        FROM async_task_item AS a JOIN `async_task` AS b ON b.`id`=a.`async_id`
        where a.task_item_id = #{ taskItemId }
    </select>

    <select id="getPageListByObj" parameterType="AsyncTaskItem" resultMap="BaseResultMap">
        SELECT task_item_id,async_id,async_start_date,async_end_date,async_time,async_result,async_result_detail,async_type,async_exec_type,async_start_time,async_end_time,async_between_time,async_between_unit,his_id
        from async_task_item
        <where>
            <if test=" asyncId != null and asyncId!='' ">
                and async_id = #{asyncId}
            </if>
        </where>
    </select>
    <select id="getPageListByObj2" parameterType="AsyncTaskItemQuery" resultMap="BaseResultMap">
        SELECT t.* FROM (

        SELECT
        ati.`task_item_id`,
        ati.`async_exec_type`,
        ati.`async_type`,
        a.id,
        a.`async_name` as asyncName,
        tis.`his_name` as hisName,
        ati.`async_start_date`,
        ati.`async_end_date`,
        ati.`async_result`,
        ati.`his_id`,
        ati.`async_start_time`,
        ati.`async_between_unit`,
        ati.`async_between_time`,
        ati.`async_end_time`,
        tdl.drug_name AS taskDetail
        FROM `async_task_item` AS ati JOIN `async_task` a ON a.`id` = ati.`async_id`
            JOIN t_interface_his AS tis ON tis.`his_id`=a.`his_id`
            JOIN `async_task_value` AS atv ON atv.`async_id` = a.`id`
        LEFT JOIN `t_drug_list` AS tdl ON tdl.`drug_id` = atv.`drug_id`
        <where>
            a.`async_type` IN ('1','2')
            <if test=" status != null and status!='' ">
                and ati.status = #{status}
            </if>
            <if test=" startTime != null ">
                and ati.async_start_date &gt;= #{startTime}
            </if>
            <if test=" endTime != null ">
                and ati.async_start_date &lt;= #{endTime}
            </if>
            <if test=" drugId != null and drugId !=''">
                and tdl.drug_id = #{drugId}
            </if>
            <if test=" asyncType != null and asyncType !=''">
                and ati.async_type = #{asyncType}
            </if>
            <if test=" asyncName != null and asyncName !=''">
                and a.async_name like CONCAT('%',trim(#{asyncName}),'%')
            </if>
            <if test=" taskDetail != null and taskDetail !=''">
                and tdl.drug_name like CONCAT('%',trim(#{taskDetail}),'%')
            </if>
        </where>
        <if test=" drugId == null or drugId ==''">
            <if test=" taskDetail == null or taskDetail ==''">

            </if>
        </if>
        <choose>
            <when test="drugId != null and drugId !='' and (taskDetail==null or taskDetail =='') ">

            </when>
        <otherwise>
            UNION
            SELECT
            ati2.`task_item_id`,
            a2.`async_exec_type`,
            a2.`async_type`,
            a2.id,
            a2.`async_name` as asyncName,
            tis2.`his_name` as hisName,
            ati2.`async_start_date`,
            ati2.`async_end_date`,
            ati2.`async_result`,
            a2.`his_id`,
            a2.`async_start_time`,
            a2.`async_between_unit`,
            a2.`async_between_time`,
            a2.`async_end_time`,
            GROUP_CONCAT(si.ins_name) AS taskDetail
            FROM `async_task_item` AS ati2 JOIN `async_task` a2 ON a2.`id` = ati2.`async_id`
            JOIN t_interface_his AS tis2 ON tis2.`his_id`=a2.`his_id` JOIN `async_task_value` AS atv2 ON atv2.`async_id` = a2.`id`
            LEFT JOIN `sys_institution` AS si ON si.`app_id`=atv2.`app_id` AND atv2.`ins_code`=si.`ins_code`
            <where>
                a2.`async_type` NOT IN ('1','2')
                <if test=" status != null and status!='' ">
                    AND ati2.status = #{status}
                </if>
                <if test=" startTime != null ">
                    AND ati2.async_start_date &gt;= #{startTime}
                </if>
                <if test=" endTime != null ">
                    AND ati2.async_start_date &lt;= #{endTime}
                </if>
                <if test=" asyncName != null and asyncName !=''">
                    AND a2.async_name LIKE CONCAT('%',TRIM(#{asyncName}),'%')
                </if>
                <if test=" taskDetail != null and taskDetail !=''">
                    AND si.ins_name LIKE CONCAT('%',TRIM(#{taskDetail}),'%')
                </if>
                <if test=" asyncType != null and asyncType !=''">
                    and ati2.async_type = #{asyncType}
                </if>
            </where>
            GROUP BY ati2.`task_item_id`
        </otherwise>
        </choose>

        ) AS t order by t.async_start_date desc
    </select>

</mapper>