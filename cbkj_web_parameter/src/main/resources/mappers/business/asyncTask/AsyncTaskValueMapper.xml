<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskValueMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskValue">
        <id column="async_id" jdbcType="INTEGER"  property="asyncId" />
        <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_id" jdbcType="VARCHAR" property="insId" />
        <result column="async_type" jdbcType="VARCHAR" property="asyncType" />
    </resultMap>


    <sql id="Base_Column_List">
    async_id,drug_id,app_id,ins_code,ins_id,async_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="AsyncTaskValue">
        delete from async_task_value where async_id = #{ asyncId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from async_task_value where async_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="AsyncTaskValue">
        insert into async_task_value (<include refid="Base_Column_List" />) values
        (#{asyncId},#{drugId},#{appId},#{insCode},#{insId},#{asyncType})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into async_task_value (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.asyncId},#{item.drugId},#{item.appId},#{item.insCode},#{item.insId},#{item.asyncType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="AsyncTaskValue">
        update async_task_value
        <set>
             <if test="drugId != null">
                drug_id = #{ drugId },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insId != null">
                ins_id = #{ insId },
             </if>
             <if test="asyncType != null">
                async_type = #{ asyncType },
             </if>
        </set>
        where async_id = #{ asyncId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from async_task_value where async_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="AsyncTaskValue" resultMap="BaseResultMap">
        SELECT async_id,drug_id,app_id,ins_code,ins_id,async_type
        from async_task_value
        <where>
            <if test=" asyncId != null and asyncId!='' ">
                and async_id = #{asyncId}
            </if>
        </where>
    </select>
    <select id="getListByAsyncId" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" /> from async_task_value where async_id = #{asyncId}
    </select>


</mapper>