<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.asynctask.AsyncTaskMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTask">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="async_name" jdbcType="VARCHAR" property="asyncName" />
        <result column="async_type" jdbcType="VARCHAR" property="asyncType" />
        <result column="async_exec_type" jdbcType="VARCHAR" property="asyncExecType" />
        <result column="async_start_time" jdbcType="VARCHAR" property="asyncStartTime" />
        <result column="async_end_time" jdbcType="VARCHAR" property="asyncEndTime" />
        <result column="async_between_time" jdbcType="VARCHAR" property="asyncBetweenTime" />
        <result column="async_between_unit" jdbcType="VARCHAR" property="asyncBetweenUnit" />
        <result column="sort" jdbcType="VARCHAR" property="sort" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="his_id" jdbcType="VARCHAR" property="hisId" />
        <result column="async_last_time" jdbcType="TIMESTAMP" property="asyncLastTime" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    </resultMap>


    <sql id="Base_Column_List">
        id,async_name,async_type,async_exec_type,async_start_time,async_end_time,async_between_time,async_between_unit,sort,status,create_user,
            create_user_name,create_date,his_id,async_last_time,update_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="AsyncTask">
        delete from async_task where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from async_task where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" useGeneratedKeys="true"  keyProperty="id" parameterType="AsyncTask">
        insert into async_task (<include refid="Base_Column_List" />) values
        (#{id},#{asyncName},#{asyncType},#{asyncExecType},#{asyncStartTime},#{asyncEndTime},#{asyncBetweenTime},
         #{asyncBetweenUnit},#{sort},#{status},
         #{createUser},#{createUserName},#{createDate},#{hisId},#{asyncLastTime},#{updateDate})
    </insert>

    <insert id="insertList" useGeneratedKeys="true"  keyProperty="id" parameterType="List">
        insert into async_task (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.asyncName},#{item.asyncType},#{item.asyncExecType},#{item.asyncStartTime},
             #{item.asyncEndTime},#{item.asyncBetweenTime},#{item.asyncBetweenUnit},#{item.sort},#{item.status},
             #{item.createUser},#{item.createUserName},#{item.createDate},#{item.hisId},#{item.asyncLastTime},#{item.updateDate}})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="AsyncTask">
        update async_task
        <set>
            <if test="asyncName != null and asyncName!=''">
                async_name = #{ asyncName },
            </if>
            <if test="asyncType != null and asyncType !=''">
                async_type = #{ asyncType },
            </if>
            <if test="asyncExecType != null and asyncExecType!=''">
                async_exec_type = #{ asyncExecType },
            </if>
            <if test="asyncStartTime != null and asyncStartTime!=''">
                async_start_time = #{ asyncStartTime },
            </if>
            <if test="asyncEndTime != null and asyncEndTime !=''">
                async_end_time = #{ asyncEndTime },
            </if>
            <if test="asyncBetweenTime != null and asyncBetweenTime!=''">
                async_between_time = #{ asyncBetweenTime },
            </if>
            <if test="asyncBetweenUnit != null and asyncBetweenUnit !=''">
                async_between_unit = #{ asyncBetweenUnit },
            </if>
            <if test="sort != null and sort !=''">
                sort = #{ sort },
            </if>
            <if test="status != null and status != ''">
                status = #{ status },
            </if>
            <if test="createUser != null and createUser!=''">
                create_user = #{ createUser },
            </if>
            <if test="createUserName != null and createUserName !=''">
                create_user_name = #{ createUserName },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>

            <if test="hisId != null and hisId != ''">
                his_id = #{ hisId },
            </if>
            <if test="asyncLastTime != null">
                async_last_time = #{ asyncLastTime },
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
        </set>
        where id = #{ id }
    </update>
    <update id="updateStatusByIds" parameterType="Map">
        update async_task set status = #{status} ,update_date = #{updateDate} where id =#{id}
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
            at.id,
            at.async_name,
            at.async_type,
            at.async_exec_type,
            at.async_start_time,
            at.async_end_time,
            at.async_between_time,
            at.async_between_unit,
            at.sort,
            at.status,
            at.create_user,
            at.create_user_name,
            at.create_date,
            at.his_id,
            at.async_last_time,
            at.update_date,
            tih.his_name as hisName
        from async_task at
        join t_interface_his as tih on tih.his_id=at.his_id
        where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="AsyncTask" resultMap="BaseResultMap">
        SELECT t.* FROM (
        SELECT ast.id,
        ast.async_name,
        ast.async_type,
        ast.async_exec_type,
        ast.async_start_time,
        ast.async_end_time,
        ast.async_between_time,
        ast.async_between_unit,
        ast.sort,
        ast.status,
        ast.create_user,
        ast.create_user_name,
        ast.create_date,
        ast.his_id,
        ast.async_last_time,
        ast.update_date,
        his.his_name as hisName
        from async_task as ast
left join t_interface_his as his on ast.his_id = his.his_id

        <where>
            ast.async_exec_type = '1'
            <if test=" asyncName != null and asyncName!='' ">
                and ast.async_name like CONCAT('%',trim(#{asyncName}),'%')
            </if>
            <if test=" asyncType != null and asyncType!='' ">
                and ast.async_type = #{asyncType}
            </if>
            <if test=" status != null and status!='' ">
                and ast.status in
                <foreach item="item" index="index" collection="status.split(',')"  separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <choose>
        <when test="asyncExecType != null and asyncExecType!='' and asyncExecType == '1'">

        </when>
        <otherwise>
            UNION
            SELECT ast2.id,
            ast2.async_name,
            ast2.async_type,
            ast2.async_exec_type,
            ast2.async_start_time,
            ast2.async_end_time,
            ast2.async_between_time,
            ast2.async_between_unit,
            ast2.sort,
            ast2.status,
            ast2.create_user,
            ast2.create_user_name,
            ast2.create_date,
            ast2.his_id,
            ast2.async_last_time,
            ast2.update_date,
            his2.his_name as hisName
            FROM async_task AS ast2
            left join t_interface_his as his2 on ast2.his_id = his2.his_id

            WHERE ast2.async_exec_type='2'

            <if test=" status2 != null and status2!='' ">
                and ast2.status in
                <foreach item="item" index="index" collection="status.split(',')"  separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" asyncName != null and asyncName!='' ">
                and ast2.async_name like CONCAT('%',trim(#{asyncName}),'%')
            </if>
            <if test=" asyncType != null and asyncType!='' ">
                and ast2.async_type = #{asyncType}
            </if>
        </otherwise>
        </choose>


        ) AS t
order by t.create_date desc
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.business.asyncTask.AsyncTaskIsC">
        select
            a.id,
            a.async_type,
            a.async_name,
            a.async_exec_type,
            a.async_start_time,
            a.async_end_time,
            a.async_between_time,
            a.async_between_unit,
            a.status,
            a.create_user,
            a.create_date,
            a.his_id,
            a.async_last_time,
            b.drug_id as drugId
            from
                async_task as a
            join async_task_value as b on b.async_id=a.id
            where
            a.async_type = #{asyncType} and
            a.async_exec_type = #{asyncExecType}
            <if test=" status != null and status!='' ">
                and a.status in
                <foreach item="item" index="index" collection="status.split(',')"  separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        <if test=" drugId != null and drugId!='' ">
            and b.drug_id = #{drugId}
        </if>
        <if test=" appId != null and appId!='' ">
            and b.app_id = #{appId}
        </if>
        <if test=" insCode != null and insCode!='' ">
            and b.ins_code = #{insCode}
        </if>
    </select>

    <select id="getPageListByObj2" parameterType="AsyncTask" resultMap="BaseResultMap">

    </select>

</mapper>