<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TChargeItemDetailDeptMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetailDept">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="charge_item_detail_id" jdbcType="VARCHAR" property="chargeItemDetailId" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />

        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
    </resultMap>


    <sql id="Base_Column_List">
    id,charge_item_detail_id,dept_id,dept_name,app_id,app_name,ins_name,ins_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TChargeItemDetailDept">
        delete from t_charge_item_detail_dept where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_charge_item_detail_dept where charge_item_detail_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TChargeItemDetailDept">
        insert into t_charge_item_detail_dept (<include refid="Base_Column_List" />) values
        (#{id},#{chargeItemDetailId},#{deptId},#{deptName},#{appId},#{appName},#{insName},#{insCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_charge_item_detail_dept (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.chargeItemDetailId},#{item.deptId},#{item.deptName},#{item.appId},#{item.appName},#{item.insName},#{item.insCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TChargeItemDetailDept">
        update t_charge_item_detail_dept
        <set>
             <if test="chargeItemDetailId != null">
                charge_item_detail_id = #{ chargeItemDetailId },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
              <if test="appName != null">
                  app_name = #{ appName },
              </if>
             <if test="insName != null">
                 ins_name = #{ insName },
             </if>
              <if test="insCode != null">
                  ins_code = #{ insCode },
              </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_charge_item_detail_dept where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TChargeItemDetailDept" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_charge_item_detail_dept
        <where>
            <if test="chargeItemDetailId != null and chargeItemDetailId != ''">
                and charge_item_detail_id = #{chargeItemDetailId}
            </if>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId}
            </if>
            <if test="deptName != null and deptName != ''">
                and dept_name like CONCAT('%',trim(#{deptName}),'%')
            </if>
        </where>
        order by id
    </select>

</mapper>