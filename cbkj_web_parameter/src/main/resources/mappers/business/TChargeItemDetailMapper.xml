<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TChargeItemDetailMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetail">
        <id column="charge_item_detail_id" jdbcType="VARCHAR"  property="chargeItemDetailId" />
        <result column="item_detail_code" jdbcType="VARCHAR" property="itemDetailCode" />
        <result column="item_detail_name" jdbcType="VARCHAR" property="itemDetailName" />
        <result column="charge_item_id" jdbcType="VARCHAR" property="chargeItemId" />
        <result column="item_detail_status" jdbcType="INTEGER" property="itemDetailStatus" />
        <result column="item_detail_single_price" jdbcType="DECIMAL" property="itemDetailSinglePrice" />
        <result column="item_detail_single_unit" jdbcType="VARCHAR" property="itemDetailSingleUnit" />
        <result column="item_detail_py" jdbcType="VARCHAR" property="itemDetailPy" />
        <result column="item_detail_wb" jdbcType="VARCHAR" property="itemDetailWb" />
        <result column="item_detail_acupoint_must" jdbcType="INTEGER" property="itemDetailAcupointMust" />
        <result column="item_detail_type_name" jdbcType="VARCHAR" property="itemDetailTypeName" />
        <result column="item_detail_type" jdbcType="VARCHAR" property="itemDetailType" />
        <result column="item_detail_binding" jdbcType="VARCHAR" property="itemDetailBinding" />
        <result column="item_detail_binding_name" jdbcType="VARCHAR" property="itemDetailBindingName" />
        <result column="item_detail_pre_check" jdbcType="INTEGER" property="itemDetailPreCheck" />
        <result column="item_detail_execute_status" jdbcType="INTEGER" property="itemDetailExecuteStatus" />
        <result column="item_detail_inner_text" jdbcType="VARCHAR" property="itemDetailInnerText" />
        <result column="item_detail_notes" jdbcType="VARCHAR" property="itemDetailNotes" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="medical_insurance_code" jdbcType="VARCHAR" property="medicalInsuranceCode" />
        <result column="medical_insurance_country_region_code" jdbcType="VARCHAR" property="medicalInsuranceCountryRegionCode" />
        <collection property="itemDetailDepts" ofType="com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemDetailDept">
            <result column="dept_id" property="deptId"/>
            <result column="dept_name" property="deptName"/>
        </collection>

    </resultMap>


    <sql id="Base_Column_List">
    charge_item_detail_id,item_detail_code,item_detail_name,charge_item_id,item_detail_status,item_detail_single_price,item_detail_single_unit,item_detail_py,item_detail_wb,
    item_detail_acupoint_must,item_detail_type_name,item_detail_type,item_detail_binding,item_detail_binding_name,item_detail_pre_check,
    item_detail_execute_status,item_detail_inner_text,item_detail_notes,create_user_id,create_user_name,create_time,update_user_id,update_user_name,
    update_time,medical_insurance_code,medical_insurance_country_region_code
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TChargeItemDetail">
        delete from t_charge_item_detail where charge_item_detail_id = #{ chargeItemDetailId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_charge_item_detail where charge_item_detail_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TChargeItemDetail">
        insert into t_charge_item_detail (<include refid="Base_Column_List" />) values
        (#{chargeItemDetailId},#{itemDetailCode},#{itemDetailName},#{chargeItemId},#{itemDetailStatus},#{itemDetailSinglePrice},#{itemDetailSingleUnit},#{itemDetailPy},#{itemDetailWb},#{itemDetailAcupointMust},#{itemDetailTypeName},#{itemDetailType},#{itemDetailBinding},#{itemDetailBindingName},#{itemDetailPreCheck},#{itemDetailExecuteStatus},#{itemDetailInnerText},#{itemDetailNotes},#{createUserId},#{createUserName},#{createTime},#{updateUserId},#{updateUserName},#{updateTime},#{medicalInsuranceCode},#{medicalInsuranceCountryRegionCode})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_charge_item_detail (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.chargeItemDetailId},#{item.itemDetailCode},#{item.itemDetailName},#{item.chargeItemId},#{item.itemDetailStatus},#{item.itemDetailSinglePrice},#{item.itemDetailSingleUnit},#{item.itemDetailPy},#{item.itemDetailWb},#{item.itemDetailAcupointMust},#{item.itemDetailTypeName},#{item.itemDetailType},#{item.itemDetailBinding},#{item.itemDetailBindingName},#{item.itemDetailPreCheck},#{item.itemDetailExecuteStatus},#{item.itemDetailInnerText},#{item.itemDetailNotes},#{item.createUserId},#{item.createUserName},#{item.createTime},#{item.updateUserId},#{item.updateUserName},#{item.updateTime},#{item.medicalInsuranceCode},#{item.medicalInsuranceCountryRegionCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TChargeItemDetail">
        update t_charge_item_detail
        <set>
             <if test="itemDetailCode != null">
                item_detail_code = #{ itemDetailCode },
             </if>
             <if test="itemDetailName != null">
                item_detail_name = #{ itemDetailName },
             </if>
             <if test="chargeItemId != null">
                charge_item_id = #{ chargeItemId },
             </if>
             <if test="itemDetailStatus != null">
                item_detail_status = #{ itemDetailStatus },
             </if>
             <if test="itemDetailSinglePrice != null">
                item_detail_single_price = #{ itemDetailSinglePrice },
             </if>
             <if test="itemDetailSingleUnit != null">
                item_detail_single_unit = #{ itemDetailSingleUnit },
             </if>
             <if test="itemDetailPy != null">
                item_detail_py = #{ itemDetailPy },
             </if>
             <if test="itemDetailWb != null">
                item_detail_wb = #{ itemDetailWb },
             </if>
             <if test="itemDetailAcupointMust != null">
                item_detail_acupoint_must = #{ itemDetailAcupointMust },
             </if>
             <if test="itemDetailTypeName != null">
                item_detail_type_name = #{ itemDetailTypeName },
             </if>
             <if test="itemDetailType != null">
                item_detail_type = #{ itemDetailType },
             </if>
             <if test="itemDetailBinding != null">
                item_detail_binding = #{ itemDetailBinding },
             </if>
             <if test="itemDetailBindingName != null">
                item_detail_binding_name = #{ itemDetailBindingName },
             </if>
             <if test="itemDetailPreCheck != null">
                item_detail_pre_check = #{ itemDetailPreCheck },
             </if>
             <if test="itemDetailExecuteStatus != null">
                item_detail_execute_status = #{ itemDetailExecuteStatus },
             </if>
             <if test="itemDetailInnerText != null">
                item_detail_inner_text = #{ itemDetailInnerText },
             </if>
             <if test="itemDetailNotes != null">
                item_detail_notes = #{ itemDetailNotes },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
             <if test="updateUserId != null">
                update_user_id = #{ updateUserId },
             </if>
             <if test="updateUserName != null">
                update_user_name = #{ updateUserName },
             </if>
             <if test="updateTime != null">
                update_time = #{ updateTime },
             </if>
             <if test="medicalInsuranceCode != null">
                medical_insurance_code = #{ medicalInsuranceCode },
             </if>
             <if test="medicalInsuranceCountryRegionCode != null">
                medical_insurance_country_region_code = #{ medicalInsuranceCountryRegionCode },
             </if>
        </set>
        where charge_item_detail_id = #{ chargeItemDetailId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        SELECT
            a.charge_item_detail_id,
            a.item_detail_code,
            a.item_detail_name,
            a.charge_item_id,
            a.item_detail_status,
            a.item_detail_single_price,
            a.item_detail_single_unit,
            a.item_detail_py,
            a.item_detail_wb,
            a.item_detail_acupoint_must,
            a.item_detail_type_name,
            a.item_detail_type,
            a.item_detail_binding,
            a.item_detail_binding_name,
            a.item_detail_pre_check,
            a.item_detail_execute_status,
            a.item_detail_inner_text,
            a.item_detail_notes,
            a.create_user_id,
            a.create_user_name,
            a.create_time,
            a.update_user_id,
            a.update_user_name,
            a.update_time,
            a.medical_insurance_code,
            a.medical_insurance_country_region_code,
            b.dept_id,
            b.dept_name
        from t_charge_item_detail as a left join t_charge_item_detail_dept as b on a.charge_item_detail_id = b.charge_item_detail_id
         where a.charge_item_detail_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TChargeItemDetail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_charge_item_detail
        <where>
            <if test="chargeItemId != null and chargeItemId != ''">
                and charge_item_id = #{chargeItemId}
            </if>
            <if test="itemDetailName != null and itemDetailName != ''">
                and item_detail_name like CONCAT('%',trim(#{itemDetailName}),'%')
            </if>
            <if test="itemDetailCode != null and itemDetailCode != ''">
                and item_detail_code like CONCAT('%',trim(#{itemDetailCode}),'%')
            </if>
        </where>
        order by create_time desc
    </select>



    <!-- 检查收费编码是否存在 -->
    <select id="checkDetailCodeExists" parameterType="String" resultType="int">
        SELECT count(1)
        from t_charge_item_detail
        where item_detail_code = #{itemDetailCode}
    </select>

    <!-- 检查收费编码是否存在（排除自己） -->
    <select id="checkDetailCodeExistsExcludeSelf" resultType="int">
        SELECT count(1)
        from t_charge_item_detail
        where item_detail_code = #{itemDetailCode}
        and charge_item_detail_id != #{chargeItemDetailId}
    </select>

    <!-- 根据查询条件获取收费项目详情列表 -->
    <select id="getDetailsByGetChargeItemList" parameterType="com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemList" resultMap="BaseResultMap">
        SELECT
        tcd.charge_item_detail_id,
        tcd.item_detail_code,
        tcd.item_detail_name,
        tcd.charge_item_id,
        tcd.item_detail_status,
        tcd.item_detail_single_price,
        tcd.item_detail_single_unit,
        tcd.item_detail_py,
        tcd.item_detail_wb,
        tcd.item_detail_acupoint_must,
        tcd.item_detail_type_name,
        tcd.item_detail_type,
        tcd.item_detail_binding,
        tcd.item_detail_binding_name,
        tcd.item_detail_pre_check,
        tcd.item_detail_execute_status,
        tcd.item_detail_inner_text,
        tcd.item_detail_notes,
        tcd.create_user_id,
        tcd.create_user_name,
        tcd.create_time,
        tcd.update_user_id,
        tcd.update_user_name,
        tcd.update_time,
        tcd.medical_insurance_code,
        tcd.medical_insurance_country_region_code,
        tcdd.dept_id,
        tcdd.dept_name
        from t_charge_item_detail tcd

            LEFT JOIN t_charge_item_detail_dept tcdd ON tcd.charge_item_detail_id = tcdd.charge_item_detail_id

        <where>
            <if test="chargeItemId != null and chargeItemId != ''">
                and tcd.charge_item_id = #{chargeItemId}
            </if>
            <if test="itemDetailCodeOrName != null and itemDetailCodeOrName != ''">
                and (tcd.item_detail_code like CONCAT('%',trim(#{itemDetailCodeOrName}),'%')
                     or tcd.item_detail_name like CONCAT('%',trim(#{itemDetailCodeOrName}),'%'))
            </if>
            <if test="itemDetailType != null and itemDetailType != ''">
                and tcd.item_detail_type = #{itemDetailType}
            </if>
            <if test="itemDetailStatus != null">
                and tcd.item_detail_status = #{itemDetailStatus}
            </if>
            <if test="itemDetailExecuteDepts != null and itemDetailExecuteDepts.size() > 0">
                and tcdd.dept_id in
                <foreach collection="itemDetailExecuteDepts" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>

        order by tcd.create_time desc
    </select>
    <select id="countDetailsByGetChargeItemList" parameterType="com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemList" resultType="Integer">
        SELECT
        COUNT(DISTINCT tcd.charge_item_detail_id)
        from t_charge_item_detail tcd

            LEFT JOIN t_charge_item_detail_dept tcdd ON tcd.charge_item_detail_id = tcdd.charge_item_detail_id

        <where>
            <if test="chargeItemId != null and chargeItemId != ''">
                and tcd.charge_item_id = #{chargeItemId}
            </if>
            <if test="itemDetailCodeOrName != null and itemDetailCodeOrName != ''">
                and (tcd.item_detail_code like CONCAT('%',trim(#{itemDetailCodeOrName}),'%')
                     or tcd.item_detail_name like CONCAT('%',trim(#{itemDetailCodeOrName}),'%'))
            </if>
            <if test="itemDetailType != null and itemDetailType != ''">
                and tcd.item_detail_type = #{itemDetailType}
            </if>
            <if test="itemDetailStatus != null">
                and tcd.item_detail_status = #{itemDetailStatus}
            </if>
            <if test="itemDetailExecuteDepts != null and itemDetailExecuteDepts.size() > 0">
                and tcdd.dept_id in
                <foreach collection="itemDetailExecuteDepts" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>

        </where>


    </select>
    <select id="getChargeItemDetailListByChargeItemId"
            parameterType="com.jiuzhekan.cbkj.beans.business.chargeitem.GetChargeItemDetailListByChargeItemId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from t_charge_item_detail
        <where>
            item_detail_status = 0
            <if test="chargeItemId != null and chargeItemId != ''">
                and charge_item_id = #{chargeItemId}
            </if>
            <if test="searchKey != null and searchKey != ''">
                and (item_detail_code like CONCAT('%',trim(#{searchKey}),'%')
                     or item_detail_name like CONCAT('%',trim(#{searchKey}),'%')
                or item_detail_py like CONCAT('%',trim(#{searchKey}),'%')
                or item_detail_wb like CONCAT('%',trim(#{searchKey}),'%')
                )
            </if>
        </where>
    </select>

</mapper>