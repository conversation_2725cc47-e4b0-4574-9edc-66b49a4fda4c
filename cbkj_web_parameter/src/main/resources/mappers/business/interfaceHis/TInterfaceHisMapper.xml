<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.interfacehis.TInterfaceHisMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis">
        <id column="his_id" jdbcType="VARCHAR" property="hisId"/>
        <result column="his_name" jdbcType="VARCHAR" property="hisName"/>
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
        <result column="his_url_type" jdbcType="VARCHAR" property="hisUrlType"/>
        <result column="jdbc_driver" jdbcType="VARCHAR" property="jdbcDriver"/>
        <result column="his_url" jdbcType="VARCHAR" property="hisUrl"/>
        <result column="jdbc_ip" jdbcType="VARCHAR" property="jdbcIp"/>
        <result column="jdbc_port" jdbcType="VARCHAR" property="jdbcPort"/>
        <result column="jdbc_database_name" jdbcType="VARCHAR" property="jdbcDatabaseName"/>
        <result column="jdbc_username" jdbcType="VARCHAR" property="jdbcUserName"/>
        <result column="jdbc_password" jdbcType="VARCHAR" property="jdbcPassWord"/>
        <result column="jdbc_table" jdbcType="VARCHAR" property="jdbcTable"/>
        <result column="jdbc_sqlwhere" jdbcType="VARCHAR" property="jdbcSqlwhere"/>
        <result column="request_method_type" jdbcType="VARCHAR" property="requestMethodType"/>
        <result column="request_params" jdbcType="VARCHAR" property="requestParams"/>
        <result column="webservice_params" jdbcType="VARCHAR" property="webserviceParams"/>
        <result column="webservice_namespace" jdbcType="VARCHAR" property="webserviceNamespace"/>
        <result column="webservice_method" jdbcType="VARCHAR" property="webserviceMethod"/>
        <result column="webservice_resultnode" jdbcType="VARCHAR" property="webserviceResultnode"/>
        <result column="webservice_contenttype" jdbcType="VARCHAR" property="webserviceContenttype"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="is_general" jdbcType="VARCHAR" property="isGeneral"/>
        <result column="interface_desc" jdbcType="VARCHAR" property="interfaceDesc"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
    </resultMap>


    <sql id="Base_Column_List">
        his_id
        ,his_name,his_url,his_url_type,create_date,create_user,create_user_name,status,request_method_type,dic_code
    </sql>

    <sql id="Base_Column_List_More">
        his_id
        ,his_name,dic_code,his_url_type,jdbc_driver,his_url,jdbc_ip,jdbc_port,jdbc_database_name,jdbc_username,jdbc_password,jdbc_table,jdbc_sqlwhere,request_method_type,request_params,webservice_params,webservice_namespace,webservice_method,webservice_resultnode,webservice_contenttype,status,is_general,interface_desc,create_date,create_user,create_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis">
        update t_interface_his
        set status='1'
        where his_id = #{ hisId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_interface_his where his_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis">
        insert into t_interface_his (his_id,his_name,dic_code,his_url_type,jdbc_driver,his_url,jdbc_ip,jdbc_port,jdbc_database_name,jdbc_username,jdbc_password,jdbc_table,jdbc_sqlwhere,request_method_type,request_params,webservice_params,webservice_namespace,webservice_method,webservice_resultnode,webservice_contenttype,status,is_general,interface_desc,create_date,create_user,create_user_name) values
        (#{hisId},#{hisName},#{dicCode},#{hisUrlType},#{jdbcDriver},#{hisUrl},#{jdbcIp},#{jdbcPort},#{jdbcDatabaseName},#{jdbcUserName},#{jdbcPassWord},#{jdbcTable},#{jdbcSqlwhere},#{requestMethodType},#{requestParams},#{webserviceParams},#{webserviceNamespace},#{webserviceMethod},#{webserviceResultnode},#{webserviceContenttype},#{status},#{isGeneral},#{interfaceDesc},#{createDate},#{createUser},#{createUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_interface_his (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.hisId},#{item.hisName},#{item.hisUrl},#{item.hisUrlType},#{item.createDate},#{item.createUser},
            #{item.createUserName},#{item.status},#{item.requestMethodType},#{iterm.dicCode})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis">
        update t_interface_his
        <set>
            <if test="hisName != null">
                his_name = #{ hisName },
            </if>
            <if test="dicCode != null">
                dic_code = #{ dicCode },
            </if>
            <if test="hisUrlType != null">
                his_url_type = #{ hisUrlType },
            </if>
            <if test="jdbcDriver != null">
                jdbc_driver = #{ jdbcDriver },
            </if>
            <if test="hisUrl != null">
                his_url = #{ hisUrl },
            </if>
            <if test="jdbcIp != null">
                jdbc_ip = #{ jdbcIp },
            </if>
            <if test="jdbcPort != null">
                jdbc_port = #{ jdbcPort },
            </if>
            <if test="jdbcDatabaseName != null">
                jdbc_database_name = #{ jdbcDatabaseName },
            </if>
            <if test="jdbcUserName != null">
                jdbc_username = #{ jdbcUserName },
            </if>
            <if test="jdbcPassWord != null and jdbcPassWord != ''">
                jdbc_password = #{ jdbcPassWord },
            </if>
            <if test="jdbcTable != null">
                jdbc_table = #{ jdbcTable },
            </if>
            <if test="jdbcSqlwhere != null">
                jdbc_sqlwhere = #{ jdbcSqlwhere },
            </if>
            <if test="requestMethodType != null">
                request_method_type = #{ requestMethodType },
            </if>
            <if test="requestParams != null">
                request_params = #{ requestParams },
            </if>
            <if test="webserviceParams != null">
                webservice_params = #{ webserviceParams },
            </if>
            <if test="webserviceNamespace != null">
                webservice_namespace = #{ webserviceNamespace },
            </if>
            <if test="webserviceMethod != null">
                webservice_method = #{ webserviceMethod },
            </if>
            <if test="webserviceResultnode != null">
                webservice_resultnode = #{ webserviceResultnode },
            </if>
            <if test="webserviceContenttype != null">
                webservice_contenttype = #{ webserviceContenttype },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="isGeneral != null">
                is_general = #{ isGeneral },
            </if>
            <if test="interfaceDesc != null">
                interface_desc = #{ interfaceDesc },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
        </set>
        where his_id = #{ hisId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        SELECT tih.his_id,
               tih.his_name,
               tih.dic_code,
               tdb.dic_name,
               tih.his_url_type,
               tih.jdbc_driver,
               tih.his_url,
               tih.jdbc_ip,
               tih.jdbc_port,
               tih.jdbc_database_name,
               tih.jdbc_username,
               tih.jdbc_password,
               tih.jdbc_table,
               tih.jdbc_sqlwhere,
               tih.request_method_type,
               tih.request_params,
               tih.webservice_params,
               tih.webservice_namespace,
               tih.webservice_method,
               tih.webservice_resultnode,
               tih.webservice_contenttype,
               tih.status,
               tih.is_general,
               tih.interface_desc,
               tih.create_date,
               tih.create_user,
               tih.create_user_name
        FROM t_interface_his AS tih
        LEFT JOIN t_dic_base AS tdb ON tih.dic_code=tdb.dic_code AND tdb.parent_id='2c133f183ef011edad8d00163f006620'
        where tih.status !='1' and tih.his_id = #{hisId}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.cbkj.beans.business.interfaceHis.TInterfaceHis"
            resultMap="BaseResultMap">
        SELECT
        his_id,his_name,his_url,his_url_type,create_date,create_user,create_user_name,status,request_method_type,dic_code
        from t_interface_his
        <where>
            <if test=" hisName != null and hisName!='' ">
                and his_name like CONCAT('%',trim(#{hisName}),'%')
            </if>
            <if test=" hisUrlType != null and hisUrlType!='' ">
                and his_url_type = #{hisUrlType}
            </if>
            <if test=" dicCode != null and dicCode!='' ">
                and dic_code in
                <foreach item="item" index="index" collection="dicCode.split(',')" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            and status='0'
        </where>
        order by create_date desc
    </select>
    <select id="getPageList" parameterType="TInterfaceHis" resultMap="BaseResultMap">
        select tih.his_id,
        tih.his_name,
        tih.dic_code,
        tdb.dic_name,
        tih.his_url_type,
        tih.status,
        tih.interface_desc
        from t_interface_his as tih
        left join t_dic_base as tdb on tih.dic_code=tdb.dic_code and tdb.parent_id='2c133f183ef011edad8d00163f006620'
        where tih.status !='1'
        <if test=" hisName != null and hisName!='' ">
            and tih.his_name like CONCAT('%',trim(#{hisName}),'%')
        </if>
        <if test=" dicCode != null and dicCode!='' ">
            and tih.dic_code = #{dicCode}
        </if>
        <if test=" hisUrlType != null and hisUrlType!='' ">
            and tih.his_url_type = #{hisUrlType}
        </if>
        <if test=" status != null and status!='' ">
            and tih.status = #{status}
        </if>
        order by tih.create_date asc
    </select>
    <select id="getAll"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List_More"/>
        from t_interface_his where status='0'
    </select>

</mapper>