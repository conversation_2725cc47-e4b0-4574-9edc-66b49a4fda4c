<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageCostMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageCost">
        <id column="dosage_cost_id" jdbcType="VARCHAR"  property="dosageCostId" />
        <result column="dic_type" jdbcType="INTEGER" property="dicType" />
        <result column="dic_id" jdbcType="VARCHAR" property="dicId" />
        <result column="cost_catalogue_id" jdbcType="VARCHAR" property="costCatalogueId" />
        <result column="cost_item_id" jdbcType="VARCHAR" property="costItemId" />
        <result column="pricing_method" jdbcType="INTEGER" property="pricingMethod" />
        <result column="num_computer_method" jdbcType="INTEGER" property="numComputerMethod" />
        <result column="doctor_edit" jdbcType="INTEGER" property="doctorEdit" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
    </resultMap>


    <sql id="Base_Column_List">
    dosage_cost_id,dic_type,dic_id,cost_catalogue_id,cost_item_id,pricing_method,doctor_edit,sort,num_computer_method
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayDosageCost">
        delete from t_display_dosage_cost where dosage_cost_id = #{ dosageCostId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_dosage_cost where dosage_cost_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDisplayDosageCost">
        insert into t_display_dosage_cost (<include refid="Base_Column_List" />) values
        (#{dosageCostId},#{dicType},#{dicId},#{costCatalogueId},#{costItemId},#{pricingMethod},#{doctorEdit},#{sort},#{numComputerMethod})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_dosage_cost (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.dosageCostId},#{item.dicType},#{item.dicId},#{item.costCatalogueId},#{item.costItemId},#{item.pricingMethod},
             #{item.doctorEdit},#{item.sort},#{item.numComputerMethod})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayDosageCost">
        update t_display_dosage_cost
        <set>
             <if test="dicType != null">
                dic_type = #{ dicType },
             </if>
             <if test="dicId != null">
                dic_id = #{ dicId },
             </if>
             <if test="costCatalogueId != null">
                cost_catalogue_id = #{ costCatalogueId },
             </if>
             <if test="costItemId != null">
                cost_item_id = #{ costItemId },
             </if>
             <if test="pricingMethod != null">
                pricing_method = #{ pricingMethod },
             </if>
             <if test="doctorEdit != null">
                doctor_edit = #{ doctorEdit },
             </if>
             <if test="sort != null">
                sort = #{ sort },
             </if>
             <if test="numComputerMethod != null">
                num_computer_method = #{ numComputerMethod },
             </if>
        </set>
        where dosage_cost_id = #{ dosageCostId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_dosage_cost where dosage_cost_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayDosageCost" resultMap="BaseResultMap">
        SELECT dosage_cost_id,dic_type,dic_id,cost_catalogue_id,cost_item_id,pricing_method,doctor_edit,sort,num_computer_method
        from t_display_dosage_cost
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getDisplayDosageFormListByBean"
            resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.business.displaydosage.GetDisplayDosageFormListByDicId">
        SELECT dosage_cost_id,
               dic_type,
               dic_id,
               cost_catalogue_id,
               cost_item_id,
               pricing_method,
               doctor_edit,
               sort,num_computer_method
        FROM t_display_dosage_cost
        WHERE dic_type = #{dicType}
          AND dic_id = #{dicId}
        order by sort asc

    </select>

    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageForm">
        <id column="set_id" jdbcType="VARCHAR"  property="setId" />
        <result column="display_id" jdbcType="VARCHAR" property="displayId" />
        <result column="outpatient_or_hospitalization" jdbcType="VARCHAR" property="outpatientOrHospitalization" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="expense_entry" jdbcType="INTEGER" property="expenseEntry" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    </resultMap>
    <select id="getPharmacyDosageInfo"
            resultMap="BaseResultMap2" parameterType="String">
        SELECT set_id,display_id,outpatient_or_hospitalization,status,expense_entry,create_date,create_user
        from t_display_dosage_form
        <where>
            <if test=" displayId != null and displayId!='' ">
               and display_id = #{displayId}
            </if>
        </where>
    </select>

</mapper>