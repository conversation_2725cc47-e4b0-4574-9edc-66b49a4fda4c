<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.SysSettingInfoMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.SysSettingInfo">
        <id column="set_id" jdbcType="INTEGER"  property="setId" />
        <result column="platform_sys_name" jdbcType="VARCHAR" property="platformSysName" />
        <result column="pre_sys_name" jdbcType="VARCHAR" property="preSysName" />
        <result column="sys_logo" jdbcType="VARCHAR" property="sysLogo" />
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="insert_user_id" jdbcType="VARCHAR" property="insertUserId" />
        <result column="insert_user_name" jdbcType="VARCHAR" property="insertUserName" />
    </resultMap>


    <sql id="Base_Column_List">
        set_id,platform_sys_name,pre_sys_name,sys_logo,insert_time,insert_user_id,insert_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysSettingInfo">
        delete from sys_setting_info where set_id = #{ setId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_setting_info where set_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="SysSettingInfo">
        insert into sys_setting_info (<include refid="Base_Column_List" />) values
        (#{setId},#{platformSysName},#{preSysName},#{sysLogo},#{insertTime},#{insertUserId},#{insertUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_setting_info (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.setId},#{item.platformSysName},#{item.preSysName},#{item.sysLogo},#{item.insertTime},#{item.insertUserId},#{item.insertUserName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysSettingInfo">
        update sys_setting_info
        <set>

            platform_sys_name = #{ platformSysName },


            pre_sys_name = #{ preSysName },


            sys_logo = #{ sysLogo },

            <if test="insertTime != null">
                insert_time = #{ insertTime },
            </if>
            <if test="insertUserId != null">
                insert_user_id = #{ insertUserId },
            </if>
            <if test="insertUserName != null">
                insert_user_name = #{ insertUserName },
            </if>
        </set>
        where set_id = #{ setId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_setting_info where set_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="SysSettingInfo" resultMap="BaseResultMap">
        SELECT set_id,platform_sys_name,pre_sys_name,sys_logo,insert_time,insert_user_id,insert_user_name
        from sys_setting_info
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>