<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageDescribeMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageDescribe">
        <id column="dosage_describe_id" jdbcType="VARCHAR"  property="dosageDescribeId" />
        <result column="dic_id" jdbcType="VARCHAR" property="dicId" />
        <result column="dic_type" jdbcType="INTEGER" property="dicType" />
        <result column="daily_dosage_show" jdbcType="INTEGER" property="dailyDosageShow" />
        <result column="daily_dosage_required" jdbcType="INTEGER" property="dailyDosageRequired" />
        <result column="daily_preparations_show" jdbcType="INTEGER" property="dailyPreparationsShow" />
        <result column="daily_preparations_required" jdbcType="INTEGER" property="dailyPreparationsRequired" />
        <result column="daily_processing_show" jdbcType="INTEGER" property="dailyProcessingShow" />
        <result column="daily_processing_required" jdbcType="INTEGER" property="dailyProcessingRequired" />
        <result column="each_time_show" jdbcType="INTEGER" property="eachTimeShow" />
        <result column="each_time_required" jdbcType="INTEGER" property="eachTimeRequired" />
    </resultMap>


    <sql id="Base_Column_List">
    dosage_describe_id,dic_id,dic_type,daily_dosage_show,daily_dosage_required,daily_preparations_show,daily_preparations_required,daily_processing_show,daily_processing_required,each_time_show,each_time_required
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayDosageDescribe">
        delete from t_display_dosage_describe where dosage_describe_id = #{ dosageDescribeId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_dosage_describe where dosage_describe_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDisplayDosageDescribe">
        insert into t_display_dosage_describe (<include refid="Base_Column_List" />) values
        (#{dosageDescribeId},#{dicId},#{dicType},#{dailyDosageShow},#{dailyDosageRequired},#{dailyPreparationsShow},#{dailyPreparationsRequired},#{dailyProcessingShow},#{dailyProcessingRequired},#{eachTimeShow},#{eachTimeRequired})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_dosage_describe (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.dosageDescribeId},#{item.dicId},#{item.dicType},#{item.dailyDosageShow},#{item.dailyDosageRequired},#{item.dailyPreparationsShow},#{item.dailyPreparationsRequired},#{item.dailyProcessingShow},#{item.dailyProcessingRequired},#{item.eachTimeShow},#{item.eachTimeRequired})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayDosageDescribe">
        update t_display_dosage_describe
        <set>
             <if test="dicId != null">
                dic_id = #{ dicId },
             </if>
             <if test="dicType != null">
                dic_type = #{ dicType },
             </if>
             <if test="dailyDosageShow != null">
                daily_dosage_show = #{ dailyDosageShow },
             </if>
             <if test="dailyDosageRequired != null">
                daily_dosage_required = #{ dailyDosageRequired },
             </if>
             <if test="dailyPreparationsShow != null">
                daily_preparations_show = #{ dailyPreparationsShow },
             </if>
             <if test="dailyPreparationsRequired != null">
                daily_preparations_required = #{ dailyPreparationsRequired },
             </if>
             <if test="dailyProcessingShow != null">
                daily_processing_show = #{ dailyProcessingShow },
             </if>
             <if test="dailyProcessingRequired != null">
                daily_processing_required = #{ dailyProcessingRequired },
             </if>
             <if test="eachTimeShow != null">
                each_time_show = #{ eachTimeShow },
             </if>
             <if test="eachTimeRequired != null">
                each_time_required = #{ eachTimeRequired },
             </if>
        </set>
        where dosage_describe_id = #{ dosageDescribeId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_dosage_describe where dosage_describe_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayDosageDescribe" resultMap="BaseResultMap">
        SELECT dosage_describe_id,dic_id,dic_type,daily_dosage_show,daily_dosage_required,daily_preparations_show,daily_preparations_required,daily_processing_show,daily_processing_required,each_time_show,each_time_required
        from t_display_dosage_describe
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getDosageDescribeInfo"
            resultMap="BaseResultMap" parameterType="com.jiuzhekan.cbkj.beans.business.displaydosage.GetDisplayDosageFormListByDicId">
        select
        <include refid="Base_Column_List"/>
        from t_display_dosage_describe
        <where>
            dic_id = #{ dicId }
            and dic_type = #{ dicType }
        </where>
    </select>

</mapper>