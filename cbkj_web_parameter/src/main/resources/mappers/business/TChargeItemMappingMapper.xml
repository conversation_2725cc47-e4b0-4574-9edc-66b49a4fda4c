<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TChargeItemMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItemMapping">
        <id column="charge_item_id" jdbcType="VARCHAR"  property="chargeItemId" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    </resultMap>


    <sql id="Base_Column_List">
    charge_item_id,app_id,ins_code,dept_id,create_date,create_user
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TChargeItemMapping">
        delete from t_charge_item_mapping where charge_item_id = #{ chargeItemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_charge_item_mapping where charge_item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TChargeItemMapping">
        insert into t_charge_item_mapping (<include refid="Base_Column_List" />) values
        (#{chargeItemId},#{appId},#{insCode},#{deptId},#{createDate},#{createUser})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_charge_item_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.chargeItemId},#{item.appId},#{item.insCode},#{item.deptId},#{item.createDate},#{item.createUser})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TChargeItemMapping">
        update t_charge_item_mapping
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
        </set>
        where charge_item_id = #{ chargeItemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_charge_item_mapping where charge_item_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TChargeItemMapping" resultMap="BaseResultMap">
        SELECT charge_item_id,app_id,ins_code,dept_id,create_date,create_user
        from t_charge_item_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>