<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.stock.TStockMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.stock.TStock">
        <id column="sto_id" jdbcType="VARCHAR"  property="stoId" />
        <result column="pha_id" jdbcType="VARCHAR" property="phaId" />
        <result column="mat_id" jdbcType="VARCHAR" property="matId" />
        <result column="mat_name" jdbcType="VARCHAR" property="matName" />
        <result column="mat_spe_id" jdbcType="VARCHAR" property="matSpeId" />
        <result column="mat_spe" jdbcType="VARCHAR" property="matSpe" />
        <result column="mat_price_id" jdbcType="VARCHAR" property="matPriceId" />
        <result column="stock_num" jdbcType="DECIMAL" property="stockNum" />
        <result column="stock_upper" jdbcType="DECIMAL" property="stockUpper" />
        <result column="stock_lower" jdbcType="DECIMAL" property="stockLower" />
        <result column="placement" jdbcType="VARCHAR" property="placement" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="placement_update_date" jdbcType="TIMESTAMP" property="placementUpdateDate" />
        <result column="placement_update_username" jdbcType="VARCHAR" property="placementUpdateUsername" />
        <result column="warning_update_date" jdbcType="TIMESTAMP" property="warningUpdateDate" />
        <result column="warning_update_username" jdbcType="VARCHAR" property="warningUpdateUsername" />
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    </resultMap>


    <sql id="Base_Column_List">
    sto_id,pha_id,mat_id,mat_name,mat_spe_id,mat_spe,mat_price_id,stock_num,stock_upper,stock_lower,placement,create_date,create_user,create_user_name,status,placement_update_date,placement_update_username,warning_update_date,warning_update_username,update_date
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TStock">
        delete from t_stock where sto_id = #{ stoId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_stock where sto_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TStock">
        insert into t_stock (<include refid="Base_Column_List" />) values
        (#{stoId},#{phaId},#{matId},#{matName},#{matSpeId},#{matSpe},#{matPriceId},#{stockNum},#{stockUpper},#{stockLower},#{placement},#{createDate},#{createUser},#{createUserName},#{status},#{placementUpdateDate},#{placementUpdateUsername},#{warningUpdateDate},#{warningUpdateUsername},#{updateDate})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_stock (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.stoId},#{item.phaId},#{item.matId},#{item.matName},#{item.matSpeId},#{item.matSpe},#{item.matPriceId},#{item.stockNum},#{item.stockUpper},#{item.stockLower},#{item.placement},#{item.createDate},#{item.createUser},#{item.createUserName},#{item.status},#{item.placementUpdateDate},#{item.placementUpdateUsername},#{item.warningUpdateDate},#{item.warningUpdateUsername},#{item.updateDate})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TStock">
        update t_stock
        <set>
             <if test="phaId != null">
                pha_id = #{ phaId },
             </if>
             <if test="matId != null">
                mat_id = #{ matId },
             </if>
             <if test="matName != null">
                mat_name = #{ matName },
             </if>
             <if test="matSpeId != null">
                mat_spe_id = #{ matSpeId },
             </if>
             <if test="matSpe != null">
                mat_spe = #{ matSpe },
             </if>
             <if test="matPriceId != null">
                mat_price_id = #{ matPriceId },
             </if>
             <if test="stockNum != null">
                stock_num = #{ stockNum },
             </if>
             <if test="stockUpper != null">
                stock_upper = #{ stockUpper },
             </if>
             <if test="stockLower != null">
                stock_lower = #{ stockLower },
             </if>
             <if test="placement != null">
                placement = #{ placement },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="placementUpdateDate != null">
                placement_update_date = #{ placementUpdateDate },
             </if>
             <if test="placementUpdateUsername != null">
                placement_update_username = #{ placementUpdateUsername },
             </if>
             <if test="warningUpdateDate != null">
                warning_update_date = #{ warningUpdateDate },
             </if>
             <if test="warningUpdateUsername != null">
                warning_update_username = #{ warningUpdateUsername },
             </if>
             <if test="updateDate != null">
                update_date = #{ updateDate },
             </if>
        </set>
        where sto_id = #{ stoId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_stock where sto_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TStock" resultMap="BaseResultMap">
        SELECT sto_id,pha_id,mat_id,mat_name,mat_spe_id,mat_spe,mat_price_id,stock_num,stock_upper,stock_lower,placement,create_date,create_user,create_user_name,status,placement_update_date,placement_update_username,warning_update_date,warning_update_username,update_date
        from t_stock
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getStockList" resultType="com.jiuzhekan.cbkj.beans.business.stock.StockListDo" parameterType="com.jiuzhekan.cbkj.beans.business.stock.TStock">
        SELECT
        tp.pha_name AS phaName,
        tm.mat_standard AS matStandard,
        tm.mat_name AS matName,
        tms.mat_spe AS matSpe,
        tmp.mat_origin_name AS matOriginName,
        tms.mat_type AS matType,
        tmp.`small_retail_price`  AS smallRetailPrice,
        tms.small_spe_unit AS smallSpeUnit,
        ts.stock_num_kf AS stockNum,
        ( SELECT SUM(tws.yk_stock_num) FROM t_withholding_stock AS tws WHERE tws.`mat_price_id`=tmp.`mat_price_id` AND tws.`pha_id`=tp.`pha_id` and  tws.status in (10,50,100) ) AS ykStockNum,

        DATE_FORMAT(
        ts.update_date,
        '%Y-%m-%d %H:%i:%s'
        ) updateDate
        FROM
        t_material_price AS tmp
        JOIN t_material AS tm ON tm.mat_id = tmp.`mat_id` AND tmp.drug_id = tm.drug_id
        JOIN t_material_specification AS tms ON tms.`mat_spe_id` = tmp.`mat_spe_id`  AND tms.status='0' AND tms.`drug_id`=tmp.`drug_id` AND tms.`mat_id`=tmp.`mat_id`
        JOIN t_stock AS ts ON ts.`mat_price_id`=tmp.`mat_price_id` AND ts.status='0'
        JOIN t_pharmacy AS tp ON tp.`pha_id` = ts.`pha_id` AND tp.status = '0' AND tp.`drug_id`=tmp.`drug_id`
        WHERE
        tm.status = '0'
            <if test="phaId != null and phaId !=''">
                and tp.`pha_id`=#{phaId}
            </if>
        <if test="matNameOrCode != null and matNameOrCode !=''">
            AND ( (tm.mat_name like CONCAT('%',trim(#{matNameOrCode}),'%') )or( tm.mat_standard like CONCAT('%',trim(#{matNameOrCode}),'%')))
        </if>
        <if test="matType != null and matType !=''">
            AND tms.mat_type=#{matType}
        </if>


    </select>

</mapper>