<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TPrescriptStatusPassMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.TPrescriptStatusPass">
        <id column="pass_id" jdbcType="BIGINT"  property="passId" />
        <result column="pre_id" jdbcType="VARCHAR" property="preId" />
        <result column="pre_no" jdbcType="VARCHAR" property="preNo" />
        <result column="pre_status" jdbcType="VARCHAR" property="preStatus" />
        <result column="pass_url" jdbcType="VARCHAR" property="passUrl" />
        <result column="pass_params" jdbcType="VARCHAR" property="passParams" />
        <result column="pass_method" jdbcType="VARCHAR" property="passMethod" />
        <result column="pass_message" jdbcType="VARCHAR" property="passMessage" />
        <result column="server_type" jdbcType="INTEGER" property="serverType" />
        <result column="pass_num" jdbcType="INTEGER" property="passNum" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="del_user" jdbcType="VARCHAR" property="delUser" />
        <result column="del_type" jdbcType="VARCHAR" property="delType" />
        <result column="del_date" jdbcType="TIMESTAMP" property="delDate" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    pass_id,pre_id,pre_no,pre_status,pass_url,pass_params,pass_method,pass_message,server_type,pass_num,create_date,create_user,del_user,del_type,del_date,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPrescriptStatusPass">
        delete from t_prescript_status_pass where pass_id = #{ passId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_prescript_status_pass where pass_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TPrescriptStatusPass">
        insert into t_prescript_status_pass (<include refid="Base_Column_List" />) values
        (#{passId},#{preId},#{preNo},#{preStatus},#{passUrl},#{passParams},#{passMethod},#{passMessage},#{serverType},#{passNum},#{createDate},#{createUser},#{delUser},#{delType},#{delDate},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_prescript_status_pass (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.passId},#{item.preId},#{item.preNo},#{item.preStatus},#{item.passUrl},#{item.passParams},#{item.passMethod},#{item.passMessage},#{item.serverType},#{item.passNum},#{item.createDate},#{item.createUser},#{item.delUser},#{item.delType},#{item.delDate},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPrescriptStatusPass">
        update t_prescript_status_pass
        <set>
             <if test="preId != null">
                pre_id = #{ preId },
             </if>
             <if test="preNo != null">
                pre_no = #{ preNo },
             </if>
             <if test="preStatus != null">
                pre_status = #{ preStatus },
             </if>
             <if test="passUrl != null">
                pass_url = #{ passUrl },
             </if>
             <if test="passParams != null">
                pass_params = #{ passParams },
             </if>
             <if test="passMethod != null">
                pass_method = #{ passMethod },
             </if>
             <if test="passMessage != null">
                pass_message = #{ passMessage },
             </if>
             <if test="serverType != null">
                server_type = #{ serverType },
             </if>
             <if test="passNum != null">
                pass_num = #{ passNum },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="delUser != null">
                del_user = #{ delUser },
             </if>
             <if test="delType != null">
                del_type = #{ delType },
             </if>
             <if test="delDate != null">
                del_date = #{ delDate },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where pass_id = #{ passId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_prescript_status_pass where pass_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TPrescriptStatusPass" resultMap="BaseResultMap">
        SELECT pass_id,pre_id,pre_no,pre_status,pass_url,pass_params,pass_method,pass_message,server_type,pass_num,create_date,create_user,del_user,del_type,del_date,status
        from t_prescript_status_pass
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getAllPass" resultMap="BaseResultMap" parameterType="TPrescriptStatusPass">
        SELECT pass_id,pre_id,pre_no,pre_status,pass_url,pass_params,pass_method,pass_message,server_type,pass_num,create_date,create_user,del_user,del_type,del_date,status
        from t_prescript_status_pass
        where status = 0 and pass_num <![CDATA[ < ]]> 5
    </select>

</mapper>