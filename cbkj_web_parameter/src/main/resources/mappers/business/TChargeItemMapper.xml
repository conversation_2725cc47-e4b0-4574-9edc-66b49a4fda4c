<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.TChargeItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.chargeitem.TChargeItem">
        <id column="charge_item_id" jdbcType="VARCHAR"  property="chargeItemId" />
        <result column="charge_item_code" jdbcType="VARCHAR" property="chargeItemCode" />
        <result column="charge_item_name" jdbcType="VARCHAR" property="chargeItemName" />
        <result column="charge_item_type" jdbcType="VARCHAR" property="chargeItemType" />
        <result column="charge_item_desc" jdbcType="VARCHAR" property="chargeItemDesc" />
        <result column="charge_item_py" jdbcType="VARCHAR" property="chargeItemPy" />
        <result column="charge_item_wb" jdbcType="VARCHAR" property="chargeItemWb" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>

    <resultMap id="IdNameResultMap" type="com.jiuzhekan.cbkj.beans.business.chargeitem.ChargeItemIdNameDTO">
        <result column="charge_item_id" jdbcType="VARCHAR" property="chargeItemId" />
        <result column="charge_item_name" jdbcType="VARCHAR" property="chargeItemName" />
    </resultMap>


    <sql id="Base_Column_List">
    charge_item_id,charge_item_code,charge_item_name,charge_item_type,charge_item_desc,charge_item_py,charge_item_wb,status,create_user_name,create_user_id,create_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TChargeItem">
        delete from t_charge_item where charge_item_id = #{ chargeItemId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_charge_item where charge_item_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TChargeItem">
        insert into t_charge_item (<include refid="Base_Column_List" />) values
        (#{chargeItemId},#{chargeItemCode},#{chargeItemName},#{chargeItemType},#{chargeItemDesc},#{chargeItemPy},#{chargeItemWb},#{status},#{createUserName},#{createUserId},#{createTime})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_charge_item (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.chargeItemId},#{item.chargeItemCode},#{item.chargeItemName},#{item.chargeItemType},#{item.chargeItemDesc},#{item.chargeItemPy},#{item.chargeItemWb},#{item.status},#{item.createUserName},#{item.createUserId},#{item.createTime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TChargeItem">
        update t_charge_item
        <set>
             <if test="chargeItemCode != null">
                charge_item_code = #{ chargeItemCode },
             </if>
             <if test="chargeItemName != null">
                charge_item_name = #{ chargeItemName },
             </if>
             <if test="chargeItemType != null">
                charge_item_type = #{ chargeItemType },
             </if>
             <if test="chargeItemDesc != null">
                charge_item_desc = #{ chargeItemDesc },
             </if>
             <if test="chargeItemPy != null">
                charge_item_py = #{ chargeItemPy },
             </if>
             <if test="chargeItemWb != null">
                charge_item_wb = #{ chargeItemWb },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createTime != null">
                create_time = #{ createTime },
             </if>
        </set>
        where charge_item_id = #{ chargeItemId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_charge_item where charge_item_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TChargeItem" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_charge_item
        <where>
            status = 0
            <if test="chargeItemName != null and chargeItemName != ''">
                and charge_item_name like CONCAT('%',trim(#{chargeItemName}),'%')
            </if>
            <if test="chargeItemCode != null and chargeItemCode != ''">
                and charge_item_code like CONCAT('%',trim(#{chargeItemCode}),'%')
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 获取收费项目列表（支持按名称模糊查询） -->
    <select id="getChargeItemList" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        from t_charge_item
        <where>
            status = 0
            <if test="chargeItemName != null and chargeItemName != ''">
                and charge_item_name like CONCAT('%',trim(#{chargeItemName}),'%')
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 获取所有收费项目的ID和名称 -->
    <select id="getChargeItemIdAndName" resultMap="IdNameResultMap">
        SELECT charge_item_id, charge_item_name
        from t_charge_item
        where status = 0
        order by create_time
    </select>

    <!-- 检查收费项目代码是否存在 -->
    <select id="checkChargeItemCodeExists" parameterType="String" resultType="int">
        SELECT count(1)
        from t_charge_item
        where charge_item_code = #{chargeItemCode}
        and status = 0
    </select>

    <!-- 检查收费项目代码是否存在（排除自己） -->
    <select id="checkChargeItemCodeExistsExcludeSelf" resultType="int">
        SELECT count(1)
        from t_charge_item
        where charge_item_code = #{chargeItemCode}
        and charge_item_id != #{chargeItemId}
        and status = 0
    </select>

    <!-- 逻辑删除收费项目 -->
    <update id="deleteChargeItemLogically" parameterType="String">
        update t_charge_item
        set status = 1
        where charge_item_id = #{chargeItemId}
    </update>

</mapper>