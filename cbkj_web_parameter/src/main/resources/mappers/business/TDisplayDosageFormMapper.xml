<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.displaydosage.TDisplayDosageFormMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.displaydosage.TDisplayDosageForm">
        <id column="set_id" jdbcType="VARCHAR"  property="setId" />
        <result column="display_id" jdbcType="VARCHAR" property="displayId" />
        <result column="outpatient_or_hospitalization" jdbcType="VARCHAR" property="outpatientOrHospitalization" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="expense_entry" jdbcType="INTEGER" property="expenseEntry" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    </resultMap>


    <sql id="Base_Column_List">
    set_id,display_id,outpatient_or_hospitalization,status,expense_entry,create_date,create_user
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayDosageForm">
        delete from t_display_dosage_form where set_id = #{ setId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_dosage_form where set_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteByDisplayId">
        delete from t_display_dosage_form where display_id = #{displayId}
    </delete>

    <insert id="insert"  parameterType="TDisplayDosageForm">
        insert into t_display_dosage_form (<include refid="Base_Column_List" />) values
        (#{setId},#{displayId},#{outpatientOrHospitalization},#{status},#{expenseEntry},#{createDate},#{createUser})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_dosage_form (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.setId},#{item.displayId},#{item.outpatientOrHospitalization},#{item.status},#{item.expenseEntry},#{item.createDate},#{item.createUser})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayDosageForm">
        update t_display_dosage_form
        <set>
             <if test="displayId != null">
                display_id = #{ displayId },
             </if>
             <if test="outpatientOrHospitalization != null">
                outpatient_or_hospitalization = #{ outpatientOrHospitalization },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="expenseEntry != null">
                expense_entry = #{ expenseEntry },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
        </set>
        where set_id = #{ setId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_dosage_form where set_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayDosageForm" resultMap="BaseResultMap">
        SELECT set_id,display_id,outpatient_or_hospitalization,status,expense_entry,create_date,create_user
        from t_display_dosage_form
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>