<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.business.banner.SysBannerMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.business.banner.SysBanner">
        <id column="banner_id" jdbcType="VARCHAR"  property="bannerId" />
        <result column="banner_name" jdbcType="VARCHAR" property="bannerName" />
        <result column="banner_url" jdbcType="VARCHAR" property="bannerUrl" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>


    <sql id="Base_Column_List">
    banner_id,banner_name,banner_url,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="SysBanner">
        delete from sys_banner where banner_id = #{ bannerId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_banner where banner_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="SysBanner">
        insert into sys_banner (<include refid="Base_Column_List" />) values
        (#{bannerId},#{bannerName},#{bannerUrl},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_banner (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.bannerId},#{item.bannerName},#{item.bannerUrl},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="SysBanner">
        update sys_banner
        <set>
             <if test="bannerName != null">
                banner_name = #{ bannerName },
             </if>
             <if test="bannerUrl != null">
                banner_url = #{ bannerUrl },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
        </set>
        where banner_id = #{ bannerId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_banner where banner_id = #{bannerId}
    </select>

    <select id="getPageListByObj" parameterType="SysBanner" resultMap="BaseResultMap">
        SELECT banner_id,banner_name,banner_url,status
        from sys_banner
        <where>
            <if test=" bannerName != null and bannerName!='' ">
                and banner_name like CONCAT('%',trim(#{bannerName}),'%')
            </if>
        </where>
    </select>

    <select id="getBannerList" resultMap="BaseResultMap">
        SELECT banner_id,banner_name,banner_url
        from sys_banner where status = '1';
    </select>

</mapper>