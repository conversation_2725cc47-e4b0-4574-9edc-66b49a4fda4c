<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayExpressMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplayExpress">
        <id column="set_id" jdbcType="VARCHAR"  property="setId" />
        <result column="display_id" jdbcType="VARCHAR" property="displayId" />
        <result column="outpatient_or_hospitalization" jdbcType="VARCHAR" property="outpatientOrHospitalization" />
        <result column="SHOW_EXPRESS" jdbcType="VARCHAR" property="showExpress" />
        <result column="IS_EXPRESS" jdbcType="VARCHAR" property="isExpress" />
        <result column="is_use_express" jdbcType="VARCHAR" property="isUseExpress" />
        <result column="express_set" jdbcType="VARCHAR" property="expressSet" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="DC_TYPE_SUBCLASS" jdbcType="VARCHAR" property="dcTypeSubclass" />
    </resultMap>


    <sql id="Base_Column_List">
    set_id,display_id,outpatient_or_hospitalization,SHOW_EXPRESS,IS_EXPRESS,is_use_express,express_set,status,
    create_date,create_user,DC_TYPE_SUBCLASS
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayExpress">
        delete from t_display_express where set_id = #{ setId }
    </delete>

    <delete id="deleteByCondition" parameterType="TDisplayExpress">
        delete from t_display_express where display_id = #{displayId} and outpatient_or_hospitalization = #{ outpatientOrHospitalization }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_express where set_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDisplayExpress">
        insert into t_display_express (<include refid="Base_Column_List" />) values
        (#{setId},#{displayId},#{outpatientOrHospitalization},#{showExpress},#{isExpress},#{isUseExpress},#{expressSet},
         #{status},#{createDate},#{createUser},#{dcTypeSubclass})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_express (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.setId},#{item.displayId},#{item.outpatientOrHospitalization},#{item.showExpress},#{item.isExpress},
             #{item.isUseExpress},#{item.expressSet},#{item.status},#{item.createDate},#{item.createUser},#{item.dcTypeSubclass})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayExpress">
        update t_display_express
        <set>
             <if test="displayId != null">
                display_id = #{ displayId },
             </if>
             <if test="outpatientOrHospitalization != null">
                outpatient_or_hospitalization = #{ outpatientOrHospitalization },
             </if>
             <if test="showExpress != null">
                SHOW_EXPRESS = #{ showExpress },
             </if>
             <if test="isExpress != null">
                IS_EXPRESS = #{ isExpress },
             </if>
             <if test="isUseExpress != null">
                is_use_express = #{ isUseExpress },
             </if>
             <if test="expressSet != null">
                express_set = #{ expressSet },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
             <if test="dcTypeSubclass != null">
                 DC_TYPE_SUBCLASS = #{dcTypeSubclass},
             </if>
        </set>
        where set_id = #{ setId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_express where set_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayExpress" resultMap="BaseResultMap">
        SELECT set_id,display_id,outpatient_or_hospitalization,SHOW_EXPRESS,IS_EXPRESS,is_use_express,
               express_set,status,create_date,create_user,DC_TYPE_SUBCLASS
        from t_display_express
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getByDisplayId" resultMap="BaseResultMap" parameterType="TDisplayExpress">
        select <include refid="Base_Column_List" />
        from t_display_express where display_id = #{displayId} and outpatient_or_hospitalization = #{outpatientOrHospitalization}
    </select>

</mapper>