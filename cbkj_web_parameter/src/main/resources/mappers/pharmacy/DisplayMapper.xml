<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.DisplayMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplay">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="display_name" jdbcType="VARCHAR" property="displayName"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="mat_type" jdbcType="VARCHAR" property="matType"/>
        <result column="pha_id" jdbcType="VARCHAR" property="phaId"/>
        <result column="pha_name" jdbcType="VARCHAR" property="phaName"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="is_default" jdbcType="VARCHAR" property="isDefault"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="pha_type" jdbcType="VARCHAR" property="phaType"/>
    </resultMap>
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.cbkj.beans.pharmacy.TPharmacy">
        <id column="pha_id" jdbcType="VARCHAR" property="phaId"/>
        <result column="pha_name" jdbcType="VARCHAR" property="phaName"/>
        <result column="pha_type" jdbcType="VARCHAR" property="phaType"/>
        <result column="pha_pid" jdbcType="VARCHAR" property="phaPid"/>
        <result column="pha_address" jdbcType="VARCHAR" property="phaAddress"/>
        <result column="drug_id" jdbcType="VARCHAR" property="drugId"/>
        <result column="drug_name" jdbcType="VARCHAR" property="drugName"/>
        <result column="pharmacy_code" jdbcType="VARCHAR" property="pharmacyCode"/>
        <result column="pha_category" jdbcType="VARCHAR" property="phaCategory"/>
        <result column="pha_address_number" jdbcType="VARCHAR" property="phaAddressNumber"/>
        <collection property="tDisplayList" ofType="com.jiuzhekan.cbkj.beans.pharmacy.TDisplay">
            <id column="id" jdbcType="VARCHAR" property="id"/>
            <result column="display_name" jdbcType="VARCHAR" property="displayName"/>
            <result column="mat_type" jdbcType="VARCHAR" property="matType"/>
            <result column="pha_id2" jdbcType="VARCHAR" property="phaId"/>
            <result column="pha_name2" jdbcType="VARCHAR" property="phaName"/>
            <result column="pha_type" jdbcType="VARCHAR" property="phaType"/>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
        id
        ,display_name,app_id,ins_code,dept_id,mat_type,pha_id,pha_name,sort,is_default,create_date,create_user,status
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplay">
        delete
        from t_display
        where id = #{ id }
    </delete>

    <delete id="deleteByPrimaryKeyL" parameterType="string">
        update t_display
        set status='1'
        where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByPhaId" parameterType="string">
        delete from t_display
        where pha_id = #{pha_id}
    </delete>
    <delete id="deleteByPhaId1">
        update  t_display
        set status='1'
        where pha_id = #{pha_id}
    </delete>

    <insert id="insert" parameterType="TDisplay">
        insert into t_display (<include refid="Base_Column_List"/>) values
        (#{id},#{displayName},#{appId},#{insCode},#{deptId},#{matType},#{phaId},#{phaName},#{sort},#{isDefault},#{createDate},#{createUser},#{status})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.displayName},#{item.appId},#{item.insCode},#{item.deptId},#{item.matType},#{item.phaId},#{item.phaName},#{item.sort},#{item.isDefault},#{item.createDate},#{item.createUser},#{item.status})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplay">
        update t_display
        <set>
            <if test="displayName != null">
                display_name = #{ displayName },
            </if>
            <if test="appId != null">
                app_id = #{ appId },
            </if>
            <if test="insCode != null">
                ins_code = #{ insCode },
            </if>
            <if test="deptId != null">
                dept_id = #{ deptId },
            </if>
            <if test="matType != null">
                mat_type = #{ matType },
            </if>
            <if test="phaId != null">
                pha_id = #{ phaId },
            </if>
            <if test="phaName != null">
                pha_name = #{ phaName },
            </if>
            <if test="sort != null">
                sort = #{ sort },
            </if>
            <if test="isDefault != null">
                is_default = #{ isDefault },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_display where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplay" resultMap="BaseResultMap">
        SELECT
        id,display_name,app_id,ins_code,dept_id,mat_type,pha_id,pha_name,sort,is_default,create_date,create_user,status
        from t_display
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getMatType" parameterType="string" resultMap="BaseResultMap">
        select * from t_display where pha_id=#{phaId} and status='0'
    </select>
    <select id="getTDisplayByPhaIdAndMatType" parameterType="string" resultMap="BaseResultMap">
        select  * from t_display where pha_id=#{phaId} and mat_type=#{matType} and status='0'
    </select>
    <select id="getPharmacyItemList" resultMap="BaseResultMap">
        SELECT td.id,
               td.display_name,
               tp.`pha_type`,
               td.`pha_name`,
               td.app_id,
               td.ins_code,
               td.dept_id,
               td.mat_type,
               td.pha_id,
               td.pha_name,
               td.sort,
               td.is_default,
               td.create_date,
               td.create_user,
               td.status
        FROM t_display AS td
                 JOIN t_pharmacy AS tp ON td.pha_id=tp.pha_id
        WHERE tp.status='0' and td.status='0'
    </select>

    <select id="getAllPharmacyDisplayList" resultMap="BaseResultMap2">
    select
        tp.pha_id,td.pha_id as pha_id2,tp.pha_name,td.pha_name as pha_name2,td.id,td.display_name
    from t_pharmacy as tp left join t_display as td on td.pha_id=tp.pha_id and td.status = '0'
where tp.status = '0' order by tp.sort_num,td.sort
    </select>

</mapper>