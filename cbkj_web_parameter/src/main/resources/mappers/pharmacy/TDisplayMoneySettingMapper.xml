<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayMoneySettingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMoneySetting">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="set_id" jdbcType="VARCHAR" property="setId"/>
        <result column="outpatient_or_hospitalization" jdbcType="VARCHAR" property="outpatientOrHospitalization"/>
        <result column="decoction_or_production_express" jdbcType="VARCHAR" property="decoctionOrProductionExpress"/>
        <result column="currency_or_formula" jdbcType="VARCHAR" property="currencyOrFormula"/>
        <result column="dic_id" jdbcType="VARCHAR" property="dicId"/>
        <result column="dic_code" jdbcType="VARCHAR" property="dicCode"/>
        <result column="dic_name" jdbcType="VARCHAR" property="dicName"/>
        <result column="charge_code" jdbcType="VARCHAR" property="chargeCode"/>
        <result column="charge_name" jdbcType="VARCHAR" property="chargeName"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="is_show_setting" jdbcType="VARCHAR" property="isShowSetting"/>
    </resultMap>


    <sql id="Base_Column_List">
        id
        ,set_id,outpatient_or_hospitalization,decoction_or_production_express,currency_or_formula,dic_id,dic_code,charge_code,charge_name,price,create_date,create_user,status,sort,is_show_setting,dic_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayMoneySetting">
        delete
        from t_display_money_setting
        where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_money_setting where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteBySetId" parameterType="String">
        delete
        from t_display_money_setting
        where set_id = #{setId}
    </delete>

    <insert id="insert" parameterType="TDisplayMoneySetting">
        insert into t_display_money_setting (<include refid="Base_Column_List"/>) values
        (#{id},#{setId},#{outpatientOrHospitalization},#{decoctionOrProductionExpress},#{currencyOrFormula},#{dicId},#{dicCode},#{chargeCode},#{chargeName},#{price},#{createDate},#{createUser},#{status},#{sort},#{isShowSetting},#{dicName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_money_setting (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.setId},#{item.outpatientOrHospitalization},#{item.decoctionOrProductionExpress},#{item.currencyOrFormula},#{item.dicId},#{item.dicCode},#{item.chargeCode},#{item.chargeName},#{item.price},#{item.createDate},#{item.createUser},#{item.status},#{item.sort},#{item.isShowSetting},#{item.dicName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayMoneySetting">
        update t_display_money_setting
        <set>
            <if test="setId != null">
                set_id = #{ setId },
            </if>
            <if test="outpatientOrHospitalization != null">
                outpatient_or_hospitalization = #{ outpatientOrHospitalization },
            </if>
            <if test="decoctionOrProductionExpress != null">
                decoction_or_production_express = #{ decoctionOrProductionExpress },
            </if>
            <if test="currencyOrFormula != null">
                currency_or_formula = #{ currencyOrFormula },
            </if>
            <if test="dicId != null">
                dic_id = #{ dicId },
            </if>
            <if test="dicCode != null">
                dic_code = #{ dicCode },
            </if>
            <if test="chargeCode != null">
                charge_code = #{ chargeCode },
            </if>
            <if test="chargeName != null">
                charge_name = #{ chargeName },
            </if>
            <if test="price != null">
                price = #{ price },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="sort != null">
                sort = #{ sort },
            </if>
            <if test="isShowSetting != null">
                is_show_setting = #{isShowSetting},
            </if>
            <if test="dicName != null">
                dic_name = #{dicName},
            </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_display_money_setting where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayMoneySetting" resultMap="BaseResultMap">
        SELECT
        id,set_id,outpatient_or_hospitalization,
        decoction_or_production_express,currency_or_formula,
        dic_id,dic_code,charge_code,charge_name,price,create_date,create_user,`status`,sort,is_show_setting,dic_name
        from t_display_money_setting
        <where>
            <if test=" setId != null and setId!='' ">
                and set_id = #{setId}
            </if>
            <if test=" outpatientOrHospitalization != null and outpatientOrHospitalization!='' ">
                and outpatient_or_hospitalization = #{outpatientOrHospitalization}
            </if>
            <if test=" decoctionOrProductionExpress != null and decoctionOrProductionExpress!='' ">
                and decoction_or_production_express = #{decoctionOrProductionExpress}
            </if>
            <if test=" currencyOrFormula != null and currencyOrFormula!='' ">
                and currency_or_formula = #{currencyOrFormula}
            </if>
            <if test=" dicId != null and dicId!='' ">
                and dic_id = #{dicId}
            </if>
        </where>
    </select>
    <select id="getByDicCode" resultMap="BaseResultMap" parameterType="TDisplayMoneySetting">
        SELECT
            b.`id`,a.`dic_name`,b.`set_id`,b.`outpatient_or_hospitalization`,b.`currency_or_formula`,b.`decoction_or_production_express`,a.`dic_id`,a.`dic_code`,
            ifnull(b.`is_show_setting`,'1') as is_show_setting,b.`charge_code`,b.`charge_name`,b.`price`
        FROM `t_dic_base` AS a LEFT JOIN `t_display_money_setting` AS b ON b.`dic_id` = a.`dic_id` AND b.`status`='0' AND b.`set_id`=#{setId} AND b.`outpatient_or_hospitalization`=#{outpatientOrHospitalization} and b.currency_or_formula=#{currencyOrFormula}
        WHERE a.`parent_id` IN (SELECT c.`dic_id` FROM `t_dic_base` AS c WHERE c.`parent_id`='0' AND c.`dic_code`=#{dicCode} and c.status='0')
        and a.app_id='000000' and a.ins_code='000000' and a.dept_id='000000' and a.status='0'

        ORDER BY a.`sort`
    </select>
    <select id="getDefult" parameterType="TDisplayMoneySetting" resultMap="BaseResultMap">
        SELECT *
        FROM `t_display_money_setting`
        WHERE `set_id` = #{setId}
          and outpatient_or_hospitalization = #{outpatientOrHospitalization}
          and decoction_or_production_express=#{decoctionOrProductionExpress}
          and currency_or_formula=#{currencyOrFormula}
    </select>
    <select id="getMoneySettingByDicId" parameterType="string" resultType="integer">
        SELECT COUNT(tdms.dic_id)
        FROM `t_display_money_setting` AS tdms
                 LEFT JOIN `t_display_decoction` AS tdd ON tdms.`set_id`=tdd.`set_id` AND tdd.`SHOW_DECOCTION`='0'
                 LEFT JOIN `t_display_express` AS tde ON tdms.`set_id`=tde.`set_id` AND tde.`SHOW_EXPRESS`='0'
                 LEFT JOIN `t_display_production` AS tdp ON tdms.`set_id`=tdp.`set_id` AND tdp.`SHOW_PRODUCTION`='0'
        WHERE tdms.dic_id=#{dicId}AND tdms.is_show_setting='0'
          AND ((tdd.`display_id` IN (SELECT id FROM t_display WHERE STATUS = '0')
            OR tde.`display_id` IN (SELECT id FROM t_display WHERE STATUS = '0')
            OR tdp.`display_id` IN (SELECT id FROM t_display WHERE STATUS = '0')))
    </select>
</mapper>