<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.PharmacyMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TPharmacy">
        <id column="pha_id" jdbcType="VARCHAR" property="phaId"/>
        <result column="pha_name" jdbcType="VARCHAR" property="phaName"/>
        <result column="pha_type" jdbcType="VARCHAR" property="phaType"/>
        <result column="pha_pid" jdbcType="VARCHAR" property="phaPid"/>
        <result column="dep_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="disable_date" jdbcType="TIMESTAMP" property="disableDate"/>
        <result column="disable_user" jdbcType="VARCHAR" property="disableUser"/>
        <result column="disable_username" jdbcType="VARCHAR" property="disableUsername"/>
        <result column="pha_address" jdbcType="VARCHAR" property="phaAddress"/>
        <result column="drug_id" jdbcType="VARCHAR" property="drugId"/>
        <result column="drug_name" jdbcType="VARCHAR" property="drugName"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="pharmacy_code" jdbcType="VARCHAR" property="pharmacyCode"/>
        <result column="sort_num" jdbcType="VARCHAR" property="sortNum"/>
        <result column="pha_category" jdbcType="VARCHAR" property="phaCategory"/>
        <result column="pha_address_number" jdbcType="VARCHAR" property="phaAddressNumber"/>
    </resultMap>


    <sql id="Base_Column_List">
        pha_id
        ,pha_name,
        pha_type,pha_pid,
        dep_id,create_date,
        create_user,create_user_name,
        status,disable_date,
        disable_user,disable_username,
        pha_address,drug_id,
        app_id,ins_code,
        pharmacy_code,sort_num,
        pha_category,
        pha_address_number
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TPharmacy">
        delete
        from t_pharmacy
        where pha_id = #{ phaId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_pharmacy where pha_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByPhaId" parameterType="string">
        update t_pharmacy
        set status = '1'
        where pha_id = #{ phaId }
    </delete>

    <insert id="insert" parameterType="TPharmacy">
        insert into t_pharmacy (<include refid="Base_Column_List"/>) values
        (#{phaId},#{phaName},
        #{phaType},#{phaPid},
        #{deptId},#{createDate},
        #{createUser},#{createUserName},
        #{status},#{disableDate},
        #{disableUser},#{disableUsername},
        #{phaAddress},#{drugId},
        #{appId},#{insCode},
        #{pharmacyCode},#{sortNum},
        #{phaCategory},
        #{phaAddressNumber})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_pharmacy (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.phaId},#{item.phaName},
            #{item.phaType},#{item.phaPid},
            #{item.deptId},#{item.createDate},
            #{item.createUser},#{item.createUserName},
            #{item.status},#{item.disableDate},#{item.disableUser},#{item.disableUsername},#{item.phaAddress},#{item.drugId},#{item.appId},#{item.insCode}#{item.sortNum},#{item.phaCategory},#{phaAddressNumber})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TPharmacy">
        update t_pharmacy
        <set>
            <if test="phaName != null">
                pha_name = #{ phaName },
            </if>
            <if test="phaType != null">
                pha_type = #{ phaType },
            </if>
            <if test="phaPid != null">
                pha_pid = #{ phaPid },
            </if>
            <if test="deptId != null">
                dep_id = #{ deptId },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="createUserName != null">
                create_user_name = #{ createUserName },
            </if>
            <if test="status != null">
                status = #{ status },
            </if>
            <if test="disableDate != null">
                disable_date = #{ disableDate },
            </if>
            <if test="disableUser != null">
                disable_user = #{ disableUser },
            </if>
            <if test="disableUsername != null">
                disable_username = #{ disableUsername },
            </if>
            <if test="phaAddress != null">
                pha_address = #{ phaAddress },
            </if>
            <if test="drugId != null">
                drug_id = #{ drugId },
            </if>
            <if test="appId != null">
                app_id = #{ appId },
            </if>
            <if test="insCode != null">
                ins_code = #{ insCode },
            </if>
            <if test="pharmacyCode != null">
                pharmacy_code = #{pharmacyCode},
            </if>
            <if test="sortNum != null">
                sort_num = #{sortNum},
            </if>
            <if test="phaCategory != null">
                pha_category = #{phaCategory},
            </if>
            <if test="phaAddressNumber != null">
                pha_address_number = #{phaAddressNumber},
            </if>
        </set>
        where pha_id = #{ phaId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_pharmacy where pha_id = #{id} and status !='1'
    </select>

    <select id="getPageListByObj" parameterType="TPharmacy" resultMap="BaseResultMap">
        SELECT tp.pha_id,
        (select count(1) from t_stock where t_stock.pha_id=tp.pha_id ) as isAlter,
        tp.pha_name,
        tp.pha_type,
        tp.pha_pid,
        tp.dep_id,
        tp.create_date,
        tp.create_user,
        tp.create_user_name,
        tp.status,
        tp.disable_date,
        tp.disable_user,
        tp.disable_username,
        tp.pha_address,
        tp.pha_address_number,
        tp.drug_id,
        (SELECT drug_name FROM t_drug_list WHERE tp.drug_id=t_drug_list.`drug_id`) AS drug_name,
        tp.app_id,
        tp.ins_code,
        tp.pharmacy_code,
        tp.sort_num,
        tp.pha_category
        FROM t_pharmacy AS tp
        where tp.status !='1'
        <if test="phaName != null and phaName !='' ">
            and tp.pha_name like CONCAT('%',trim(#{phaName}),'%')
        </if>
        <if test="drugId != null and drugId !='' ">
            and tp.drug_id = #{drugId}
        </if>
        <if test="status != null and status !='' ">
            and tp.status = #{status}
        </if> order by tp.create_date desc
    </select>
    <select id="getSuperiorPharmacy" parameterType="string" resultMap="BaseResultMap">
        select pha_id, pha_name
        from t_pharmacy
        where pha_id !=#{phaId} and status='0'
    </select>
    <select id="getSuperiorPharmacyInsert" parameterType="string" resultMap="BaseResultMap">
        select pha_id, pha_name
        from t_pharmacy where status='0'
    </select>
    <select id="getMaxNum" parameterType="string" resultType="java.lang.Integer">
        SELECT MAX(sort_num)
        FROM t_pharmacy
        WHERE drug_id = #{drugId}
    </select>

    <select id="getSortNums" parameterType="string" resultType="java.lang.Integer">
        SELECT sort_num
        FROM t_pharmacy
        WHERE drug_id = #{drugId}
    </select>
    <select id="getTPharmacy" parameterType="string" resultMap="BaseResultMap">
        SELECT tp.pha_id,
               tp.pha_name,
               tp.pha_type,
               tp.pha_pid,
               tp.dep_id,
               tp.create_date,
               tp.create_user,
               tp.create_user_name,
               tp.status,
               tp.disable_date,
               tp.disable_user,
               tp.disable_username,
               tp.pha_address,
               tp.pha_address_number,
               tp.drug_id,
               (SELECT drug_name FROM t_drug_list WHERE tp.drug_id = t_drug_list.`drug_id`) AS drug_name,
               tp.app_id,
               tp.ins_code,
               tp.pharmacy_code,
               tp.sort_num,
               tp.pha_category
        FROM t_pharmacy AS tp
        where tp.pha_id = #{phaId}
    </select>
    <select id="getTPharmacyLastSort" resultType="java.lang.Integer">
        select sort from t_display where sort is not null order by sort desc limit 1
    </select>


</mapper>