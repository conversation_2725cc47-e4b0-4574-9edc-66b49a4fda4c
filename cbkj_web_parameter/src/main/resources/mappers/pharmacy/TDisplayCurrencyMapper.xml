<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayCurrencyMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplayCurrency">
        <id column="display_currency_id" jdbcType="VARCHAR" property="displayCurrencyId"/>
        <result column="display_id" jdbcType="VARCHAR" property="displayId"/>
        <result column="withhold_switch" jdbcType="VARCHAR" property="withholdSwitch"/>
        <result column="pre_stock_switch" jdbcType="VARCHAR" property="preStockSwitch"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="many_spe_switch" jdbcType="INTEGER" property="manySpeSwitch"/>
        <result column="oral_medication_time_switch" jdbcType="INTEGER" property="oralMedicationTimeSwitch"/>
        <result column="urgent_sign" jdbcType="INTEGER" property="urgentSign"/>
    </resultMap>


    <sql id="Base_Column_List">
        display_currency_id
        ,display_id,withhold_switch,pre_stock_switch,create_date,create_user,many_spe_switch,oral_medication_time_switch,urgent_sign
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayCurrency">
        delete
        from t_display_currency
        where display_currency_id = #{ displayCurrencyId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_currency where display_currency_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBylistByDisplayId" parameterType="list">
        delete from t_display_currency where display_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByDisplayId" parameterType="string">
        delete
        from t_display_currency
        where display_id = #{displayId}
    </delete>
    <insert id="insert" parameterType="TDisplayCurrency">
        insert into t_display_currency (<include refid="Base_Column_List"/>) values
        (#{displayCurrencyId},#{displayId},#{withholdSwitch},#{preStockSwitch},
         #{createDate},#{createUser},#{manySpeSwitch},#{oralMedicationTimeSwitch},#{urgentSign})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_currency (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.displayCurrencyId},#{item.displayId},#{item.withholdSwitch},#{item.preStockSwitch},
             #{item.createDate},#{item.createUser},#{item.manySpeSwitch},#{item.oralMedicationTimeSwitch},#{item.urgentSign})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayCurrency">
        update t_display_currency
        <set>
            <if test="displayId != null">
                display_id = #{ displayId },
            </if>
            <if test="withholdSwitch != null">
                withhold_switch = #{ withholdSwitch },
            </if>
            <if test="preStockSwitch != null">
                pre_stock_switch = #{ preStockSwitch },
            </if>
            <if test="createDate != null">
                create_date = #{ createDate },
            </if>
            <if test="createUser != null">
                create_user = #{ createUser },
            </if>
            <if test="manySpeSwitch != null">
                many_spe_switch = #{manySpeSwitch},
            </if>
            <if test="oralMedicationTimeSwitch != null">
                oral_medication_time_switch = #{oralMedicationTimeSwitch},
            </if>
            <if test="urgentSign != null">
                urgent_sign = #{urgentSign},
            </if>
        </set>
        where display_currency_id = #{ displayCurrencyId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from t_display_currency where display_currency_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayCurrency" resultMap="BaseResultMap">
        SELECT display_currency_id,display_id,withhold_switch,pre_stock_switch,create_date,
               create_user,many_spe_switch,oral_medication_time_switch,urgent_sign
        from t_display_currency
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getTDisplayCurrency" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_display_currency where display_id=#{displayId}
    </select>

</mapper>