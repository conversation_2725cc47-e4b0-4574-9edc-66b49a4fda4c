<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayDecoctionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplayDecoction">
        <id column="set_id" jdbcType="VARCHAR"  property="setId" />
        <result column="display_id" jdbcType="VARCHAR" property="displayId" />
        <result column="outpatient_or_hospitalization" jdbcType="VARCHAR" property="outpatientOrHospitalization" />
        <result column="SHOW_DECOCTION" jdbcType="VARCHAR" property="showDecoction" />
        <result column="IS_DECOCTION" jdbcType="VARCHAR" property="isDecoction" />
        <result column="Is_decoction_must" jdbcType="VARCHAR" property="isDecoctionMust" />
        <result column="Is_decoction_num" jdbcType="VARCHAR" property="isDecoctionNum" />
        <result column="LEAST_DECOCTION" jdbcType="INTEGER" property="leastDecoction" />
        <result column="usually_decoction_set" jdbcType="VARCHAR" property="usuallyDecoctionSet" />
        <result column="Formula_decoction_set" jdbcType="VARCHAR" property="formulaDecoctionSet" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    </resultMap>


    <sql id="Base_Column_List">
    set_id,display_id,outpatient_or_hospitalization,SHOW_DECOCTION,IS_DECOCTION,Is_decoction_must,Is_decoction_num,LEAST_DECOCTION,usually_decoction_set,Formula_decoction_set,status,create_date,create_user
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayDecoction">
        delete from t_display_decoction where set_id = #{ setId }
    </delete>

    <delete id="deleteByCondition" parameterType="TDisplayDecoction">
        delete from t_display_decoction where display_id = #{displayId}
        <if test=" outpatientOrHospitalization != null and  outpatientOrHospitalization != ''">
            and outpatient_or_hospitalization = #{ outpatientOrHospitalization }
        </if>
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_decoction where set_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDisplayDecoction">
        insert into t_display_decoction (<include refid="Base_Column_List" />) values
        (#{setId},#{displayId},#{outpatientOrHospitalization},#{showDecoction},#{isDecoction},#{isDecoctionMust},#{isDecoctionNum},#{leastDecoction},#{usuallyDecoctionSet},#{formulaDecoctionSet},#{status},#{createDate},#{createUser})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_decoction (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.setId},#{item.displayId},#{item.outpatientOrHospitalization},#{item.showDecoction},#{item.isDecoction},#{item.isDecoctionMust},#{item.isDecoctionNum},#{item.leastDecoction},#{item.usuallyDecoctionSet},#{item.formulaDecoctionSet},#{item.status},#{item.createDate},#{item.createUser})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayDecoction">
        update t_display_decoction
        <set>
             <if test="displayId != null">
                display_id = #{ displayId },
             </if>
             <if test="outpatientOrHospitalization != null">
                outpatient_or_hospitalization = #{ outpatientOrHospitalization },
             </if>
             <if test="showDecoction != null">
                SHOW_DECOCTION = #{ showDecoction },
             </if>
             <if test="isDecoction != null">
                IS_DECOCTION = #{ isDecoction },
             </if>
             <if test="isDecoctionMust != null">
                Is_decoction_must = #{ isDecoctionMust },
             </if>
             <if test="isDecoctionNum != null">
                Is_decoction_num = #{ isDecoctionNum },
             </if>
             <if test="leastDecoction != null">
                LEAST_DECOCTION = #{ leastDecoction },
             </if>
             <if test="usuallyDecoctionSet != null">
                usually_decoction_set = #{ usuallyDecoctionSet },
             </if>
             <if test="formulaDecoctionSet != null">
                Formula_decoction_set = #{ formulaDecoctionSet },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
        </set>
        where set_id = #{ setId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_decoction where set_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayDecoction" resultMap="BaseResultMap">
        SELECT set_id,display_id,outpatient_or_hospitalization,SHOW_DECOCTION,IS_DECOCTION,Is_decoction_must,Is_decoction_num,LEAST_DECOCTION,usually_decoction_set,Formula_decoction_set,status,create_date,create_user
        from t_display_decoction
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getTDisplayDecoctionByDisplayId" parameterType="TDisplayDecoction" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_display_decoction where display_id=#{displayId} and outpatient_or_hospitalization=#{outpatientOrHospitalization}
    </select>

</mapper>