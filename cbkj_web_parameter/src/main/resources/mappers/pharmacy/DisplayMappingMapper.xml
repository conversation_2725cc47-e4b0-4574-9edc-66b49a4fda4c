<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.DisplayMappingMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplayMapping">
        <id column="display_id" jdbcType="VARCHAR"  property="displayId" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    </resultMap>


    <sql id="Base_Column_List">
    display_id,app_id,ins_code,dept_id,create_date,create_user
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayMapping">
        delete from t_display_mapping where display_id = #{ displayId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_mapping where display_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>
    <delete id="deleteById">
        delete from t_display_mapping where display_id = #{id}
    </delete>

    <insert id="insert"  parameterType="TDisplayMapping">
        insert into t_display_mapping (<include refid="Base_Column_List" />) values
        (#{displayId},#{appId},#{insCode},#{deptId},#{createDate},#{createUser})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_mapping (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.displayId},#{item.appId},#{item.insCode},#{item.deptId},#{item.createDate},#{item.createUser})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayMapping">
        update t_display_mapping
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
        </set>
        where display_id = #{ displayId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_mapping where display_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayMapping" resultMap="BaseResultMap">
        SELECT display_id,app_id,ins_code,dept_id,create_date,create_user
        from t_display_mapping
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getConfigApp" parameterType="string" resultMap="BaseResultMap">
        select * from t_display_mapping where display_id=#{id}
    </select>

</mapper>