<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.cbkj.mapper.pharmacy.TDisplayProductionMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.cbkj.beans.pharmacy.TDisplayProduction">
        <id column="set_id" jdbcType="VARCHAR"  property="setId" />
        <result column="display_id" jdbcType="VARCHAR" property="displayId" />
        <result column="outpatient_or_hospitalization" jdbcType="VARCHAR" property="outpatientOrHospitalization" />
        <result column="SHOW_PRODUCTION" jdbcType="VARCHAR" property="showProduction" />
        <result column="Is_show_production" jdbcType="VARCHAR" property="isShowProduction" />
        <result column="isProduction" jdbcType="VARCHAR" property="isProduction" />
        <result column="Is_show_production_type" jdbcType="VARCHAR" property="isShowProductionType" />
        <result column="production_num" jdbcType="INTEGER" property="productionNum" />
        <result column="usually_production_set" jdbcType="VARCHAR" property="usuallyProductionSet" />
        <result column="formula_production_set" jdbcType="VARCHAR" property="formulaProductionSet" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    </resultMap>


    <sql id="Base_Column_List">
    set_id,display_id,outpatient_or_hospitalization,SHOW_PRODUCTION,Is_show_production,Is_show_production_type,production_num,usually_production_set,formula_production_set,status,create_date,create_user,isProduction
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="TDisplayProduction">
        delete from t_display_production where set_id = #{ setId }
    </delete>

    <delete id="deleteByCondition" parameterType="TDisplayExpress">
        delete from t_display_production where display_id = #{displayId}
                                        <if test=" outpatientOrHospitalization != null and  outpatientOrHospitalization != ''">
                                            and outpatient_or_hospitalization = #{ outpatientOrHospitalization }
                                        </if>
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_display_production where set_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="TDisplayProduction">
        insert into t_display_production (<include refid="Base_Column_List" />) values
        (#{setId},#{displayId},#{outpatientOrHospitalization},#{showProduction},#{isShowProduction},#{isShowProductionType},#{productionNum},#{usuallyProductionSet},#{formulaProductionSet},#{status},#{createDate},#{createUser},#{isProduction})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_display_production (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.setId},#{item.displayId},#{item.outpatientOrHospitalization},#{item.showProduction},#{item.isShowProduction},#{item.isShowProductionType},#{item.productionNum},#{item.usuallyProductionSet},#{item.formulaProductionSet},#{item.status},#{item.createDate},#{item.createUser},,#{item.isProduction})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="TDisplayProduction">
        update t_display_production
        <set>
             <if test="displayId != null">
                display_id = #{ displayId },
             </if>
             <if test="outpatientOrHospitalization != null">
                outpatient_or_hospitalization = #{ outpatientOrHospitalization },
             </if>
             <if test="showProduction != null">
                SHOW_PRODUCTION = #{ showProduction },
             </if>
             <if test="isShowProduction != null">
                Is_show_production = #{ isShowProduction },
             </if>
             <if test="isShowProductionType != null">
                Is_show_production_type = #{ isShowProductionType },
             </if>
            <if test="isProduction != null">
                isProduction = #{isProduction},
            </if>
             <if test="productionNum != null">
                production_num = #{ productionNum },
             </if>
             <if test="usuallyProductionSet != null">
                usually_production_set = #{ usuallyProductionSet },
             </if>
             <if test="formulaProductionSet != null">
                formula_production_set = #{ formulaProductionSet },
             </if>
             <if test="status != null">
                status = #{ status },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="createUser != null">
                create_user = #{ createUser },
             </if>
        </set>
        where set_id = #{ setId }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_display_production where set_id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="TDisplayProduction" resultMap="BaseResultMap">
        SELECT set_id,display_id,outpatient_or_hospitalization,isProduction,SHOW_PRODUCTION,Is_show_production,Is_show_production_type,production_num,usually_production_set,formula_production_set,status,create_date,create_user
        from t_display_production
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getByDisplayId" resultMap="BaseResultMap" parameterType="TDisplayProduction">
        select <include refid="Base_Column_List" />
        from t_display_production where display_id = #{displayId} and outpatient_or_hospitalization = #{outpatientOrHospitalization}
    </select>

</mapper>