<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true" /><!-- 全局映射器启用缓存 -->
        <setting name="useGeneratedKeys" value="true" />
        <setting name="defaultExecutorType" value="REUSE" />
        <!-- 调试打印SQL-->
        <!--<setting name="logImpl" value="STDOUT_LOGGING" /> -->
        <!--日志打印SQL -->
        <setting name="logImpl" value="SLF4J" />
    </settings>

</configuration>