package com.jiuzhekan.cbkj.common.utils;

import cn.hutool.json.JSONUtil;
import com.jiuzhekan.cbkj.beans.sysExt.SysProductMatrix;
import junit.framework.TestCase;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class StreamUtilsTest extends TestCase {

    public void testDistinctBy() {
    }

    public void testToMapByKey() {
    }

    public void testExtractField() {
    }

    public void testToMap() {
        SysProductMatrix b1 = new SysProductMatrix();
        b1.setName("11");
        b1.setCategory("1");

        SysProductMatrix b2 = new SysProductMatrix();
        b2.setName("22");
        b2.setCategory("2");

        SysProductMatrix b3 = new SysProductMatrix();
        b3.setName("33");
        b3.setCategory("3");

        SysProductMatrix b4 = new SysProductMatrix();
        b4.setName("4");
        b4.setCategory(null);

        ArrayList<SysProductMatrix> list = new ArrayList<>();
        list.add(b1);
        list.add(b2);
        list.add(b3);
        list.add(b4);

        Map<String, SysProductMatrix> map = StreamUtils.toMap(list, SysProductMatrix::getCategory);
        System.out.println(JSONUtil.toJsonStr( map));
    }

    public void testConvertToMap() {
    }

    public void testConvertGroupMap() {
    }

    public void testConvertGroupLinkedMap() {
        SysProductMatrix b1 = new SysProductMatrix();
        b1.setName("11");
        b1.setCategory("1");

        SysProductMatrix b2 = new SysProductMatrix();
        b2.setName("22");
        b2.setCategory("1");

        SysProductMatrix b3 = new SysProductMatrix();
        b3.setName("33");
        b3.setCategory("3");

        SysProductMatrix b4 = new SysProductMatrix();
        b4.setName("4");
        b4.setCategory(null);

        SysProductMatrix b5 = new SysProductMatrix();
        b5.setName("5");
        b5.setCategory("");

        ArrayList<SysProductMatrix> list = new ArrayList<>();
        list.add(b3);
        list.add(b2);
        list.add(b1);
        list.add(b4);
        list.add(b5);

        Map<String, List<SysProductMatrix>> map = StreamUtils.toGroupLinkedMap(list, SysProductMatrix::getCategory);
        System.out.println(JSONUtil.toJsonStr( map));

        System.out.println("  ");
        Map<String, List<SysProductMatrix>> map1 = StreamUtils.toGroupMap(list, SysProductMatrix::getCategory);
        System.out.println(JSONUtil.toJsonStr( map1));
    }
}