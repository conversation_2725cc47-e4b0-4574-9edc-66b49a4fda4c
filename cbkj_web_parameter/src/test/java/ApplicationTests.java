import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.PreApiApplication;
import com.jiuzhekan.cbkj.beans.drug.StandTMAutoMappingVO;
import com.jiuzhekan.cbkj.beans.drug.TKnowMaterialMappingVo;
import com.jiuzhekan.cbkj.beans.drug.TMaterialKnowMapping;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulation;
import com.jiuzhekan.cbkj.beans.sysParam.PrescriptionRegulationItem;
import com.jiuzhekan.cbkj.controller.sysParam.PrescriptionRegulationController;
import com.jiuzhekan.cbkj.service.drug.TAppMaterialMappingService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 2017-08-01 21:32:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PreApiApplication.class)
public class ApplicationTests {

	@Autowired
	ApplicationContext ctx;

	@Autowired
	PrescriptionRegulationController prescriptionRegulationController;


	@Test
	public void testContextLoads() throws Exception {
		Assert.assertNotNull(this.ctx);
		Assert.assertTrue(this.ctx.containsBean("preApiApplication"));
	}

	@Test
	public  void  test(){
		List<PrescriptionRegulation> events = new ArrayList<>();

		PrescriptionRegulation event1 = new PrescriptionRegulation();
		event1.setLevel(8);
		event1.setEvent("响应事件");
		List<PrescriptionRegulationItem> children1 = new ArrayList<>();
		PrescriptionRegulationItem child1 = new PrescriptionRegulationItem();
		child1.setCategory(1);
		child1.setOpt("1,2");
		children1.add(child1);

	/*	PrescriptionRegulationItem child4 = new PrescriptionRegulationItem();
		child4.setCategory(1);
		child4.setOpt("1,2");
		children1.add(child4);*/
		event1.setChildren(children1);
		events.add(event1);

		PrescriptionRegulation event2 = new PrescriptionRegulation();
		event2.setLevel(7);
		event2.setEvent("响应事件");
		List<PrescriptionRegulationItem> children2 = new ArrayList<>();
		PrescriptionRegulationItem child2 = new PrescriptionRegulationItem();
		child2.setCategory(1);
		child2.setOpt("1,2");
		children2.add(child2);

		/*PrescriptionRegulationItem child3 = new PrescriptionRegulationItem();
		child3.setCategory(1);
		child3.setOpt("1,2");
		children2.add(child3);*/
		event2.setChildren(children2);
		events.add(event2);



		prescriptionRegulationController.prescriptionRegulationInsertOrUpdate(events);
	}


	@Test
	public void test1(){
		String name ="小";
		ResEntity condition = (ResEntity)prescriptionRegulationController.getCondition(name);
		System.out.println(JSON.toJSONString(condition.getData()));
	}

	@Test
	public void test2(){
		ResEntity prescriptionRegulation = prescriptionRegulationController.getPrescriptionRegulation();
		System.out.println(JSON.toJSONString(prescriptionRegulation.getData()));
	}

	@Test
	public void test3(){
		ResEntity event = (ResEntity)prescriptionRegulationController.getEvent();
		System.out.println(JSON.toJSONString(event.getData()));
	}


	@Autowired
	private TAppMaterialMappingService tAppMaterialMappingService;
	@Test
	public void test4(){
		TKnowMaterialMappingVo tKnowMaterialMappingVo = new TKnowMaterialMappingVo();
		tKnowMaterialMappingVo.setKMatId("32f1198c48f948bcb5d6702fff9dbdb2");
		tKnowMaterialMappingVo.setKMatName("冰糖");
		List<TMaterialKnowMapping> tMaterials = new ArrayList<>();
		TMaterialKnowMapping tMaterialKnowMapping = new TMaterialKnowMapping();
		tMaterialKnowMapping.setMatPriceId("32f1198c48f948bcb5d6702fff9dbdb2");
		tMaterialKnowMapping.setDrugId("0607169efbaa431ea99317a12a675710");
		tMaterials.add(tMaterialKnowMapping);
		tKnowMaterialMappingVo.setTMaterials(tMaterials);
		tAppMaterialMappingService.getMappingOne(tKnowMaterialMappingVo);


	}

	@Test
	public void test5(){
		StandTMAutoMappingVO standTmAutoMappingVO = new StandTMAutoMappingVO();
		standTmAutoMappingVO.setDrugId("6ec8ead40b79415a982bbdddc09d2258");
		tAppMaterialMappingService.getMappingByDrugId(standTmAutoMappingVO);


	}




}