<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="150" type="DEPLOY_HOST_RUN_CONFIGURATION" factoryName="Deploy to Host">
    <option name="accountModel" />
    <option name="accountModels" />
    <option name="address" />
    <option name="afterCommand" value="docker restart pre_parameter" />
    <option name="alreadyReset" value="true" />
    <option name="autoOpen" value="true" />
    <option name="beforeCommand" value="" />
    <option name="defaultTabIdx" value="0" />
    <option name="ecsInstance">
      <EcsInstance>
        <option name="OSType" />
        <option name="instanceId" />
        <option name="instanceName" />
        <option name="netType" />
        <option name="privateIps" />
        <option name="publicIps" />
        <option name="regionId" />
        <option name="tags" />
      </EcsInstance>
    </option>
    <option name="ecsInstances" />
    <option name="hostIds">
      <list>
        <option value="4" />
      </list>
    </option>
    <option name="hostTagId" value="0" />
    <option name="location" value="/home/<USER>" />
    <option name="pathOrUrl" value="D:\ZJH\git_project\pre_parameter\cbkj_web_parameter\target\pre_parameter_1.0.jar" />
    <option name="tagId" value="0" />
    <option name="terminalCommand" value="docker logs -f  --tail 100 cbkj_pre_parameter" />
    <option name="type" value="HOST" />
    <option name="uploadType" value="FILE" />
    <method v="2">
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/pom.xml" goal="clean" />
      <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/pom.xml" goal="package" />
    </method>
  </configuration>
</component>